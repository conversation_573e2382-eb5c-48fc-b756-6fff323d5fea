Config = {}

Config.MLOs = {
  VSPD = {
    MugshotLocation = vector3(-1074.137, -816.426, 2.688), -- VSPD suspect location
    MugshotSuspectHeading = 218.0, -- VSPD suspect heading
    MugShotCamera = {
      x = -1073.103,
      y = -817.883,
      z = 4.2,
      r = {x = 0.0, y = 0.0, z = 37.287}
    },
    BoardHeader = "Los Santos Police Department",
    BoardFooter = "Vespucci"
  },
  VPD = {
    MugshotLocation = vector3(610.943, 9.833, 74.045), -- VPD suspect location
    MugshotSuspectHeading = 247.271, -- VPD suspect heading
    MugShotCamera = {
      x = 612.521,
      y = 9.210,
      z = 75.545,
      r = {x = 0.0, y = 0.0, z = 69.542}
  },
    BoardHeader = "Los Santos Police Department",
    BoardFooter = "Vinewood"
  },
  SANDYSO = {
    MugshotLocation = vector3(1853.851,3677.810,33.333), -- SANDYSO suspect location
    MugshotSuspectHeading = 119.443, -- SANDYSO suspect heading
    MugShotCamera = {
      x = 1852.192,
      y = 3676.771,
      z = 34.726,
      r = {x = 0.0, y = 0.0, z = 299.697}
    },
    BoardHeader = "Blaine County Sheriff’s Office",
    BoardFooter = "Sandy Shores"
  },
  PALETOSO = {
    MugshotLocation = vector3(-453.812, 6000.526, 26.596),
    MugshotSuspectHeading = 223.014,
    MugShotCamera = {
      x = -452.287,
      y = 5999.058,
      z = 27.950,
      r = {x = 0.0, y = 0.0, z = 43.334}
    },
    BoardHeader = "Blaine County Sheriff’s Office",
    BoardFooter = "Paleto Bay"
  }
}

Config.DefaultMLO = "VSPD" -- Default MLO if none specified
Config.WaitTime = 2000 -- Time before and after the photo is taken
Config.Photos = 1 -- Front, Back, Side (use 4 for both sides)