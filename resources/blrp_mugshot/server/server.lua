pMugshot = {}
P.bindInstance('mugshot', pMugshot)
tMugshot = T.getInstance('blrp_mugshot', 'mugshot')
tZones = T.getInstance('blrp_zones', 'zones')

local MugShots = {}

RegisterNetEvent('blrp_mugshot:server:triggerSuspect', function(_, event_data)

  local character = exports.blrp_core:character(source)
  local suspect = event_data.target_source
  local suspect_character = exports.blrp_core:character(suspect)
  local department

  if suspect then
    if tZones.isInsideMatchedZone(suspect, { 'VSPD_MugShot' }) then
      department = 'VSPD'
    elseif tZones.isInsideMatchedZone(suspect, { 'SandySO_MugShot' }) then
      department = 'SANDYSO'
    elseif tZones.isInsideMatchedZone(suspect, { 'VPD_MugShot' }) then
      department = 'VPD'
    elseif tZones.isInsideMatchedZone(suspect, { 'PaletoSO_MugShot' }) then
      department = 'PALETOSO'
    else
      department = Config.MLOs[Config.DefaultMLO]
    end
    tMugshot.startMugShot(suspect_character.source, { department, character.source })
  else
    character.notify('No suspect found nearby.')
  end
end)

-- Store mugshot URLs and send them to the cop
pMugshot.storeMugshots = function(characterid, MugShotURLs, copId)
  local character = exports.blrp_core:character(copId)
  MugShots[characterid] = MugShotURLs
  if copId and MugShotURLs and #MugShotURLs > 0 then
    local urlString = table.concat(MugShotURLs, "\n")
    character.prompt('Mugshot URLs (Ctrl + A / Ctrl + C)', urlString)
  end
end
