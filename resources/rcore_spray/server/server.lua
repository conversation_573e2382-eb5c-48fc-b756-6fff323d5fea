pSprays = {}
P.bindInstance('sprays', pSprays)

pSprays.getStencils = function()
  return exports.blrp_core:character(source).getItemsFromCategory('spray_stencils', true)
end

SPRAYS = {}

exports('GetSprays', function()
  return SPRAYS
end)

FastBlacklist = {}

Citizen.CreateThread(function()
  if Config.Blacklist then
    for _, word in pairs(Config.Blacklist) do
      FastBlacklist[word] = word
    end
  end
end)

function GetSprayAtCoords(pos)
  for _, spray in pairs(SPRAYS) do
    if spray.location == pos then
      return spray
    end
  end
end

local primary_colors = {
  ['black'] = vector3(0, 0, 0),
  ['white'] = vector3(255, 255, 255),
  ['red'] = vector3(255, 0, 0),
  ['orange'] = vector3(255, 128, 0),
  ['yellow'] = vector3(255, 255, 0),
  ['green'] = vector3(0, 255, 0),
  ['blue'] = vector3(0, 0, 255),
  ['purple'] = vector3(148, 0, 211),
}

function findNearestColorString(color_rgb)
  local closest_color = nil
  local closest_color_distance = -1

  for primary_color_name, primary_color_rgb in pairs(primary_colors) do
    local distance = #(color_rgb - primary_color_rgb)

    if closest_color_distance == -1 or distance < closest_color_distance then
      closest_color_distance = distance

      closest_color = {
        name = primary_color_name,
        rgb = primary_color_rgb,
      }
    end
  end

  return closest_color.name
end

function checkHasColoredCan(character, color_rgb, quantity_needed, subtract_quantity)
  quantity_needed = quantity_needed * 2.0

  local color_needed = findNearestColorString(color_rgb)
  local spraypaint_item_id = 'spraypaint_' .. color_needed

  local item_meta, idname = character.hasGetItemMeta(spraypaint_item_id, true)

  if idname then
    spraypaint_item_id = idname
  elseif not idname and character.take(spraypaint_item_id, 1, false) then
    character.give(spraypaint_item_id .. ':meta:' .. GetGameTimer(), 1, {
      spray_quantity = 100.0
    }, false)
  end

  item_meta, idname = character.hasGetItemMeta(spraypaint_item_id)

  if not idname then
    character.notify('You do not have ' .. color_needed .. ' spray paint')
    return false
  end

  local current_quantity = 100.0

  if item_meta and item_meta.spray_quantity then
    current_quantity = item_meta.spray_quantity
  end

  if current_quantity < quantity_needed then
    character.notify('You do not have enough paint in your can to spray this')
    return false
  end

  if subtract_quantity then
    exports.blrp_core:ModifyItemMeta(character.source, idname, 'spray_quantity', (current_quantity - quantity_needed))
  end

  return true
end

pSprays.precheck = function(color_rgb, text, spray_scale)
  local character = exports.blrp_core:character(source)

  if text then
    if exports.blrp_core:ScanInputForBadWords(character.source, 'spray', text) then
      return false
    end
  end

  if not checkHasColoredCan(character, color_rgb, spray_scale) then
    return false
  end

  return true
end

RegisterNetEvent('rcore_spray:addSpray', function(spray)
  local character = exports.blrp_core:character(source)

  if spray.image then
    local spray_config = nil

    for _, v in pairs(IMAGES) do
      if v.name == spray.image then
        spray_config = v
        break
      end
    end

    if spray_config and spray_config.item_id and not character.take(spray_config.item_id, 1) then
      return
    end
  end

  local target_color_rgb = nil

  if spray.text then
    target_color_rgb = hex2rgb(spray.color)
  else
    local image_config = FastImageMap[spray.imageDict .. spray.image]

    target_color_rgb = vector3(image_config.color[1], image_config.color[2], image_config.color[3])
  end

  if not checkHasColoredCan(character, target_color_rgb, spray.rangedScale, true) then
    return false
  end

  local insert_id = MySQL.Sync.insert([[
  INSERT sprays
  (`x`, `y`, `z`, `origX`, `origY`, `origZ`, `rx`, `ry`, `rz`, `scale`, `text`, `imageDict`, `image`, `font`, `color`, `interior`, `identifier`, `instance`)
  VALUES
  (@x, @y, @z, @origX, @origY, @origZ, @rx, @ry, @rz, @scale, @text, @imageDict, @image, @font, @color, @interior, @identifier, @instance)
  ]], {
    ['@x'] = spray.realLocation.x,
    ['@y'] = spray.realLocation.y,
    ['@z'] = spray.realLocation.z,
    ['@origX'] = spray.location.x,
    ['@origY'] = spray.location.y,
    ['@origZ'] = spray.location.z,
    ['@rx'] = spray.realRotation.x,
    ['@ry'] = spray.realRotation.y,
    ['@rz'] = spray.realRotation.z,
    ['@scale'] = spray.scale,
    ['@text'] = spray.text,
    ['@imageDict'] = spray.imageDict,
    ['@image'] = spray.image,
    ['@font'] = spray.font,
    ['@color'] = spray.color,
    ['@interior'] = spray.interior,
    ['@identifier'] = character.get('id'),
    ['@instance'] = character.getInstance(),
  })

  spray.identifier = character.get('id')
  spray.id = insert_id
  spray.instance = character.getInstance()

  local i = 1

  while SPRAYS[i] do
    i = i + 1
  end

  SPRAYS[i] = spray

  if spray.text then
    exports.blrp_core:LogDiscord('chat', '**SPRAY: ' .. character.getStringIdentifierVrpOnly() .. '** text = ' .. spray.text .. ' / coords = ' .. spray.realLocation, 'SPRAY')

    TriggerEvent('rcore_sprays:addSpray', character.source, spray.text, spray.location)
  else
    local cfgImg = FastImageMap[spray.imageDict .. spray.image]

    if cfgImg and cfgImg.gangTags then
      if type(cfgImg.gangTags) == 'string' then
        TriggerEvent('rcore_sprays:addSpray', character.source, cfgImg.gangTags, spray.location)
      else
        for _, t in pairs(cfgImg.gangTags) do
          TriggerEvent('rcore_sprays:addSpray', character.source, t, spray.location)
        end
      end
    end
  end

  local spray_type = 'Text'
  local spray_title = spray.text

  if spray.imageDict then
    spray_type = 'Image'
    spray_title = spray.imageDict .. '/' .. spray.image
  end

  character.log('ACTION', 'Sprayed paint at location / location = ' .. spray.location .. ' / type = ' .. spray_type .. ' / title = ' .. spray_title)

  TriggerClientEvent('rcore_spray:addSpray', -1, spray)
end)

RegisterNetEvent('rcore_spray:remove', function(id, remover_can)
  local character = exports.blrp_core:character(source)

  if remover_can then
    if not character.take('spray_remover', 1) then
      return
    end
  elseif not character.hasItemQuantity('paint_thinner', 1, true) or
         not character.hasItemQuantity('rag', 1, true) or
         not character.take('paint_thinner', 1) or
         not character.take('rag', 1) then
    if not character.hasItemQuantity('paint_thinner_hm', 1, true) or
       not character.hasItemQuantity('rag', 1, true) or
       not character.take('paint_thinner_hm', 1) or
       not character.take('rag', 1) then
      return
    end
  end

  local removed_spray = removeSpray(id, remover_can)

  if removed_spray then
    local spray_type = 'Text'
    local spray_title = removed_spray.text

    if removed_spray.imageDict then
      spray_type = 'Image'
      spray_title = removed_spray.imageDict .. '/' .. removed_spray.image
    end

    character.log('ACTION', 'Cleaned spray paint / location = ' .. removed_spray.location .. ' / id = ' .. removed_spray.id .. ' / type = ' .. spray_type .. ' / title = ' .. spray_title)

    -- TODO: something with gang turfs and reputation in the future?

    return
  end

  character.notify('No spray found nearby')
end)

function removeSpray(id)
  for spray_idx, spray_data in pairs(SPRAYS) do
    if spray_data.id == id then
      MySQL.Sync.execute('DELETE FROM sprays WHERE id = @id LIMIT 1', {
        id = spray_data.id
      })

      SPRAYS[spray_idx] = nil

      TriggerClientEvent('rcore_spray:removeSpray', -1, spray_data.location)

      return spray_data
    end
  end

  return false
end

exports('RemoveSpray', removeSpray)

Citizen.CreateThread(function()
  --[[ Don't want to auto wipe sprays for now, so turned off
  MySQL.Sync.execute('DELETE FROM sprays WHERE DATEDIFF(NOW(), created_at) >= @days', {
    days = Config.SPRAY_PERSIST_DAYS
  })
  ]]

  local results = MySQL.Sync.fetchAll('SELECT * FROM sprays')

  for _, s in pairs(results) do
    table.insert(SPRAYS, {
      id = s.id,
      location = vector3(s.origX + 0.0, s.origY + 0.0, s.origZ + 0.0),
      realLocation = vector3(s.x + 0.0, s.y + 0.0, s.z + 0.0),
      realRotation = vector3(s.rx + 0.0, s.ry + 0.0, s.rz + 0.0),
      scale = tonumber(s.scale) + 0.0,
      text = s.text,
      imageDict = s.imageDict,
      image = s.image,
      font = s.font,
      color = s.color,
      interior = (s.interior == 1) and true or false,
      identifier = s.identifier,
      instance = s.instance or 'global',
    })
  end

  TriggerLatentClientEvent('rcore_spray:setSprays', -1, 1000000, SPRAYS)
end)

RegisterNetEvent('rcore_spray:playerSpawned', function()
  local player = source
  TriggerLatentClientEvent('rcore_spray:setSprays', player, 1000000, SPRAYS)
end)
