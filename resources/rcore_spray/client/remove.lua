local IsCurrentlyRemoving = false

RegisterNetEvent('rcore_spray:removeClosestSpray', function(remover_can)
  if IsCurrentlyRemoving then
    return
  end

  local ped = PlayerPedId()
  local coords = GetEntityCoords(ped)

  local closestSprayId = nil
  local closestSprayDist = nil

  for _, spray in pairs(SPRAYS) do
    local sprayPos = spray.location
    local dist = #(sprayPos - coords)

    if dist < 3.0 and (not closestSprayDist or closestSprayDist > dist) then
      closestSprayId = spray.id
      closestSprayDist = dist
    end
  end

  if not closestSprayId then
    exports.blrp_core:me().notify(Config.Text.NO_SPRAY_NEARBY)
  end

  local ragProp = CreateSprayRemoveProp(ped)

  IsCurrentlyRemoving = true

  local duration = Config.SPRAY_REMOVE_DURATION

  if remover_can then
    duration = 30000
  end

  exports.mythic_progbar:Progress({
    name = 'spray_clean_progress',
    duration = duration,
    label = 'Cleaning Spray',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = true,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = 'timetable@maid@cleaning_window@idle_a',
      anim = 'idle_a',
      flags = 49,
    },
  }, function(cancelled)
    if cancelled then
      IsCurrentlyRemoving = false
      RemoveSprayRemoveProp(ragProp)

      return
    end

    IsCurrentlyRemoving = false
    RemoveSprayRemoveProp(ragProp)
    TriggerServerEvent('rcore_spray:remove', closestSprayId, remover_can)
  end)
end)

function CreateSprayRemoveProp(ped)
    local ragProp = CreateObject(
        `p_loose_rag_01_s`, --`prop_ecola_can`,
        0.0, 0.0, 0.0,
        true, false, false
    )

    AttachEntityToEntity(ragProp, ped, GetPedBoneIndex(ped, 28422), x, y, z, ax, ay, az, true, true, false, true, 1, true)

    return ragProp
end

function RemoveSprayRemoveProp(ent)
    if NetworkGetEntityOwner(ent) ~= PlayerId() then
        NetworkRequestControlOfEntity(ent)
        Wait(500)
    end

    DeleteEntity(ent)
end
