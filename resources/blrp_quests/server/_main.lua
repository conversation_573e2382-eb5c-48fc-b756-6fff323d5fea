tQuests = T.getInstance('blrp_quests', 'main')

RegisterNetEvent('blrp_quests:server:talkToPed', function(_, event_data)
  if not event_data or (not event_data.filter_value and not event_data.quest_id) then
    return
  end

  local character = exports.blrp_core:character(source)

  TriggerEvent('blrp_quests:broadcastTalkToPed', character, event_data.filter_value or event_data.quest_id, event_data)
end)
