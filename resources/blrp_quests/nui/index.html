<html>
  <head>
    <title>BLRP Quests NUI</title>

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto+Mono">

    <style type="text/css">
      body {
        font-family: "Roboto Mono", sans-serif;
      }

      /* ------------------------------ */

      #container {
        display: none;
        justify-content: center;
        align-items: flex-end;
        height: 100%;
      }

      #wrapper {
        width: 600px;
        height: auto;
        padding: 2% 0;
        position: relative;
        margin: 75px auto;

        color: black;
      }

      #wrapper-bot {
        width: 600px;
        height: 36px;
        position: absolute;
        bottom: -5px;
      }

      .reel-row {
        display: flex;
        justify-content: space-around;
      }

      .reel-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        row-gap: 10px;
        /* min-width: 100px; */
      }

      img {
        height: 30px;
        width: 30px;
      }

      #mugshot-left, #mugshot-right {
        height: 100px;
        width: 100px;
        object-fit: contain;
      }

      #sub-wrapper {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: 0 3%;
      }

      #quest-wrap {
        max-width: 450px;
        width: 450px;
      }

      #quest-continue {
        font-style: italic;
        font-size: 13px;
      }

      #quest-text {
        display: flex;
        flex-direction: column;
        padding: 2% 4%;
      }

      #quest-text span {
        font-size: 14px;
      }
    </style>
  </head>

  <body>
    <div id="container">
      <div id="wrapper">
        <div id="wrapper-bot"></div>
        <div id="sub-wrapper">
          <div class="reel-row">

            <div class="reel-item">
              <img id="mugshot-left" src="person1.png" />
            </div>

            <div class="reel-item" id="quest-wrap">
              <div id="quest-text">#QUEST_TEXT#</div>

              <div id="quest-continue">
                Press <kbd>Space</kbd> to continue
              </div>
            </div>

            <div class="reel-item">
              <img id="mugshot-right" src="person2.png" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <script type="text/javascript">
      let theme = false;

      window.addEventListener("keyup", (e) => {
        let code = e.code
        let numeric = false;

        for (let i = 1; i <= 5; i++) {
          if (code == `Digit${i}` || code == `Numpad${i}`) {
            code = i;
            numeric = true;
          }
        }

        if(code === 'Space' || numeric) {
          fetch('https://blrp_quests/keyPressed', {
            method: 'POST',
            body: JSON.stringify({
              code: code
            })
          }).then(resp => resp.json()).then(resp => {});
        }

        if(code === 'Escape') {
          fetch('https://blrp_quests/escapePressed', {
            method: 'POST'
          }).then(resp => resp.json()).then(resp => {});
        }
      });

      window.addEventListener("message", (e) => {
        let data = e.data
        let action = data.action

        let container = document.getElementById('container');

        if (action === 'hide') {
          container.style.display = 'none';
        } else if (action === 'setQuestInfo') {
          if(data.theme && data.theme != theme) {
            theme = data.theme;

            document.getElementById('wrapper').style.backgroundImage = `url('theme_${data.theme}/top.png')`;
            document.getElementById('wrapper-bot').style.backgroundImage = `url('theme_${data.theme}/bottom.png')`;
          }

          container.style.display = 'flex';

          let [img_left, img_right, dialogue] = data.quest_text;
          let img_left_el = document.getElementById('mugshot-left');
          let img_right_el = document.getElementById('mugshot-right');
          let continue_el = document.getElementById('quest-continue');

          /*
            <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
            <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
          */

          if(img_left) {
            if (img_left.includes('item:')) {
              image_src = `https://cfx-nui-blrp_inventory/images/${img_left.replace('item:', '')}.webp`;
            }
            else if(img_left.includes('static:')) {
              img_left = img_left.replace('static:', '');
              let [dict, img] = img_left.split('/');

              image_src = `https://nui-img/${dict}/${img}`;
            }
            else
            {
              image_src = 'https://nui-img/' + data.img_dicts[img_left] + '/' + data.img_dicts[img_left] + '?t=' + String(Math.round(new Date().getTime() / 1000));
            }

            img_left_el.style.display = 'block';
            img_left_el.src = image_src;
          } else {
            img_left_el.style.display = 'none';
          }

          if(img_right) {
            if (img_right.includes('item:')) {
              image_src = `https://cfx-nui-blrp_inventory/images/${img_right.replace('item:', '')}.webp`;
            }
            else if(img_right.includes('static:')) {
              img_right = img_right.replace('static:', '');
              let [dict, img] = img_right.split('/');

              image_src = `https://nui-img/${dict}/${img}`;
            }
            else
            {
              image_src = 'https://nui-img/' + data.img_dicts[img_right] + '/' + data.img_dicts[img_right] + '?t=' + String(Math.round(new Date().getTime() / 1000));
            }

            img_right_el.style.display = 'block';
            img_right_el.src = image_src;
          } else {
            img_right_el.style.display = 'none';
          }

          if(typeof(dialogue) == 'string') {
            document.getElementById('quest-text').innerHTML = dialogue;
            continue_el.style.display = 'block';
          } else {
            let dialog_compiled = '';

            dialogue.forEach(function(element, index) {
              dialog_compiled += `<span class="quest-continue">${index + 1}. ${element}</span>`;
            });

            document.getElementById('quest-text').innerHTML = dialog_compiled;
            continue_el.style.display = 'none';
          }
        }
      })
    </script>
  </body>
</html>
