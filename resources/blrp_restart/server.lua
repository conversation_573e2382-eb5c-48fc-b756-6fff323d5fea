local next_reboot = nil

function calculateNextAutoRestart()
  local year = tonumber(os.date('%Y'))
  local month = tonumber(os.date('%m'))
  local day = tonumber(os.date('%d'))
  local hour = tonumber(os.date('%H'))

  if hour < 14 then
    -- reboot is TODAY at 14:00
    next_reboot = os.time{ year = year, month = month, day = day, hour = 14, minute = 0 }
  else
    -- reboot is TOMORROW at 14:00
    next_reboot = os.time{ year = year, month = month, day = day, hour = 14, minute = 0 } + (60 * 60 * 24)
  end

  print('Next reboot scheduled for', os.date('%Y-%m-%d %H:%M', next_reboot))
end

calculateNextAutoRestart()

RegisterCommand('scheduleReboot', function(sender, args, raw)
  if sender ~= 0 then
    return
  end

  local minutes = tonumber(args[1])

  if not minutes or minutes < 0 then
    print(raw .. ': Invalid minutes argument')
    return
  end

  next_reboot = os.time() + (60 * minutes)
  print(raw .. ': Next reboot set to ' .. os.date('%Y-%m-%d %H:%M', next_reboot))
end, true)

local warnings = {
  [30] = false,
  [15] = false,
  [10] = false,
  [5] = false,
  [4] = false,
  [3] = false,
  [2] = false,
  [1] = false,
  [0] = false,
}

Citizen.CreateThread(function()
  while true and not GlobalState.is_dev do
    local diff_seconds = math.max(0, (next_reboot - os.time()))
    local diff_minutes = math.ceil(diff_seconds / 60)

    if warnings[diff_minutes] == false then
      warnings[diff_minutes] = true

      -- Don't send an event for 0 minutes
      if diff_minutes ~= 0 then
        local data = {
          secondsRemaining = diff_seconds,
          minutesRemaining = diff_minutes,
        }

        TriggerEvent('txAdmin:events:scheduledRestart', data)
        TriggerClientEvent('txAdmin:events:scheduledRestart', -1, data)

        local message = 'This server is scheduled to restart in ' .. diff_minutes .. ' minute(s)'

        if diff_minutes <= 2 then
          message = message .. '. Please disconnect now'
        end

        TriggerClientEvent('mythic_notify:client:SendAlert', -1, {
          type = 'error',
          text = message,
          length = 15000,
        })

        print('Server shutting down in ' .. diff_minutes .. ' minutes')
      end

      -- Kick everyone at 0 minutes
      if diff_minutes == 0 then
        print('Disconnecting all clients')

        for _, player in pairs(GetPlayers()) do
          DropPlayer(player, 'Server shutting down for scheduled restart')
        end
      end
    end

    Citizen.Wait(2000)
  end
end)
