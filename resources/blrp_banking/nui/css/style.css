* {
  font-family: system-ui,
  -apple-system, BlinkMacSystemFont,
  "Segoe UI",
  "Roboto",
  "Oxygen",
  "Ubuntu",
  "Cantarell",
  "Fira Sans",
  "Droid Sans",
  "Helvetica Neue",
  Aria<PERSON>, sans-serif;
  text-shadow: 0px 0px 6px rgb(9, 10, 26);
}

::-webkit-scrollbar {
    width: 0.3vw;
}

::-webkit-scrollbar-thumb {
    background: deepskyblue
}

:disabled {
  cursor: not-allowed;
}

#container {
  height: 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;

  width: 100%;
  height: 100%;
}

#wrapper {
  width: 1200px;
  height: 700px;
  overflow: hidden;

  background: rgba(36, 38, 60, 0.8);
  box-shadow: 0px 0px 10px deepskyblue;
  border: 1px solid deepskyblue;
  border-radius: 25px;

  color: white;

  display: flex;
  flex-direction: column;
}

#header {
  height: 50px;
  padding: 10px 20px;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  border-bottom: 1px solid #00BEFE;

  display: flex;
  justify-content: space-between;
  align-items: center;
}

  #header .icon {
    font-size: 40px;
  }

  .header-buttons {
    display: flex;
    align-items: center;
  }

  .header-buttons button {
    color: white;
    background-color: rgba(0, 0, 0, 0.4);
    cursor: pointer;
    border: 1px solid #47474C;
    border-radius: 20px;
    padding: 10px;
    font-size: 16px;
    margin-left: 5px;
  }

  .header-buttons button.close {
    background: none;
    border: none;
    font-size: 26px;
  }

#contents {
  height: 650px;
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;

  display: flex;
}

#left {
  display: flex;
  flex-direction: column;
  width: 30%;
}

#cash {
  padding: 10px;
}

#cash .account-main {
  background-color: rgba(0, 0, 0, 0.4);
  border: 1px solid #47474C;
  border-radius: 12px;
}

#accounts {
  overflow-y: scroll;

  height: 100%;
  padding: 10px;
}

  .account {
    background-color: rgba(0, 0, 0, 0.4);
    cursor: pointer;
    border: 1px solid #47474C;
    border-radius: 12px;
    margin-bottom: 15px;
    line-height: 1.5;
  }

    .account-active {
      box-shadow: 0px 0px 10px deepskyblue;
    }

    .account-main {
      padding: 10px;
      display: flex;
      justify-content: space-between;
    }

      .account-info {
        font-size: 14px;
      }

        .account-type {
          font-size: 16px;
          font-weight: bold;
        }

      .account-balance-outer {
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }

        .account-balance-inner {
          display: inline-block;
          font-size: 12px;
          font-weight: 700;
          padding: 3px 5px;
          border-radius: 5px;
          min-width: 60px;
          text-align: center;

          background-color: #1c6860;
        }

        .negative {
          background-color: #681c24;
        }

    .account-buttons {
      display: flex;
      background-color: #2C2D4D;
      border-top: 1px solid #47474C;
      padding: 10px;
      justify-content: space-around;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
    }

      .account-buttons button {
        cursor: pointer;
        background: none;
        color: #FFF;
        font-weight: bold;
        border: none;
      }

      .account-buttons button:hover {
        color: #00BEFE;
      }

#transactions {
  border-left: 1px solid #00BEFE;
  width: 75%;
  padding: 10px;
  overflow-y: scroll;
}

  .transaction {
    background-color: rgba(0, 0, 0, 0.4);
    border: 1px solid #47474C;
    border-radius: 12px;
    margin-bottom: 15px;
    line-height: 1.5;
    padding: 10px;
    display: flex;
    justify-content: space-between;
  }

    .transaction-body {
      display: flex;
      justify-content: space-between;
      flex-direction: column;
    }

      .transaction-details {

      }

        .transaction-type {
          font-size: 16px;
          font-weight: bold;
          text-transform: uppercase;
        }


      .transaction-amount-outer {
        display: flex;
        align-items: center;
      }

      .transaction-amount-inner {
        font-size: 20px;
        font-weight: 700;
        min-width: 100px;
        text-align: center;
        background-color: #47474C;
        border-radius: 5px;
        padding: 3px 5px;
      }

    .transaction-note {

    }

.popup {
  position: absolute;
  font-size: 16px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 480px;

  background: rgba(36, 38, 60, 1.0);
  box-shadow: 0px 0px 10px deepskyblue;
  border: 1px solid deepskyblue;
  border-radius: 20px;

  color: white;

  text-align: center;

  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.popup-title {
  text-transform: uppercase;
  font-size: 20px;
  font-weight: bold;
  border-bottom: 1px solid #00BEFE;
  margin-bottom: 10px;
  padding: 15px;
}

.popup-error {
  font-size: 18px;
  background-color: #681c24;
  padding: 10px;
  margin-top: 10px;
}

.popup-buttons {
  display: flex;
  padding: 15px;
  padding-bottom: 20px;
  justify-content: space-between;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
}

.popup-buttons button {
  cursor: pointer;
  font-size: 16px;
  color: #00BEFE;
  background: none;
  font-weight: bold;
  border: 1px solid #00BEFE;
  width: 48%;
  padding: 8px;
  text-shadow: none;
}

.popup-buttons button:hover {
  background-color: rgba(0, 190, 254, 0.15);
}

.input-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
}

.input-group label {
  text-align: left;
  width: 110px;
  margin-right: 10px;
}

.input-group > *:last-child {
  width: 80%;
}

.input-help {
  display: flex;
  flex-direction: column;
}

  .input-help span {
    text-align: left;
    color: #6c757d;
  }

.input-prepend {
  display: flex;
}

.input-prepend div {
  display: inline-flex;
  align-items: center;
  background-color: #363959;
  padding: 10px;
  margin: 4px 0 4px 0;
}

.input-prepend input, .input-prepend select {
  flex-grow: 1;
  width: 0;
  margin-left: 0;
}

.input-prepend-rounded {
  padding-bottom: 10px;
}
.input-prepend-rounded div {
  border-radius: 12px 0 0 12px;
  border: 1px solid #47474C;
}

.input-prepend-button div:hover {
  background-color: rgba(0, 190, 254, 0.15);
}


.input-prepend-rounded input {
  border-radius: 0 12px 12px 0;
  border: 1px solid #47474C;
}

.known-accounts {
  display: flex;
}

  .known-left {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 10px;
  }

    .known-list {
      background-color: rgba(0, 0, 0, 0.4);
      border: 1px solid #47474C;
      border-bottom: none;
      justify-content: space-between;
      height: 150px;
      overflow-y: scroll;
    }

      .known-active {
        background-color: #00BEFE
      }

    .known-buttons {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 10px 0;
    }

      .known-buttons button {
        cursor: pointer;
        font-size: 16px;
        color: #00BEFE;
        background: none;
        font-weight: bold;
        border: 1px solid #00BEFE;
        padding: 3px;
        text-shadow: none;
      }

      .known-buttons button:hover {
        background-color: rgba(0, 190, 254, 0.15);
      }

      .known-buttons button:last-of-type {
        margin-bottom: 10px;
      }

  .known-inputs {
    display: flex;
  }

    .known-inputs input {
      font-size: 15px;
      margin: 0;
      border: 1px solid #47474C;
    }

    .known-inputs input:last-of-type {
      border-left: none;
    }

/* */

.manage-users {
  display: flex;
}

  .manage-users-left {
    display: flex;
    justify-content: center;
    flex-direction: column;
    padding: 10px;
    padding-left: 0;
    flex: 2;
  }

    .manage-users-list {
      background-color: rgba(0, 0, 0, 0.4);
      /* border: 1px solid #47474C; */
      border-bottom: none;
      justify-content: space-between;
      height: 150px;
      overflow-y: scroll;
    }

      .manage-active {
        background-color: #00BEFE
      }

    .manage-buttons {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 10px 0;
    }

      .manage-buttons button {
        cursor: pointer;
        font-size: 16px;
        color: #00BEFE;
        background: none;
        font-weight: bold;
        border: 1px solid #00BEFE;
        padding: 3px;
        text-shadow: none;
      }

      .manage-buttons button:hover {
        background-color: rgba(0, 190, 254, 0.15);
      }

      .manage-buttons button:last-of-type {
        margin-bottom: 10px;
      }

  .manage-users-left input {
    font-size: 15px;
    margin: 0;
    /* border: 1px solid #47474C; */
  }

/* */

i.fa-fw {
  padding-top: 2px;
}

*:not(input) {
  user-select: none;
}

input, select {
  border: none;
  padding: 10px 16px;
  font-size: 16px;
  margin: 4px 0;
  text-shadow: none;
  background: rgba(0, 0, 0, 0.5);
  color: white;
}

input:focus {
  outline: 1px solid deepskyblue;
  box-shadow: 0px 0px 4px deepskyblue;
}

select option {
  background: rgba(36, 38, 60, 1.0);
}

button.leave {
  cursor: pointer;
  font-size: 16px;
  color: #fe4000;
  background: none;
  font-weight: bold;
  border: 1px solid #fe4000;
  /* width: 48%; */
  padding: 8px;
  text-shadow: none;
  margin: 0 15px;
}


button.leave:hover {
  background-color: rgba(254, 64, 0, 0.15);
}

.no-results {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
  cursor: pointer;
}

.no-results:hover {
  color: #999;
}

.no-results i {
  margin-right: 8px;
}