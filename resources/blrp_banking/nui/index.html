<html>
  <head>
    <link rel="stylesheet" href="css/style.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
    <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />
  </head>

  <body>
    <div id="container">
      <div id="wrapper" v-if="visible">
        <div id="header">
          <i class="icon fa-light fa-piggy-bank fa-fw"></i>

          <div class="header-buttons">
            <button v-if="mode == 'bank' && peso_exchange" @click="openModality('Exchange')"><i class="fa-solid fa-money-bill-transfer fa-fw"></i> Exchange Pesos</button>
            <button @click="openModality('Manage')"><i class="fa-solid fa-envelope-open-dollar fa-fw"></i> Open Account</button>
            <button @click="openModality('Known')"><i class="fa-solid fa-magnifying-glass-dollar fa-fw"></i> Known Accounts</button>
            <button @click="close()"><i class="fa-solid fa-xmark fa-fw"></i></button>
          </div>
        </div>

        <div id="contents">
          <div id="left">
            <div id="cash">
              <div class="account-main">
                  <div class="account-info">
                    <div class="account-type">Available Cash</div>
                  </div>

                  <div class="account-balance-outer">
                    <div class="account-balance-inner">{{ formatAmount(cash) }}</div>
                  </div>
                </div>
            </div>

            <div id="accounts">
              <div
                class="account"
                v-for="account in accessibleAccounts"
                :class="{ 'account-active': account.active }"
              >
                <div
                  class="account-main"
                  @click="select(account)"
                >
                  <div class="account-info">
                    <div class="account-type">{{ account.account_type }} Account</div>
                    <div v-if="account.account_type != 'Personal' && account.account_name">{{ account.account_name }}</div>
                    <div>Account # {{ account.account_number }}</div>
                  </div>

                  <div class="account-balance-outer">
                    <div class="account-balance-inner" :class="{ 'negative': account.balance < 0 }">{{ formatAmount(account.balance) }}</div>
                  </div>
                </div>

                <div class="account-buttons">
                  <button @click="openModality('Withdraw', account)" v-if="mode == 'bank' || mode == 'atm'">Withdraw</button>
                  <button @click="openModality('Deposit', account)" v-if="mode == 'bank'">Deposit</button>
                  <button @click="openModality('Transfer', account)">Transfer</button>
                  <button @click="openModality('Manage', account)" v-if="mode == 'bank' && account.account_type != 'Personal' && account.manager">Manage</button>
                  <button @click="openModality('Leave', account)" v-if="mode == 'bank' && account.account_type != 'Personal' && !account.manager">Leave</button>
                </div>
              </div>
            </div>
          </div>
        
          <div
            id="transactions"
            ref="transactions"
            @scroll.passive="transactionsScroll"
          >
      
          <div class="input-prepend input-prepend-rounded input-prepend-button" v-if="transactions.length > 0">
            <div @click="submitSearch">
              <i class="fa-solid fa-search fa-fw"></i>
            </div>
            <input type="text" v-model="searchTerm" @keyup.enter="submitSearch" placeholder="Filter Transactions by: Account Number, Name, Notes or Transaction Amount" />
          </div>
          <div 
          v-if="searchAttempted && transactions.length === 0" 
          class="no-results"
          @click="resetSearch"
        >
          <i class="fa-solid fa-info-circle"></i>
          No results found, click here to reset the search
        </div>
            <div
              class="transaction"
              v-for="transaction in transactions"
            >
              <div class="transaction-body">
                <div class="transaction-details">
                  <div class="transaction-type">{{ transaction.type }}</div>
                  <div>{{ transaction.transactor }}</div>
                  <div>{{ transaction.uid }}</div>
                  <div>{{ transaction.date }}</div>
                </div>

                <div class="transaction-note" v-if="transaction.notes && transaction.notes != ''">{{ transaction.notes }}</div>
              </div>

              <div class="transaction-amount-outer">
                <div class="transaction-amount-inner">{{ formatAmount(transaction.amount) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Withdraw / Deposit / Transfer modal -->
      <div class="popup" v-if="visible && (modality == 'Withdraw' || modality == 'Deposit' || modality == 'Transfer')">
        <div class="popup-title">{{ modality }} Money</div>

        <div v-if="!transferAllowSelectSource" class="input-group">
          <label>Account</label>
          <input type="text" :value="activeAccountDisplay" readonly />
        </div>

        <!-- select(account, close = true, force = false) -->
        <div v-if="transferAllowSelectSource" class="input-group">
          <label>Account</label>
          <select v-model="transferAccountSelected">
            <option v-for="option in accessibleAccounts" :value="option.account_number">
              {{ option.account_number }} ({{ option.account_type == 'Personal' ? 'Personal Account' : option.account_name }})
            </option>
          </select>
        </div>

        <div class="input-group">
          <label>Amount</label>

          <div class="input-prepend">
            <div><i class="fa-light fa-dollar-sign fa-fw"></i></div>
            <input type="text" :disabled="transferReadOnly" ref="amount" v-model="amount" @keypress="numericFilter" />
          </div>
        </div>

        <div class="input-group" v-if="atm_cayo">
          <label></label>

          <div class="input-prepend">
            <div><i class="fa-light fa-peso-sign fa-fw"></i></div>
            <input type="text" ref="amount-peso" :value="amountPesos" readonly disabled />
          </div>
        </div>

        <div class="input-group" v-if="atm_fee">
          <label>Fee</label>

          <div class="input-prepend">
            <div><i class="fa-light fa-dollar-sign fa-fw"></i></div>
            <input type="text" :value="atm_fee" readonly disabled />
          </div>
        </div>

        <div class="input-group">
          <label>Note</label>
          <input type="text" :disabled="transferReadOnly" v-model="note" @keypress="clearError" placeholder="Optional" />
        </div>

        <div class="input-group" v-if="modality == 'Transfer'">
          <label>Target</label>

          <div class="input-prepend">
            <div style="width: 25%">Account</div>
            <select :disabled="transferReadOnly" v-model="transferAccountKnown" @change="selectTransferAccount('select')">
              <option v-for="option in knownAccounts" :value="option.account_number">
                {{ option.account_number }} ({{ option.account_type == 'Personal' ? 'Personal Account' : option.account_name }})
              </option>
            </select>
          </div>
        </div>

        <div class="input-group" v-if="modality == 'Transfer'">
          <label></label>

          <div class="input-prepend">
            <div style="width: 25%">Account #</div>
            <input type="text" :disabled="transferReadOnly" v-model="transferAccountManual" @keypress="numericFilter" @keyup="selectTransferAccount('text')" />
          </div>
        </div>

        <div class="input-group" v-if="modality == 'Transfer' && transferAccountManual">
          <label></label>

          <div class="input-prepend">
            <div style="width: 25%">Verify Account #</div>
            <input type="text" :disabled="transferReadOnly" v-model="transferAccountManualVerify" @keypress="numericFilter" @keyup="selectTransferAccount('text')" />
          </div>
        </div>

        <div class="popup-error" v-if="error">{{ error }}</div>
        <div class="popup-buttons">
          <button @click="closeModality()">Close</button>
          <button @click="submitModality()">{{ modality }}</button>
        </div>
			</div>

      <!-- Currency Exchange modal -->
      <div class="popup" v-if="visible && (modality == 'Exchange')">
        <div class="popup-title">{{ modality }} Pesos to USD</div>

        <div class="input-group">
          <label>Available</label>

          <div class="input-prepend">
            <div><i class="fa-light fa-peso-sign fa-fw"></i></div>
            <input type="text" :disabled="true" :value="pesos_available" />
          </div>
        </div>

        <div class="input-group">
          <label>Amount</label>

          <div class="input-prepend">
            <div><i class="fa-light fa-peso-sign fa-fw"></i></div>
            <input type="text" :disabled="transferReadOnly" ref="amount" v-model="amount" @keypress="numericFilter" />
          </div>
        </div>

        <div class="input-group">
          <label></label>

          <div class="input-prepend">
            <div><i class="fa-light fa-dollar-sign fa-fw"></i></div>
            <input type="text" ref="amount-dollar" :value="amountDollars" readonly disabled />
          </div>
        </div>

        <div class="popup-error" v-if="error">{{ error }}</div>
        <div class="popup-buttons">
          <button @click="closeModality()">Close</button>
          <button @click="submitModality()">{{ modality }}</button>
        </div>
			</div>

      <!-- Known Accounts Modal -->
      <div class="popup" v-if="visible && modality == 'Known'">
        <div class="popup-title">Known Accounts</div>

        <div class="known-accounts">
          <div class="known-left">
            <div class="known-list">
              <div
                class="known-account"
                v-for="account in aliasAccounts"
                :class="{ 'known-active': account.active }"
                @click="selectKnown(account)"
              >
                <span>{{ account.account_number }} - {{ account.account_name }}</span>
              </div>
            </div>

            <div class="known-inputs">
              <input type="text" placeholder="Account #" v-model="known_number" @keypress="numericFilter" />
              <input type="text" placeholder="Alias" v-model="known_name" />
            </div>
          </div>

          <div class="known-buttons">
            <button @click="removeKnown()"><i class="fa-solid fa-minus fa-fw"></i></button>
            <button @click="addKnown()"><i class="fa-solid fa-plus fa-fw"></i></button>
          </div>
        </div>

        <div class="popup-error" v-if="error">{{ error }}</div>
        <div class="popup-buttons">
          <button @click="closeModality()">Close</button>
          <button @click="submitModality()">Save</button>
        </div>
      </div>

      <!-- Manage Account Modal -->
      <div class="popup" v-if="visible && modality == 'Manage'">
        <div class="popup-title">{{ manage_new ? 'Open' : 'Manage' }} Account</div>

        <div class="input-group" v-if="!manage_new">
          <label>Account #</label>
          <input type="text" :value="manage_account.account_number" readonly />
        </div>

        <div class="input-group" v-if="manage_new || manage_account.account_type != 'Personal'">
          <label>Account Name</label>
          <input type="text" v-model="manage_account.account_name" placeholder="Optional" />
        </div>

        <div class="input-group">
          <label>Users</label>
          <div class="manage-users">
            <div class="manage-users-left">
              <div class="manage-users-list">
                <div
                  class="known-account"
                  v-for="user in manageUsers"
                  :class="{ 'manage-active': (manage_user && user.character_id == manage_user.character_id) }"
                  @click="selectUser(user)"
                >
                  <span>{{ user.name }} - {{ user.owner ? 'Owner' : user.manager ? 'Manager' : 'User' }}</span>
                </div>
              </div>

              <input type="text" placeholder="Add by Drivers License #" v-model="manage_dl" @keypress="numericFilter" />
            </div>

            <div class="manage-buttons">
              <button @click="removeUser()"><i class="fa-solid fa-minus fa-fw"></i></button>
              <button @click="promoteUser()" v-if="manage_user"><i class="fa-solid fa-user-plus fa-fw"></i></button>
              <button @click="demoteUser()" v-if="manage_user"><i class="fa-solid fa-user-minus fa-fw"></i></button>
              <button @click="addUser()"><i class="fa-solid fa-plus fa-fw"></i></button>
            </div>
          </div>
        </div>

        <div class="input-group" v-if="manage_new">
          <label>Opening Fee</label>
          <input type="text" readonly :value="formatAmount(22500)" />
        </div>

        <div class="input-group" v-if="manage_new">
          <label>Monthly Fee</label>
          <input type="text" readonly :value="formatAmount(7500)" />
        </div>

        <div class="input-group" v-if="manage_new">
          <label>Confirmation</label>

          <div class="input-help">
            <input type="text" v-model="manage_confirm" @keypress="clearError" />
            <span>Type "I accept fees" to confirm opening</span>
          </div>
        </div>

        <div class="input-group" v-if="this.manage_account.owner_character_id == this.my_info.id">
          <label>Close Account</label>
          <button class="leave" @click="closeFromManage()">Close Account</button>
        </div>

        <button class="leave" @click="leaveFromManage()" v-if="!manage_new && this.manage_account.owner_character_id != this.my_info.id">Leave</button>

        <div class="popup-error" v-if="error">{{ error }}</div>
        <div class="popup-buttons">
          <button @click="closeModality()">Close</button>
          <button @click="submitModality()">{{ manage_processing ? 'Processing' : (manage_new ? 'Open Account' : 'Save Account') }}</button>
        </div>
			</div>

      <!-- Leave account modal -->
      <div class="popup" v-if="visible && modality == 'Leave'">
        <div class="popup-title">Leave Account</div>

        <div class="input-group">
          <label>Account #</label>
          <input type="text" :value="active_account.account_number" readonly />
        </div>

        <div class="input-group">
          <label>Confirmation</label>

          <div class="input-help">
            <input type="text" v-model="manage_confirm" @keypress="clearError" />
            <span>Type "Leave account {{ active_account.account_number }}" to confirm leaving</span>
          </div>
        </div>

        <div class="popup-error" v-if="error">{{ error }}</div>
        <div class="popup-buttons">
          <button @click="closeModality()">Close</button>
          <button @click="submitModality()">Leave</button>
        </div>
			</div>

      <!-- Close account modal -->
      <div class="popup" v-if="visible && modality == 'Close'">
        <div class="popup-title">Close Account</div>

        <div class="input-group">
          <label>Account #</label>
          <input type="text" :value="active_account.account_number" readonly />
        </div>

        <div class="input-group">
          <label>Confirmation</label>

          <div class="input-help">
            <input type="text" v-model="manage_confirm" @keypress="clearError" />
            <span>Type "Close account {{ active_account.account_number }}" to confirm leaving. Any remaining funds will be transferred to your primary account</span>
          </div>
        </div>

        <div class="popup-error" v-if="error">{{ error }}</div>
        <div class="popup-buttons">
          <button @click="closeModality()">Close</button>
          <button class="leave" @click="submitModality()">Close Account Permanently</button>
        </div>
			</div>
    </div>

    <script src="https://cfx-nui-blrp_ui/ui/vue.global.prod.js"></script>
    <script src="js/app.js"></script>
  </body>
</html>
