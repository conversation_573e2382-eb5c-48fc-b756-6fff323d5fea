const initialData = () => ({
  mode: 'bank',
  cash: 0,
  visible: false,
  modality: false,
  active_account: {},
  accounts: [],
  my_info: {},

  /* Transaction history */
  transactions: [],
  transactions_page: 0,
  transactions_polling: false,
  transactions_last_page: false,
  searchAttempted: false,

  /* Transaction modal bindings */
  amount: null,
  note: null,
  target: null,
  error: null,
  transferAllowSelectSource: false,
  transferAccountSelected: null,
  transferAccountKnown: null,
  transferAccountManual: null,
  transferAccountManualVerify: null,
  transferReadOnly: false,

  /* Known accounts bindings */
  known_selected: {},
  known_number: null,
  known_name: null,

  /* Account manage bindings */
  manage_new: false,
  manage_processing: false,
  manage_account: {},
  manage_users: [],
  manage_user: null,
  manage_confirm: '',
  manage_dl: null,
})

const app = Vue.createApp({
  data() {
    return initialData();
  },

  mounted() {
    window.addEventListener('message', (event) => {
      if(!event.data) return;

      if(event.data.cash) {
        root.cash = event.data.cash;
      }

      if(event.data.accounts) {
        root.accounts = event.data.accounts;
      }

      if(event.data.action == 'open') {
        root.mode = event.data.mode;
        root.my_info = event.data.my_info;
        root.modality = false;
        root.visible = true;

        Object.keys(event.data.extra).forEach(k => root[k] = event.data.extra[k]);

        if (event.data.extra.known_number) {
          this.known_number = event.data.extra.known_number;
        }
      }

      if(event.data.action == 'close') {
        Object.assign(this.$data, initialData());
      }
    });

    document.addEventListener('keydown', (event) => {
      if(event.key == 'Escape') {
        // Close modal if open, else close entire UI
        if(!this.closeModality()) {
          this.close();
        }
      }

      if(event.key == 'Enter') {
        this.submitModality();
      }
    })
  },

  methods: {
    fetchNUI(route, params = {}) {
      return fetch(`https://${GetParentResourceName()}/${route}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=UTF-8', },
        body: JSON.stringify(params)
      }).then(r => r.json());
    },

    close() {
      this.fetchNUI('close');
    },

    openModality(modality, account = null) {
      if(this.closeModality()) {
        return;
      }

      if(modality == 'Manage') {
        if(!account) {
          // Handle new account
          this.manage_new = true;
          this.manage_account = {};
          this.manage_users = [
            {
              owner: true,
              manager: false,
              character_id: this.my_info.id,
              account_number: -1,
              name: this.my_info.name,
            }
          ];
          this.modality = modality;
        } else {
          this.fetchNUI('manage', {
            account: account.account_number
          }).then(response => {
            if(!response.success) {
              this.closeModality();
              return;
            }

            this.manage_account = response.data.account;
            this.manage_users = response.data.users;
            this.modality = modality;
          });
        }
        return;
      }

      this.modality = modality;

      if(this.modality == 'Leave' || this.modality == 'Close') {
        this.select(account, false);
      }

      // Focus amount input on deposit, withdraw, transfer
      if(['Deposit', 'Withdraw', 'Transfer', 'Exchange'].includes(this.modality)) {
        this.select(account, false);
        this.$nextTick(() => {
          this.$refs.amount.focus();
        });
      }
    },

    closeModality() {
      if(this.modality != false) {
        if(this.modality == 'Known') {
          this.fetchNUI('refresh');
        }

        this.error = false;
        this.modality = false;

        this.amount = null;
        this.note = null;
        this.target = null;
        this.error = null;
        this.transferAllowSelectSource = false;
        this.transferAccountSelected = null;
        this.transferAccountKnown = null;
        this.transferAccountManual = null;
        this.transferAccountManualVerify = null;
        this.transferReadOnly = false;
        this.known_selected = {};
        this.known_number = null;
        this.known_name = null;
        this.manage_new = false;
        this.manage_processing = false;
        this.manage_account = {};
        this.manage_users = [];
        this.manage_user = null;
        this.manage_confirm = '';
        this.manage_dl = null;

        return true;
      }

      return false;
    },

    submitModality() {
      if(!this.modality) {
        return;
      }

      let modality = this.modality.toLowerCase();

      if(
        this.modality == 'Transfer' &&
        this.transferAccountManual != this.transferAccountManualVerify
      ) {
        this.error = 'Destination account # and verification do not match';
        return;
      }

      if(this.modality == 'Known') {
        this.fetchNUI('transaction', {
          modality: modality,
          accounts: this.accounts.filter(account => account.account_type == 'Alias')
        }).then(response => {
          if(!response.success) {
            this.error = response.message;
          } else {
            this.closeModality();
          }
        });

        return;
      }

      if(this.modality == 'Leave') {
        if(this.manage_confirm.toLowerCase().trim() != 'leave account ' + this.active_account.account_number) {
          this.error = 'You must type the confirmation text';
          return;
        }

        this.fetchNUI('leave', {
          account: this.active_account,
        }).then(response => {
          if(!response.success) {
            this.error = response.message;
            return;
          }

          this.closeModality();

          this.transactions = [];
          this.transactions_page = 0;
          this.transactions_last_page = false;
        });

        return;
      }

      if(this.modality == 'Close') {
        if(this.manage_confirm.toLowerCase().trim() != 'close account ' + this.active_account.account_number) {
          this.error = 'You must type the confirmation text';
          return;
        }

        this.fetchNUI('closeAccount', {
          account: this.active_account,
        }).then(response => {
          if(!response.success) {
            this.error = response.message;
            return;
          }

          this.closeModality();

          this.transactions = [];
          this.transactions_page = 0;
          this.transactions_last_page = false;

          this.fetchNUI('refresh');
        });

        return;
      }

      if(this.modality == 'Manage') {
        if(this.manage_processing) {
          return;
        }

        this.manage_processing = true;

        if(this.manage_new && this.manage_confirm.toLowerCase().trim() != 'i accept fees') {
          this.error = 'You must type the confirmation text';
          return;
        }

        this.fetchNUI('submitManage', {
          new: this.manage_new,
          account: this.manage_account,
          users: this.manage_users,
        }).then(response => {
          if(!response.success) {
            this.error = response.message;
            return;
          }

          this.closeModality();
        });

        return;
      }

      let transaction_account_from = this.active_account.account_number;

      if(this.modality == 'Transfer' && this.transferAllowSelectSource) {
        if(!this.transferAccountSelected) {
          this.error = 'You must select an account to transfer funds from';
          return;
        }

        transaction_account_from = this.transferAccountSelected;
      }

      this.fetchNUI('transaction', {
        modality: modality,
        account_from: transaction_account_from,
        account_to: this.target,
        amount: this.amount,
        note: this.note,
      }).then(response => {
        if(!response.success) {
          this.error = response.message;
        } else {
          if(modality == 'exchange' && root.pesos_available != null) {
            root.pesos_available -= root.amount;
          }

          this.select(this.active_account, true, true);
        }
      });
    },

    formatAmount(amount) {
      let negative = (amount < 0);

      amount = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);
      amount = amount.replace('.00', '');

      if(negative) {
        amount = '(' + amount + ')';
      }

      return amount;
    },

    select(account, close = true, force = false) {
      if(close && this.closeModality() && !force) {
        return;
      }

      if(!account || !this.active_account) {
        return;
      }

      if(!force && this.active_account && this.active_account.account_number == account.account_number) {
        return;
      }

      this.active_account = account;
      this.transactions_polling = true;

      this.fetchNUI('transactions', {
        account: this.active_account.account_number,
      }).then(response => {
        this.transactions_polling = false;

        if(!response.success) {
          return;
        }

        this.transactions = response.transactions;
        this.transactions_page = 0;
        this.transactions_last_page = response.last_page;

        this.$nextTick(() => {
          this.$refs.transactions.scrollTop = 0;
        });
      });
    },

    transactionsScroll(e) {
      if(this.transactions_polling || this.transactions_last_page) {
        return;
      }

      let scroll_percent = (e.srcElement.scrollTop / e.srcElement.scrollHeight) * 100;

      if(scroll_percent > 75) {
        this.transactions_polling = true;
        this.transactions_page++;

        this.fetchNUI('transactions', {
          account: this.active_account.account_number,
          page: this.transactions_page,
        }).then(response => {
          this.transactions_polling = false;
          this.transactions_last_page = response.last_page;

          if(!response.success) {
            return;
          }

          this.transactions = this.transactions.concat(response.transactions);
        });
      }
    },

    clearError() {
      this.error = null;
    },

    selectTransferAccount(source) {
      this.clearError();

      if(source == 'select') {
        this.transferAccountManual = null;
        this.transferAccountManualVerify = null;
        this.target = this.transferAccountKnown;
      } else {
        this.transferAccountKnown = null;
        this.target = this.transferAccountManual;
      }
    },

    numericFilter(evt) {
      this.clearError();

      var charCode = (evt.which) ? evt.which : evt.keyCode;

      if ((charCode > 31 && (charCode < 48 || charCode > 57))) {
        evt.preventDefault();
      } else {
        return true;
      }
    },

    /* Known accounts */
    selectKnown(account) {
      this.known_selected = account;
    },

    removeKnown() {
      if(!this.known_selected) {
        return
      }

      this.accounts = this.accounts.filter(account => {
        return account.account_number != this.known_selected.account_number
      });
      this.known_selected = null;
    },

    addKnown() {
      if(!this.known_number || !this.known_name) {
        this.error = 'Must specify account number and alias';
        return;
      }

      let exists = this.accounts.find(account => account.account_number == Number(this.known_number));

      if(exists) {
        if(exists.account_type == 'Alias') {
          this.error = 'An alias for this account already exists. Delete it first if you wish to change it';
          return;
        }

        this.error = 'Cannot add an alias for an account you belong to'
        return;
      }

      this.accounts.push({
        account_type: 'Alias',
        account_number: this.known_number,
        account_name: this.known_name,
      });

      this.known_number = null;
      this.known_name = null;
    },

    /* Manage account */
    selectUser(user) {
      if(user.owner) {
        return;
      }

      this.manage_user = user;
    },

    addUser() {
      this.fetchNUI('adduser', {
        dl: this.manage_dl,
      }).then(response => {
        if(!response.success) {
          this.error = 'Invalid DL #';
          return;
        }

        if(this.manage_users.find(user => Number(user.character_id) == Number(response.character.id))) {
          this.error = 'User already has access to account';
          return;
        }

        this.manage_dl = null;
        this.manage_users.push({
          owner: false,
          manager: false,
          character_id: response.character.id,
          account_number: -1,
          name: response.character.name,
        });
      });
    },

    removeUser() {
      this.manage_users = this.manage_users
        .filter(user => user.character_id != this.manage_user.character_id)
      ;
      this.manage_user = null;
    },

    promoteUser() {
      if(!this.manage_user) {
        return;
      }

      this.manage_users = this.manage_users
        .map(user => {
          if(user.character_id == this.manage_user.character_id) {
            user.manager = true;
          }

          return user;
        })
      ;
    },

    demoteUser() {
      if(!this.manage_user) {
        return;
      }

      this.manage_users = this.manage_users
        .map(user => {
          if(user.character_id == this.manage_user.character_id) {
            user.manager = false;
          }

          return user;
        })
      ;
    },

    leaveFromManage() {
      let account = this.manage_account;
      this.closeModality();
      this.$nextTick(() => {
        this.openModality('Leave', account);
      });
    },

    closeFromManage() {
      let account = this.manage_account;
      this.closeModality();
      this.$nextTick(() => {
        this.openModality('Close', account);
      });
    },

    /* Search transactions */

    searchTransactions() {
      this.transactions_polling = true;
      this.searchAttempted = true;
    
      this.fetchNUI('searchTransactions', {
        account: this.active_account.account_number,
        searchTerm: this.searchTerm
      }).then(response => {
        this.transactions_polling = false;
        
        if (response.transactions.length === 0) {
          this.transactions = [];
          return;
        }
    
        this.transactions = response.transactions;
        this.transactions_page = 0;
        this.transactions_last_page = response.last_page;
    
        this.$nextTick(() => {
          this.$refs.transactions.scrollTop = 0;
        });
      });
    },
    submitSearch() {
      this.searchTransactions();
    },
    resetSearch() {
      this.searchTerm = '';
      this.searchAttempted = false;
      this.select(this.active_account, false, true);
    }
  },
  watch: {
    searchTerm(newTerm) {
      if (newTerm.trim() !== '') {
        this.searchTransactions();
      } else {
        this.select(this.active_account, false, true); //Reload Trans after enter
      }
    }

  },

  computed: {
    activeAccountDisplay() {
      return `${root.active_account.account_number} (${root.active_account.account_type} Account)`;
    },

    knownAccounts() {
      return this.accounts.sort((a, b) => {
        return Number(a.account_number) - Number(b.account_number);
      });
    },

    aliasAccounts() {
      return this.accounts
        .filter(account => account.account_type == 'Alias')
        .sort((a, b) => {
          return Number(a.account_number) - Number(b.account_number);
        })
        .map(account => {
          if(this.known_selected) {
            account.active = account.account_number == this.known_selected.account_number;
          }

          return account;
        })
      ;
    },

    accessibleAccounts() {
      return this.accounts
        .filter(account => account.account_type != 'Alias')
        .map(account => {
          account.active = account.account_number == this.active_account.account_number;
          account.manager = account.manager ? true : Number(account.owner_character_id) == Number(this.my_info.id)

          return account;
        })
        .sort((a, b) => {
          return Number(a.account_number) - Number(b.account_number);
        })
      ;
    },

    manageUsers() {
      return Array.from(this.manage_users)
        .sort((a, b) => {
          // Owner desc
          if(a.owner != b.owner) {
            return b.owner - a.owner;
          }

          // Manager desc
          if(a.manager != b.manager) {
            return b.manager - a.manager;
          }

          // Name asc
          return a.name.localeCompare(b.name);
        })
      ;
    },

    amountPesos() {
      if(!this.peso_exchange_rate) {
        return this.amount;
      }

      let pesos = this.amount * this.peso_exchange_rate;

      if(pesos == 0) {
        return '';
      }

      return this.formatAmount(this.amount * this.peso_exchange_rate).toString().replace('$', '');
    },

    amountDollars() {
      if(!this.peso_exchange_rate) {
        return this.amount;
      }

      let dollars = this.amount / this.peso_exchange_rate;

      if(dollars == 0) {
        return '';
      }

      dollars = Math.floor(dollars);

      return this.formatAmount(dollars).toString().replace('$', '');
    }
  }
});

const root = app.mount('#container');
