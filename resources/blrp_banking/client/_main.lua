tBanking = {}
T.bindInstance('main', tBanking)

pBanking = P.getInstance(GetCurrentResourceName(), 'main')

local visible = false

tBanking.setVisible = function(_visible, mode, accounts, cash, extra)
  visible = _visible
  SetNuiFocus(_visible, _visible)

  if _visible then
    local me = exports.blrp_core:me()

    SendNUIMessage({
      action = 'open',
      mode = mode,
      accounts = accounts,
      cash = cash,
      my_info = {
        id = me.get('id'),
        dl = me.get('dlnumber'),
        name = me.get('firstname') .. ' ' .. me.get('lastname')
      },
      extra = extra,
    })
  else
    SendNUIMessage({
      action = 'close',
    })
  end
end

tBanking.updateFrontend = function(accounts, cash)
  SendNUIMessage({
    action = 'Update',
    accounts = accounts,
    cash = cash,
  })
end

RegisterNUICallback('adduser', function(data, callback)
  local success, character = pBanking.addUser({ (data.dl or -1) })

  callback({
    success = success,
    character = character
  })
end)

RegisterNUICallback('close', function(_, callback)
  callback({ success = true })
  tBanking.setVisible(false)
end)

RegisterNuiCallback('refresh', function(_, callback)
  callback({ success = true })
  pBanking.refresh()
end)

RegisterNUICallback('submitManage', function(data, callback)
  local success, message = pBanking.submitManage({ data.account, data.users, data.new })

  callback({
    success = success,
    message = message
  })
end)

RegisterNuiCallback('manage', function(data, callback)
  local success, message, data = pBanking.tryManage({ (data.account or nil) })

  callback({
    success = success,
    message = message,
    data = data
  })
end)

RegisterNUICallback('leave', function(data, callback)
  local success, message = pBanking.leaveAccount({ (data.account or nil) })

  callback({
    success = success,
    message = message
  })
end)

RegisterNUICallback('closeAccount', function(data, callback)
  local success, message = pBanking.closeAccount({ ((data.account and data.account.account_number) or nil) })

  callback({
    success = success,
    message = message
  })
end)

RegisterNUICallback('transaction', function(data, callback)
  local success = false
  local message = 'NUI Error'

  if(data.modality and data.modality == 'known') then
    success, message = pBanking.saveKnown({ (data.accounts or {}) })
  else
    success, message = pBanking.transaction({ data })
  end

  callback({
    success = success,
    message = message
  })
end)

RegisterNUICallback('transactions', function(data, callback)
  if not data.account then
    callback({ success = false })
    return
  end

  local success, transactions, last_page = pBanking.getTransactions({ data.account, data.page })

  callback({
    success = success,
    transactions = transactions,
    last_page = last_page,
  })
end)

RegisterNUICallback('searchTransactions', function(data, callback)
  if not data.account or not data.searchTerm then
    callback({ success = false, message = "Invalid input" })
    return
  end

  local success, transactions = pBanking.searchTransactions({ data.account, data.searchTerm })

  callback({
    success = success,
    transactions = transactions,
  })
end)
