exports('DepositToAccount', function(transactor_name, data, uuid)
  if not data.amount or not data.account_to then
    return false
  end

  local rows_affected = MySQL.update.await([[
    UPDATE bank_accounts SET balance = balance + ?
      WHERE account_number = ?
  ]], { data.amount, data.account_to })

  if rows_affected <= 0 then
    return false
  end

  data.account_from = data.account_to

  -- Return UUID
  return logTransaction(transactor_name, 'Deposit', data, uuid)
end)

exports('WithdrawFromAccount', function(transactor_name, data)
  if not data.amount or not data.account_from then
    return false
  end

  local rows_affected = MySQL.update.await([[
    UPDATE bank_accounts SET balance = balance - ?
      WHERE account_number = ?
  ]], { data.amount, data.account_from })

  if rows_affected <= 0 then
    return false
  end

  -- Return UUID
  return logTransaction(transactor_name, 'Withdrawal', data)
end)
