math.randomseed(os.time())

pBanking = {}
P.bindInstance('main', pBanking)

tBanking = T.getInstance(GetCurrentResourceName(), 'main')
tZones = T.getInstance('blrp_zones', 'zones')

Citizen.CreateThread(function()
  local rows_affected = MySQL.update.await('UPDATE bank_accounts SET account_type = "Business" WHERE account_type = "Shared"')

  print('Changed ' .. rows_affected .. ' bank accounts Shared -> Business')
end)

local atm_info = {}

local function getFrontendData(character)
  local accounts = MySQL.query.await([[
    SELECT ba.*, bac.manager FROM bank_accounts ba
    INNER JOIN bank_account_characters bac ON bac.account_number = ba.account_number AND bac.character_id = ?
  ]], { character.get('id') })

  -- Load account aliases
  local aliases = character.getUserData('banking:accountaliases:' .. character.get('id'), false, true) or {}

  for _, alias in pairs(aliases) do
    local skip = false

    for _, account in pairs(accounts) do
      -- Skip accounts the user has access to and has previously aliased
      if tonumber(account.account_number) == tonumber(alias.account_number) then
        skip = true
      end
    end

    if not skip then
      table.insert(accounts, {
        account_type = 'Alias',
        account_number = alias.account_number,
        account_name = alias.account_name,
      })
    end
  end

  -- TODO: frozen accounts?

  return accounts, character.getCash()
end

exports('GetAccounts', function(character)
  local accounts, _ = getFrontendData(character)

  return accounts
end)

function logTransaction(character, transaction_type, data, uuid)
  local log_account_number = 0

  local transactor_name = character

  if type(character) == 'table' then
    transactor_name = character.get('firstname') .. ' ' .. character.get('lastname')
  end

  if not uuid then
    uuid = randomUuid()
  end

  if data.hidden == nil then
    data.hidden = false
  end

  if transaction_type == 'Transfer - Inbound' then
    log_account_number = data.account_to
  elseif
    transaction_type == 'Deposit' or
    transaction_type == 'Withdrawal' or
    transaction_type == 'Transfer - Outbound'
  then
    log_account_number = data.account_from
  end

  if data.type then
    transaction_type = data.type
  end

  local note_prepend = ''

  if transaction_type == 'Transfer - Inbound' or transaction_type == 'Transfer - Outbound' then
    note_prepend = 'Account to account: ' .. data.account_from .. ' -> ' .. data.account_to
  end

  if transaction_type == 'Transfer - Inbound' and data.account_from == 1000000 then
    transactor_name = 'STATE OF SAN ANDREAS'
  end

  MySQL.insert([[
    INSERT INTO bank_account_transactions
      (account_number, transaction_uuid, transactor_name, transaction_type, note, amount, hidden, timestamp)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  ]], {
    log_account_number,
    uuid,
    transactor_name,
    transaction_type,
    note_prepend .. ' ' .. (data.note or ''),
    data.amount,
    data.hidden,
    os.date("%Y-%m-%d %H:%M:%S"),
  })

  local log = transaction_type

  if transaction_type == 'Deposit' or transaction_type == 'Transfer - Inbound' then
    log = log .. ' / target_account = ' .. log_account_number
  elseif transaction_type == 'Withdrawal' or transaction_type == 'Transfer - Outbound' then
    log = log .. ' / source_account = ' .. log_account_number

    if transaction_type == 'Transfer - Outbound' then
      log = log .. ' / target_account = ' .. data.account_to
    end
  end

  log = log .. ' / amount = ' .. data.amount

  if type(character) == 'table' and (transaction_type == 'Deposit' or transaction_type == 'Withdrawal' or transaction_type == 'Transfer - Outbound') then
    character.log('BANKING', log)

    if data.amount >= 500000 then
      character.log('BIGTRANSACTION', log)
    end
  end

  return uuid
end

exports('LogTransaction', logTransaction)

local migrationCooldowns = {}

AddEventHandler('core:server:registerSelectedPlayer', function(player)
  local character = exports.blrp_core:character(player)

  local user_id = character.get('identifier')
  local currentTime = os.time()

  if migrationCooldowns[user_id] and currentTime - migrationCooldowns[user_id] < 60 then
    character.log('COMPENSATION', 'Migration cooldown not elapsed', { cooldown_remaining = 60 - (currentTime - migrationCooldowns[user_id]) })
    return
  end

  migrationCooldowns[user_id] = currentTime

  local bank_amount = character.get('bank')
  local wallet_amount = character.get('wallet')

  local total_amount = (bank_amount + wallet_amount)

  local account = MySQL.single.await('SELECT id FROM bank_accounts WHERE account_number = ?', { character.get('personalbanknumber') })

  -- Create personal account if it doesn't exist
  if not account then
    MySQL.insert.await([[
      INSERT INTO bank_accounts
        (owner_character_id, account_type, account_number, account_name, balance)
        VALUES (?, "Personal", ?, ?, 0)
    ]], { character.get('id'), character.get('personalbanknumber'), character.get('id') .. ' Personal Account' })

    MySQL.insert.await([[
      INSERT INTO bank_account_characters
        (account_number, character_id)
        VALUES (?, ?)
    ]], { character.get('personalbanknumber'), character.get('id') })
  end

  -- Migrate legacy bank / wallet amounts
  if total_amount > 0 then
    MySQL.update('UPDATE bank_accounts SET balance = balance + ? WHERE account_number = ?', {
      total_amount, character.get('personalbanknumber')
    })

    character.log('COMPENSATION', 'Migrated bank account and wallet from characters table to account', {
      account_number = character.get('personalbanknumber'),
      bank_amount = bank_amount,
      wallet_amount = wallet_amount,
    })

    character.set('bank', 0)
    character.set('wallet', 0)

    MySQL.update('UPDATE characters SET bank = 0, wallet = 0 WHERE id = ?', { character.get('id') })
  end
end)

RegisterNetEvent('blrp_banking:server:open', function(_, event_data, player)
  if not player then
    player = source
  end

  local character = exports.blrp_core:character(player)

	if
    not event_data.access_type or
    (
      event_data.access_type ~= 'atm' and
      event_data.access_type ~= 'bank' and
      event_data.access_type ~= 'phone'
    )
  then
		return
	end

  local extra = event_data.extra or {}

  extra.atm_fee = 20
  extra.atm_cayo = false

  if event_data.modality then
    extra.modality = event_data.modality
  end

  if event_data.known_number then
    extra.known_number = event_data.known_number
  end

  local cayo_running = (GetResourceState('blrp_cayo') == 'started')

  extra.peso_exchange = cayo_running

  if cayo_running then
    extra.peso_exchange_rate = GlobalState.peso_exchange_rate
    extra.pesos_available = character.getItemQuantity('cash_peso')

    if tZones.isInsideMatchedZone(character.source, { 'CayoExclusionOuter' }) then
      extra.atm_fee = 500
      extra.atm_cayo = true
    end
  end

  if event_data.access_type == 'atm' then
    atm_info[character.source] = { extra.atm_fee, (extra.atm_cayo and 'cash_peso' or 'cash') }
    character.animate({{"amb@prop_human_atm@male@enter","enter"},{"amb@prop_human_atm@male@idle_a","idle_a"}}, false, false)
  else
    atm_info[character.source] = false
  end

  local accounts, cash = getFrontendData(character)

  -- TODO: frozen accounts?

  tBanking.setVisible(character.source, { true, event_data.access_type, accounts, cash, extra })
end)

pBanking.closeAccount = function(account_number)
  local character = exports.blrp_core:character(source)

  if not account_number then
    return false
  end

  -- Check that this character can manage the origin bank account
  -- Either is the owner character ID or has manager flag on pivot
  local source_account = MySQL.single.await([[
    SELECT ba.* FROM bank_accounts ba WHERE account_number = ? AND owner_character_id = ?
  ]], {
    account_number, character.get('id')
  })

  if
    not source_account or
    not source_account.account_number or
    tonumber(source_account.owner_character_id) ~= tonumber(character.get('id'))
  then
    return false, 'Access denied'
  end

  if source_account.balance > 0 then
    return false, 'You must transfer all funds out of the account before closing it'
  end

  MySQL.query.await('DELETE FROM bank_accounts WHERE account_number = ? LIMIT 1', { account_number })
  MySQL.query.await('DELETE FROM bank_account_characters WHERE account_number = ?', { account_number })

  return true
end

pBanking.leaveAccount = function(account)
  local character = exports.blrp_core:character(source)

  if not account or not account.account_number then
    return
  end

  -- Check that this character can access the origin bank account
  local source_account = MySQL.single.await([[
    SELECT ba.* FROM bank_accounts ba
      INNER JOIN bank_account_characters bac ON bac.account_number = ba.account_number AND bac.character_id = ?
      WHERE ba.account_number = ?
  ]], {
    character.get('id'), account.account_number
  })

  if
    not source_account or
    not source_account.account_number or
    tonumber(source_account.account_number) ~= tonumber(account.account_number)
  then
    return false, 'Invalid request'
  end

  if tonumber(source_account.owner_character_id) == tonumber(character.get('id')) then
    return false, 'Cannot leave this account'
  end

  local rows_affected = MySQL.update.await('DELETE FROM bank_account_characters WHERE account_number = ? AND character_id = ? LIMIT 1', {
    source_account.account_number, character.get('id')
  })

  if rows_affected <= 0 then
    return false, 'Database error'
  end

  character.log('Left bank account #' .. source_account.account_number)
  tBanking.updateFrontend(character.source, { getFrontendData(character) })

  return true
end

pBanking.addUser = function(dl_number)
  local character = MySQL.single.await("SELECT id, CONCAT(firstname, ' ', lastname) as `name`, dlnumber FROM characters WHERE dlnumber = ?", { dl_number })

  if not character then
    return false
  end

  return true, character
end

pBanking.submitManage = function(account_data, users_new, new)
  local function recurseAccountNumber()
    local number = math.random(1000000, 9999999)

    local row = MySQL.single.await('SELECT account_number FROM bank_accounts WHERE account_number = ?', { number })

    if row then
      return recurseAccountNumber()
    end

    return number
  end

  local character = exports.blrp_core:character(source)

  local account_number = account_data.account_number

  local owner = false

  for _, user in pairs(users_new) do
    if user.owner then
      owner = user
    end
  end

  if not owner then
    return false, 'Account must have an owner'
  end

  if new then
    if not character.tryTakeBankMoney(22500) then
      return false, 'Insufficient funds to open account'
    end

    account_number = recurseAccountNumber()

    MySQL.insert.await([[
      INSERT INTO bank_accounts (owner_character_id, account_type, account_number, account_name, balance)
        VALUES (?, ?, ?, ?, 0)
    ]], {
      owner.character_id, 'Business', account_number, (account_data.account_name or nil),
    })

    MySQL.insert.await('INSERT INTO bank_account_characters (account_number, character_id) VALUES (?, ?)', {
      account_number, character.get('id')
    })

    character.log('BANKING', 'Opened bank account', {
      account_name = account_data.account_name,
      account_number = account_number,
      fee = 22500
    })
  end

  -- Check that this character can manage the origin bank account
  -- Either is the owner character ID or has manager flag on pivot
  local account = MySQL.single.await([[
    SELECT ba.*, bac.manager FROM bank_accounts ba
      INNER JOIN bank_account_characters bac ON bac.account_number = ba.account_number AND bac.character_id = ?
      WHERE ba.account_number = ? AND (ba.owner_character_id = ? OR bac.manager = true)
  ]], {
    character.get('id'), account_number, character.get('id')
  })

  if not account then
    return false, 'Insufficient permission'
  end

  -- Update non-personal account name as necessary
  if account.account_type ~= 'Personal' and account.account_name ~= account_data.account_name then
    if exports.blrp_core:ScanInputForBadWords(character.source, 'banking-account', account_data.account_name) then
      return false
    end

    MySQL.update.await('UPDATE bank_accounts SET account_name = ? WHERE account_number = ? LIMIT 1', {
      account_data.account_name, account_number
    })

    character.log('BANKING', 'Updated bank account name', {
      account_number = account_number,
      name_old = account.account_name,
      name_new = account_data.account_name
    })
  end

  local users_old = MySQL.query.await('SELECT * FROM bank_account_characters WHERE account_number = ?', {{ account_number }})

  -- Iterate old user data and remove as necessary
  for _, user_old in pairs(users_old) do
    local found = false

    for _, user_new in pairs(users_new) do
      if not found and tonumber(user_old.character_id) == tonumber(user_new.character_id) then
        found = true
      end
    end

    -- Don't let anyone delete the account owner
    if(tonumber(user_old.character_id) == tonumber(account.owner_character_id)) then
      found = true
    end

    -- Existing user from DB not found in user data from front end,
    -- delete from DB
    if not found then
      MySQL.update.await('DELETE FROM bank_account_characters WHERE account_number = ? AND character_id = ? LIMIT 1', {
        account_number, user_old.character_id
      })

      character.log('BANKING', 'Removed user from bank account', {
        account_number = account_number,
        character_id = user_old.character_id
      })
    end
  end

  -- Iterate new user data and add / promote as necessary
  for _, user_new in pairs(users_new) do
    local found = false

    for _, user_old in pairs(users_old) do
      if not found and tonumber(user_old.character_id) == tonumber(user_new.character_id) then
        found = user_old
      end
    end

    if not found then
      -- Handle adding user to account
      MySQL.insert.await('INSERT INTO bank_account_characters (account_number, character_id, manager) VALUES (?, ?, ?)', {
        account_number, user_new.character_id, user_new.manager
      })

      character.log('BANKING', 'Added user to bank account', {
        account_number = account_number,
        character_id = user_new.character_id,
        manager = user_new.manager
      })
    elseif found and found.manager ~= user_new.manager then
      -- Handle manager promotion / demotion
      MySQL.update.await('UPDATE bank_account_characters SET manager = ? WHERE account_number = ? AND character_id = ?', {
        user_new.manager, account_number, user_new.character_id
      })

      character.log('BANKING', 'Updated bank account manager status for user', {
        account_number = account_number,
        character_id = user_new.character_id,
        manager = user_new.manager
      })
    end
  end

  tBanking.updateFrontend(character.source, { getFrontendData(character) })

  return true
end

pBanking.tryManage = function(account_number)
  local character = exports.blrp_core:character(source)

  if not account_number then
    return false
  end

  -- Check that this character can manage the origin bank account
  -- Either is the owner character ID or has manager flag on pivot
  local source_account = MySQL.single.await([[
    SELECT ba.*, bac.manager FROM bank_accounts ba
      INNER JOIN bank_account_characters bac ON bac.account_number = ba.account_number AND bac.character_id = ?
      WHERE ba.account_number = ? AND (ba.owner_character_id = ? OR bac.manager = true)
  ]], {
    character.get('id'), account_number, character.get('id')
  })

  if
    not source_account or
    not source_account.account_number or
    tonumber(source_account.account_number) ~= tonumber(account_number)
  then
    return false, 'Access denied'
  end

  if source_account.account_type == 'Personal' then
    return false, 'Cannot manage personal account'
  end

  local users = MySQL.query.await([[
    SELECT bac.account_number, bac.manager, c.id as character_id, c.firstname, c.lastname FROM bank_account_characters bac
      INNER JOIN characters c ON c.id = bac.character_id
      WHERE bac.account_number = ?
  ]], { account_number })

  for _, user in pairs(users) do
    user.owner = tonumber(user.character_id) == tonumber(source_account.owner_character_id)
    user.name = user.firstname .. ' ' .. user.lastname
    user.firstname = nil
    user.lastname = nil
  end

  local data = {
    account = source_account,
    users = users
  }

  return true, '', data
end

pBanking.refresh = function()
  local character = exports.blrp_core:character(source)

  tBanking.updateFrontend(character.source, { getFrontendData(character) })
end

pBanking.saveKnown = function(known)
  local character = exports.blrp_core:character(source)

  local aliases = {}

  for _, known_account in pairs(known or {}) do
    table.insert(aliases, {
      account_number = known_account.account_number,
      account_name = known_account.account_name,
    })
  end

  character.setUserData('banking:accountaliases:' .. character.get('id'), aliases, true, true)

  return true, ''
end

pBanking.addKnown = function(known)
  local character = exports.blrp_core:character(source)

  local aliases = character.getUserData('banking:accountaliases:' .. character.get('id'), false, true) or {}

  for i = #aliases, 1, -1 do
    if tostring(aliases[i].account_number) == tostring(known.account_number) then
      table.remove(aliases, i)
    end
  end

  table.insert(aliases, {
    account_number = known.account_number,
    account_name = known.account_name,
  })

  character.setUserData('banking:accountaliases:' .. character.get('id'), aliases, true, true)
  return true, ''
end



pBanking.getTransactions = function(account_number, page)
  local character = exports.blrp_core:character(source)

  -- Check that this character can access the origin bank account
  local source_account = MySQL.single.await([[
    SELECT ba.* FROM bank_accounts ba
      INNER JOIN bank_account_characters bac ON bac.account_number = ba.account_number AND bac.character_id = ?
      WHERE ba.account_number = ?
  ]], {
    character.get('id'), account_number
  })

  if
    not source_account or
    not source_account.account_number or
    tonumber(source_account.account_number) ~= tonumber(account_number)
  then
    return false
  end

  local offset = 25 * (page or 0)
  local transactions = {}
  local results = MySQL.query.await([[
    SELECT * FROM bank_account_transactions
      WHERE account_number = ? AND hidden = false ORDER BY id DESC LIMIT 25 OFFSET ?
  ]], { account_number, offset }) or {}

  for _, transaction in pairs(results) do
    table.insert(transactions, {
      type = transaction.transaction_type,
      transactor = transaction.transactor_name,
      uid = transaction.transaction_uuid,
      amount = transaction.amount,
      notes = transaction.note,
      date = transaction.timestamp
    })
  end

  return true, transactions, (#results < 25)
end

local last_transactions = {}
local account_locks = {}

pBanking.transaction = function(data)
  local character = exports.blrp_core:character(source)

  if not last_transactions[character.source] then
    last_transactions[character.source] = 0
  end

  if GetGameTimer() - last_transactions[character.source] <= 150 then
    return false, 'Performing action too quickly'
  end

  -- Ensure no simultaneous transactions on the same account
  if account_locks[data.account_from] and GetGameTimer() - account_locks[data.account_from] <= 1000 then
    return false, 'Transaction in progress, please wait'
  end

  account_locks[data.account_from] = GetGameTimer() -- Lock the account

  last_transactions[character.source] = GetGameTimer()

  -- Parameter verification
  if
    not data.modality or
    not data.account_from or
    not data.amount or
    data.account_from == data.account_to or
    (
      data.modality == 'transfer' and
      not data.account_to
    )
  then
    if not data.modality or data.modality ~= 'exchange' then
      return false, 'Invalid request'
    end
  end

  if exports.blrp_core:ScanInputForBadWords(character.source, 'banking-tranaction', data.note) then
    return false
  end

  data.amount = tonumber(data.amount) or 0
  data.amount = math.floor(data.amount)

  if
    not data.amount or
    (
      type(data.amount) == 'number'
      and data.amount <= 0
    )
  then
    return false, 'Invalid amount'
  end

  data.account_from = tonumber(data.account_from)
  data.account_to = tonumber(data.account_to)

  -- Check that this character can access the origin bank account
  local source_account = MySQL.single.await([[
    SELECT ba.* FROM bank_accounts ba
      INNER JOIN bank_account_characters bac ON bac.account_number = ba.account_number AND bac.character_id = ?
      WHERE ba.account_number = ?
  ]], {
    character.get('id'), data.account_from
  })

  if
    data.modality ~= 'exchange' and
    (
      not source_account or
      not source_account.account_number or
      tonumber(source_account.account_number) ~= tonumber(data.account_from)
    )
  then
    return false, 'Access denied'
  end

  -- TODO: refactor elsewhere
  if character.get('assets_frozen') then
    return false, 'ACCOUNT FROZEN BY THE STATE'
  end

  if data.modality == 'exchange' then
    --------------
    -- EXCHANGE --
    --------------

    if not character.tryPayment(data.amount, false, false, true, false, 'cash_peso') then
      return false, 'Insufficient cash for exchange'
    end

    local dollar_amount = math.floor(data.amount / GlobalState.peso_exchange_rate)

    character.giveCash(dollar_amount)
    character.log('MONEY', 'Exchanged pesos for USD', {
      amount_pesos = data.amount,
      amount_usd = dollar_amount
    })

    return true, 'Received dollars: ' .. dollar_amount
  elseif data.modality == 'deposit' then
    -------------
    -- DEPOSIT --
    -------------

    if not character.tryPayment(data.amount, false, false, true) then
      return false, 'Insufficient cash for deposit'
    end

    local rows_affected = MySQL.update.await('UPDATE bank_accounts SET balance = balance + ? WHERE account_number = ?', {
      data.amount, data.account_from
    })

    if rows_affected <= 0 then
      return false, 'Error depositing money'
    end

    logTransaction(character, 'Deposit', data)
    character.notify('$' .. data.amount .. ' deposited')
    tBanking.updateFrontend(character.source, { getFrontendData(character) })

    return true
  elseif data.modality == 'withdraw' then
    --------------
    -- WITHDRAW --
    --------------

    if
      source_account.balance < data.amount and
      not config.allow_overdraft[tonumber(source_account.account_number)]
    then
      return false, 'Insufficient funds for transfer'
    end

    local rows_affected = MySQL.update.await('UPDATE bank_accounts SET balance = balance - ? WHERE account_number = ? AND balance >= ?', {
      data.amount, data.account_from, data.amount
    })

    if rows_affected <= 0 then
      return false, 'Error withdrawing money'
    end

    local currency = 'cash'
    local atm_fee, atm_currency = table.unpack(atm_info[character.source] or {})
    local give_amount = data.amount

    if atm_currency and atm_currency ~= currency then
      currency = atm_currency
    end

    if currency == 'cash_peso' then
      give_amount = give_amount * GlobalState.peso_exchange_rate
    end

    if atm_fee then
      character.animate({{"amb@prop_human_atm@male@exit","exit"}}, false, false)

      logTransaction(character, 'Withdrawal', {
        account_from = data.account_from,
        account_to = -1,
        transaction_type = 'withdraw',
        amount = atm_fee,
        note = 'ATM Fee',
      })

      character.takeBankMoney(atm_fee)
    end

    logTransaction(character, 'Withdrawal', data)
    character.notify(exports.blrp_core:CurrencySymbol(currency) .. give_amount .. ' withdrawn')
    character.giveCash(give_amount, false, currency)
    tBanking.updateFrontend(character.source, { getFrontendData(character) })

    return true
  elseif data.modality == 'transfer' then
    --------------
    -- TRANSFER --
    --------------

    local skip_destination_account_actions = false

    local destination_account = MySQL.single.await([[
      SELECT
          bank_accounts.*, characters.identifier as vrp_id
        FROM bank_accounts
        JOIN characters ON bank_accounts.owner_character_id = characters.id
        WHERE account_number = ?
    ]], {
      data.account_to
    })

    -- Special handling for electrical billing payments (account number ******** - ********)
    if not destination_account and data.account_to >= ******** and data.account_to <= ******** then
      skip_destination_account_actions = true
      destination_account = {
        account_type = 'Commercial',
        vrp_id = -1,
        owner_character_id = -1,
      }
    end

    if not destination_account then
      return false, 'Destination account not found'
    end

    -- Anticheat exploit check (trying to transfer money from one character's account to another)
    if
      not character.hasGroup('staff') and
      destination_account.account_type == 'Personal' and
      tonumber(destination_account.vrp_id) == tonumber(character.get('identifier')) and
      tonumber(destination_account.owner_character_id) ~= tonumber(character.get('id'))
    then
      local anticheat_log = 'Attempted to transfer money between characters via banking system / source_character_id = ' .. character.get('id') .. ' / destination_character_id = ' .. destination_account.owner_character_id .. ' / amount = ' .. data.amount

      character.log('EXPLOIT', anticheat_log)
      character.ban(character.get('identifier') .. ' exploit must appeal (anticheat)', 0, anticheat_log, true)
      return
    end

    if
      source_account.balance < data.amount and
      not config.allow_overdraft[tonumber(source_account.account_number)]
    then
      return false, 'Insufficient funds for transfer'
    end

    -- Add funds to destination account
    if not skip_destination_account_actions then
      local rows_affected = MySQL.update.await([[
        UPDATE bank_accounts SET balance = balance + ?
          WHERE account_number = ?
      ]], {
        data.amount, data.account_to
      })

      if rows_affected <= 0 then
        return false, 'Error transferring money'
      end
    else
      if data.account_to >= ******** and data.account_to <= ******** then
        local property_id = data.account_to - ********
        local electrical_record = MySQL.single.await('SELECT * FROM core_electrical_bills WHERE property_id = ? AND usage_dollars = ? AND paid = false', {
          property_id, data.amount
        })

        if not electrical_record then
          return false, 'No bill found to pay'
        end

        MySQL.update.await('UPDATE core_electrical_bills SET paid = true WHERE id = ?', {
          electrical_record.id
        })

        character.notify('LSDWP thanks you for paying your bill')
      end
    end

    -- Remove funds from source account
    MySQL.update.await([[
      UPDATE bank_accounts SET balance = balance - ?
        WHERE account_number = ?
    ]], {
      data.amount, data.account_from
    })

    local uuid = randomUuid()

    -- Log outbound transaction on source account
    logTransaction(character, 'Transfer - Outbound', data, uuid)

    -- Log inbound transaction on destination account
    if not skip_destination_account_actions then
      logTransaction(character, 'Transfer - Inbound', data, uuid)
    end

    character.notify('$' .. data.amount .. ' transferred')
    tBanking.updateFrontend(character.source, { getFrontendData(character) })

    return true
  end

  return false, 'Unknown Error'
end


    --------------
    --  SEARCH  --
    --------------

    pBanking.searchTransactions = function(account_number, searchTerm)
      local character = exports.blrp_core:character(source)
    
      -- Check that this character can access the origin bank account
      local source_account = MySQL.single.await([[
        SELECT ba.* FROM bank_accounts ba
          INNER JOIN bank_account_characters bac ON bac.account_number = ba.account_number AND bac.character_id = ?
          WHERE ba.account_number = ?
      ]], {
        character.get('id'), account_number
      })
    
      if
        not source_account or
        not source_account.account_number or
        tonumber(source_account.account_number) ~= tonumber(account_number)
      then
        return false, "Unauthorized access"
      end
    
      local transactions = {}
      local searchTermLike = '%' .. searchTerm .. '%'
      local searchTermNumber = tonumber(searchTerm)
    
      local results = MySQL.query.await([[
        SELECT *
        FROM bank_account_transactions
        WHERE account_number = ? AND hidden = false
          AND (
          ? = ''
            OR amount = ? 
            OR transactor_name LIKE ?
            OR note LIKE ?
          )
        ORDER BY id DESC LIMIT 50
      ]], {
        account_number,
         searchTerm, searchTermNumber, searchTermLike, searchTermLike
      }) or {}
    
    
      for _, transaction in pairs(results) do
        table.insert(transactions, {
          type = transaction.transaction_type,
          transactor = transaction.transactor_name,
          uid = transaction.transaction_uuid,
          amount = transaction.amount,
          notes = transaction.note,
          date = transaction.timestamp,
    
        })
      end
    
      return true, transactions
    end