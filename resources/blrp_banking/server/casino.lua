AddEventHandler('blrp_banking:server:handleCasinoPayout', function(game_name, profit)
  if profit == 0 then
    return
  end

  if profit > 0 then
    local profit_vault = math.ceil(profit * 0.2)
    local profit_holdings = profit - profit_vault

    exports.blrp_banking:DepositToAccount('Diamond Casino Floor', {
      account_to = 2000000, -- Diamond Casino Holdings
      amount = profit_holdings,
      note = game_name,
      hidden = true,
    })

    exports.blrp_banking:DepositToAccount('Diamond Casino Floor', {
      account_to = 2000002, -- Diamond Casino Vault
      amount = profit_vault,
      note = game_name,
      hidden = true,
    })

    return
  end

  if profit < 0 then
    exports.blrp_banking:WithdrawFromAccount('Diamond Casino Floor', {
      account_from = 2000000, -- Diamond Casino Holdings
      amount = math.abs(profit),
      note = game_name,
      hidden = true,
    })

    return
  end
end)
