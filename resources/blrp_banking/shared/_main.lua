function randomUuid()
  local a = string.format("%x", math.random(10000000, 99999999) * 255)
  local b = string.format("%x", math.random(1000, 9999) * 255)
  local c = string.format("%x", math.random(1000, 9999) * 255)
  local d = string.format("%x", math.random(1000, 9999) * 255)
  local e = string.format("%x", math.random(100000000000, ************) * 255)

  return string.sub(a, 0, 8) .. '-' .. string.sub(b, 0, 4) .. '-' .. string.sub(c, 0, 4) .. '-' .. string.sub(d, 0, 4) .. '-' .. string.sub(e, 0, 12)
end

exports('randomUuid', randomUuid)
