egghunt_config = {
  start_delay = 10 * 1000,     -- Milliseconds - "warm up" time
  search_time = 3 * 60 * 1000, -- Milliseconds - time the round should last

  time_min = 80, -- minutes, min to wait before the next egghunt
  time_max = 100, -- minutes, max to wait before the next egghunt

  variance = 50, -- RNG variance radius
  max_available = 50, -- Maximum eggs available at once (anti-cheese measure)

  water_rate_nerf_percent = 20, -- Nerf egg spawn rate by 50% if the amount of water around the player is >= this percent

  weights = {
    { 1, 96 },
    { 2, 92 },
    { 3, 88 },
    { 4, 84 },
    { 5, 80 },
    { 6, 38 },
    { 7, 36 },
    { 8, 34 },
    { 9, 32 },
    { 10, 30 },
    { 11, 28 },
    { 12, 26 },
    { 13, 24 },
    { 14, 24 },
    { 15, 24 },
    { 16, 24 },
    { 17, 23 },
    { 18, 22 },
    { 19, 20 },
    { 20, 9 },
    { 21, 8 },
    { 22, 7 },
    { 23, 6 },
    { 24, 5 },
  },

  weights_tebex = {
    { 1, 196 },
    { 2, 192 },
    { 3, 188 },
    { 4, 184 },
    { 5, 180 },
    { 6, 138 },
    { 7, 136 },
    { 8, 134 },
    { 9, 132 },
    { 10, 130 },
    { 11, 128 },
    { 12, 126 },
    { 13, 124 },
    { 14, 124 },
    { 15, 124 },
    { 16, 124 },
    { 17, 123 },
    { 18, 122 },
    { 19, 120 },
    { 20, 109 },
    { 21, 108 },
    { 22, 107 },
    { 23, 106 },
    { 24, 105 },
    { 'bronze', 150 },
    { 'silver', 60 },
    { 'gold', 20 },
  },
}
