local dialogue = {
  [1] = {
    part_a = {
      { false, 'EasterPerson01', "Outsider! The children of the mountain question you!" },
      { 'SELF', false, "Uh, what's the question?" },
      { false, 'EasterPerson01', "The children of the mountain wish to know your purpose" },
      { 'SELF', false, "My... purpose. Well, I don't really know" },
      { false, 'EasterPerson01', "The children of the mountain can show you a purpose, a purpose far greater than all of us" },
      { false, 'EasterPerson01', "The spring festival is upon us, and so too is <PERSON><PERSON>, the great rabbit deity!" },
      { 'SELF', false, "A rabbit... god?" },
      { false, 'EasterPerson01', "Of course, see for yourself!" },
    },

    part_b = {
      { 'SELF', false, "I looked, I saw, and I did not see a rabbit god" },
      { false, "EasterPerson01", "Your mind is narrow, your eyes are closed!" },
      { false, "EasterPerson01", "Drink this and open your mind. Drink this and be free from the confines of your brain!" },
      { 'item:estr24_drink', false, "The man hands you a disgusting looking - and smelling - concoction" },
    },

    previous_visitor = {
      { 'SELF', false, "Uh, hi... do you remember me?" },
      { false, 'EasterPerson01', "You... yes.. outsider!" },
      { false, 'EasterPerson01', "You're back in time for the spring festival!" },
      { false, 'EasterPerson01', "We saved something special for you!" },
      { 'item:estr24_drink', false, "The man hands you a delicious home made drink" },
    },
  },

  [2] = {
    EasterPerson01 = {
      { false, 'EasterPerson01', "Drink up, open your mind!" },
    },
  },

  [3] = {
    EasterPerson01 = {
      { false, 'EasterPerson01', "I can see it in your eyes... they are open to the reality of our world!" },
    },

    EasterRabbit01 = {
      { 'SELF', false, "Err, hello?" },
      { false, 'static:blrp_egghunt/a_c_rabbit_02', "Err, hello?" },
      { 'SELF', false, "Are you... real?" },
      { false, 'static:blrp_egghunt/a_c_rabbit_02', "As real as any part of your mind" },
      { 'SELF', false, "What does that mean?" },
      { false, 'static:blrp_egghunt/a_c_rabbit_02', "That's a good question. What could it mean?" },
      { 'SELF', false, "Are you just going to philosophy me to death here?" },
      { false, 'static:blrp_egghunt/a_c_rabbit_02', "No, I merely answered your question" },
      { 'SELF', false, "Well okay, what should I do next?" },
      { false, 'static:blrp_egghunt/a_c_rabbit_02', "You should follow the trail for my eggs" },
      { 'SELF', false, "Your... eggs?" },
      { false, 'static:blrp_egghunt/a_c_rabbit_02', "It is the time of the year for the eggs to come home. Bring them to me" },
      { false, false, "Egg hunt events will happen approximately every 90 minutes. Collect as many as you can!" },
    },
  },

  [4] = {
    EasterPerson01 = {
      { false, false, "As you approach, the man murmurs indistinguishable words under his breath" },
      { 'SELF', false, "Maybe I'll come back later" },
    },

    EasterRabbit01 = {
      { false, 'static:blrp_egghunt/a_c_rabbit_02', "Bring me my eggs and I will reward you handsomely" },
    },
  },
}

AddEventHandler('blrp_quests:broadcastTalkToPed', function(character, ped_id, event_data)
  -- return if we don't care about the ped they're talking to
  if not ({
    ['EasterPerson01'] = true,
    ['EasterRabbit01'] = true,
  })[ped_id] then
    return
  end

  local content_flags = tonumber(character.get('flags_easter2025') or 0)

  local stage = 1

  -- Stage 1 complete - player has talked to the guy and received the drink
  if content_flags & EGG_QUEST_2025_STAGES.STAGE_1 ~= 0 then
    stage = 2
  end

  -- Stage 2 complete - player has drunk the drink
  if content_flags & EGG_QUEST_2025_STAGES.STAGE_2 ~= 0 then
    stage = 3
  end

  -- Stage 3 complete - player has talked to the rabbit, quest complete
  if content_flags & EGG_QUEST_2025_STAGES.STAGE_3 ~= 0 then
    stage = 4
  end

  if ped_id == 'EasterPerson01' then
    if stage == 1 then
      local content_flags_2024 = tonumber(character.getUserData('flags_easter2024') or 0)
      local previous_visitor = content_flags_2024 & EGG_QUEST_2024_STAGES.STAGE_3 ~= 0

      if previous_visitor then
        if not tQuests.runDialogue(character.source, { dialogue[stage].previous_visitor }) then
          return
        end

        content_flags = content_flags | EGG_QUEST_2025_STAGES.STAGE_1
        character.set('flags_easter2025', content_flags)
        character.setUserData('flags_easter2025', content_flags, true)
        character.give('estr24_drink', 1, false, false)
        return
      end

      if not tQuests.runDialogue(character.source, { dialogue[stage].part_a }) then
        return
      end

      tEggHunt.camSceneA(character.source)

      if not tQuests.runDialogue(character.source, { dialogue[stage].part_b }) then
        return
      end

      content_flags = content_flags | EGG_QUEST_2025_STAGES.STAGE_1
      character.set('flags_easter2025', content_flags)
      character.setUserData('flags_easter2025', content_flags, true)
      character.give('estr24_drink', 1, false, false)
    elseif stage >= 2 then
      return tQuests.runDialogue(character.source, { dialogue[stage].EasterPerson01 })
    end
  elseif ped_id == 'EasterRabbit01' then
    if stage == 3 then
      if not tQuests.runDialogue(character.source, { dialogue[stage].EasterRabbit01 }) then
        return
      end

      content_flags = content_flags | EGG_QUEST_2025_STAGES.STAGE_3
      character.set('flags_easter2025', content_flags)
      character.setUserData('flags_easter2025', content_flags, true)
    elseif stage == 4 then
      return tQuests.runDialogue(character.source, { dialogue[stage].EasterRabbit01 })
    end
  end
end)

exports('DrinkAction', function(character, item_id)
  character.hideInventory(true)

  local content_flags = tonumber(character.get('flags_easter2025') or 0)

  if content_flags & EGG_QUEST_2025_STAGES.STAGE_2 ~= 0 then
    return
  end

  if character.distanceFrom(vector3(-1144.647, 4908.683, 220.969)) < 5.0 then
    Citizen.Wait(2000)

    T.getInstance('blrp_egghunt', 'main').camSceneB(character.source)
    content_flags = content_flags | EGG_QUEST_2025_STAGES.STAGE_2
    character.set('flags_easter2025', content_flags)
    character.setUserData('flags_easter2025', content_flags, true)
  else
    character.give('estr24_drink', 1, false, false)
  end
end)
