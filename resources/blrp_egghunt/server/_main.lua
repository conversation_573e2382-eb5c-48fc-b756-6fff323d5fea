math.randomseed(GetGameTimer())

pEggHunt = {}
P.bindInstance('main', pEggHunt)
tEggHunt = T.getInstance('blrp_egghunt', 'main')
tQuests = T.getInstance('blrp_quests', 'main')

GlobalState.egghunt_active = false
GlobalState.egghunt_starting = false

local next_egghunt_start = 0

function generateNewStart()
  next_egghunt_start = GetGameTimer() + (math.random(egghunt_config.time_min, egghunt_config.time_max) * 60 * 1000)
end

-- Main loop
-- On start, set the next start time based on config values (RNG)
-- If start time is in the past, start the egg hunt sequence
Citizen.CreateThread(function()
  generateNewStart()

  while true do
    if next_egghunt_start < GetGameTimer() then
      generateNewStart()

      GlobalState.egghunt_starting = true

      SetTimeout(egghunt_config.start_delay, function()
        GlobalState.egghunt_starting = false
        GlobalState.egghunt_active = true

        SetTimeout(egghunt_config.search_time, function()
          GlobalState.egghunt_active = false
        end)
      end)
    end

    Citizen.Wait(1000)
  end
end)

pEggHunt.tryTakeEgg = function(egg, has_tebex_package_client)
  local character = exports.blrp_core:character(source)

  if not GlobalState.egghunt_active then
    return
  end

  local item_id = egg.item_id
  local model = egg.model
  local coords = egg.coords

  if string.gsub(item_id, 'prop_bl_easteregg_2025_', 'prop_bl_easteregg_') ~= model then
    return
  end

  local content_flags = tonumber(character.get('flags_easter2025') or 0)

  local item_definition = exports.blrp_core:GetItemDefinition(item_id)

  if
    not item_definition or
    not ({
      ['easter2025_eggs'] = true,
      ['easter2025_elemental_eggs'] = true,
    })[item_definition.category]
  then
    return
  end

  if not has_tebex_package_client then
    has_tebex_package_client = false
  end

  local has_tebex_package_server = character.hasTebexPackage(TBX_PKG_EASTER_2025) or false

  if has_tebex_package_client ~= has_tebex_package_server then
    character.log('EXPLOIT', 'Potential tebex client side exploit', {
      package_id = TBX_PKG_EASTER_2025,
      client = has_tebex_package_client,
      server = has_tebex_package_server,
    })

    print('exploit alert')
  end

  -- Animate picking up egg
  if character.hasItemFromCategory('easter_baskets') then
    character.animate({{ 'pickup_object', 'pickup_low', 1 }}, true, false)
  else
    if not character.progressPromise('Taking Egg', 1, {
      animation = {
        animDict = 'pickup_object',
        anim = 'pickup_low',
        flags = 49
      },
      controlDisables = {
        disableMovement = true,
        disableSprint = true,
        disableCarMovement = false,
        disableMouse = false,
        disableCombat = true,
      }
    }) then
      return
    end
  end

  -- Remove egg on local client
  local removed = tEggHunt.removeEgg(character.source, { model, coords })

  if not removed then
    return
  end

  -- Increment egg count for player
  character.incrementUserData('count_easter2025_painted', 1)

  character.give(item_id, 1)
  character.log('EASTER2025', 'Picked up egg', {
    item_id = item_id
  })
end

AddEventHandler('chatCommand', function(player, command, args)
  if command ~= 'egghunt' then
    return
  end

  local character = exports.blrp_core:character(player)

  if not character.hasOrInheritsGroup('admin') then
    return
  end

  local new_state = not GlobalState.egghunt_active

  if not character.request('Confirm set egghunt state to ' .. tostring(new_state)) then
    return
  end

  if new_state then
    if character.request('Push next start time?') then
      generateNewStart()
      character.notify('pushed next start time: ' .. next_egghunt_start)
    end

    character.notify('start egghunt sequence')

    GlobalState.egghunt_starting = true

    SetTimeout(egghunt_config.start_delay, function()
      GlobalState.egghunt_starting = false
      GlobalState.egghunt_active = true

      SetTimeout(egghunt_config.search_time, function()
        GlobalState.egghunt_active = false
      end)
    end)
  end

  if not new_state then
    character.notify('end egghunt')
    GlobalState.egghunt_starting = false
    GlobalState.egghunt_active = false
  end
end)

-- Set content flags
AddEventHandler('core:server:registerSelectedPlayer', function(player)
  Citizen.Wait(2000)

  local character = exports.blrp_core:character(player)

  character.set('flags_easter2025', tonumber(character.getUserData('flags_easter2025') or 0))
end)

-- -- dev - for hot rebooting
-- if GlobalState.is_dev then
--   local character = exports.blrp_core:character(1)

--   local flag = tonumber(character.getUserData('flags_easter2025') or 0)

--   character.set('flags_easter2025', tonumber(flag))
-- end
