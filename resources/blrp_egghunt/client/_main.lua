math.randomseed(GetGameTimer())

local egghunt_starting = false
local egghunt_active = false
local egghunt_end_time = 0
local variance = egghunt_config.variance
local variance_n = (0 - variance)
local center_coords = nil
local last_egg_spawn = 0
local last_hint = 0
local spawned_eggs = {}
local eggs_available = 0
local eggs_collected = 0
local spawn_delay_default = 2500
local spawn_delay = spawn_delay_default
local water_test_nerf = false
local has_tebex_package = false

tEggHunt = {}
T.bindInstance('main', tEggHunt)
pEggHunt = P.getInstance('blrp_egghunt', 'main')

tEggHunt.camSceneA = function()
  local cam = CreateCamWithParams('DEFAULT_SCRIPTED_CAMERA',
    -1155.882, 4911.788, 222.279,
    0.0, 0.0, 27.842, 70.0, true, 2
  )

  RenderScriptCams(true, true, 1000, true, false)

  Citizen.Wait(3000)

  RenderScriptCams(false, true, 1000, true, false)

  Citizen.Wait(1000)

  DestroyCam(cam)
end

tEggHunt.camSceneB = function()
  local cam_a = CreateCamWithParams('DEFAULT_SCRIPTED_CAMERA',
    -1155.882, 4911.788, 222.279,
    0.0, 0.0, 27.842, 70.0, true, 2
  )

  local cam_b = CreateCamWithParams('DEFAULT_SCRIPTED_CAMERA',
    -1165.890, 4922.306, 224.745,
    -10.0, 0.0, 42.443, 70.0, false, 2
  )

  RenderScriptCams(true, true, 1000, true, false)

  while not HasNamedPtfxAssetLoaded('core') do
    RequestNamedPtfxAsset('core')
    Citizen.Wait(0)
  end

  UseParticleFxAsset('core')

  Citizen.Wait(2000)

  -- Run ptfx smoke
  local scale = 1.0
  local ptfx = StartParticleFxLoopedAtCoord('ent_amb_steam_vent_rnd_hvy', -1170.751, 4926.867, 223.815 + 0.1, 0.0, 0.0, 0.0, scale)

  SetCamActiveWithInterp(cam_b, cam_a, 1000, false, true)

  -- Smoke ptfx grows
  for i = 1, 4 do
    Citizen.Wait(100)
    scale = scale + 0.5
    SetParticleFxLoopedScale(ptfx, scale)
  end

  Citizen.Wait(2000)

  -- Rabbit comes in here
  override_rabbit_visibility = true
  local alpha = 51
  SetEntityAlpha(rabbit_ped, alpha)
  SetEntityVisible(rabbit_ped, true)

  -- Rabbit fades in
  while alpha < 255 do
    alpha = math.min(255, alpha + 20)
    SetEntityAlpha(rabbit_ped, alpha)
    SetEntityVisible(rabbit_ped, true)
    Citizen.Wait(50)
  end

  Citizen.Wait(1000)

  -- Smoke ptfx shrinks
  for i = 1, 12 do
    Citizen.Wait(50)
    scale = scale - 0.25
    SetParticleFxLoopedScale(ptfx, scale)
  end

  Citizen.Wait(1000)

  -- Kill smoke
  RemoveParticleFx(ptfx)

  Citizen.Wait(2000)

  RenderScriptCams(false, true, 1000, true, false)

  Citizen.Wait(1000)

  -- Kill cams
  DestroyCam(cam_a)
  DestroyCam(cam_b)
end

tEggHunt.removeEgg = function(model, coords)
  if not egghunt_active then
    return false
  end

  local found = false

  for idx, egg in pairs(spawned_eggs) do
    if not egg.removed and egg.model == model and #(egg.coords - coords) < 1.0 then
      egg.removed = true
      found = egg
      break
    end
  end

  if not found then
    return false
  end

  if not DoesEntityExist(found.handle) then
    return false
  end

  DeleteEntity(found.handle)

  eggs_collected = eggs_collected + 1
  eggs_available = eggs_available - 1

  return found
end

AddStateBagChangeHandler('egghunt_active', 'global', function(_, _, active)
  egghunt_active = active

  -- Activated. Set end time and reset egg counter variables
  if active then
    egghunt_end_time = GetGameTimer() + egghunt_config.search_time
    eggs_available = 0
    eggs_collected = 0
  end

  if not active then
    -- Clear eggs when egghunt ends
    SetTimeout(100, function()
      for _, egg in pairs(spawned_eggs) do
        if DoesEntityExist(egg.handle) then
          DeleteEntity(egg.handle)
        end
      end

      spawned_eggs = {}
    end)

    -- Hide UI after 10 seconds
    SetTimeout(10000, function()
      if not egghunt_starting and not egghunt_active then
        SendNUIMessage({
          action = 'hide',
        })

        eggs_available = 0
        eggs_collected = 0
      end
    end)
  end
end)

function findSpawnLocation(coords)
  local target_coords = coords

  -- x/y variation, set z to 999 for GetGroundZFor_3dCoord purposes
  target_coords = target_coords + vector3(math.random(variance_n, variance), math.random(variance_n, variance), 999.0)

  local heading = math.random(0, 359) + .0
  local ground, coordsZ = GetGroundZFor_3dCoord(target_coords.x, target_coords.y, target_coords.z, 1)

  target_coords = vector3(target_coords.x, target_coords.y, coordsZ)

  local in_water, _ = GetWaterHeightNoWaves(target_coords.x, target_coords.y, target_coords.z)
  local on_road = IsPointOnRoad(target_coords.x, target_coords.y, target_coords.z)

  if not ground or in_water or on_road then
    return findSpawnLocation(coords)
  end

  return target_coords
end

function waterTest()
  local tests = 100
  local water = 0

  local initial_coords = GetEntityCoords(PlayerPedId())

  for i = 1, tests do
    local target_coords = initial_coords + vector3(math.random(variance_n, variance), math.random(variance_n, variance), 999.0)
    local heading = math.random(0, 359) + .0
    local ground, coordsZ = GetGroundZFor_3dCoord(target_coords.x, target_coords.y, target_coords.z, 1)

    target_coords = vector3(target_coords.x, target_coords.y, coordsZ)

    local in_water, _ = GetWaterHeightNoWaves(target_coords.x, target_coords.y, target_coords.z)

    if in_water then
      water = water + 1
    end
  end

  if (water / tests) * 100 >= egghunt_config.water_rate_nerf_percent then
    return true
  end

  return false
end

AddStateBagChangeHandler('egghunt_starting', 'global', function(_, _, starting)
  has_tebex_package = exports.blrp_core:me().hasTebexPackage(TBX_PKG_EASTER_2025)

  if starting then
    egghunt_starting = GetGameTimer() + egghunt_config.start_delay

    spawn_delay = 2500
    water_test_nerf = false

    if has_tebex_package then
      spawn_delay = 1000
    end

    -- Heavy nerf for water cheese
    if waterTest() then
      water_test_nerf = true
      spawn_delay = spawn_delay * 2.5
    end
  else
    egghunt_starting = false
  end
end)

function playEggSound(coords)
  PlaySoundFromCoord(-1, 'Click', coords.x, coords.y, coords.z, 'DLC_HEIST_HACKING_SNAKE_SOUNDS', true, 100.0)
end

function getClosestEgg(coords)
  local closest = nil
  local closest_distance = nil

  for _, egg in pairs(spawned_eggs) do
    local distance = #(coords - egg.coords)

    if not closest_distance or distance < closest_distance then
      closest = egg
      closest_distance = distance
    end
  end

  return closest, closest_distance
end

function isEligibile()
  local u_flag = exports.blrp_core:me().get('flags_easter2025') or 0

  return u_flag & EGG_QUEST_2025_STAGES.STAGE_2 == EGG_QUEST_2025_STAGES.STAGE_2
end

local last_water_test = 0
local last_data_update = 0

-- UI thread
Citizen.CreateThread(function()
  local u_flag = 0
  local is_eligible = isEligibile()

  local function updateData()
    u_flag = exports.blrp_core:me().get('flags_easter2025') or 0
    is_eligible = isEligibile()
  end

  while true do
    if GetGameTimer() - last_data_update > 10000 then
      last_data_update = GetGameTimer()
      updateData()
    end

    -- Has started the egg event
    if is_eligible then
      if egghunt_starting or egghunt_active then
        local time_remaining = 0

        if egghunt_starting then
          time_remaining = -(egghunt_starting - GetGameTimer())
        elseif egghunt_active then
          time_remaining = egghunt_end_time - GetGameTimer()

          if time_remaining < 1000 then
            time_remaining = 0
          end
        end

        -- Re-run water test
        if GetGameTimer() - last_water_test > 10000 then
          if waterTest() and not water_test_nerf then
            water_test_nerf = true
            spawn_delay = spawn_delay * 2.5
          end

          last_water_test = GetGameTimer()
        end


        SendNUIMessage({
          action = 'update',
          remaining = time_remaining,
          collected = eggs_collected,
          available = eggs_available,
          gold = has_tebex_package,
        })
      end
    end

    Wait(0)
  end
end)

-- Egg spawning thread
Citizen.CreateThread(function()
  local blip = AddBlipForCoord(-1112.377, 4922.328, 218.473)

  SetBlipSprite(blip, 66)
  SetBlipAsShortRange(blip, true)
  SetBlipColour(blip, 28)
  SetBlipScale(blip, 0.7)

  BeginTextCommandSetBlipName("STRING")
  AddTextComponentString("Easter Special Event")
  EndTextCommandSetBlipName(blip)

  while true do
    Citizen.Wait(100)

    if egghunt_active then
      local u_flag = exports.blrp_core:me().get('flags_easter2025') or 0

      -- Has started the egg event
      if isEligibile() then
        -- Moving center logic
        local player_coords = GetEntityCoords(PlayerPedId())

        if not center_coords or #(player_coords - center_coords) > variance then
          center_coords = player_coords
        end

        -- Spawn egg logic
        if GetGameTimer() - last_egg_spawn > spawn_delay then
          local egg, egg_coords, egg_model = spawnEgg(player_coords)

          if egg then
            last_egg_spawn = GetGameTimer()

            table.insert(spawned_eggs, {
              handle = egg,
              coords = egg_coords,
              model = egg_model,
              item_id = string.gsub(egg_model, 'prop_bl_easteregg_', 'prop_bl_easteregg_2025_'),
            })
          end
        end

        -- Ping nearest egg logic, play sound on closest egg every 5 seconds
        if GetGameTimer() - last_hint > 5000 then
          last_hint = GetGameTimer()
          local closest_egg = getClosestEgg(player_coords)

          if closest_egg then
            playEggSound(closest_egg.coords)
          end
        end
      end
    end
  end
end)

function spawnEgg(center_coords)
  -- Don't spawn eggs if game not active, within 10 seconds of game end, or max available is met/exceeded
  if not egghunt_active or (egghunt_end_time - GetGameTimer()) < 10000 or eggs_available >= egghunt_config.max_available then
    return
  end

  local weights = egghunt_config.weights

  if has_tebex_package then
    weights = egghunt_config.weights_tebex
  end

  local variant = math.weightedrandom(weights)

  if tonumber(variant) and variant < 10 then
    variant = '0' .. variant
  end

  local model_str = 'prop_bl_easteregg_' .. variant
  local model = GetHashKey(model_str)
  local spawn_timeout = GetGameTimer() + 10000

  while not HasModelLoaded(model) do
    RequestModel(model)
    Citizen.Wait(0)

    if GetGameTimer() > spawn_timeout then
      break
    end
  end

  if not HasModelLoaded(model) then
    print('Failed to load model', model_str)
    return
  end

  local egg
  local egg_coords
  local i = 0
  local success = false

  while i < 10 do
    egg_coords = findSpawnLocation(center_coords)

    if not egghunt_active then
      return
    end

    egg = CreateObject(model, egg_coords.x, egg_coords.y, egg_coords.z, false, true)

    FreezeEntityPosition(egg, true)
    PlaceObjectOnGroundProperly(egg)
    SetEntityVisible(egg, false)

    local ur = GetEntityUprightValue(egg)

    if ur > 0.75 then
      SetEntityVisible(egg, true)
      success = true
      break
    else
      DeleteEntity(egg)
    end

    i = i + 1
    Citizen.Wait(0)
  end

  if not egghunt_active then
    DeleteEntity(egg)
    return
  end

  eggs_available = eggs_available + 1

  return egg, egg_coords, model_str
end

exports('TryPickupEgg', function()
  if IsPedInAnyVehicle(PlayerPedId(), false) then
    return
  end

  local egg, egg_distance = getClosestEgg(GetEntityCoords(PlayerPedId()))

  -- Only allow picking up an egg if <= 2.5 units away from it
  if not egg or egg_distance > 2.5 then
    return
  end

  pEggHunt.tryTakeEgg({ egg, exports.blrp_core:me().hasTebexPackage(TBX_PKG_EASTER_2025) })
end)
