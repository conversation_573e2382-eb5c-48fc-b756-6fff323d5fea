Number.prototype.pad = function(size) {
    var s = String(this);
    while (s.length < (size || 2)) {s = "0" + s;}
    return s;
}

function parseTimeString(millis) {
  let prefix = '';

  if (millis < 0) {
    millis = Math.abs(millis);
    prefix = '-';
  }

  let minutes = parseInt(millis / 1000 / 60);

  millis = millis - (minutes * 60 * 1000);

  let seconds = parseInt(millis / 1000);

  millis = parseInt((millis - (seconds * 1000)) / 10);

  return prefix + minutes.pad() + ':' + seconds.pad() + ':' + millis.pad();
}

window.addEventListener("message", function (event) {
  let data = event.data;

  if (data.action == 'update') {
    $("#info").show();

    $("#starting").show();
    $("#collected").hide();
    $("#available").hide();

    let time_remaining = data.remaining;

    if (time_remaining >= 0) {
      $("#starting").hide();
      $("#collected").show();
      $("#available").show();
    }

    $("#eggInfo_collected").text(data.collected.toString().padStart(3, '0'));
    $("#eggInfo_available").text(data.available.toString().padStart(3, '0'));
    $("#eggInfo_time").text(parseTimeString(data.remaining));

    if(data.gold && $("#info").css('background-image').includes('border.png')) {
      $("#info").css('background-image', 'url("border_gold.png")');
    }
  }

  if (data.action == 'hide') {
    $("#info").fadeOut();
  }
});
