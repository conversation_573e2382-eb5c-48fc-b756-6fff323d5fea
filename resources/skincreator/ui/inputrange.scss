input[type=range] {
  -webkit-appearance: none; /* Hides the slider so that custom slider can be made */
  width: 280px; /* Specific width is required for Firefox. */
  background: transparent; /* Otherwise white in Chrome */
  vertical-align: middle;
  padding: 0 10px;
}

input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
}

input[type=range]:focus {
  outline: none; /* Removes the blue border. You should probably do some kind of focus styling for accessibility reasons though. */
}

input[type=range]::-ms-track {
  width: 100%;
  cursor: pointer;

  /* Hides the slider so custom styles can be added */
  background: transparent;
  border-color: transparent;
  color: transparent;
}

/* Special styling for WebKit/Blink */
input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 12px;
  width: 12px;
  border-radius: 100%;
  background: #ffffff;
  cursor: pointer;
  margin-top: -6px; /* You need to specify a margin in Chrome, but in Firefox and IE it is automatic */
}

/* All the same stuff for Firefox */
input[type=range]::-moz-range-thumb {
  height: 12px;
  width: 12px;
  border-radius: 100%;
  background: #ffffff;
  cursor: pointer;
}

/* All the same stuff for IE */
input[type=range]::-ms-thumb {
  height: 12px;
  width: 12px;
  border-radius: 100%;
  background: #ffffff;
  cursor: pointer;
}
input[type=range]::-webkit-slider-runnable-track {
  width: 330px;
  height: 2px;
  cursor: pointer;
  background: rgba(255,255,255,.2);
}

input[type=range]:focus::-webkit-slider-runnable-track {
  background: rgba(255,255,255,.2);
}

input[type=range]::-moz-range-track {
  width: 330px;
  height: 2px;
  cursor: pointer;
  background: rgba(255,255,255,.2);
}

input[type=range]::-ms-track {
  width: 330px;
  height: 2px;
  cursor: pointer;
  background: transparent;
  border-color: transparent;
  color: transparent;
}
input[type=range]::-ms-fill-lower {
  background: rgba(255,255,255,.2);
}
input[type=range]:focus::-ms-fill-lower {
  background: rgba(255,255,255,.2);
}
input[type=range]::-ms-fill-upper {
  background: rgba(255,255,255,.2);
}
input[type=range]:focus::-ms-fill-upper {
  background: rgba(255,255,255,.2);
}
