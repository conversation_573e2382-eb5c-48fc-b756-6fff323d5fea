pVoting = P.getInstance('blrp_voting', 'voting')

tVoting = {}
T.bindInstance('voting', tVoting)

local guiVisible = false

--[[
Citizen.CreateThread(function()
  Citizen.Wait(500)
  SetNuiFocus(false, false)

  local ballot = pVoting.getBallot({ 1 })

  if not ballot then
    return
  end

  exports.blrp_core:print_r(ballot)

  SendNUIMessage({
    command = 'interface:setBallot',
    ballot = ballot,
  })

  guiVisible = true
  SetNuiFocus(true, true)
end)
]]

RegisterNUICallback('escape', function(data, callback)
  TriggerEvent('blrp_voting:client:setInterfaceVisibility', false)
  callback('ok')
end)

RegisterNUICallback('submit', function(data, callback)
  TriggerEvent('blrp_voting:client:setInterfaceVisibility', false)

  pVoting.submitBallot({ data })

  callback('ok')
end)

-- AddEventHandler('blrp_voting:client:openMusicBallot', function()
--   local ballot = pVoting.getBallot({13})
--
--   if not ballot then
--     return
--   end
--
--   SendNUIMessage({
--     command = 'interface:setBallot',
--     ballot = ballot,
--   })
--
--   guiVisible = true
--   SetNuiFocus(true, true)
-- end)

AddEventHandler('blrp_voting:client:openElectionBallot', function()
  local ballot = pVoting.getBallotForElection()

  if not ballot then
    return
  end

  SendNUIMessage({
    command = 'interface:setBallot',
    ballot = ballot,
  })

  guiVisible = true
  SetNuiFocus(true, true)
end)

AddEventHandler('blrp_voting:client:openSheriffBallot', function()
  local ballot = pVoting.getBallotForSheriff()

  if not ballot then
    return
  end

  SendNUIMessage({
    command = 'interface:setBallot',
    ballot = ballot,
  })

  guiVisible = true
  SetNuiFocus(true, true)
end)

AddEventHandler('blrp_voting:client:openReferendumBallot', function()
  local ballot = pVoting.getBallotForUtopiaReferendum()

  if not ballot then
    return
  end

  SendNUIMessage({
    command = 'interface:setBallot',
    ballot = ballot,
  })

  guiVisible = true
  SetNuiFocus(true, true)
end)

RegisterNetEvent('blrp_voting:client:setInterfaceVisibility', function(visible, access_type, cash)
	if visible or guiVisible then
		guiVisible = visible
		SetNuiFocus(visible, visible)
		SendNUIMessage({
			command = 'interface:setVisible',
			visible = visible,
		})
	end
end)
