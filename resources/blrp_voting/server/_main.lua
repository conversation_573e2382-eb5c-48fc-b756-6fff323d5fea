tVoting = T.getInstance('blrp_voting', 'voting')
tZones = T.getInstance('blrp_zones', 'zones')

pVoting = {}
P.bindInstance('voting', pVoting)

local compiled_ballots = {}

function buildBallot(ballot_id)
  local ballot_data = MySQL.single.await('SELECT * FROM voting_ballots WHERE id = ?', {
    ballot_id
  })

  local questions = MySQL.query.await('SELECT * FROM voting_questions WHERE id IN (SELECT question_id FROM voting_ballot_questions WHERE ballot_id = ?)', {
    ballot_id
  })

  local question_ids = ''

  for _, question in pairs(questions) do
    if question_ids ~= '' then
      question_ids = question_ids .. ', '
    end

    question_ids = question_ids .. tonumber(question.id)
  end

  local options = MySQL.query.await('SELECT * FROM voting_options WHERE question_id IN (' .. question_ids .. ')')

  local ballot = {
    id = ballot_data.id,
    title = ballot_data.title,
    description = ballot_data.description,
    vote_uid = ballot_data.vote_uid,

    questions = {},
    question_ids = {},
  }

  for question_id, question_data in pairs(questions) do
    local question = {
      id = question_data.id,
      question = question_data.question,
      description = question_data.description,
      maxOptions = question_data.max_choices,
      options = {}
    }

    for option_id, option_data in pairs(options) do
      if option_data.question_id == question_data.id then
        local description = option_data.description

        if not description or description == '' then
          description = '&nbsp;'
        end

        table.insert(question.options, {
          id = option_data.id,
          qid = question_data.id,
          option = option_data.option,
          description = description,
        })
      end
    end

    table.insert(ballot.questions, question)
    ballot.question_ids[question_data.id] = true
  end

  return ballot
end

function getBallot(ballot_id)
  if compiled_ballots[ballot_id] then
    return compiled_ballots[ballot_id]
  end

  local ballot = buildBallot(ballot_id)

  compiled_ballots[ballot_id] = ballot

  return ballot
end

pVoting.getBallot = function(ballot_id)
  local character = exports.blrp_core:character(source)

  local submitted_ballot = MySQL.single.await('SELECT * FROM voting_ballots_submitted WHERE ballot_id = ? AND character_id = ?', {
    ballot_id,
    character.get('id')
  })

  if submitted_ballot then
    character.notify('You cannot cast another ballot in this election')
    return false
  end

  return getBallot(ballot_id)
end

-- State election (quarterly)
pVoting.getBallotForElection = function()
  local character = exports.blrp_core:character(source)

  local ballot_id = nil

  local owned_houses = exports.blrp_core:PropertiesGetByCharacter(character.get('id'))

  local property_city = false
  local property_county = false

  -- Determine voting district based on owned property
  for _, owned_house in pairs(owned_houses) do
    if owned_house.door_location then
      if tZones.isInsideMatchedZone(character.source, { 'CityOfLosSantos', false, false, vector3(owned_house.door_location.x, owned_house.door_location.y, owned_house.door_location.z) }) then
        property_city = true
      else
        property_county = true
      end
    end
  end

  -- No owned property, determine voting district based on home apartment
  if not property_city and not property_county then
    local home = character.get('home')

    if home == 'downbad' then
      property_city = true
    elseif home == 'sandy' or home == 'paleto' then
      property_county = true
    end
  end

  if property_city and property_county then
    ballot_id = 16
  elseif property_city then
    ballot_id = 14
  elseif property_county then
    ballot_id = 15
  end

  if not ballot_id then
    character.notify('The election system was not able to provide you a ballot based on your home address')
    return
  end

  local ballot = getBallot(ballot_id)

  local submitted_ballot = MySQL.single.await('SELECT * FROM voting_ballots_submitted WHERE vote_uid = ? AND character_id = ?', {
    ballot.vote_uid,
    character.get('id')
  })

  if submitted_ballot then
    character.notify('You cannot cast another ballot in this election')
    return false
  end

  local open = MySQL.query.await('SELECT * FROM voting_ballots WHERE vote_uid = ? AND open = true', {
    'SAGEN2024Q1'
  })

  if #open <= 0 then
    character.notify('Voting for this ballot has closed')
    return false
  end

  return ballot
end

local sheriff_vote_types = {}

pVoting.getBallotForSheriff = function()
  local character = exports.blrp_core:character(source)

  --[[
    3 = BCSO Employee
    2 = Blaine County Civilian
    1 = Blaine County Felon
  ]]
  local vote_type = nil

  if tonumber(character.get('rank_bcso')) > -1 then
    vote_type = 3
  end

  if not vote_type then
    local home = character.get('home')

    if home ~= 'downbad' then
      if character.get('has_felonies') then
        vote_type = 1
      else
        vote_type = 2
      end
    end
  end

  if not vote_type then
    local owned_houses = exports.blrp_core:PropertiesGetByCharacter(character.get('id'))

    -- Determine voting district based on owned property
    for _, owned_house in pairs(owned_houses) do
      if owned_house.door_location then
        if not tZones.isInsideMatchedZone(character.source, { 'CityOfLosSantos', false, false, vector3(owned_house.door_location.x, owned_house.door_location.y, owned_house.door_location.z) }) then
          if character.get('has_felonies') then
            vote_type = 1
          else
            vote_type = 2
          end
        end
      end
    end
  end

  if not vote_type then
    character.notify('You are not eligible to vote for the Sheriff of Blaine County')
    return false
  end

  sheriff_vote_types[tonumber(character.get('id'))] = vote_type

  local ballot = getBallot(18)

  local submitted_ballot = MySQL.single.await('SELECT * FROM voting_ballots_submitted WHERE vote_uid = ? AND character_id = ?', {
    ballot.vote_uid,
    character.get('id')
  })

  if submitted_ballot then
    character.notify('You cannot cast another ballot in this election')
    return false
  end

  local open = MySQL.query.await('SELECT * FROM voting_ballots WHERE vote_uid = ? AND open = true', { ballot.vote_uid })

  if #open <= 0 then
    character.notify('Voting for this election has closed')
    return false
  end

  return ballot
end

-- Utopia Gardens naming referendum
pVoting.getBallotForUtopiaReferendum = function()
  local character = exports.blrp_core:character(source)

  local ballot_id = nil

  local owned_houses = exports.blrp_core:PropertiesGetByCharacter(character.get('id'))

  local property_city = false

  -- Determine voting district based on owned property
  for _, owned_house in pairs(owned_houses) do
    if owned_house.door_location then
      if tZones.isInsideMatchedZone(character.source, { 'CityOfLosSantos', false, false, vector3(owned_house.door_location.x, owned_house.door_location.y, owned_house.door_location.z) }) then
        property_city = true
      end
    end
  end

  -- No owned property, determine voting district based on home apartment
  if not property_city and not property_county then
    local home = character.get('home')

    if home == 'downbad' then
      property_city = true
    end
  end

  if not property_city then
    character.notify('You do not reside in the City of Los Santos and cannot vote in this referendum')
    return
  end

  local ballot = getBallot(7)

  local submitted_ballot = MySQL.single.await('SELECT * FROM voting_ballots_submitted WHERE vote_uid = ? AND character_id = ?', {
    ballot.vote_uid,
    character.get('id')
  })

  if submitted_ballot then
    character.notify('You cannot cast another ballot in this referendum')
    return false
  end

  local open = MySQL.query.await('SELECT * FROM voting_ballots WHERE vote_uid = ? AND open = true', {
    'LSREF202301'
  })

  if #open <= 0 then
    character.notify('Voting for this referendum has closed')
    return false
  end

  return ballot
end

pVoting.submitBallot = function(data)
  local character = exports.blrp_core:character(source)

  if not character.request('Are you sure you wish to submit your ballot? Any questions left with no option selected will not count and will not be able to be amended in the future. Press F2 if you wish to complete another ballot', 60) then
    return
  end

  if not data or not data.ballot_id then
    return
  end

  local ballot = getBallot(data.ballot_id)

  local ballot_data = MySQL.single.await('SELECT * FROM voting_ballots WHERE id = ?', {
    ballot.id
  })

  if not ballot_data.open then
    character.notify('Voting for this ballot has closed')
    return
  end

  local submitted_ballot = MySQL.single.await('SELECT * FROM voting_ballots_submitted WHERE vote_uid = ? AND character_id = ?', {
    ballot.vote_uid,
    character.get('id')
  })

  if submitted_ballot then
    character.notify('You cannot cast another ballot in this election')
    return
  end

  MySQL.insert('INSERT INTO voting_ballots_submitted (vote_uid, ballot_id, user_id, character_id, created_at) VALUES (?, ?, ?, ?, ?)', {
    ballot.vote_uid,
    ballot.id,
    character.get('identifier'),
    character.get('id'),
    os.date("%Y-%m-%d %H:%M:%S"),
  })

  MySQL.update('UPDATE voting_ballots SET submissions = submissions + 1 WHERE id = ?', {
    ballot.id
  })

  local extra = nil

  if string.match(ballot.vote_uid, 'BCSHRF') then
    extra = sheriff_vote_types[tonumber(character.get('id'))]
  end

  for _, response in pairs(data.responses) do
    if response.checked and ballot.question_ids[response.qid] then
      MySQL.insert('INSERT IGNORE INTO voting_option_responses (user_id, character_id, question_id, option_id, extra, created_at) VALUES (?, ?, ?, ?, ?, ?)', {
        character.get('identifier'),
        character.get('id'),
        response.qid,
        response.oid,
        extra,
        os.date("%Y-%m-%d %H:%M:%S"),
      })
    end
  end

  character.notify('Your votes have been cast. Thank you for doing your civic duty')
  if string.match(ballot.vote_uid, 'SAGEN20') then
    character.give('sticker_voted', 1)
  end
end
