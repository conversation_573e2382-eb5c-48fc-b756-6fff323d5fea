<html>
  <head>
    <link rel="stylesheet" href="css/style.css" />
  </head>

  <body>
    <div id="app"></div>

    <script src="https://code.jquery.com/jquery-3.6.1.min.js" crossorigin="anonymous"></script>
    <script src="js/voting.js"></script>

    <script type="text/template" data-template="ballot">
      <div id="ballot">
        <div id="ballot-header">
          <h1>${title}</h1>
          <h3>${description}</h3>
          <h5>To vote, mark a check in the circle next to your option(s) of choice</h5>
        </div>

        <div id="ballot-body"></div>

        <button id="submit">SUBMIT BALLOT</button>
        <button id="discard">DISCARD BALLOT</button>
      </div>
    </script>

    <script type="text/template" data-template="question">
      <div class="question" id="question-${id}">
        <div class="question-header">
          <h3>${question}</h3>
          <h4>${description}</h4>
          <h5>Select a maximum of ${maxOptions} option(s)</h5>
        </div>

        <div class="question-body"></div>
      </div>
    </script>

    <script type="text/template" data-template="option">
      <div class="option">
        <div class="option-left">
          <h3>${option}</h3>
          <h4>${description}</h4>
        </div>

        <div class="option-right">
          <div class="checkbox">
            <input type="checkbox" data-qid="${qid}" data-oid="${id}" id="o${id}-checkbox" />
            <label for="o${id}-checkbox"></label>
          </div>
        </div>
      </div>
    </script>
  </body>
</html>
