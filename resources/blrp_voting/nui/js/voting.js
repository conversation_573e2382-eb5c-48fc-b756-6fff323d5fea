let interfaceVisible = false;

function setInterfaceVisibility(visible) {
  if(interfaceVisible) {
    escapeInterface();
  }

  interfaceVisible = visible

  if(interfaceVisible) {
    $('body').show();
  } else {
    $('body').hide();
  }
}

function escapeInterface() {
  fetch(`https://blrp_voting/escape`, {
    method: 'POST',
    headers: {'Content-Type': 'application/json; charset=UTF-8',},
    body: JSON.stringify({})
  }).then(resp => resp.json()).then(resp => {
    return resp;
  });
}

document.onkeydown = evt => {
  if(!interfaceVisible) return false;
  evt = evt || window.event;
  if (evt.keyCode === 27) escapeInterface();
}

function renderTemplate(props) {
  return function(tok, i) {
    return (i % 2) ? props[tok] : tok;
  };
}

let ballot = {}
let submitting = false

$(document).on('change', 'input[type="checkbox"]', function() {
  let qid = $(this).data('qid');
  let maxOptions = 1;

  ballot.questions.forEach((question) => {
    if(Number(qid) == question.id) {
      maxOptions = question.maxOptions;
    }
  })

  if(maxOptions == 1) {
    $('input[data-qid="' + qid + '"]:checked').not($(this)).prop('checked', false);
  } else {
    let selected_count = $('input[data-qid="' + qid + '"]:checked').length

    if(selected_count > maxOptions) {
      $(this).prop('checked', false);
    }
  }
}).on('click', '#discard', function() {
  escapeInterface();
}).on('click', '#submit', function() {
  if(submitting) {
    return;
  }

  submitting = true

  let data = {
    ballot_id: ballot.id,
    responses: []
  };

  $('input[type="checkbox"]').each(function() {
    data.responses.push({
      qid: $(this).data('qid'),
      oid: $(this).data('oid'),
      checked: this.checked
    })
  });

  fetch(`https://blrp_voting/submit`, {
    method: 'POST',
    headers: {'Content-Type': 'application/json; charset=UTF-8',},
    body: JSON.stringify(data)
  }).then(resp => resp.json()).then(resp => {
    submitting = false

    return resp;
  });
});

function renderBallot(_ballot) {
  ballot = _ballot

  $('#app').html('');

  let ballot_template = $('script[data-template="ballot"]').text().split(/\$\{(.+?)\}/g);
  let question_template = $('script[data-template="question"]').text().split(/\$\{(.+?)\}/g);
  let option_template = $('script[data-template="option"]').text().split(/\$\{(.+?)\}/g);

  $('#app').append(ballot_template.map(renderTemplate(ballot)).join(''));

  ballot.questions.forEach((question, index) => {
    let question_dom = $('#ballot-body').append(question_template.map(renderTemplate(question)).join(''));

    question.options.forEach((option, index) => {
      $('#question-' + question.id).find('.question-body').append(option_template.map(renderTemplate(option)).join(''));
    })
  })
}

window.addEventListener('message', event => {
  if(!event.data) return false;

  if(event.data.command === 'interface:setVisible') {
    setInterfaceVisibility(event.data.visible);
  }

  if(event.data.command === 'interface:setBallot') {
    renderBallot(event.data.ballot);
    setInterfaceVisibility(true);
  }
});
