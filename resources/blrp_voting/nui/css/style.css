@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;0,700;1,300&display=swap');

:root {
  --checkbox-height: 34px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  min-width: 100%;
  min-height: 100%;
  overflow: hidden;
  position: relative;
}

body {
  font-family: 'Roboto', sans-serif;
  background-size: cover;
  background-repeat: no-repeat;
  display: none;
}

#app {
  display: flex;
  align-items: center;
  flex-direction: column;

  max-height: 100%;
  height: 100%;
  overflow-y: scroll;

  padding: 10px;
}

#app::-webkit-scrollbar {
  width: 5px;
}

#app::-webkit-scrollbar-track {
  background-color: white;
}

#app::-webkit-scrollbar-thumb {
  background: #4e4e4e;
  border-radius: 10px;
}

#ballot {
  border: 1px solid black;

  min-width: 30% !important;
  max-width: 30% !important;

  padding: 10px;

  background-image: url('../paper.jpg');
  background-color: white;
}

#ballot-header {
  border: 1px dashed black;
  padding: 5px;
  margin-bottom: 5px;
}

.question {
  border: 1px dashed black;
  padding: 5px;

  margin-bottom: 10px;
}

.option {
  margin: 10px;

  display: flex;
  align-items: stretch;

  border: 1px solid black;
  border-left: 10px solid black;
  border-radius: 1px;
}

.option-left {
  padding: 10px;
  text-align: right;
  flex-grow: 4;
  width: min-content;
}

.option-right {
  position: relative;

  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  background-color: black;
  width: 50px;
}

.checkbox label {
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 50%;
  cursor: pointer;
  height: var(--checkbox-height);
  width: var(--checkbox-height);
  left: 8px;
  position: absolute;
  top: calc((100% / 2) - (var(--checkbox-height) / 2));
}

.checkbox label:after {
  border: 2px solid #000;
  border-top: none;
  border-right: none;
  content: "";
  height: 6px;
  left: 8px;
  opacity: 0;
  position: absolute;
  top: 11px;
  transform: rotate(-45deg);
  width: 15px;
}

.checkbox input[type="checkbox"] {
  visibility: hidden;
}

.checkbox input[type="checkbox"]:checked + label {
  background-color: #fff;
  border-color: #fff;
}

.checkbox input[type="checkbox"]:checked + label:after {
  opacity: 1;
}

button {
  margin-left: 5px;
  float: right;
  background-color: #e7e7e7;
  border: 1px solid black;
  color: black;
  padding: 10px 25px;
  text-align: center;
  text-decoration: none;
  display: inline-block;
  font-size: 16px;
  font-weight: bold;
}
