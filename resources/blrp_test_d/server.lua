local weapon_hashes_unsigned = {}
local weapon_hashes_signed = {
  -- Animals
  [`WEAPON_ANIMAL`] = 'WEAPON_ANIMAL',
  [`WEAPON_COYOTE`] = 'WEAPON_COYOTE',
  [`WEAPON_COUGAR`] = 'WEAPON_COUGAR',

  -- Vehicle
  [`WEAPON_HIT_BY_WATER_CANNON`] = 'HIT BY WATER CANNON',
  [`WEAPON_RAMMED_BY_CAR`] = 'RAMMED BY CAR',
  [`WEAPON_RUN_OVER_BY_CAR`] = 'RUN OVER BY CAR',
  [`WEAPON_HELI_CRASH`] = 'HELI CRASH',

  [`WEAPON_UNARMED`] = 'WEAPON_UNARMED',

  [`WEAPON_KNIFE`] = 'WEAPON_KNIFE',
  [`WEAPON_DAGGER`] = 'WEAPON_DAGGER',
  [`WEAPON_BOTTLE`] = 'WEAPON_BOTTLE',
  [`WEAPON_FLASHLIGHT`] = 'WEAPON_FLASHLIGHT',
  [`WEAPON_FLASHLIGHT_UV`] = 'WEAPON_FLASHLIGHT_UV',
  [`WEAPON_NIGHTSTICK`] = 'WEAPON_NIGHTSTICK',
  [`WEAPON_HAMMER`] = 'WEAPON_HAMMER',
  [`WEAPON_LUCILLE`] = 'WEAPON_LUCILLE',
  [`WEAPON_HOBBYHORSE`] = 'WEAPON_HOBBYHORSE',
  [`WEAPON_DRAGON_KATANA_BLUE`] = 'WEAPON_DRAGON_KATANA_BLUE',
  [`WEAPON_KATANA_2`] = 'WEAPON_KATANA_2',
  [`WEAPON_BAT`] = 'WEAPON_BAT',
  [`WEAPON_GOLFCLUB`] = 'WEAPON_GOLFCLUB',
  [`WEAPON_CROWBAR`] = 'WEAPON_CROWBAR',
  [`WEAPON_FIREEXTINGUISHER`] = 'WEAPON_FIREEXTINGUISHER',
  [`WEAPON_FLARE`] = 'WEAPON_FLARE',
  [`WEAPON_SWITCHBLADE`] = 'WEAPON_SWITCHBLADE',
  [`WEAPON_MILSPECKNIFE`] = 'WEAPON_MILSPECKNIFE',
  [`WEAPON_BATTLEAXE`] = 'WEAPON_BATTLEAXE',
  [`WEAPON_POOLCUE`] = 'WEAPON_POOLCUE',
  [`WEAPON_WRENCH`] = 'WEAPON_WRENCH',
  [`WEAPON_SMG_MK2`] = 'WEAPON_SMG_MK2',
  [`WEAPON_UMP45`] = 'WEAPON_UMP45',
  [`WEAPON_CERAMICPISTOL`] = 'WEAPON_CERAMICPISTOL',
  [`WEAPON_COMBATPISTOL`] = 'WEAPON_COMBATPISTOL',
  [`WEAPON_Glock17`] = 'WEAPON_GLOCK17',
  [`WEAPON_PISTOL`] = 'WEAPON_PISTOL',
  [`WEAPON_APPISTOL`] = 'WEAPON_APPISTOL',
  [`WEAPON_PISTOL50`] = 'WEAPON_PISTOL50',
  [`WEAPON_HEAVYPISTOL`] = 'WEAPON_HEAVYPISTOL',
  [`WEAPON_REVOLVER`] = 'WEAPON_REVOLVER',
  [`WEAPON_SNSPISTOL`] = 'WEAPON_SNSPISTOL',
  [`WEAPON_SNSPISTOL_MK2`] = 'WEAPON_SNSPISTOL_MK2',
  [`WEAPON_PISTOL_MK2`] = 'WEAPON_PISTOL_MK2',
  [`WEAPON_PISTOLXM3`] = 'WEAPON_PISTOLXM3',
  [`WEAPON_SERVICEPISTOL_45`] = 'WEAPON_SERVICEPISTOL_45',
  [`WEAPON_SERVICEPISTOL_9MM`] = 'WEAPON_SERVICEPISTOL_9MM',
  [`WEAPON_SERVICEPISTOL_AUTO`] = 'WEAPON_SERVICEPISTOL_AUTO',
  [`WEAPON_VP897`] = 'WEAPON_VP897',
  [`WEAPON_SP45`] = 'WEAPON_SP45',
  [`WEAPON_VINTAGEPISTOL`] = 'WEAPON_VINTAGEPISTOL',
  [`WEAPON_MICROSMG`] = 'WEAPON_MICROSMG',
  [`WEAPON_SMG`] = 'WEAPON_SMG',
  [`WEAPON_ASSAULTSMG`] = 'WEAPON_ASSAULTSMG',
  [`WEAPON_MINISMG`] = 'WEAPON_MINISMG',
  [`WEAPON_MACHINEPISTOL`] = 'WEAPON_MACHINEPISTOL',
  [`WEAPON_COMBATPDW`] = 'WEAPON_COMBATPDW',
  [`WEAPON_PUMPSHOTGUN`] = 'WEAPON_PUMPSHOTGUN',
  [`WEAPON_PUMPSHOTGUN2`] = 'WEAPON_PUMPSHOTGUN2',
  [`WEAPON_SAWNOFFSHOTGUN`] = 'WEAPON_SAWNOFFSHOTGUN',
  [`WEAPON_ASSAULTSHOTGUN`] = 'WEAPON_ASSAULTSHOTGUN',
  [`WEAPON_ASSAULTRIFLE`] = 'WEAPON_ASSAULTRIFLE',
  [`WEAPON_ASSAULTRIFLE2`] = 'WEAPON_ASSAULTRIFLE2',
  [`WEAPON_ASSAULTRIFLE_MK2`] = 'WEAPON_ASSAULTRIFLE_MK2',
  [`WEAPON_HEAVYSHOTGUN`] = 'WEAPON_HEAVYSHOTGUN',
  [`WEAPON_CARBINERIFLE`] = 'WEAPON_CARBINERIFLE',
  [`WEAPON_CARBINERIFLE_MK2`] = 'WEAPON_CARBINERIFLE_MK2',
  [`WEAPON_CARBINERIFLET`] = 'WEAPON_CARBINERIFLET',
  [`WEAPON_CARBINERIFLEMK2T`] = 'WEAPON_CARBINERIFLEMK2T',
  [`WEAPON_ADVANCEDRIFLE`] = 'WEAPON_ADVANCEDRIFLE',
  [`WEAPON_SPECIALCARBINE`] = 'WEAPON_SPECIALCARBINE',
  [`WEAPON_SPECIALCARBINE_MK2`] = 'WEAPON_SPECIALCARBINE_MK2',
  [`WEAPON_BULLPUPRIFLE`] = 'WEAPON_BULLPUPRIFLE',
  [`WEAPON_COMPACTRIFLE`] = 'WEAPON_COMPACTRIFLE',
  [`WEAPON_MILITARYRIFLE`] = 'WEAPON_MILITARYRIFLE',
  [`WEAPON_MG`] = 'WEAPON_MG',
  [`WEAPON_TECPISTOL`] = 'WEAPON_TECPISTOL',
  [`WEAPON_DOUBLEACTION`] = 'WEAPON_DOUBLEACTION',
  [`WEAPON_GUSENBERG`] = 'WEAPON_GUSENBERG',
  [`WEAPON_BEANBAG`] = 'WEAPON_BEANBAG',
  [`WEAPON_STUNGUN`] = 'WEAPON_STUNGUN',
  [`WEAPON_DBSHOTGUN`] = 'WEAPON_DBSHOTGUN',
  [`WEAPON_MUSKET`] = 'WEAPON_MUSKET',
  [`WEAPON_REVOLVER_MK2`] = 'WEAPON_REVOLVER_MK2',
  [`WEAPON_COMBATMG`] = 'WEAPON_COMBATMG',
  [`WEAPON_COMBATMG_MK2`] = 'WEAPON_COMBATMG_MK2',
  [`WEAPON_SNIPERRIFLE`] = 'WEAPON_SNIPERRIFLE',
  [`WEAPON_HEAVYSNIPER`] = 'WEAPON_HEAVYSNIPER',
  [`WEAPON_HEAVYSNIPER_MK2`] = 'WEAPON_HEAVYSNIPER_MK2',
  [`WEAPON_MARKSMANRIFLE`] = 'WEAPON_MARKSMANRIFLE',
  [`WEAPON_MARKSMANRIFLE_MK2`] = 'WEAPON_MARKSMANRIFLE_MK2',
  [`WEAPON_HATCHET`] = 'WEAPON_HATCHET',
  [`WEAPON_KNUCKLE`] = 'WEAPON_KNUCKLE',
  [`WEAPON_MACHETE`] = 'WEAPON_MACHETE',
  [`WEAPON_TOMAHAWK`] = 'WEAPON_TOMAHAWK',
  [`WEAPON_FLAREGUN`] = 'WEAPON_FLAREGUN',
  [`WEAPON_MARKSMANPISTOL`] = 'WEAPON_MARKSMANPISTOL',
  [`WEAPON_RAYPISTOL`] = 'WEAPON_RAYPISTOL',
  [`WEAPON_NAVYREVOLVER`] = 'WEAPON_NAVYREVOLVER',
  [`WEAPON_GADGETPISTOL`] = 'WEAPON_GADGETPISTOL',
  [`WEAPON_MILITARYRIFLE`] = 'WEAPON_MILITARYRIFLE',
  [`WEAPON_HEAVYRIFLE`] = 'WEAPON_HEAVYRIFLE',
  [`WEAPON_TACTICALRIFLE`] = 'WEAPON_TACTICALRIFLE',
  [`WEAPON_BATTLERIFLE`] = 'WEAPON_BATTLERIFLE',
  [`WEAPON_RAYCARBINE`] = 'WEAPON_RAYCARBINE',
  [`WEAPON_PUMPSHOTGUN_MK2`] = 'WEAPON_PUMPSHOTGUN_MK2',
  [`WEAPON_BULLPUPSHOTGUN`] = 'WEAPON_BULLPUPSHOTGUN',
  [`WEAPON_AUTOSHOTGUN`] = 'WEAPON_AUTOSHOTGUN',
  [`WEAPON_COMBATSHOTGUN`] = 'WEAPON_COMBATSHOTGUN',
  [`WEAPON_BULLPUPRIFLE_MK2`] = 'WEAPON_BULLPUPRIFLE_MK2',
  [`WEAPON_RPG`] = 'WEAPON_RPG',
  [`WEAPON_GRENADELAUNCHER`] = 'WEAPON_GRENADELAUNCHER',
  [`WEAPON_GRENADELAUNCHER_SMOKE`] = 'WEAPON_GRENADELAUNCHER_SMOKE',
  [`WEAPON_GRENADE`] = 'WEAPON_GRENADE',
  [`WEAPON_CANDYBOMB`] = 'WEAPON_CANDYBOMB',
  [`WEAPON_MINIGUN`] = 'WEAPON_MINIGUN',
  [`WEAPON_FIREWORK`] = 'WEAPON_FIREWORK',
  [`WEAPON_RAILGUN`] = 'WEAPON_RAILGUN',
  [`WEAPON_HOMINGLAUNCHER`] = 'WEAPON_HOMINGLAUNCHER',
  [`WEAPON_COMPACTLAUNCHER`] = 'WEAPON_COMPACTLAUNCHER',
  [`WEAPON_RAYMINIGUN`] = 'WEAPON_RAYMINIGUN',
  [`WEAPON_HUNTINGRIFLE`] = 'WEAPON_HUNTINGRIFLE',
  [`WEAPON_SPEEDGUN`] = 'WEAPON_SPEEDGUN',
  [`WEAPON_CANDYCANE`] = 'WEAPON_CANDYCANE',
  [`WEAPON_STONE_HATCHET`] = 'WEAPON_STONE_HATCHET',
  [`WEAPON_SNOWBALL`] = 'WEAPON_SNOWBALL',
  [`WEAPON_LESSLAUNCHER`] = 'WEAPON_LESSLAUNCHER',
}

Citizen.CreateThread(function()
  for k, v in pairs(weapon_hashes_signed) do
    weapon_hashes_unsigned[exports.blrp_clothingstore:SignedToUnsigned(k)] = v
  end
end)

function WeaponHashForwardLookup(hash, unsigned)
  local tbl = weapon_hashes_signed

  if unsigned then
    tbl = weapon_hashes_unsigned
  end

  return tbl[tostring(hash)]
end

---


damage_flags = {
  FLAG_UNK_1 = 1,
  FLAG_UNK_2 = 2,
  FLAG_UNK_3 = 4,
  FLAG_UNK_4 = 8,
  FLAG_UNK_5 = 16,
  FLAG_UNK_6 = 32,
  FLAG_UNK_7 = 64,
  FLAG_UNK_8 = 128,
  FLAG_UNK_9 = 256,
  FLAG_UNK_10 = 512,
  FLAG_UNK_11 = 1024,
  FLAG_UNK_12 = 2048,
  FLAG_UNK_13 = 4096,
}

function makeFlags(flag_list, ...)
  local target_flags = 0

  for _, target_flag in pairs({...}) do
    if flag_list[target_flag] then
      target_flags = target_flags | context_flags[target_flag]
    end
  end

  return target_flags
end

function hasAllFlags(flag_list, flags, ...)
  if not flags then
    return false
  end

  for _, target_flag in pairs({...}) do
    if not flag_list[target_flag] or (flags & flag_list[target_flag] == 0) then
      return false
    end
  end

  return true
end

function hasAnyFlags(flag_list, flags, ...)
  if not flags then
    return false
  end

  for _, target_flag in pairs({...}) do
    if flag_list[target_flag] and (flags & flag_list[target_flag] ~= 0) then
      return true
    end
  end

  return false
end

function getFlags(flag_list, flags)
  local flags_array = {}

  for flag, flag_value in pairs(flag_list) do
    if flags & flag_value ~= 0 then
      table.insert(flags_array, tostring(flag))
    end
  end

  return flags_array
end


AddEventHandler('weaponDamageEvent', function(player, data)
  local entity = NetworkGetEntityFromNetworkId(data.hitGlobalId)

  if not entity or entity <= 0 or not DoesEntityExist(entity) then
    return
  end

  if GetEntityType(entity) ~= 1 or not IsPedAPlayer(entity) then
    return
  end

  local character = exports.blrp_core:character(player)
  local character_target = exports.blrp_core:character(NetworkGetEntityOwner(entity))

  local weapon_id = WeaponHashForwardLookup(data.weaponType, true) or 'UNKNOWN WEAPON (' .. data.weaponType .. ')'

  data.damageFlags = {
    bits = data.damageFlags,
    flags = getFlags(damage_flags, data.damageFlags)
  }

  local verb = 'damaged'

  if data.willKill then
    verb = 'killed'
  end

  print(character.get('identifier') .. ' @ ' .. character.getCoordinates() .. ' ' .. verb .. ' ' .. character_target.get('identifier') .. ' @ ' .. character_target.getCoordinates() .. ' with ' .. weapon_id)

  data.attacker = {
    source = player,
    vrp = character.get('identifier'),
    char = character.get('id'),
    coords = character.getCoordinates(),
  }

  data.victim = {
    source = character_target.source,
    vrp = character_target.get('identifier'),
    char = character_target.get('id'),
    coords = character_target.getCoordinates(),
  }

  data.weaponName = weapon_id

  exports.blrp_core:print_r(data)
end)
