local all_scenes = { }

tScenes = { }
T.bindInstance('scenes', tScenes)
pScenes = P.getInstance('blrp_scenes', 'scenes')

tScenes.add = function(scene)
  all_scenes[scene.id] = scene
end

tScenes.remove = function(scene_id)
  all_scenes[scene_id] = nil
end

tScenes.setScenes = function(scenes)
  all_scenes = scenes
end

local current_bucket_name = LocalPlayer.state.bucket_name

AddStateBagChangeHandler('bucket_name', nil, function(bag_name, key, new_bucket_name, reserved, replicated)
  if GetPlayerFromStateBagName(bag_name) ~= PlayerId() then
    return
  end

  current_bucket_name = new_bucket_name
end)

local cached_scenes = {}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(250)

    local player_coords = GetEntityCoords(PlayerPedId())

    local _cached_scenes = { }

    for _, scene_data in pairs(all_scenes) do
      if
        scene_data and
        scene_data.coords and
        scene_data.coords.x and
        scene_data.instance == current_bucket_name and
        player_coords and
        player_coords.x
      then
        if #(player_coords - scene_data.coords) <= scene_data.distance then
          table.insert(_cached_scenes, scene_data)
        end
      end
    end

    cached_scenes = _cached_scenes
  end
end)

----

local hidden = false
local settingScene = false
local coords = {}

colors = {
  ["white"] = {255, 255, 255},
  ["red"] = {255, 0, 0},
  ["blue"] = {0, 0, 255},
  ["green"] = {0, 128, 0},
  ["yellow"] = {255, 255, 0},
  ["purple"] = {128, 0, 128},
  ["orange"] = { 245, 164, 66},
  ["pink"] = { 241, 20, 252 },
  ["cyan"] = { 20, 252, 225 },
  ["lightblue"] = { 20, 167, 252 },
}

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    if not hidden then
      for _, scene_data in pairs(cached_scenes) do
        DrawScene(scene_data.coords, scene_data.message, scene_data.color, scene_data.distance,
                  scene_data.font_size, scene_data.bg_color, scene_data.emote, scene_data.font_style, scene_data.background_type)
      end
    end
  end
end)

exports('AddScene', function()
  if exports.blrp_core:me().hasInterfaceOpen() then
    return
  end

  if settingScene then settingScene = false return end
  local placement = SceneTarget()
  coords = {}
  settingScene = true

  while settingScene do
    Wait(3)
    DisableControlAction(0, 200, true)
    placement = SceneTarget()

    if placement ~= nil then
      DrawMarker(28, placement.x, placement.y, placement.z, 0, 0, 0, 0, 0, 0, 0.15, 0.15, 0.15, 93, 17, 100, 255, false, false)
    end

    if IsControlJustReleased(0, 202) then
      settingScene = false
      return
    end
  end

  if placement[1] == 0.0 or placement == nil or not placement then return end
  coords = placement

  local function GetFontLabels(fontTable)
    local labels = {}
    for i, font in ipairs(fontTable) do
      table.insert(labels, font.label)
    end
    return labels
  end

  local fields = {
    {
      id = 'message',
      txt = 'Message'
    },
    {
      id = 'font_color',
      txt = 'Font Color',
      options = { 'white', 'red', 'blue', 'green', 'yellow', 'purple', 'orange', 'lightblue', 'pink', 'cyan'}
    },
    {
      id = 'font_size',
      txt = 'Font Size',
      options = { '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '30', '40', '50' }
    },
    {
      id = 'font_style',
      txt = 'Font Style',
      options = GetFontLabels(Fonts),
    },
    {
      id = 'bg_color',
      txt = 'Background Color',
      options = { 'default', 'clear', 'white', 'red', 'blue', 'green', 'yellow', 'purple', 'orange', 'lightblue', 'pink', 'cyan'}
    },
    {
      id = 'bg_type',
      txt = 'Background Type',
      options = getAllSpriteLabels(BackgroundSprites),
    },
    {
      id = 'distance',
      txt = 'Distance {1.1 - 10.0}'
    },
  }

  local me = exports.blrp_core:me()

  if me.hasGroup('Misfit Advertising') or me.hasGroup('Omerta Advertising') or me.hasGroup('staff') then
    table.insert(fields, {
      id = 'emote',
      txt = 'Emote',
      options = exports.dpemotes:GetEmoteNames()
    })

    table.insert(fields, {
      id = 'persist',
      txt = 'Persist'
    })
  end

  local scene = exports.blrp_ui:TriggerFormWait({
    header = 'Add Scene',
    fields = fields
  })

  local function GetFontValueByLabel(label)
    for i, font in ipairs(Fonts) do
      if font.label == label then
        return font.value
      end
    end
    return 0 -- Return nil if no match is found
  end

  if not scene then return end
  if scene['message'] == nil then return end
  local message = scene['message']
  local color = scene['font_color']
  local fontSize = scene['font_size']
  local bgColor = scene['bg_color']
  local font_style = GetFontValueByLabel(scene['font_style'])
  local background_type = getSpriteByLabel(BackgroundSprites, scene['bg_type']).value
  local emote = scene['emote']

  local distance = tonumber(scene['distance'])
  if type(distance) ~= 'number' or distance > 10.0 then distance = 10.0 end

  distance = distance + 0.0
  if distance < 1.1 then distance = 1.1 end

  if color == nil or string.lower(color) == nil or colors[string.lower(color)] == nil then color = 'white' end

  color = colors[string.lower(color)]

  if bgColor ~= 'clear' then
    if bgColor == nil or string.lower(bgColor) == nil or colors[string.lower(bgColor)] == nil then
      bgColor = 'default'
    end

    bgColor = colors[string.lower(bgColor)]
  end


  local persist = false

  if scene['persist'] then
    persist = scene['persist']
  end

  font_style = font_style or 0

  local packet = {
    coords = coords,
    message = message,
    color = color,
    distance = distance,
    persistent = persist,
    bg_color = bgColor,
    font_size = fontSize,
    font_style = font_style,
    emote = emote,
    instance = current_bucket_name,
    background_type = background_type,
  }

  pScenes.add({ packet })
end)

exports('RemoveScene', function()
  local closest_scene_id = nil
  local closest_distance = 100000

  local player_coords = GetEntityCoords(PlayerPedId())

  for scene_id, scene_data in pairs(all_scenes) do
    if player_coords
            and scene_data
            and scene_data.coords
            and player_coords.x
            and scene_data.coords.x
            and type(player_coords) ~= 'table'
            and type(scene_data.coords) ~= 'table'
    then
      -- ERROR: attempt to perform arithmetic on a vector3 value (local 'player_coords')
      local distance = #(player_coords - scene_data.coords)

      if distance <= 10 and distance < closest_distance then
        closest_scene_id = scene_data.id
        closest_distance = distance
      end
    end
  end

  if closest_scene_id then
    pScenes.remove({ closest_scene_id })
  end
end)

exports('ToggleScenes', function()
  hidden = not hidden

  local verb = 'enabled'

  if hidden then
    verb = 'disabled'
  end

  exports.blrp_core:me().notify('Scenes ' .. verb)
end)
