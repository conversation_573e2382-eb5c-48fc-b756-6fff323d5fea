local did_recently_notify_about_emote = false

function SceneTarget(distance)
    local Cam = GetGameplayCamCoord()
    local _, Hit, Coords, _, Entity = GetShapeTestResult(StartExpensiveSynchronousShapeTestLosProbe(Cam, GetCoordsFromCam(10.0, Cam), -1, PlayerPedId(), 4))
    return Coords
end

function GetCoordsFromCam(distance, coords)
    local rotation = GetGameplayCamRot()
    local adjustedRotation = vector3((math.pi / 180) * rotation.x, (math.pi / 180) * rotation.y, (math.pi / 180) * rotation.z)
    local direction = vector3(-math.sin(adjustedRotation[3]) * math.abs(math.cos(adjustedRotation[1])), math.cos(adjustedRotation[3]) * math.abs(math.cos(adjustedRotation[1])), math.sin(adjustedRotation[1]))
    return vector3(coords[1] + direction[1] * distance, coords[2] + direction[2] * distance, coords[3] + direction[3] * distance)
end

function CanPlayerSeeScene(sceneCoords)
  local coords = GetEntityCoords(PlayerPedId())
	local _, hit, _, _, _ = GetShapeTestResult(StartShapeTestRay(coords.x, coords.y, coords.z, sceneCoords.x, sceneCoords.y, sceneCoords.z, -1, PlayerPedId(), 0))
	return hit == 1
end

function DrawScene(coords, text, color, distance, font_size, bg_color, emote, font_style, background_type)
    local onScreen, x, y = GetScreenCoordFromWorldCoord(coords.x, coords.y, coords.z)
    local dist = #(GetGameplayCamCoord() - coords)

    local real_font_size = (font_size or 11) * 5

    local scale = ((1 / dist) * 2) * (1 / GetGameplayCamFov()) * real_font_size

    local camCoords = GetFinalRenderedCamCoord()
    local distance = #(coords - camCoords)
    local fov = (1 / GetGameplayCamFov()) * 75
    local background_scale = (1 / distance) * (4) * fov * (((font_size or 11) / 2) or 1)

    local bg_alpha = 90
    local bg_r = 0
    local bg_g = 0
    local bg_b = 0

    if  bg_color == 'clear' then
        bg_alpha = 0
    end

    -- Do nothing
   if type(bg_color) == 'table' then
       bg_r = bg_color[1]
       bg_g = bg_color[2]
       bg_b = bg_color[3]
       bg_alpha = 200
   end

    if onScreen then
      BeginTextCommandDisplayText("STRING")
      AddTextComponentSubstringKeyboardDisplay(text)
      SetTextColour(color[1], color[2], color[3], 255)
      SetTextScale(0.0 * scale, 0.50 * scale)
      SetTextFont(tonumber(font_style))
      SetTextCentre(1)
      SetTextDropshadow(1, 0, 0, 0, 155)
      EndTextCommandDisplayText(x, y)

      local height = GetTextScaleHeight(1 * scale, 0) - 0.005
      local length = string.len(text)
      local limiter = 120
      if length > 98 then
          length = 98
          limiter = 200
      end
      local width = length / limiter * scale
      if background_type and background_type ~= "none" then
        local background = background_type or nil
        local bgX = backgroundX or 0
        local bgY = backgroundY or 0
        local bgWidth = 0.01
        local bgHeight = 0.01
        local bgAlpha = bg_alpha or 255
        local bgColor = bg_color or {255, 255, 255}

        local br, bg, bb = bgColor[1], bgColor[2], bgColor[3]

        if background then
          DrawSprite(
            "scenes",
            background,
            x + bgX * background_scale,
            y + bgY * background_scale,
            bgWidth * background_scale,
            bgHeight * background_scale,
            0,
            br or 255, bg or 255, bb or 255,
            bgAlpha
          )
        end
      else
        DrawRect(x, (y + scale / 50), width, height, bg_r, bg_g, bg_b, bg_alpha)
      end
    end

    if emote then
        doWhenPedInDistanceOfSet(1, coords, function()
            if not did_recently_notify_about_emote then
                did_recently_notify_about_emote = true
                TriggerEvent('vrp:client:notify', 'Press [G] to play emote for scene')
                Citizen.CreateThread(function()
                    Citizen.Wait(60000)
                    did_recently_notify_about_emote = false
                end)
            end

            doWhenPressed(coords, 'G', '', function()
                Citizen.Wait(10)
                TriggerEvent('dpEmotes:doEmote', emote)
            end)
        end)
    end
end

function ClosestScene()
    local closestscene = 1000.0
    local coords = GetEntityCoords(PlayerPedId())
    for i = 1, #scenes do
        local distance = Vdist(scenes[i].coords[1], scenes[i].coords[2], scenes[i].coords[3], coords[1], coords[2], coords[3])
        if (distance < closestscene) then
            closestscene = distance
        end
    end
    return closestscene
end

function ClosestSceneLooking()
    local closestscene = 1000.0
    local scanid = nil
    local coords = SceneTarget()
    for i = 1, #scenes do
        local distance = Vdist(scenes[i].coords[1], scenes[i].coords[2], scenes[i].coords[3], coords[1], coords[2], coords[3])
        if (distance < closestscene and distance < scenes[i].distance) then
            scanid = i
            closestscene = distance
        end
    end
    return scanid
end


function LoadStreamedTextureDict(texturesDict)
    local step = 1000
    local timeout = 5 * 1000
    local currentTime = 0

    RequestStreamedTextureDict(texturesDict, 0)
    while not HasStreamedTextureDictLoaded(texturesDict) do
        RequestStreamedTextureDict(texturesDict, 0)
        Citizen.Wait(step)
        currentTime = currentTime + step
        if (currentTime >= timeout) then return false end
    end
    return true
end

print('loading ytd', LoadStreamedTextureDict('scenes'))

