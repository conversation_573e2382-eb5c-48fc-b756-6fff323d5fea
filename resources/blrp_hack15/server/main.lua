local props = {}

RegisterServerEvent("SK-Minigames:addProps", function(gameName, propList)
    local src = source
    if not props[src] then props[src] = {} end

    for _, propData in ipairs(propList) do
        -- Generate a unique networked object ID
        local netId = math.random(10000, 99999)
        propData.netId = netId

        table.insert(props[src], propData)

        -- Notify all clients to spawn this prop
        TriggerClientEvent("SK-Minigames:spawnProp", -1, src, propData)
    end
end)

RegisterServerEvent("SK-Minigames:ClearProps", function()
    local src = source
    if props[src] then
        TriggerClientEvent("SK-Minigames:deleteProps", -1, props[src])
        props[src] = nil
    end
end)

AddEventHandler("playerDropped", function()
    local src = source
    if props[src] then
        TriggerClientEvent("SK-Minigames:deleteProps", -1, props[src])
        props[src] = nil
    end
end)
