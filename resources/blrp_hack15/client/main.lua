tHack15 = {}
T.bindInstance('main', tHack15)

---@diagnostic disable-next-line: lowercase-global
playerPed = PlayerPedId()
---@diagnostic disable-next-line: lowercase-global
lib.onCache("ped", function(value) playerPed = value end)

---@diagnostic disable-next-line: lowercase-global
plaSrvIn = GetPlayerServerId(PlayerId())

local playerProps = {}

RegisterNetEvent("SK-Minigames:spawnProp", function(owner, propData)
  local model = propData.model
  local bone = propData.bone or 57005 -- Right hand default
  local pos = propData.pos or vec3(0.1, 0.0, 0.0)
  local rot = propData.rot or vec3(0.0, 0.0, 0.0)
  local netId = propData.netId

  RequestModel(model)
  while not HasModelLoaded(model) do Wait(10) end

  local prop = CreateObject(model, 0, 0, 0, true, true, true) -- true for networking
  SetNetworkIdExistsOnAllMachines(NetworkGetNetworkIdFromEntity(prop), true)
  AttachEntityToEntity(prop, GetPlayerPed(GetPlayerFromServerId(owner)), GetPedBoneIndex(GetPlayerPed(GetPlayerFromServerId(owner)), bone), pos.x, pos.y, pos.z, rot.x, rot.y, rot.z, true, true, false, true, 1, true)
  SetModelAsNoLongerNeeded(model)

  playerProps[netId] = prop
end)

RegisterNetEvent("SK-Minigames:deleteProps", function(propList)
  for _, propData in ipairs(propList) do
    local prop = playerProps[propData.netId]
    if prop and DoesEntityExist(prop) then
      DeleteEntity(prop)
      playerProps[propData.netId] = nil
    end
  end
end)


local miniGameStatus = {
  success = false,
  canceled = false,
  isPlaying = false,
}

local miniGamePromise = nil
local miniGameOtherOptions = nil

local function sendAction(app, action, payload)
  SendNUIMessage({
    event = "sendAppEvent",
    app = app,
    action = action,
    payload = payload
  })
end

---@param gameName string -- name of the minigame
---@param options table -- options related to animations, props on player etc.
local function execOtherOptionsOnPlayer(gameName, options)
    if options.animation then
      ExecuteCommand("e c")

      ClearPedTasks(playerPed)

      if not IsPedInAnyVehicle(playerPed, true) then
        ClearPedTasksImmediately(playerPed)
      end
    end

    if options.animation?.e then
      ExecuteCommand(("e %s"):format(options.animation.e))
    elseif options.animation and not options.animation.scenario then
      RequestAnimDict(options.animation.dict)
      while not HasAnimDictLoaded(options.animation.dict) do
        Wait(0)
      end
      TaskPlayAnim(playerPed, options.animation.dict, options.animation.name, 4.0, 4.0, -1, options.animation.flag, 0, false, false, false)
    elseif options.animation and options.animation.scenario then
      TaskStartScenarioInPlace(playerPed, options.animation.scenario, 0, true)
    end

  if options.prop then
    if #options.prop == 0 then options.prop = { options.prop } end
    TriggerServerEvent("SK-Minigames:addProps", gameName, options.prop)
  end
end

---@param gameName string -- name of the minigame
---@param gameOptions table | nil -- options related to minigame itself
---@param otherOptions table | nil -- options related to animations, props on player etc.
local function StartMinigame(gameName, gameOptions, otherOptions)
  if miniGameStatus.isPlaying then
    return false
  end

  TriggerEvent('blrp_inventory:hide')

  miniGameStatus.isPlaying = true
	miniGameStatus.success = false
  miniGameStatus.canceled = false

  miniGameOtherOptions = otherOptions

  if otherOptions then execOtherOptionsOnPlayer(gameName, otherOptions) end

  gameOptions = gameOptions or {}

  if gameOptions and gameOptions.cursorX ~= nil and gameOptions.cursorY ~= nil then
    SetCursorLocation(gameOptions.cursorX, gameOptions.cursorY)
  else
    SetCursorLocation(0.5, 0.5)
  end

  local cursor = true
  if otherOptions and otherOptions.cursor ~= nil then
    cursor = otherOptions.cursor
  end
	SetNuiFocus(true, cursor);

  sendAction(gameName, "setOptions", gameOptions)
  SendNUIMessage({
    event = "setCurrentGame",
    game = gameName,
  })

  if otherOptions and otherOptions.onTick then
    Citizen.CreateThread(function()
      while miniGameStatus.isPlaying do
        otherOptions.onTick()
        Citizen.Wait(0)
      end
    end)
  end

	miniGamePromise = promise:new()

	return Citizen.Await(miniGamePromise)
end

---@param status boolean
local function minigameCleanUp(status)
  if miniGamePromise then
    miniGamePromise:resolve(status)
    miniGamePromise = nil
  end

  if miniGameOtherOptions then
    if miniGameOtherOptions.animation then
      ExecuteCommand("e c")

      ClearPedTasks(playerPed)

      if not IsPedInAnyVehicle(playerPed, true) then
        ClearPedTasksImmediately(playerPed)
      end
    end

    if miniGameOtherOptions.prop then
      TriggerServerEvent("SK-Minigames:ClearProps")
    end
  end
  miniGameOtherOptions = nil
  miniGameStatus.isPlaying = false
end

RegisterNUICallback("setStatus", function(data, cb)
  cb("ok")
  local status = data.status
  SetNuiFocus(false, false)

  miniGameStatus.success = status
  miniGameStatus.canceled = false

  minigameCleanUp(status)
end)

local function stopMinigame()
  SetNuiFocus(false, false)

	if miniGameStatus.isPlaying then
		SendNUIMessage({
			event = "setCurrentGame",
      game = nil,
		})
		miniGameStatus.canceled = true
	end

  minigameCleanUp(false)
end

tHack15.hack = function (gameName, gameOptions, otherOptions)
  return StartMinigame(gameName, gameOptions, otherOptions)
end

exports("Start", StartMinigame)

exports("Stop", stopMinigame)

exports("IsPlaying", function()
  return miniGameStatus.isPlaying
end)

RegisterNUICallback("arrowClicker:finishGame", function(data, cb)
  cb("ok")
  TriggerEvent("arrowClicker:onGameFinish", data.gameNumber)
end)

lib.callback.register("SK-Minigames:StartMinigame", StartMinigame)

RegisterNetEvent("SK-Minigames:stopMinigame", stopMinigame)

AddStateBagChangeHandler("isDead", ("player:%s"):format(plaSrvIn), function(bagName, key, value, reserved, replicated)
  if value then
    stopMinigame()
  end
end)
