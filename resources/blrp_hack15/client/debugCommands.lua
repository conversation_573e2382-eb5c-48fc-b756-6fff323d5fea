if Config.DebugCommands == true then
   RegisterCommand("holematch", function()
      local success =  exports["blrp_hack15"]:Start("holeMatch", {radialSpeed = 0.0025, objCount = 4, time = 30, maxErrors = 1}) -- Radial Speed Ranges from 0.001 - 0.01
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works Fully. 1

   RegisterCommand("aimlab", function()
      local success =  StartMinigame("aimLab", {x = 5, y = 5, time = 5000, generationSpeed = 300, maxActive = 4, text = "SK!"})
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works Fully 2

   RegisterCommand("arrowclicker", function()
      local success =  exports["blrp_hack15"]:Start("arrowClicker", {arrowCount = 10, time = 7000, text = "SK Developments!", gameCount = 1}, {
         animation = {
            dict = "veh@break_in@0h@p_m_one@",
            name = "low_force_entry_ds",
            flag = 16,
        },
        prop = {
            model = "w_me_crowbar",
            bone = 57005,
            pos = vec3(0.1,0.03,0.15),
            rot = vec3(0.0,0.0,0.0)
        }
      }) -- time = ms
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works Fully 3

   RegisterCommand("arrowmaze", function()
      local success = exports["blrp_hack15"]:Start("arrowMaze", {
         time = 15000, -- 15 secs
         canRepeatArrows = true,
         canRepeatBlocks = true,
         hardMode = false,
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works Fully 4

   RegisterCommand("balance", function()
      local success = exports["blrp_hack15"]:Start("balance", {
         forceMultiplier = 1,
         greenZone = 0.5
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 5

   RegisterCommand("brakerpuzzle", function()
      local success = exports["blrp_hack15"]:Start("brakerPuzzle", {
         gameCount = 5,
         switchCount = 8,
         switchTime = 1000,
         readyTime = 2000,
         finishTime = 3000
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 6

   RegisterCommand("buttonmash", function()
      local success = exports["blrp_hack15"]:Start("buttonMash", {
         cpsRequired = 5,
         button = "H"
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 7

   RegisterCommand("cableconnect", function()
      local success = exports["blrp_hack15"]:Start("cableConnect", {
         time = 15000,
         text = "Hey!"
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 8

   RegisterCommand("cablejigsaw", function()
      local success = exports["blrp_hack15"]:Start("cableJigsaw", {
         time = 15000,
         x = 2,
         y = 2,
         inventorySize = 10
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 9

   RegisterCommand("codefind", function()
      local success = exports["blrp_hack15"]:Start("codeFind", {
         signSet = "ho",
         changeInterval = 3000,
         time = 15000,
         amount = 2,
         x = 1,
         y = 2
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 10

   RegisterCommand("colorcount", function()
      local success = exports["blrp_hack15"]:Start("colorCount", {
         time = 15000,
         amount = 1,
         maxCount = 2,
         x = 1,
         y = 2,
         answerTime = 12000
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- works fine 11

   RegisterCommand("fishing", function()
      local success = exports["blrp_hack15"]:Start("fishing", {
         fishCount = 2,
         fishSpeed = 0.2,
         hoverTime = 4000,
         timeMargin = 1000
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 12

   RegisterCommand("flappybird", function()
      local success = exports["blrp_hack15"]:Start("flappyBird", {
         time = 15000,
         text = "Hey",
         speed = 0.2,
         acceleration = 0.1
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 13

   RegisterCommand("jigsawpuzzle", function()
      local success = exports["blrp_hack15"]:Start("jigsawPuzzle", {
         text = "Hey",
         time = 15000,
         presetName = "hand",
         pieceCount = 5,
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works 14

   RegisterCommand("knobturning", function()
      local success = exports["blrp_hack15"]:Start("knobTurning", {
         text = "hey",
         time = 15000
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works 15

   RegisterCommand("lettersfall", function()
      local success = exports["blrp_hack15"]:Start("lettersFall", {
         time = 15000,
         text = "hey",
         speed = 0.2,
         difficulty = 1 -- 1-5 to my knowledge needs testing
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 16

   RegisterCommand("lockpick", function()
      local success = exports["blrp_hack15"]:Start("lockpick", {
         holeCount = 2,
         speed = 7,
         skipCountdown = 0
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 17

   RegisterCommand("minesweeper", function()
      local success = exports["blrp_hack15"]:Start("mineSweeper", {
         x = 2,
         y = 3,
         mineCount = 1
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 18

   RegisterCommand("pairmatch", function()
      local success = exports["blrp_hack15"]:Start("pairMatch", {
         text = "Hey",
         time = 15000,
         x = 2,
         y = 2,
         displayTime = 3000,
         pairCount = 1,
         allowedErrors = 2
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 19

   RegisterCommand("pipedodge", function()
      local success = exports["blrp_hack15"]:Start("pipeDodge", {
         time = 25000
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 20

   RegisterCommand("rythmarrows", function()
      local success = exports["blrp_hack15"]:Start("rythmArrows", {
         arrowCount = 4,
         text = "hey",
         speed = 120,
         threshold = 20
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 21

   RegisterCommand("rythmclick", function()
      local success = exports["blrp_hack15"]:Start("rythmClick", {
         interval = 350,
         targetCount = 10
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 22

   RegisterCommand("shoplockpick", function()
      local success = exports["blrp_hack15"]:Start("shopLockpick", {
         holeCount = 2,
         speed = 1,
         bounce = 0
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 23

   RegisterCommand("signmemory", function()
      local success = exports["blrp_hack15"]:Start("signMemory", {
         questionTime = 3000,
         time = 15000,
         questionCount = 2,
         hardMode = 0,
         imageSet = "standard"
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 24

   RegisterCommand("tempreduce", function()
      local success = exports["blrp_hack15"]:Start("tempReduce", {
         time = 15000,
         spawnRate = 1500,
         winThreshold = 0.3
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 25

   RegisterCommand("traceshape", function()
      local success = exports["blrp_hack15"]:Start("traceShape", {
         time = 15000,
         text = "Hey",
         background = "img/traceBackgrounds/bg1.png",
         difficulty = 0.5
   })
      if success then
      print("Success!")
      else
      print("Failure!")
      end
   end, false) -- Works fine 26
end