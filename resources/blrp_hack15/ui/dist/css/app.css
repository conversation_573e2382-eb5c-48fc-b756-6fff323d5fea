/* Font Imports */
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&display=swap');

@font-face {
  font-family: 'Geist';
  src: url('../fonts/Geist-Regular.woff2');
  font-weight: 400;
}

@font-face {
  font-family: 'Geist';
  src: url('../fonts/Geist-Bold.woff2');
  font-weight: 700;
}

@font-face {
  font-family: 'Geist';
  src: url('../fonts/Geist-Medium.woff2');
  font-weight: 401 699;
}

@font-face {
  font-family: 'Geist';
  src: url('../fonts/Geist-Light.woff2');
  font-weight: 0 399;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  user-select: none;
}

h1, h2, h3, h4, h5, p {
  margin: 0;
}

.error {
  color: red;
}

:root {
  font-size: min(0.83333333vw, 1.48148148vh);
  font-family: Rajdhani, sans-serif;
  transition: font-size 0.15s linear;
}

/* App Container */
#app, .app {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
  perspective: 62.5rem;
}

/* Aim Lab */
.aim-lab[data-v-63a8a385] {
  display: flex;
  padding: 4rem 5.5rem;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  border-radius: 0.5rem;
  background: hsla(0, 0%, 7%, 0.8);
}

.aim-lab h2[data-v-63a8a385] {
  color: #fff;
  font-family: Oswald;
  font-size: 3rem;
  font-weight: 400;
  text-transform: uppercase;
}

.aim-lab .grid[data-v-63a8a385] {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(5, 1fr);
}

.aim-lab .grid .cell[data-v-63a8a385] {
  width: 6.25rem;
  height: 6.25rem;
  border: 1px solid hsla(0, 0%, 100%, 0.15);
  background: hsla(0, 0%, 7%, 0.5);
}

.aim-lab .grid .cell.active[data-v-63a8a385] {
  background: #87da21;
  box-shadow: 0 0 4px rgba(226, 46, 46, 0.5), 0 0 8px rgba(226, 46, 46, 0.25), 0 0 16px rgba(226, 46, 46, 0.15);
}

.aim-lab .timebar-container[data-v-63a8a385] {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  align-self: stretch;
}

.aim-lab .timebar-container h4[data-v-63a8a385] {
  color: hsla(0, 0%, 100%, 0.65);
  text-align: center;
  font-size: 0.875rem;
  font-family: Geist;
  font-weight: 400;
  line-height: 150%;
}

.aim-lab .timebar-container .timebar[data-v-63a8a385] {
  display: flex;
  height: 0.6875rem;
  align-items: center;
  align-self: stretch;
  border-radius: var(--border-radius-rounded-md, 0.375rem);
  background: var(--Blue-Blue-50, rgba(0, 6, 23, 0.5));
}

.aim-lab .timebar-container .timebar .timebar-fill[data-v-63a8a385] {
  border-radius: inherit;
  height: 100%;
  background: #87da21;
  box-shadow: 0 0 18.911px rgba(135, 218, 33, 0.7), 0 0 73.212px rgba(135, 218, 33, 0.5), inset 0 0 7.321px rgba(135, 218, 33, 0.5);
  width: 100%;
  transition: width 0.1s linear;
}

/* Progress Bar */
.progbar-wrapper[data-v-257dd42e] {
  width: 100%;
  height: 0.475rem;
  background: hsla(0, 0%, 100%, 0.15);
  border-radius: 1.5rem;
}

.progbar-wrapper.big[data-v-257dd42e] {
  height: 1.32rem;
}

.progbar-wrapper .progbar[data-v-257dd42e] {
  border-radius: inherit;
  background: var(--Brand, #87da21);
  height: 100%;
  width: var(--progress);
}

.progbar-wrapper .progbar.red[data-v-257dd42e] {
  background: #fe3a3a;
}

/* Arrow Clicker */
.arrow-clicker[data-v-b08a75a8], .arrow-clicker[data-v-5305a514] {
  position: absolute;
  left: 50%;
  bottom: 3rem;
  transform: translate(-50%, -50%) scale(1.25);
  color: #fff;
  font-family: Oswald;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.arrow-clicker[data-v-5305a514] {
  left: 70%;
  top: 50%;
  transform: translate(-50%, -50%) rotateY(-20deg);
  transform-origin: right center;
}

.arrow-clicker p[data-v-b08a75a8], .arrow-clicker p[data-v-5305a514] {
  text-transform: uppercase;
}

.arrow-clicker .arrows[data-v-b08a75a8], .arrow-clicker .arrows[data-v-5305a514] {
  display: flex;
  gap: 0.5625rem;
}

.arrow-clicker .arrows .arrow[data-v-b08a75a8], .arrow-clicker .arrows .arrow[data-v-5305a514] {
  border-radius: 0.5rem;
  border: 1px solid hsla(0, 0%, 100%, 0.05);
  background: rgba(34, 34, 34, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
}

.arrow-clicker .arrows .arrow[data-v-5305a514] {
  position: absolute;
  border: 1px solid hsla(0, 0%, 100%, 0.25);
  background: rgba(0, 0, 0, 0.5);
  transition: background 0.2s ease, border 0.2s ease, box-shadow 0.2s ease, color 0.2s ease;
  left: var(--x);
  top: var(--y);
  width: 3rem;
  height: 3rem;
}

.arrow-clicker .arrows .arrow svg[data-v-b08a75a8] {
  width: 60%;
  height: 60%;
}

.arrow-clicker .arrows .arrow svg[data-v-5305a514] {
  width: 40%;
  height: 40%;
}

.arrow-clicker .arrows .arrow.clicked[data-v-b08a75a8], .arrow-clicker .arrows .arrow.clicked[data-v-5305a514] {
  border: 1px solid #87da21;
  background: linear-gradient(0deg, rgba(135, 218, 33, 0.2), rgba(135, 218, 33, 0.2)), linear-gradient(164deg, hsla(0, 0%, 6%, 0.55) 0.49%, rgba(9, 9, 9, 0.55) 99.34%);
  color: #87da21;
}

.arrow-clicker .arrows .arrow.clicked[data-v-5305a514] {
  box-shadow: 0 0 16px rgba(135, 218, 33, 0.5);
}

.arrow-clicker .arrows .arrow.failed[data-v-b08a75a8], .arrow-clicker .arrows .arrow.failed[data-v-5305a514] {
  border: 1px solid #fe3a3a;
  background: linear-gradient(0deg, rgba(135, 16, 16, 0.2), rgba(135, 16, 16, 0.2)), linear-gradient(164deg, hsla(0, 0%, 6%, 0.55) 0.49%, rgba(9, 9, 9, 0.55) 99.34%);
  color: #fe3a3a;
}

.arrow-clicker .arrows .arrow.failed[data-v-5305a514] {
  box-shadow: 0 0 16px rgba(255, 0, 0, 0.5);
}

.arrow-clicker .arrows .line[data-v-5305a514] {
  height: 0.5rem;
  width: var(--length);
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  border: 1px solid hsla(0, 0%, 100%, 0.15);
  background: rgba(0, 0, 0, 0.35);
  position: absolute;
  left: var(--x);
  top: var(--y);
  transform: translate(-50%, -50%) rotate(var(--angle));
  transform-origin: center center;
  overflow: hidden;
}

.arrow-clicker .arrows .line.failed[data-v-5305a514] {
  background: linear-gradient(0deg, rgba(135, 16, 16, 0.2), rgba(135, 16, 16, 0.2)), linear-gradient(164deg, hsla(0, 0%, 6%, 0.55) 0.49%, rgba(9, 9, 9, 0.55) 99.34%);
}

.arrow-clicker .arrows .line.failed .inner[data-v-5305a514] {
  transition: scale 0.1s linear, background 0.1s ease;
  scale: 1 1;
  background: red;
}

.arrow-clicker .arrows .line .inner[data-v-5305a514] {
  width: 100%;
  height: 100%;
  background: #87da21;
  border-radius: inherit;
  transform-origin: left center;
  scale: var(--value) 1;
}

/* Arrow Maze */
.arrow-maze-wrapper[data-v-df80956e] {
  position: absolute;
  top: 50%;
  left: 70%;
  translate: -50% -50%;
  transform: rotateY(-8deg);
  transform-origin: right center;
  min-width: 45rem;
  min-height: 45rem;
  background: rgba(26, 31, 20, 0.95);
  color: #fff;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  font-size: 0.75rem;
  font-weight: 700;
  font-family: Oswald;
}

.arrow-maze-wrapper .notice[data-v-df80956e] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  font-weight: 400;
}

.arrow-maze-wrapper .game[data-v-df80956e] {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 3.84375rem 2.75rem;
  border-radius: var(--icon-size, 1.5rem);
  background: rgba(18, 21, 14, 0.95);
}

.arrow-maze-wrapper .game h4[data-v-df80956e] {
  font-size: 1.25rem;
  font-weight: 700;
}

.arrow-maze-wrapper .game .grid[data-v-df80956e] {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
}

.arrow-maze-wrapper .game .grid .start[data-v-df80956e] {
  position: absolute;
  width: 0.15rem;
  height: 5rem;
  top: 0;
  left: -0.5rem;
  background: #87da21;
  border-radius: 0.3125rem;
}

.arrow-maze-wrapper .game .grid .end[data-v-df80956e] {
  position: absolute;
  width: 5rem;
  height: 0.15rem;
  bottom: -0.5rem;
  right: 0;
  background: #87da21;
  border-radius: 0.3125rem;
}

.arrow-maze-wrapper .game .grid .row[data-v-df80956e] {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.arrow-maze-wrapper .game .grid .row .cell[data-v-df80956e] {
  width: 5rem;
  height: 5rem;
  display: grid;
  grid-gap: 0.25rem;
  grid-template-columns: 1fr 1fr;
}

.arrow-maze-wrapper .game .grid .row .cell.current .arrow[data-v-df80956e] {
  cursor: pointer;
}

.arrow-maze-wrapper .game .grid .row .cell.current .arrow[data-v-df80956e]:hover {
  background: #517d1b;
  border: 0.05rem solid #87da21;
}

.arrow-maze-wrapper .game .grid .row .cell .arrow[data-v-df80956e] {
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--border-radius-rounded, 0.25rem);
  background: var(--White-White-5, hsla(0, 0%, 100%, 0.05));
  border: 0.05rem solid transparent;
}

.arrow-maze-wrapper .game .grid .row .cell .arrow.clicked[data-v-df80956e] {
  background: #517d1b;
  border: 0.05rem solid #87da21;
}

.arrow-maze-wrapper .game .grid .row .cell .arrow.rotate-1 svg[data-v-df80956e] {
  transform: rotate(90deg);
}

.arrow-maze-wrapper .game .grid .row .cell .arrow.rotate-2 svg[data-v-df80956e] {
  transform: rotate(180deg);
}

.arrow-maze-wrapper .game .grid .row .cell .arrow.rotate-3 svg[data-v-df80956e] {
  transform: rotate(270deg);
}

/* Key */
.key[data-v-678f18cc] {
  padding: 0.75rem;
  border-radius: 0.25rem;
  border: 1px solid hsla(0, 0%, 100%, 0.15);
  background: hsla(0, 0%, 100%, 0.05);
  line-height: 100%;
}

/* Balance */
.balance[data-v-5b403b4c] {
  position: absolute;
  top: 85%;
  left: 50%;
  translate: -50% -50%;
  width: 16rem;
  height: 16rem;
}

.balance canvas[data-v-5b403b4c] {
  width: 100%;
  height: 100%;
}

.balance .inner-key[data-v-5b403b4c] {
  position: absolute;
  top: 50%;
  translate: 0 -50%;
  color: #fff;
  font-family: Oswald;
}

.balance .inner-key.left[data-v-5b403b4c] {
  left: -0.5rem;
}

.balance .inner-key.right[data-v-5b403b4c] {
  right: -0.5rem;
}

.balance .inner-key .key[data-v-5b403b4c] {
  width: 2.25rem;
  height: 2.25rem;
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.651);
  border: 2px solid hsla(0, 0%, 100%, 0.15);
  transition: all 0.2s ease;
}

.balance .inner-key .key.clicked[data-v-5b403b4c] {
  border: 2px solid hsla(0, 0%, 100%, 0.45);
  background: rgba(0, 0, 0, 0.25);
}

/* Braker Puzzle */
.braker-puzzle[data-v-445718a6] {
  padding: 3rem;
  border-radius: var(--icon-size, 1.5rem);
  background: #1d1f25;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.braker-puzzle .top-row[data-v-445718a6] {
  min-width: 18rem;
  display: flex;
  padding: 1rem var(--icon-size, 1.5rem);
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
  border-radius: 0.3125rem;
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  background: linear-gradient(86deg, #111 -4.34%, #232323 71.32%, #141414 93.96%, #161616 96.05%, #282828 187.75%);
  color: #f2e800;
  text-shadow: 0 4px 12px rgba(255, 251, 150, 0.25), 0 4px 24px rgba(242, 232, 0, 0.25);
  font-family: Oswald;
  font-size: 2rem;
  font-weight: 400;
  text-transform: uppercase;
}

.braker-puzzle .top-row p[data-v-445718a6] {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 0;
  line-height: 100%;
}

.braker-puzzle .top-row p .box[data-v-445718a6] {
  width: 2rem;
  height: 2rem;
  border-radius: 0.3125rem;
  border: 1px solid #f2e800;
  transition: all 0.2s ease;
}

.braker-puzzle .top-row p .box.fill[data-v-445718a6] {
  background: #f2e800;
  box-shadow: 0 0 8px rgba(255, 225, 67, 0.25);
}

.braker-puzzle .top-row p.white[data-v-445718a6] {
  color: #fff;
  font-size: 1.25rem;
}

.braker-puzzle .row[data-v-445718a6] {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
}

.braker-puzzle .row .col[data-v-445718a6] {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.braker-puzzle .row .col .switch-wrapper[data-v-445718a6] {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  cursor: pointer;
}

.braker-puzzle .row .col .switch-wrapper .label[data-v-445718a6] {
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #746f6f;
  font-family: Oswald;
  font-size: 2rem;
  font-weight: 400;
  text-transform: uppercase;
  line-height: 100%;
}

.braker-puzzle .row .col .switch-wrapper.on .label[data-v-445718a6] {
  border-radius: 0.3125rem;
  border: 1px solid #ffe143;
  background: linear-gradient(86deg, #111 -4.34%, #232323 71.32%, #141414 93.96%, #161616 96.05%, #282828 187.75%);
  box-shadow: 0 0 8px rgba(255, 225, 67, 0.25);
  color: #f2e800;
  text-shadow: 0 4px 12px rgba(255, 251, 150, 0.25), 0 4px 24px rgba(242, 232, 0, 0.25);
}

.braker-puzzle .row .col .switch-wrapper .switch[data-v-445718a6] {
  width: 4rem;
  height: 1rem;
  border-radius: 0.5rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  background: linear-gradient(86deg, #1c1b1b -4.34%, #0e0e0e 71.32%, #1b1b1b 93.96%, #121212 96.05%, #1f1e1e 187.75%);
  position: relative;
}

.braker-puzzle .row .col .switch-wrapper .switch .img[data-v-445718a6] {
  position: absolute;
  width: 1.5rem;
  height: 1.5rem;
  transition: left 0.2s ease;
  top: 50%;
  translate: -50% -50%;
  left: 0.5rem;
}

.braker-puzzle .row .col .switch-wrapper.on .switch .img[data-v-445718a6] {
  left: calc(100% - 0.5rem);
}

.braker-puzzle .row .col .switch-wrapper .switch .img img[data-v-445718a6] {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.braker-puzzle .progress-wrapper[data-v-445718a6] {
  height: 1.75rem;
  width: 100%;
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  background: linear-gradient(86deg, #111 -4.34%, #232323 71.32%, #141414 93.96%, #161616 96.05%, #282828 187.75%);
}

.braker-puzzle .progress-wrapper .progress[data-v-445718a6] {
  width: 100%;
  height: 100%;
  background: #fff;
  box-shadow: 0 4px 12px hsla(0, 0%, 100%, 0.25);
  scale: var(--progress) 1;
  transform-origin: left center;
}

/* Button Mash */
.button-mash[data-v-45e4c51c] {
  width: 100%;
  height: 100%;
  position: relative;
}

.button-mash .progress[data-v-45e4c51c] {
  position: absolute;
  height: 11.04488rem;
  width: 1.07756rem;
  left: calc(50% + 10rem);
  top: 50%;
  translate: -50% -50%;
  background: rgba(25, 31, 19, 0.85);
  border-radius: 1.61631rem;
}

.button-mash .progress .success-field[data-v-45e4c51c] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 20%;
  border-radius: 1.61631rem 1.61631rem 0.13469rem 0.13469rem;
  border: 1.078px solid var(--Brand, #14e000);
  background: #489e00;
  box-shadow: 0 2.299px 2.299px rgba(72, 158, 0, 0.85);
}

.button-mash .progress .tick[data-v-45e4c51c] {
  transition: bottom 0.3s linear;
  position: absolute;
  left: 0;
  bottom: var(--progress, 0);
  width: 1.549rem;
  height: 0.06738rem;
  background: #fff;
}

.button-mash .progress .tick svg[data-v-45e4c51c] {
  position: absolute;
  top: 50%;
  right: 0;
  translate: 100% -50%;
}

.button-mash .button[data-v-45e4c51c] {
  position: absolute;
  left: 50%;
  bottom: 10%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  width: 5rem;
  height: 5rem;
  border-radius: 50%;
  border: 2px solid var(--Brand, #14e000);
  background: linear-gradient(0deg, rgba(104, 221, 0, 0.05), rgba(104, 221, 0, 0.05)), rgba(0, 0, 0, 0.55);
  box-shadow: inset 0 0 12px #68dd00;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-family: Oswald;
  transition: box-shadow 0.2s ease;
}

.button-mash .button.clicked[data-v-45e4c51c] {
  box-shadow: inset 0 0 16px #68dd00;
}

.button-mash .button .outer-ring[data-v-45e4c51c] {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid var(--Brand, #14e000);
  animation: pulse-45e4c51c 1s;
  opacity: 0;
}

@keyframes pulse-45e4c51c {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(3);
    opacity: 0;
  }
}

/* Pipe Connect */
.pipe_connect_wrapper[data-v-2a99efd2], .pipe_connect_wrapper[data-v-838c768e] {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--border-radius-rounded-lg, 8px);
  background: hsla(0, 0%, 7%, 0.8);
  padding: 4rem;
  gap: 2rem;
  color: #fff;
}

.pipe_connect_wrapper h2[data-v-2a99efd2], .pipe_connect_wrapper h2[data-v-838c768e] {
  font-size: 3rem;
  margin: 0;
  font-weight: 400;
  font-family: Oswald;
}

.pipe_connect_wrapper p[data-v-2a99efd2], .pipe_connect_wrapper p[data-v-838c768e] {
  font-family: Oswald;
}

.pipe_connect_wrapper .grid[data-v-2a99efd2], .pipe_connect_wrapper .grid[data-v-838c768e] {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.pipe_connect_wrapper .grid .row[data-v-2a99efd2], .pipe_connect_wrapper .grid .row[data-v-838c768e] {
  width: 100%;
  border-bottom: 1px solid hsla(0, 0%, 100%, 0.05);
  display: grid;
  grid-template-columns: repeat(8, 1fr);
}

.pipe_connect_wrapper .grid .row[data-v-838c768e] {
  grid-template-columns: repeat(var(--x), 1fr);
}

.pipe_connect_wrapper .grid .row:first-child[data-v-2a99efd2], .pipe_connect_wrapper .grid .row:first-child[data-v-838c768e] {
  border-top: 1px solid hsla(0, 0%, 100%, 0.05);
}

.pipe_connect_wrapper .grid .row .cell[data-v-2a99efd2], .pipe_connect_wrapper .cell[data-v-838c768e] {
  width: 8rem;
  height: 8rem;
  flex-shrink: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-left: 1px solid hsla(0, 0%, 100%, 0.05);
}

.pipe_connect_wrapper .grid .row .cell:last-child[data-v-2a99efd2], .pipe_connect_wrapper .cell:last-child[data-v-838c768e] {
  border-right: 1px solid hsla(0, 0%, 100%, 0.05);
}

.pipe_connect_wrapper .cell.drag[data-v-838c768e] {
  cursor: grab;
}

.pipe_connect_wrapper .cell img[data-v-838c768e] {
  width: 8rem;
  height: 8rem;
}

.pipe_connect_wrapper .inventory[data-v-838c768e] {
  --inventorySize: 0;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 1rem;
  margin-top: 2rem;
  min-width: 32rem;
  height: calc(8rem * var(--height));
}

.cell .pipe-finish[data-v-2a99efd2] {
  width: 100%;
  height: 100%;
}

.cell .pipe_flat[data-v-2a99efd2] {
  width: 5%;
  height: 100%;
  box-shadow: inset 0 16.122px 23.1496px -14.8819px hsla(0, 0%, 100%, 0.5), inset 0 2.8937px 4.54724px -1.65354px #fff, inset 0 -33.8976px 28.1102px -26.4567px rgba(96, 68, 145, 0.3), inset 0 40.5118px 41.3386px -19.8425px rgba(202, 172, 255, 0.3), inset 0 1.65354px 7.44095px rgba(154, 146, 210, 0.3), inset 0 0.413386px 16.5354px rgba(227, 222, 255, 0.2);
  backdrop-filter: blur(41.3386px);
}

.cell .pipe_corner[data-v-2a99efd2] {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cell .pipe_corner .top_part[data-v-2a99efd2] {
  align-self: center;
  width: 5%;
  height: 52.5%;
}

.cell .pipe_corner .side_part[data-v-2a99efd2] {
  margin-top: -5%;
  height: 5%;
  width: 47.6%;
}

.cell .pipe_corner .side_part[data-v-2a99efd2], .cell .pipe_corner .top_part[data-v-2a99efd2] {
  box-shadow: inset 0 16.122px 23.1496px -14.8819px hsla(0, 0%, 100%, 0.5), inset 0 2.8937px 4.54724px -1.65354px #fff, inset 0 -33.8976px 28.1102px -26.4567px rgba(96, 68, 145, 0.3), inset 0 40.5118px 41.3386px -19.8425px rgba(202, 172, 255, 0.3), inset 0 1.65354px 7.44095px rgba(154, 146, 210, 0.3), inset 0 0.413386px 16.5354px rgba(227, 222, 255, 0.2);
  backdrop-filter: blur(41.3386px);
}

.cell .glow[data-v-2a99efd2] {
  background: linear-gradient(0deg, hsla(0, 0%, 100%, 0.8), hsla(0, 0%, 100%, 0.8)), linear-gradient(180deg, #fff, hsla(0, 0%, 100%, 0)) !important;
  box-shadow: 0 0 18.9113px rgba(140, 49, 255, 0.7), 0 0 73.2115px rgba(140, 49, 255, 0.5), inset 0 0 7.32115px rgba(140, 49, 255, 0.5) !important;
}

/* Rotations */
.rotated-1[data-v-2a99efd2], .rotated-1[data-v-838c768e] {
  transform: rotate(90deg);
}

.rotated-2[data-v-2a99efd2], .rotated-2[data-v-838c768e] {
  transform: rotate(180deg);
}

.rotated-3[data-v-2a99efd2], .rotated-3[data-v-838c768e] {
  transform: rotate(270deg);
}

/* Code Find */
.code-find-wrapper[data-v-6c9acd86] {
  position: absolute;
  top: 50%;
  left: 70%;
  translate: -50% -50%;
  transform: rotateY(-8deg);
  transform-origin: right center;
  min-width: 45rem;
  min-height: 45rem;
  background: rgba(26, 31, 20, 0.95);
  color: #fff;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Oswald;
  gap: 2rem;
}

.code-find-wrapper .notice[data-v-6c9acd86] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 2rem;
  font-weight: 400;
}

.code-find-wrapper .game[data-v-6c9acd86] {
  width: 100%;
  flex: 1;
  gap: 1rem;
  display: flex;
  flex-direction: column;
  padding: 2rem 1.5rem;
  border-radius: var(--icon-size, 1.5rem);
  background: rgba(18, 21, 14, 0.95);
}

.code-find-wrapper .game .text[data-v-6c9acd86] {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 2rem;
}

.code-find-wrapper .game .text .sm[data-v-6c9acd86] {
  font-size: 1rem;
}

.code-find-wrapper .game .grid[data-v-6c9acd86] {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(var(--x), 3.5rem);
  gap: 0.75rem;
  justify-content: center;
  align-content: center;
  padding: 2rem;
}

.code-find-wrapper .game .cell[data-v-6c9acd86] {
  width: 3.5rem;
  height: fit-content;
  display: flex;
  justify-content: center;
  align-items: center;
  place-self: center;
  padding: 0.5rem 1rem;
  font-weight: 400;
  cursor: pointer;
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  background: var(--White-White-5, hsla(0, 0%, 100%, 0.05));
  border: 1px solid transparent;
}

.code-find-wrapper .game .cell[data-v-6c9acd86]:hover {
  background: #517d1b;
  border: 0.05rem solid #87da21;
}

.code-find-wrapper .game .grid-guess[data-v-6c9acd86] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
}

/* Color Count */
.color-count-wrapper[data-v-ce6e4450] {
  position: absolute;
  top: 50%;
  left: 70%;
  translate: -50% -50%;
  transform: rotateY(-8deg);
  transform-origin: right center;
  min-width: 45rem;
  min-height: 45rem;
  background: rgba(26, 31, 20, 0.95);
  color: #fff;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Oswald;
}

.color-count-wrapper .notice[data-v-ce6e4450], .color-count-wrapper h2[data-v-ce6e4450] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 400;
}

.color-count-wrapper .game[data-v-ce6e4450] {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 2rem;
  gap: 1rem;
}

.color-count-wrapper .game .input-wrapper[data-v-ce6e4450] {
  width: 100%;
}

.color-count-wrapper .game .input-wrapper input[data-v-ce6e4450] {
  width: 100%;
  margin: 0 auto;
  padding: 2rem 3.4375rem;
  border-radius: var(--icon-size, 1.5rem);
  background: rgba(18, 21, 14, 0.95);
  font-family: Oswald;
  font-size: 2rem;
  border: none;
  color: #fff;
  text-align: center;
}

.color-count-wrapper .game .input-wrapper input[data-v-ce6e4450]:focus {
  outline: none;
}

.color-count-wrapper .game .grid[data-v-ce6e4450] {
  display: grid;
  grid-template-columns: repeat(var(--x), 2.5rem);
  grid-gap: 0.5rem;
  padding: 3.5rem;
  border-radius: var(--icon-size, 1.5rem);
  background: rgba(18, 21, 14, 0.95);
}

.color-count-wrapper .game .grid .cell[data-v-ce6e4450] {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--border-radius-rounded, 0.25rem);
  border: 1px solid transparent;
  background: var(--White-White-5, hsla(0, 0%, 100%, 0.05));
}

.color-count-wrapper .game .grid .cell.w[data-v-ce6e4450] {
  border: 1px solid #ff0;
  background: linear-gradient(0deg, rgba(255, 255, 0, 0.2), rgba(255, 255, 0, 0.2)), var(--White-White-5, hsla(0, 0%, 100%, 0.05));
}

.color-count-wrapper .game .grid .cell.g[data-v-ce6e4450] {
  border: 1px solid #228b22;
  background: linear-gradient(0deg, rgba(34, 139, 34, 0.2), rgba(34, 139, 34, 0.2)), var(--White-White-5, hsla(0, 0%, 100%, 0.05));
}

.color-count-wrapper .game .grid .cell.r[data-v-ce6e4450] {
  border: 1px solid skyblue;
  background: linear-gradient(0deg, rgba(135, 206, 235, 0.2), rgba(135, 206, 235, 0.2)), var(--White-White-5, hsla(0, 0%, 100%, 0.05));
}

/* Fishing */
.fishing[data-v-5c5500d7] {
  position: absolute;
  left: 70%;
  top: 50%;
  color: #fff;
  font-family: Oswald;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  translate: -50% -50%;
  transform: rotateY(-20deg);
  transform-origin: right center;
  width: 15rem;
  height: 15rem;
  border-radius: 50%;
  isolation: isolate;
}

.fishing .background[data-v-5c5500d7] {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  overflow: hidden;
  z-index: -1;
}

.fishing .background img[data-v-5c5500d7] {
  position: relative;
  width: 120%;
  height: 120%;
  object-fit: cover;
  animation: anim-bg-5c5500d7 5s ease-in-out infinite;
  translate: -50% -50%;
}

.fishing canvas[data-v-5c5500d7] {
  position: absolute;
  top: calc(-1.75rem - 1px);
  left: calc(-1.75rem - 1px);
  width: calc(100% + 3.5rem + 2px);
  height: calc(100% + 3.5rem + 2px);
  z-index: -2;
}

.fishing .fishes[data-v-5c5500d7] {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.fishing .fishes .fish[data-v-5c5500d7] {
  width: 2.5rem;
  height: 1.875rem;
  position: absolute;
  left: 0;
  top: 0;
  transition: rotate 0.5s ease;
  translate: -50% -50%;
  rotate: y 0deg;
  animation: bob-5c5500d7 2s ease-in-out infinite;
}

.fishing .fishes .fish img[data-v-5c5500d7] {
  position: relative;
  top: 50%;
  left: 50%;
  translate: -50% -50%;
}

.fishing .fishes .fish .circ[data-v-5c5500d7] {
  position: absolute;
  top: 50%;
  left: 50%;
  translate: -50% -50%;
}

@keyframes bob-5c5500d7 {
  0% {
    translate: -50% calc(-50% - 0.15rem);
  }
  50% {
    translate: -50% calc(-50% + 0.15rem);
  }
  100% {
    translate: -50% calc(-50% - 0.15rem);
  }
}

@keyframes anim-bg-5c5500d7 {
  0% {
    top: 50%;
    left: 50%;
  }
  25% {
    top: 55%;
    left: 50%;
  }
  75% {
    top: 45%;
    left: 55%;
  }
  100% {
    top: 50%;
    left: 50%;
  }
}

/* Flappy Bird */
.flappy-bird[data-v-613520ac], .flappy-bird[data-v-88cdacf2] {
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  background: hsla(0, 0%, 7%, 0.8);
  display: flex;
  padding: 4rem 5.5rem;
  flex-direction: column;
  align-items: center;
  gap: 3rem;
  color: #fff;
}

.flappy-bird h2[data-v-613520ac], .flappy-bird h2[data-v-88cdacf2] {
  font-size: 3rem;
  margin: 0;
  font-weight: 400;
  font-family: Oswald;
  max-width: 30rem;
  text-align: center;
}

.flappy-bird p[data-v-88cdacf2] {
  margin: -1rem 0;
  font-family: Oswald;
}

.flappy-bird canvas[data-v-613520ac], .flappy-bird canvas[data-v-88cdacf2] {
  width: 34.9375rem;
  height: 32rem;
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  border: 1px solid hsla(0, 0%, 100%, 0.15);
  background: linear-gradient(105deg, transparent 2.09%, rgba(135, 218, 33, 0.2) 101.32%), #0b0b0b;
}

.flappy-bird .instructions[data-v-613520ac] {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-family: Oswald;
}

.flappy-bird input[data-v-88cdacf2] {
  background: hsla(0, 0%, 7%, 0.8);
  display: flex;
  padding: 1.5rem 2.9375rem;
  align-items: center;
  gap: 0.625rem;
  align-self: stretch;
  color: #fff;
  font-family: Geist;
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  border: 1px solid hsla(0, 0%, 100%, 0.15);
}

.flappy-bird input[data-v-88cdacf2]:focus-within {
  outline: none;
}

/* Game */
.game[data-v-45ac9f1a] {
  position: absolute;
  bottom: 2.5rem;
  left: 50%;
  translate: -50% 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.25rem;
  color: #fff;
  font-family: Oswald;
}

.game .label[data-v-45ac9f1a] {
  padding: 0.25rem var(--gap-gap-3, 0.75rem);
  border-radius: var(--border-radius-rounded, 0.25rem);
  background: rgba(26, 31, 20, 0.75);
}

.game canvas[data-v-45ac9f1a] {
  width: 12rem;
  height: 12rem;
}

/* Jigsaw Puzzle */
.jigsaw-puzzle[data-v-538cf754] {
  display: flex;
  padding: 4rem;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
  max-width: 30rem;
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  background: hsla(0, 0%, 8%, 0.8);
  color: #fff;
}

.jigsaw-puzzle h2[data-v-538cf754] {
  text-align: center;
  font-size: 2.5rem;
  margin: 0;
  font-weight: 400;
  font-family: Oswald;
}

.jigsaw-puzzle p[data-v-538cf754] {
  font-family: Oswald;
  margin: -2rem 0;
}

.jigsaw-puzzle .cols[data-v-538cf754] {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 1.5rem;
}

.jigsaw-puzzle .elements[data-v-538cf754] {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  min-width: 6rem;
}

.jigsaw-puzzle .elements .item[data-v-538cf754] {
  position: unset;
}

.jigsaw-puzzle .item[data-v-538cf754] {
  position: absolute;
  user-select: none;
}

.jigsaw-puzzle .item img[data-v-538cf754] {
  width: 100%;
  height: 100%;
  pointer-events: none;
  filter: brightness(2);
}

.jigsaw-puzzle .content[data-v-538cf754] {
  position: relative;
  isolation: isolate;
  display: flex;
  justify-content: center;
  align-items: center;
}

.jigsaw-puzzle .content .xray[data-v-538cf754] {
  position: absolute;
  left: -1rem;
  height: 0.1875rem;
  top: 0;
  background: var(--Brand, #87da21);
  box-shadow: 0 0 18.911px rgba(135, 218, 33, 0.7), 0 0 73.212px rgba(135, 218, 33, 0.5), inset 0 0 7.321px rgba(135, 218, 33, 0.5);
}

.jigsaw-puzzle .content .item[data-v-538cf754] {
  transition: filter 0.5s ease, opacity 0.5s ease;
}

.jigsaw-puzzle .content .background[data-v-538cf754] {
  position: absolute;
  z-index: -1;
  pointer-events: none;
  top: 0;
  left: 0;
}

.jigsaw-puzzle .content .background img[data-v-538cf754] {
  margin: 0 auto;
  height: 100%;
}

.jigsaw-puzzle .shouldGlow[data-v-538cf754] {
  opacity: 1 !important;
  overflow: hidden;
  filter: opacity(0.5) drop-shadow(0 0 0 #87DA21);
}

.fade-enter-active[data-v-538cf754], .fade-leave-active[data-v-538cf754] {
  transition: opacity 0.5s ease;
}

.fade-enter-from[data-v-538cf754], .fade-leave-to[data-v-538cf754] {
  opacity: 0;
}

/* Knob */
.knob[data-v-30838d60] {
  position: relative;
}

.knob canvas[data-v-30838d60] {
  width: 13.83494rem;
  height: 13.83494rem;
}

.knob .image[data-v-30838d60] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.knob .image img[data-v-30838d60] {
  width: 100%;
}

/* Knob Turning */
.knob-turning[data-v-09a6d85a] {
  display: flex;
  padding: 4rem;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  background: hsla(0, 0%, 8%, 0.8);
  color: #fff;
}

.knob-turning .data[data-v-09a6d85a] {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.knob-turning h2[data-v-09a6d85a] {
  font-size: 3rem;
  margin: 0;
  font-weight: 4

00;
  font-family: Oswald;
  max-width: 30rem;
  text-align: center;
}

.knob-turning p[data-v-09a6d85a] {
  font-family: Oswald;
  margin: -2rem 0;
}

.knob-turning .score[data-v-09a6d85a] {
  padding: 2.4375rem 2.75rem;
  min-width: 13.3125rem;
  text-align: center;
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  border: 1px solid hsla(0, 0%, 100%, 0.15);
  background: linear-gradient(105deg, transparent 2.09%, rgba(135, 218, 33, 0.2) 101.32%), #0b0b0b;
  font-family: Oswald;
  font-size: 4rem;
  font-weight: 400;
  line-height: 100%;
}

.knob-turning .knobs[data-v-09a6d85a] {
  display: flex;
  gap: 7.6125rem;
}

/* Lockpick */
.lockpick[data-v-a20cea8a] {
  position: absolute;
  bottom: 5rem;
  left: 50%;
  translate: -50% 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.25rem;
  color: #fff;
  font-family: Oswald;
}

.lockpick .label[data-v-a20cea8a] {
  padding: 0.25rem var(--gap-gap-3, 0.75rem);
  border-radius: var(--border-radius-rounded, 0.25rem);
  background: rgba(26, 31, 20, 0.75);
}

.lockpick .bar[data-v-a20cea8a] {
  border-radius: var(--border-radius-rounded, 0.25rem);
  background: rgba(26, 31, 20, 0.65);
  border: 1px solid hsla(0, 0%, 100%, 0.15);
  width: calc(10rem + var(--holeCount) * 2.55rem);
  height: 1rem;
  position: relative;
}

.lockpick .bar .bg[data-v-a20cea8a] {
  width: 100%;
  height: 100%;
  background: #5f9c13;
  scale: var(--progress) 1;
  transform-origin: left center;
}

.lockpick .bar .block[data-v-a20cea8a] {
  border-radius: var(--border-radius-rounded, 0.25rem);
  position: absolute;
  width: 1.75rem;
  height: 1.5rem;
  top: 50%;
  translate: -50% -50%;
  border: 1px solid hsla(0, 0%, 100%, 0.15);
  background: #0d0f0a;
  transition: background 0.1s ease, border 0.1s ease;
}

.lockpick .bar .block.active[data-v-a20cea8a] {
  border: 1px solid var(--Brand, #87da21);
  background: #325507;
}

.lockpick .bar .block.fail[data-v-a20cea8a] {
  border: 1px solid var(--Red-Red, red);
  background: #710303;
}

.lockpick .bar .pick[data-v-a20cea8a] {
  position: absolute;
  left: 0;
  top: 50%;
  height: 1rem;
  width: 1rem;
  translate: -50% -50%;
  border-radius: var(--border-radius-rounded, 0.25rem);
  border: 1px solid var(--Brand, #87da21);
  background: #5f9c13;
  transition: width var(--timeout) ease, height var(--timeout) ease, background var(--timeout) ease, border var(--timeout) ease;
}

.lockpick .bar .pick.fillingHole[data-v-a20cea8a] {
  width: 1.75rem;
  height: 1.5rem;
  border: 1px solid var(--Brand, #87da21);
  background: #325507;
}

/* Mine Sweeper */
.mine-sweeper[data-v-15ccece0] {
  position: absolute;
  top: 50%;
  left: 70%;
  translate: -50% -50%;
  transform: rotateY(-8deg);
  transform-origin: right center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--border-radius-rounded-lg, 8px);
  background: rgba(26, 31, 20, 0.95);
  padding: 4rem;
  gap: 2rem;
  color: #fff;
  font-family: Oswald;
}

.mine-sweeper h2[data-v-15ccece0] {
  font-size: 2rem;
  font-weight: 400;
}

.mine-sweeper .grid[data-v-15ccece0] {
  display: grid;
  grid-template-columns: repeat(var(--x), 1fr);
  border: 1px solid hsla(0, 0%, 100%, 0.1);
}

.mine-sweeper .grid .cell[data-v-15ccece0] {
  width: 3rem;
  height: 3rem;
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  background: rgba(0, 0, 0, 0.2);
  font-size: 1.125rem;
  display: flex;
  justify-content: center;
  align-items: center;
  line-height: 100%;
}

.mine-sweeper .grid .cell:not(.revealed)[data-v-15ccece0] {
  cursor: pointer;
}

.mine-sweeper .grid .cell.revealed[data-v-15ccece0] {
  background: transparent;
}

.mine-sweeper .grid .cell.revealed:not(.hasMines)[data-v-15ccece0] {
  border-color: transparent;
}

.mine-sweeper .grid .cell.revealed.c-1[data-v-15ccece0] {
  background: #4b652d;
}

.mine-sweeper .grid .cell.revealed.c-2[data-v-15ccece0] {
  background: #102901;
}

.mine-sweeper .grid .cell.revealed.c-3[data-v-15ccece0] {
  background: #232801;
}

.mine-sweeper .grid .cell.revealed.c-4[data-v-15ccece0] {
  background: #280f01;
}

.mine-sweeper .grid .cell.revealed.c-5[data-v-15ccece0] {
  background: #102901;
}

.mine-sweeper .grid .cell.revealed.c-6[data-v-15ccece0] {
  background: #280116;
}

.mine-sweeper .grid .cell.revealed.c-7[data-v-15ccece0] {
  background: #012807;
}

.mine-sweeper .grid .cell.revealed.c-8[data-v-15ccece0] {
  background: #1e0128;
}

.mine-sweeper .grid .cell.revealed.mine[data-v-15ccece0] {
  background: #310000;
}

.mine-sweeper .grid .cell.flagged[data-v-15ccece0] {
  border: 1px solid hsla(0, 0%, 100%, 0.25);
  background: #303030;
}

/* Grid Cell */
.grid-cell[data-v-43ff24a5] {
  width: 3.75rem;
  height: 3.75rem;
}

.grid-cell img[data-v-43ff24a5] {
  width: 100%;
  height: 100%;
}

.rotate-enter-active[data-v-43ff24a5], .rotate-leave-active[data-v-43ff24a5] {
  transition: transform 0.15s ease-in-out;
}

.rotate-enter-from[data-v-43ff24a5] {
  transform: rotateY(90deg);
}

.rotate-leave-to[data-v-43ff24a5] {
  transform: rotateY(-90deg);
}

/* Pair Match */
.pair-match[data-v-0e2b3913] {
  position: absolute;
  top: 50%;
  left: 70%;
  translate: -50% -50%;
  display: flex;
  padding: 4rem;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
  transform: rotateY(-8deg);
  transform-origin: right center;
  border-radius: var(--border-radius-rounded-lg, 0.5rem);
  background: rgba(26, 31, 20, 0.95);
  color: #fff;
  font-family: Oswald;
  min-width: 35rem;
}

.pair-match .data[data-v-0e2b3913] {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
  font-size: 2rem;
}

.pair-match .data .dark[data-v-0e2b3913] {
  color: hsla(0, 0%, 100%, 0.5);
}

.pair-match .data .dark .green[data-v-0e2b3913] {
  color: #87da21;
}

.pair-match .grid[data-v-0e2b3913] {
  display: grid;
  grid-template-columns: repeat(var(--x), 1fr);
  perspective: 200rem;
}

/* Pipe Dodge */
.pipe-dodge-wrapper[data-v-b7817bce] {
  position: absolute;
  top: 50%;
  left: 70%;
  translate: -50% -50%;
  transform: rotateY(-8deg);
  transform-origin: right center;
  min-width: 45rem;
  min-height: 45rem;
  background: rgba(26, 31, 20, 0.95);
  color: #fff;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Oswald;
  gap: 2rem;
}

.pipe-dodge-wrapper .notice[data-v-b7817bce] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 400;
}

.pipe-dodge-wrapper .game[data-v-b7817bce] {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 2rem;
  gap: 1rem;
}

.pipe-dodge-wrapper .game h2[data-v-b7817bce] {
  font-size: 1.5rem;
  font-weight: 400;
}

.pipe-dodge-wrapper .game canvas[data-v-b7817bce] {
  width: 100%;
  flex: 1;
  border-radius: var(--icon-size, 1.5rem);
  background: rgba(18, 21, 14, 0.95);
}

/* SVG Arrows */
svg[data-v-f7d29376] {
  scale: 0.75;
}

.ArrowRight[data-v-f7d29376] {
  transform: none;
}

.ArrowUp[data-v-f7d29376] {
  transform: rotate(270deg);
}

.ArrowLeft[data-v-f7d29376] {
  transform: rotate(180deg);
}

.ArrowDown[data-v-f7d29376] {
  transform: rotate(90deg);
}

/* Rhythm Click */
.rythm-click-wrapper[data-v-49124304] {
  min-width: 100%;
  min-height: 100%;
  color: #fff;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Oswald;
  gap: 1rem;
}

.rythm-click-wrapper .notice[data-v-49124304], .rythm-click-wrapper h2[data-v-49124304] {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 400;
}

.rythm-click-wrapper .notice[data-v-49124304] {
  flex: 1;
}

.rythm-click-wrapper .game[data-v-49124304] {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 3rem;
}

.rythm-click-wrapper .game canvas[data-v-49124304] {
  flex: 1;
  border-radius: var(--icon-size, 1.5rem);
  cursor: none;
}

/* Shop Lockpick */
.shop-lockpick[data-v-3f7eb9d4] {
  position: absolute;
  top: 80%;
  left: 50%;
  translate: -50% -50%;
  width: 16rem;
  height: 16rem;
}

.shop-lockpick canvas[data-v-3f7eb9d4] {
  width: 100%;
  height: 100%;
}

/* Color Definitions */
.color-0[data-v-2a07ef25] {
  --color: red;
  --filter: brightness(0) invert(12%) sepia(91%) saturate(5580%) hue-rotate(358deg) brightness(110%) contrast(116%);
}

.color-1[data-v-2a07ef25] {
  --color: #87ceeb;
  --filter: brightness(0) invert(81%) sepia(11%) saturate(1580%) hue-rotate(165deg) brightness(97%) contrast(89%);
}

.color-2[data-v-2a07ef25] {
  --color: #87da21;
  --filter: brightness(0) invert(76%) sepia(98%) saturate(408%) hue-rotate(29deg) brightness(88%) contrast(94%);
}

.color-3[data-v-2a07ef25] {
  --color: #ff0;
  --filter: brightness(0) invert(94%) sepia(36%) saturate(2862%) hue-rotate(1deg) brightness(101%) contrast(104%);
}

.color-4[data-v-2a07ef25] {
  --color: purple;
  --filter: brightness(0) invert(9%) sepia(89%) saturate(7406%) hue-rotate(297deg) brightness(69%) contrast(112%);
}

.color-5[data-v-2a07ef25] {
  --color: #ff69b4;
  --filter: brightness(0) invert(69%) sepia(64%) saturate(3713%) hue-rotate(295deg) brightness(101%) contrast(101%);
}

/* Sign Memory */
.sign-memory-wrapper[data-v-2a07ef25] {
  position: absolute;
  top: 50%;
  left: 70%;
  translate: -50% -50%;
  transform: rotateY(-8deg);
  transform-origin: right center;
  min-width: 45rem;
  min-height: 45rem;
  background: rgba(26, 31, 20, 0.95);
  color: #fff;
  padding: 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Oswald;
  gap: 2rem;
}

.sign-memory-wrapper .notice[data-v-2a07ef25] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
}

.sign-memory-wrapper .game[data-v-2a07ef25] {
  flex: 1;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.sign-memory-wrapper .game h4[data-v-2a07ef25] {
  font-size: 1.5rem;
  font-weight: 400;
}

.sign-memory-wrapper .game .grid[data-v-2a07ef25] {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(3, 9rem);
  justify-content: center;
  align-content: center;
  grid-gap: 4rem 2rem;
  padding: 2rem 5rem;
  border-radius: var(--icon-size, 1.5rem);
  background: rgba(18, 21, 14, 0.95);
}

.sign-memory-wrapper .game .grid.single[data-v-2a07ef25] {
  flex: 0;
  grid-template-columns: 9rem;
}

.sign-memory-wrapper .game .grid .cell[data-v-2a07ef25] {
  width: 9rem;
  height: 9rem;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 2.5rem;
  isolation: isolate;
  border-radius: 0.25rem;
}

.sign-memory-wrapper .game .grid .cell[data-v-2a07ef25]:before {
  position: absolute;
  z-index: -1;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color);
  opacity: 0.25;
  border-radius: inherit;
}

.sign-memory-wrapper .game .grid .cell.click[data-v-2a07ef25] {
  cursor: pointer;
  transition: filter 0.1s ease;
}

.sign-memory-wrapper .game .grid .cell.click[data-v-2a07ef25]:hover {
  filter: brightness(0.9);
}

.sign-memory-wrapper .game .grid .cell p[data-v-2a07ef25] {
  font-size: 3rem;
}

.sign-memory-wrapper .game .grid .cell .box[data-v-2a07ef25] {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 2rem;
  height: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 0.05rem solid var(--color);
  font-size: 1.375rem;
  line-height: 2rem;
  isolation: isolate;
  border-radius: 0.25rem;
}

.sign-memory-wrapper .game .grid .cell .box[data-v-2a07ef25]:before {
  position: absolute;
  z-index: -1;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--color);
  opacity: 0.25;
  border-radius: inherit;
}

.sign-memory-wrapper .game .grid .cell img[data-v-2a07ef25] {
  width: 100%;
  height: 100%;
  filter: var(--filter);
}

.sign-memory-wrapper .game .grid .cell.color[data-v-2a07ef25] {
  padding: 0;
}

.sign-memory-wrapper .game .grid .cell.color .color-box[data-v-2a07ef25] {
  width: 100%;
  height: 100%;
  background: var(--color);
  opacity: 0.25;
}

/* Wrapper */
.wrapper[data-v-53a0b398], .wrapper[data-v-06d6c97f] {
  position: absolute;
  bottom: 2.5rem;
  left: 50%;
  translate: -50% 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.25rem;
  color: #fff;
  font-family: Oswald;
}

.wrapper .label[data-v-53a0b398], .wrapper .label[data-v-06d6c97f] {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  background: rgba(26, 31, 20, 0.75);
}

/* Blocks */
.blocks[data-v-53a0b398], .blocks[data-v-06d6c97f] {
  display: grid;
  grid-template-columns: repeat(var(--cols), 1fr);
  grid-gap: 0.75rem;
}

.blocks .block[data-v-53a0b398], .blocks .block[data-v-06d6c97f] {
  background: var(--color, #2e2e2e);
  width: 4rem;
  height: 4rem;
  position: relative;
  border-radius: 0.25rem;
  border: 2px solid var(--flash, transparent);
}

.blocks .block[data-v-06d6c97f] {
  cursor: pointer;
}

.blocks .block:before {
  content: "";
  padding-bottom: 100%;
}

.blocks .block:after {
  content: var(--content);
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  color: #fff;
  background: radial-gradient(circle, rgba(0, 0, 0, 0.1) 0, transparent 50%, transparent 100%);
}

.blocks .block.color-3:after {
  background: radial-gradient(circle, rgba(0, 0, 0, 0.2) 0, transparent 50%, transparent 100%);
}

.blocks .block.flash[data-v-53a0b398], .blocks .block.flash-user[data-v-53a0b398] {
  background: var(--flash);
  border: 2px solid var(--flash);
}

.blocks .block.flash[data-v-06d6c97f] {
  background: var(--flash);
  box-shadow: var(--box);
}

.blocks .block.success[data-v-06d6c97f] {
  --color: #87da21 !important;
  --flash: #87da21 !important;
  --box: 0 4px 4px 0 rgba(135, 218, 33, 0.5) !important;
  box-shadow: var(--box);
}

/* Color Definitions */
.color-0[data-v-53a0b398], .color-0[data-v-06d6c97f] {
  --flash: red;
  --color: #4d0e0e;
  --box: 0 4px 4px 0 rgba(255, 0, 0, 0.5);
  --content: "1";
}

.color-1[data-v-53a0b398], .color-1[data-v-06d6c97f] {
  --flash: #0084ff;
  --color: #072038;
  --box: 0 4px 4px 0 rgba(34, 135, 229, 0.5);
  --content: "2";
}

.color-2[data-v-53a0b398], .color-2[data-v-06d6c97f] {
  --flash: #ff6c3d;
  --color: #411000;
  --box: 0 4px 4px 0 rgba(255, 108, 61, 0.5);
  --content: "3";
}

.color-3[data-v-53a0b398], .color-3[data-v-06d6c97f] {
  --flash: #ffe602;
  --color: #302b01;
  --box: 0 4px 4px 0 rgba(255, 230, 2, 0.5);
  --content: "4";
}

/* Temp Reduce Wrapper */
.temp-reduce-wrapper[data-v-e103409e] {
  position: absolute;
  top: 50%;
  left: 50%;
  translate: -50% -50%;
  background: rgba(18, 26, 32, 0.95);
  color: #fff;
  padding: 5rem 3rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  border-radius: 1rem;
  font-family: Oswald;
}

.temp-reduce-wrapper .bg[data-v-e103409e] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: inherit;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.temp-reduce-wrapper .bg .bottom[data-v-e103409e] {
  margin-top: auto;
  rotate: 180deg;
}

.temp-reduce-wrapper .notice[data-v-e103409e] {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 400;
  width: 30rem;
  min-height: 30rem;
}

.temp-reduce-wrapper .game[data-v-e103409e] {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
}

.temp-reduce-wrapper .game h2[data-v-e103409e] {
  font-size: 1.5rem;
  font-weight: 400;
}

.temp-reduce-wrapper .game canvas[data-v-e103409e] {
  flex: 1;
  border-radius: 1.5rem;
  background: rgba(18, 21, 14, 0.95);
  width: 30rem;
  min-height: 30rem;
  cursor: none;
}

/* Temp Progress */
.temp-progress-wrapper[data-v-e103409e] {
  width: 100%;
  height: 1.32rem;
  background: hsla(0, 0%, 100%, 0.15);
  border-radius: 1.5rem;
}

.temp-progress-wrapper .temp-progress[data-v-e103409e] {
  height: 100%;
  width: var(--progress);
  border-radius: inherit;
  background: #cce7f9;
  box-shadow: 0 4px 24px 0 rgba(204, 231, 249, 0.25);
  transition: box-shadow 0.2s ease, background 0.2s ease, width 0.35s ease;
}

.temp-progress-wrapper .temp-progress.hot[data-v-e103409e] {
  background: #ec3d00;
  box-shadow: 0 4px 24px 0 rgba(236, 60, 0, 0.25);
}

/* Scalpel */
.scalpel[data-v-3e2e1838] {
  position: fixed;
  top: 0;
  left: 0;
  translate: -7% -78%;
  transition: translate 0.2s ease;
  width: 10rem;
  pointer-events: none;
}

.scalpel img[data-v-3e2e1838] {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.scalpel.clicked[data-v-3e2e1838] {
  translate: -10% -74%;
}

/* Trace Shape */
.trace-shape[data-v-3e2e1838] {
  display: flex;
  padding: 4rem;
  flex-direction: column;
  align-items: center;
  gap: 4rem;
  border-radius: 0.5rem;
  background: hsla(0, 0%, 8%, 0.8);
  color: #fff;
  font-family: Oswald;
}

.trace-shape h2[data-v-3e2e1838] {
  font-size: 3rem;
  margin: 0;
  font-weight: 400;
  max-width: 25rem;
  text-align: center;
}

.trace-shape p[data-v-3e2e1838] {
  margin: -2rem 0;
}

.trace-shape .canvas[data-v-3e2e1838] {
  width: 26rem;
  height: 35rem;
  position: relative;
  isolation: isolate;
}

.trace-shape .canvas canvas[data-v-3e2e1838] {
  width: 100%;
  height: 100%;
}

.trace-shape .canvas .image[data-v-3e2e1838] {
  pointer-events: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
}

.trace-shape .canvas .image img[data-v-3e2e1838] {
  width: 100%;
  height: 100%;
  object-fit: cover;
}