fx_version   "cerulean"
lua54        "yes"
games        { "gta5" }

ui_page "ui/dist/index.html"

escrow_ignore {
  "conf.lua",
  "server/*.lua",
  "client/*.lua"
}

shared_scripts {
  "@ox_lib/init.lua",
}

files {
  "ui/dist/**/*"
}

client_scripts {
  '@blrp_rpc/tunnel/client.lua',
	'@blrp_rpc/proxy/client.lua',
  "client/main.lua",
  "client/buttonMash.lua",
  "client/balance.lua",
  "client/debugCommands.lua"
}

shared_scripts {
  "conf.lua"
}

server_scripts {
  "server/*.lua"
}
