tImpound = {}
T.bindInstance('main', tImpound)

pImpound = P.getInstance(GetCurrentResourceName(), 'main')

local visible = false

tImpound.setVisible = function(_visible, mode, accounts, cash)
  visible = _visible
  SetNuiFocus(_visible, _visible)

  if _visible then
    local me = exports.blrp_core:me()

    SendNUIMessage({
      action = 'open',
      FullName = me.get('fullname') or 'fuckface',
    })
  else
    SendNUIMessage({
      action = 'close',
    })
  end
end

RegisterNUICallback('close', function(_, callback)
  callback({ success = true })
  tImpound.setVisible(false)
end)

RegisterNUICallback('fetchRecords', function(data, callback)
  local success, transactions, last_page = pImpound.getRecords({ data.page })

  callback({
    success = success,
    impoundRecords = transactions,
    impoundLastPage = last_page,
  })
end)
