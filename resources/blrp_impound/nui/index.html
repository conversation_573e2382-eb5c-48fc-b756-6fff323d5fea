<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vue UI</title>
  <link rel="stylesheet" href="css/style.css" />
  <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/fontawesome.min.css" />
  <link rel="stylesheet" href="https://cfx-nui-blrp_ui/fontawesome/css/all.min.css" />

  <style>
    body, html {
      margin: 0;
      padding: 0;
      height: 100%;
      overflow: hidden; /* Added overflow: hidden to body and html */
    }

    #container {
      height: 100%;
      overflow-y: auto; /* Added overflow-y: auto to enable vertical scrolling */
    }

    #wrapper {
      /* Add your existing styles for #wrapper */
    }

    #impound-records {
      /* Add your existing styles for #impound-records */
    }
  </style>
</head>
<body>
<div id="app"></div>
<script src="https://cdn.jsdelivr.net/npm/vue@2"></script>
<script>
  const initialData = () => ({
    FullName:'',
    Query: '',
    visible: false,
    impoundRecords: [],
    impoundPage: 1,
    impoundPolling: false,
    impoundLastPage: false,
  });

  new Vue({
    el: '#app',
    data() {
      return initialData();
    },
    mounted() {
      window.addEventListener('message', (event) => {
        if (!event.data) return;

        if (event.data.action == 'open') {
          this.visible = true;
          this.FullName = event.data.FullName;
          this.fetchImpoundRecords(); // Fetch impound records when the UI is opened
          this.initializeDataTable();
        }

        if (event.data.action == 'close') {
          const data = initialData();
          Object.keys(data).forEach((k) => (this[k] = data[k]));
          this.visible = false;
        }
      });
    },
    methods: {
      initializeDataTable() {
        $(document).ready(function () {
          $('#impoundTable').DataTable();
        });
      },
      fetchNUI(route, params = {}) {
        return fetch(`https://${GetParentResourceName()}/${route}`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json; charset=UTF-8', },
          body: JSON.stringify(params)
        }).then(r => r.json());
      },
      formatDate(dateString) {
        const timestamp = parseFloat(dateString);
        if (!isNaN(timestamp)) {
          const date = new Date(timestamp);
          return date.toLocaleString(); // Adjust the format as needed
        }
        return 'Invalid Date';
      },
      close() {
        this.visible = false;
      },
      fetchImpoundRecords() {
        if (this.impoundPolling || this.impoundLastPage) {
          return;
        }

        this.impoundPolling = true;

        // Assuming 'fetchRecords' is your backend API endpoint for impound records
        this.fetchNUI('fetchRecords', {
          page: this.impoundPage,
        }).then(response => {
          this.impoundPolling = false;
          if (!response.success) {
            // Handle error if needed
            return;
          }
          console.log(response)

          this.impoundRecords = this.impoundRecords.concat(response.impoundRecords);
          this.impoundPage = 0;
          this.impoundLastPage = response.impoundLastPage;
        });
      },
      impoundRecordsScroll(e) {
        if (this.impoundPolling || this.impoundLastPage) {
          return;
        }

        let scroll_percent = (e.target.scrollTop + e.target.clientHeight) / e.target.scrollHeight * 100;
        console.log(scroll_percent);

        if (scroll_percent > 75) {
          this.fetchImpoundRecords();
        }
      },

    },
    template: `
        <div id="container">
          <div id="wrapper" v-if="visible">
            <div id="header">
              <div class="icon">San Andreas Impound Records</div>
              <div class="header-buttons">
                {{ FullName }}
                <button @click="close">Close</button>
              </div>
            </div>
            <div id="contents">
              <div v-if="impoundRecords.length > 0" @scroll.passive="impoundRecordsScroll">
                <table>
                  <thead>
                  <tr>
                    <th>Vehicle Registration</th>
                    <th>Impound Description</th>
                    <th>Impound Date</th>
                    <th>Impound Type</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="record in impoundRecords" :key="record.id" class="record-item">
                    <td>{{ record.registration }}</td>
                    <td>{{ record.time }}</td>
                    <td>{{ formatDate(record.date) }}</td>
                    <td>{{ record.type }}</td>
                  </tr>
                  </tbody>
                </table>
              </div>
              <div v-else>
                <p>No results found.</p>
              </div>
            </div>
          </div>
        </div>
    `,
  });
</script>
</body>
</html>
