* {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", Aria<PERSON>, sans-serif;
  text-shadow: 0px 0px 6px rgb(9, 10, 26);
}

::-webkit-scrollbar {
  width: 0.3vw;
}

::-webkit-scrollbar-thumb {
  background: deepskyblue;
}

#container {
  height: 100% !important;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

#wrapper {
  width: 1200px;
  height: 700px;
  overflow: hidden;
  background: rgba(36, 38, 60, 0.8);
  box-shadow: 0px 0px 10px deepskyblue;
  border: 1px solid deepskyblue;
  border-radius: 25px;
  color: white;
  display: flex;
  flex-direction: column;
}

#header {
  height: 50px;
  padding: 10px 20px;
  border-top-left-radius: 25px;
  border-top-right-radius: 25px;
  border-bottom: 1px solid #00BEFE;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

#header .icon {
  font-size: 40px;
}

.header-buttons {
  display: flex;
  align-items: center;
}

.header-buttons button {
  color: white;
  background-color: rgba(0, 0, 0, 0.4);
  cursor: pointer;
  border: 1px solid #47474C;
  border-radius: 20px;
  padding: 10px;
  font-size: 16px;
  margin-left: 5px;
}

.header-buttons button.close {
  background: none;
  border: none;
  font-size: 26px;
}

#contents {
  height: 650px;
  border-bottom-left-radius: 25px;
  border-bottom-right-radius: 25px;
  display: flex;
}

#impound-records {
  border-left: 1px solid #00BEFE;
  width: 100%;
  padding: 10px;
  overflow-y: scroll;
}

.popup {
  position: absolute;
  font-size: 16px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 480px;
  background: rgba(36, 38, 60, 1.0);
  box-shadow: 0px 0px 10px deepskyblue;
  border: 1px solid deepskyblue;
  border-radius: 20px;
  color: white;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* ... (Other styles not removed) */
