math.randomseed(os.time())

pImpound = {}
P.bindInstance('main', pImpound)

tImpound = T.getInstance(GetCurrentResourceName(), 'main')

if GlobalState.is_dev then
  RegisterCommand('testimp', function(source)
    tImpound.setVisible(source, { true })
  end)

  RegisterCommand('closeimp', function(source)
    tImpound.setVisible(source, { false })
  end)
end

pImpound.getRecords = function(page)
  local character = exports.blrp_core:character(source)
  -- Fetch impound records with JOIN operations
  local impound_records = MySQL.query.await([[
    SELECT imp.*, veh.*, charact.*
    FROM core_impound_history imp
    LEFT JOIN vrp_user_vehicles veh ON imp.vehicle_id = veh.id
    LEFT JOIN characters charact ON imp.impnd_by_char = charact.id
    ORDER BY imp.id DESC LIMIT ? OFFSET ?
  ]], { 25, math.max(page and (page - 1) * 25 or 0, 0) }) or {}


  -- Process impound records
  local records = {}

  for _, record in pairs(impound_records) do
    table.insert(records, {
      registration = record.registration,
      type = record.impound_type,
      time = record.impnd_length,
      date = record.impnd_time
    })
  end


  return true, records, (#impound_records < 25)
end



