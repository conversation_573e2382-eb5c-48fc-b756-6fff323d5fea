fx_version 'adamant'

game 'gta5'

dependencies {
  'PolyZone',
}

ui_page 'html/index.html'

shared_scripts {
  '@blrp_core/flags/drug_lab_flags.lua',
  'shared/*.lua',
}

client_scripts {
	'@PolyZone/client.lua',
	'@PolyZone/BoxZone.lua',
	'@PolyZone/EntityZone.lua',
	'@PolyZone/CircleZone.lua',
	'@PolyZone/ComboZone.lua',

  '@blrp_rpc/proxy/client.lua',
  '@blrp_rpc/tunnel/client.lua',

	'client/*.lua',

  'common_zones.lua',

  'definitions/**/*.lua',
}

server_scripts {
  '@blrp_rpc/proxy/server.lua',
  '@blrp_rpc/tunnel/server.lua',

  'server/*.lua',
  'definitions/**/*.lua',
}

files {
	'html/index.html',
	'html/css/style.css',
	'html/js/script.js'
}
