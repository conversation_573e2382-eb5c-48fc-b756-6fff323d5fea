pTarget = {}
P.bindInstance('main', pTarget)
tTarget = T.getInstance('blrp_target', 'main')

local is_dev = GlobalState.is_dev

local zone_name_coords = {}
local zones_shutdown = {}

function ShutdownZone(name)
  zones_shutdown[name] = true

  TriggerClientEvent('blrp_target:client:shutdownZone', -1, name)
end

AddEventHandler('core:server:registerSelectedPlayer', function(player)
  Citizen.Wait(2000)
  TriggerClientEvent('blrp_target:client:shutdownZone', player, zones_shutdown)
end)

-- Required tables / functions

common_zones = {}
BoxZone = {}
function BoxZone:Create() end
function debugPolys() end
function disableContext() end
function GetModelDimensions() return vector3(0, 0, 0), vector3(0, 0, 0) end
function AddCookingZone() end

-- Register valid crafting item options

local item_id_crafting_spot = {}

function canCraftItem(player, item_id)
  local crafting_spots = item_id_crafting_spot[item_id]

  if not crafting_spots then
    return false
  end

  local character = exports.blrp_core:character(player)
  local character_coords = character.getCoordinates()

  for _, crafting_spot in pairs(crafting_spots) do
    if not crafting_spot.coords then
      if crafting_spot.gang_bench then
        return true
      end
    elseif #(character_coords - crafting_spot.coords) <= (crafting_spot.distance + 0.5) then
      if #crafting_spot.groups == 0 or not crafting_spot.groups then
        return true
      else
        for _, group_name in pairs(crafting_spot.groups) do
          if character.hasGroup(group_name) or character.hasGroup('LEO') then
            return true
          end
        end
      end
    end
  end

  return false
end

exports('CanCraftItem', canCraftItem)

local business_component_spots = {
  ['management'] = {},
  ['storage'] = {},
  ['fridge'] = {},
}

local dynamic_component_counts = {
  ['crafting'] = 0,
  ['shoplink'] = 0,
  ['bcsb'] = 0,
}

exports('ValidBusinessComponent', function(player, event_data)
  if
    not event_data or
    not event_data.component_id or
    not business_component_spots[event_data.component_id] or
    not event_data.position
  then
    print('returning nil')
    return nil
  end

  local character = exports.blrp_core:character(player)
  local character_coords = character.getCoordinates()

  for _, component_spot in pairs(business_component_spots[event_data.component_id]) do
    if
      component_spot.definition.business_name == event_data.business_name and
      (
        character.hasGroup(component_spot.definition.business_name) or
        character.hasGroup('LEO')
      ) and
      #(character_coords - component_spot.coords) <= (component_spot.distance + 0.5)
    then
      if
        (component_spot.definition.args and component_spot.definition.args.named and event_data.args and (component_spot.definition.args.named == event_data.args.named)) or
        (component_spot.definition.args and component_spot.definition.args.uid and event_data.args and (component_spot.definition.args.uid == event_data.args.uid)) or
        (not component_spot.definition.args and not event_data.args)
      then
        return component_spot.definition
      end
    end
  end

  if is_dev then
    print('Would trigger anticheat check, bypass on dev')
    return nil
  end

  return false
end)

local shop_link_coords = {}

exports('ValidShopLink', function(player, event_data)
  local character = exports.blrp_core:character(player)

  if
    not event_data or
    not event_data.event_server or
    event_data.event_server ~= 'blrp_core:server:item-store:resolveTargetConfig' or
    not event_data.uid or
    not event_data.position
  then
    return false
  end
  local uid = event_data.uid
  local character_coords = character.getCoordinates()

  -- Workaround for Vespucci Beach Market ped state bags
  if
    string.sub(uid, 1, 8) == 'vbmarket' and
    character.distanceFrom(vector3(-1217.409, -1452.186, 4.366)) <= 150.0 and
    event_data.entity_hash and
    ({
      [`a_m_y_beach_02`] = true,
      [`a_m_y_beachvesp_01`] = true,
      [`a_f_m_beach_01`] = true,
      [`a_m_m_beach_01`] = true,
    })[event_data.entity_hash]
  then
    return true, character.getCoordinates()
  end

  -- NY override
  if string.match(uid, 'xm23silver') and character.distanceFrom(vector3(3100.711, -4808.620, 112.370)) <= 15.0 then
    return true, character.getCoordinates()
  elseif string.match(uid, 'xm23animals') and character.distanceFrom(vector3(3556.089, -4685.394, 114.590)) <= 15.0 then
    return true, character.getCoordinates()
  elseif string.match(uid, 'xm23mbg') and character.distanceFrom(vector3(6029.999, -5414.336, 89.797)) <= 15.0 then
    return true, character.getCoordinates()
  end

  -- Easter 2025 override
  if string.match(uid, 'ea25') and character.distanceFrom(vector3(-1170.751, 4926.867, 224.315)) < 15.0 then
    return true, character.getCoordinates()
  end

  -- Cayo 2024 override
  if (uid == 'cp24-q1-pelts' or uid == 'cp24-q1-fangs' or uid == 'cp24-q1-supplies') and character.distanceFrom(vector3(4988.448, -5201.299, 2.514)) < 5.0 then
    return true, character.getCoordinates()
  elseif (uid == 'cp24-q2-sell' or uid == 'cp24-q2-sell-doubloons') and character.distanceFrom(vector3(4471.734, -4596.207, 5.641)) < 5.0 then
    return true, character.getCoordinates()
  elseif uid == 'cp24-q3-flowers' and character.distanceFrom(vector3(4973.728, -5182.910, 2.474)) < 5.0 then
    return true, character.getCoordinates()
  end

  -- Target store entity check (ped)
  if event_data.entity_hash then
    for _, static_store_ped in pairs(static_store_peds) do
      if static_store_ped.model == event_data.entity_hash then
        local store_coords = static_store_ped.coords.xyz

        if static_store_ped.store_id == uid then
          if #(character_coords - store_coords) <= 10.0 then
            return true, store_coords
          end
        end

        if static_store_ped.extra_options then
          for _, extra_option in pairs(static_store_ped.extra_options) do
            if
              extra_option.event_server and
              extra_option.event_server == 'blrp_core:server:item-store:resolveTargetConfig' and
              extra_option.uid and
              extra_option.uid == uid
            then
              if #(character_coords - store_coords) <= 10.0 then
                return true, store_coords
              end
            end
          end
        end
      end
    end
  end

  local shop_links = shop_link_coords[uid]
  if not shop_links then
    return false
  end

  for _, link in pairs(shop_links) do

    if link.gang_bench and event_data.position then
      return true, event_data.position
    end

    if event_data.position and link.coords and link.distance and #(character_coords - link.coords) <= (link.distance * 1.5) then
      return true, link.coords
    end

  end

  return false
end)

local bcsb_interact_coords = {}

exports('GetBCSBSearchCoords', function()
  return bcsb_interact_coords
end)

function processOptions(center, options, distance, models, gang_bench)
  center = center or nil

  for k, option in pairs(options) do
    local groups = option.groups

    if not groups then
      groups = { option.business_name }
    end

    -- Auto register base components
    if
      option.event_server and
      option.event_server == 'core:server:businesses:custom:targetInteract' and
      option.component_id and
      option.business_name
    then
      local component_id = option.component_id

      -- if business_component_spots[component_id] or string.match(component_id, 'custom') then
      if business_component_spots[component_id] then
        if not business_component_spots[component_id] then
          business_component_spots[component_id] = {}
        end

        if not dynamic_component_counts[component_id] then
          dynamic_component_counts[component_id] = 0
        end

        local definition = option

        definition.coords = center

        table.insert(business_component_spots[component_id], {
          coords = center,
          distance = distance,
          groups = groups,
          definition = definition,
        })

        dynamic_component_counts[component_id] = dynamic_component_counts[component_id] + 1
      end
    end

    -- Auto register crafting
    if
      option.event_server and
      option.event_server == 'core:server:target-backhaul:craft' and
      option.item_id
    then
      if not item_id_crafting_spot[option.item_id] then
        item_id_crafting_spot[option.item_id] = {}
      end

      table.insert(item_id_crafting_spot[option.item_id], {
        coords = center,
        distance = distance,
        groups = groups,
        gang_bench = gang_bench,
      })

      dynamic_component_counts['crafting'] = dynamic_component_counts['crafting'] + 1
    end

    -- Auto register item store links
    if
      option.event_server and
      option.event_server == 'blrp_core:server:item-store:resolveTargetConfig'
    then
      if option.vbmarket then
        option.uid = 'vbmarket'
      end

      if not option.uid then
        exports.blrp_core:print_r('no option.uid for resolveTargetConfig option', option)
      else
        if not shop_link_coords[option.uid] then
          shop_link_coords[option.uid] = {}
        end

        local coords = center

        if option.uid_coords then
          coords = option.uid_coords
        end

        table.insert(shop_link_coords[option.uid], {
          coords = coords,
          distance = distance,
          models = models,
          gang_bench = gang_bench,
        })

        dynamic_component_counts['shoplink'] = dynamic_component_counts['shoplink'] + 1
      end
    end

    -- Auto register BCSB search spots
    if
      option.event_server and
      option.event_server == 'core:server:bcsb:searchSpot'
    then
      table.insert(bcsb_interact_coords, center)

      dynamic_component_counts['bcsb'] = dynamic_component_counts['bcsb'] + 1
    end
  end
end

function AddTargetModel(models, settings)
  if not settings or not settings.options or not settings.distance then
    return
  end

  processOptions(false, settings.options, settings.distance, models)
end

function AddBoxZone(name, center, length, width, options, targetoptions)
  if not options.name then
    options.name = name
  end

  if type(center) == 'vector4' then
    options.heading = center.w
    center = center.xyz
  end

  if not targetoptions.options or not targetoptions.distance then
    return
  end

  zone_name_coords[name] = center

  processOptions(center, targetoptions.options, targetoptions.distance)
end

local counters = {}
local collisions = {}

function AddBoxZoneAutoname(center, length, width, options, targetoptions)
  local debug_source = debug.getinfo(2)
  local debug_source_ex = string.split(debug_source.source, '/')
  local file = string.gsub(debug_source_ex[#debug_source_ex], '.lua', '')

  if not counters[file] then
    counters[file] = 1
  end

  name = file .. ':' .. debug_source.currentline .. ':' .. counters[file]
  counters[file] = counters[file] + 1

  if collisions[name] then
    print('Target zone name collision', name)
  end

  collisions[name] = true

  AddBoxZone(name, center, length, width, options, targetoptions)
end

function AddGangBenchOption(business_name, option)
  processOptions(false, { option }, 5.0, false, true)
end

function AddDrugLabOption(business_name, option)
  processOptions(false, { option }, 5.0, false, true)
end

Citizen.CreateThread(function()
  Citizen.Wait(5000)

  exports.blrp_core:print_r('Registered dynamic components', dynamic_component_counts)
end)

AddEventHandler('chatCommand', function(player, command, args)
  if command ~= 'shutdownlocation' then
    return
  end

  local character = exports.blrp_core:character(player)

  if not character.hasPermissionCore('police.shutdownhidden') then
    character.notify('You do not have permission to use this command')
    return
  end

  local character_coords = character.getCoordinates()

  local closest_zone_name = nil
  local closest_zone_coords = nil
  local closest_zone_distance = nil

  for zone_name, zone_coords in pairs(zone_name_coords) do
    local distance = #(character_coords.xy - zone_coords.xy)

    if (not closest_zone_distance or distance < closest_zone_distance) and distance < 5.0 then
      closest_zone_name = zone_name
      closest_zone_coords = zone_coords
      closest_zone_distance = distance
    end
  end

  if
    not closest_zone_name or
    zones_shutdown[closest_zone_name] or
    not character.request('Shut down closest third eye zone?') or
    not character.progressPromise('Shutting down location', 30, {
      animation = {
        animDict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@',
        anim = 'machinic_loop_mechandplayer',
        flags = 49,
      },
    })
  then
    return
  end

  ShutdownZone(closest_zone_name)

  character.notify('Location shut down')
  character.log('ACTION', 'Shut down third eye location', {
    zone_name = closest_zone_name,
    zone_coords = closest_zone_coords,
  })
end)
