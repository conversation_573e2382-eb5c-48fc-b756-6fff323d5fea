for _, coords in pairs({
  vector4(-627.5946, -234.3678, 37.64523, 215.0),
  vector4(-626.5439, -233.6047, 37.64523, 215.0),
  vector4(-622.6159, -232.5636, 37.64523, 305.0),
  vector4(-625.2751, -238.2881, 37.64523, 215.0),
  vector4(-626.325, -239.0509, 37.64523, 215.0),
  vector4(-618.7984, -234.1509, 37.64523, 215.0),
  vector4(-619.8483, -234.9137, 37.64523, 215.0),
  vector4(-623.6147, -228.6247, 37.64523, 215.0),
  vector4(-625.33, -227.3697, 37.64523, 35.0),
  vector4(-620.1764, -230.7865, 37.64523, 125.0),
  vector4(-621.5175, -228.9474, 37.64523, 125.0),
  vector4(-617.8492, -229.1128, 37.64523, 305.0),
  vector4(-617.0856, -230.1627, 37.64523, 305.0),
  vector4(-619.9663, -226.198, 37.64523, 305.0),
  vector4(-619.2031, -227.2482, 37.64523, 305.0),
  vector4(-627.2115, -234.8942, 37.64523, 35.0),
  vector4(-626.1613, -234.1315, 37.64523, 35.0),
  vector4(-620.5215, -232.8823, 37.64523, 35.0),
  vector4(-624.2796, -226.6065, 37.64523, 35.0),
  vector4(-623.9558, -230.7263, 37.64523, 305.0),
}) do
  AddBoxZoneAutoname(coords, 0.6, 1.3, {
    minZ = coords.z - 0.1,
    maxZ = coords.z + 0.599887 + 0.1,
  }, {
    options = {
      {
        event_client = '',
        icon = 'fa-solid fa-square',
        label = 'Secure Glass',

        groups = {
          'Civilian'
        },

        filter = function()
          return GlobalState.vangelico_stage <= 2
        end,
      },
      {
        event_server = 'core:server:vangelico:lootCase',
        icon = 'fa-regular fa-hammer-crash',
        label = 'Loot',

        groups = {
          'Civilian'
        },

        filter = function()
          local _, weapon_hash = GetCurrentPedWeapon(PlayerPedId(), true)

          return weapon_hash == `WEAPON_CROWBAR` and GlobalState.vangelico_stage == 3
        end,
      },
    },
    context_disable = true,
    distance = 3.0
  })
end

AddBoxZoneAutoname(vector4(-629.3906, -230.4335, 38.45, 35.0), 0.15, 0.25, {
  minZ = 38.25,
  maxZ = 38.5,
}, {
  options = {
    {
      event_server = 'core:server:vangelico:resetManually',
      icon = 'fa-solid fa-power-off',
      label = 'Reset Alarm',

      groups = {
        'LEO',
      },

      filter = function()
        local stage = GlobalState.vangelico_stage

        return stage > 1 and stage < 4
      end
    },
  },
  distance = 1.6
})

AddBoxZone('VangelicoFence', vector3(818.24, -284.54, 66.46), 1.2, 1.2, {
  name = 'VangelicoFence',
  heading = 0.0,
  minZ = 65.86,
  maxZ = 66.86,
}, {
  options = {
    {
      event_server = 'core:server:vangelico:sell',
      icon = 'fa-regular fa-money-bill-simple-wave',
      label = 'Sell Items',
      interact = 'selling',
       groups = {
        'Civilian'
      }
    },
  },
  context_disable = true,
  distance = 3.5
})
