-- AddBoxZoneAutoname(vector4(881.8786, -2264.601, 32.95737, 175), 0.1, 0.2, {
--   minZ = 32.8,
--   maxZ = 33.1,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:bobcat:useKeypad',
--       icon = 'fa-regular fa-circle-caret-right',
--       label = 'Use',

--       groups = {
--         'Civilian'
--       }
--     },
--     {
--       event_server = 'core:server:bobcat:removeKeycard',
--       icon = 'fa-regular fa-circle-caret-left',
--       label = 'Remove Keycard',

--       groups = {
--         'LEO'
--       },

--       filter = function()
--         return GlobalState.bobcat_stage > 1
--       end
--     },
--   },

--   distance = 1.5,
--   context_disable = true
-- })

-- AddBoxZoneAutoname(vector4(874.6317, -2288.878, 32.83877, 355), 0.2, 0.3, {
--   minZ = 32.72,
--   maxZ = 32.95,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:bobcat:swipeCardInner',
--       icon = 'fa-regular fa-circle-caret-right',
--       label = 'Use',

--       groups = {
--         'Civilian'
--       },

--       filter = function()
--         return GlobalState.is_dev or GlobalState.bobcat_stage > 2
--       end
--     },
--   },

--   distance = 1.5,
--   context_disable = true
-- })

-- AddBoxZoneAutoname(vector4(872.0244, -2287.131, 32.83877, 355), 0.2, 0.3, {
--   minZ = 32.72,
--   maxZ = 32.95,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:bobcat:swipeCardInner',
--       icon = 'fa-regular fa-circle-caret-right',
--       label = 'Use',

--       groups = {
--         'Civilian'
--       },

--       filter = function()
--         return GlobalState.is_dev or GlobalState.bobcat_stage > 2
--       end
--     },
--   },

--   distance = 1.5,
--   context_disable = true
-- })

-- AddBoxZoneAutoname(vector4(888.7783, -2296.543, 32.24368, 0), 0.2, 0.2, {
--   minZ = 32.22,
--   maxZ = 32.30,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:bobcat:takeCard',
--       icon = 'fa-solid fa-arrow-up',
--       label = 'Take',

--       groups = {
--         'Civilian'
--       },

--       filter = function()
--         return GlobalState.bobcat_card_available
--       end
--     },
--   },

--   distance = 1.5,
--   context_disable = true
-- })

-- AddTargetModel({
--   `prop_plg_adv_case`,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:bobcat:lootCase',
--       icon = 'fa-regular fa-circle-caret-right',
--       label = 'Lockpick',

--       groups = {
--         'Civilian',
--       },

--       zones = {
--         'BobcatVault',
--       },
--     }
--   },
--   distance = 1.5
-- })

-- AddTargetModel({
--   `plogint_vau_safe`
-- }, {
--   options = {
--     {
--       event_server = 'core:server:bobcat:openSafe',
--       icon = 'fa-regular fa-circle-caret-right',
--       label = 'Open',

--       groups = {
--         'Civilian',
--       },

--       zones = {
--         'BobcatVault',
--       },
--     },
--     {
--       event_server = 'core:server:bobcat:checkDialer',
--       icon = 'fa-regular fa-circle-caret-right',
--       label = 'Check dialer',

--       groups = {
--         'Civilian',
--       },

--       zones = {
--         'BobcatVault',
--       },

--       filter = function(entity)
--         local status = exports.blrp_core:GetBobcatSafeStatus(GetEntityCoords(entity))

--         return status and status > 0 and status < 5
--       end,
--     },
--   },
--   distance = 1.5
-- })

-- AddTargetModel({
--   `plogint_vau_safe_open`
-- }, {
--   options = {
--     {
--       event_server = 'core:server:bobcat:lootSafe',
--       icon = 'fa-regular fa-treasure-chest',
--       label = 'Loot',

--       groups = {
--         'Civilian',
--       },

--       zones = {
--         'BobcatVault',
--       },
--     }
--   },
--   distance = 1.5
-- })
