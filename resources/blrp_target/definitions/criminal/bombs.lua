AddTargetModel({
  `prop_bomb_01_s`
}, {
  options = {
    {
      event_server = 'core:server:explosives:checkTimer',
      icon = 'fa-solid fa-clock',
      label = 'Check Timer',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
    {
      event_server = 'core:server:explosives:cutWire',
      icon = 'fa-solid fa-scissors',
      label = 'Cut red wire',
      wire_color = 'red',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
    {
      event_server = 'core:server:explosives:cutWire',
      icon = 'fa-solid fa-scissors',
      label = 'Cut orange wire',
      wire_color = 'orange',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
    {
      event_server = 'core:server:explosives:cutWire',
      icon = 'fa-solid fa-scissors',
      label = 'Cut yellow wire',
      wire_color = 'yellow',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
    {
      event_server = 'core:server:explosives:cutWire',
      icon = 'fa-solid fa-scissors',
      label = 'Cut green wire',
      wire_color = 'green',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
    {
      event_server = 'core:server:explosives:cutWire',
      icon = 'fa-solid fa-scissors',
      label = 'Cut blue wire',
      wire_color = 'blue',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
    {
      event_server = 'core:server:explosives:cutWire',
      icon = 'fa-solid fa-scissors',
      label = 'Cut indigo wire',
      wire_color = 'indigo',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
    {
      event_server = 'core:server:explosives:cutWire',
      icon = 'fa-solid fa-scissors',
      label = 'Cut violet wire',
      wire_color = 'violet',

      filter = function(entity, ray_intersect_coords)
        local state = Entity(entity).state

        return state.synced_prop
      end
    },
  },
  distance = 2.0
})
