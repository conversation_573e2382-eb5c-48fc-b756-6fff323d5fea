for _, v in pairs({
  -- Little Seoul
  { coords = vector4(-658.0432, -859.8, 24.491, 180.0), length = 0.7, width = 5.4, minZ = 24.45, maxZ = 24.65 },

  -- <PERSON><PERSON>
  { coords = vector4(87.800, -227.01, 54.46, 251.0), length = 0.8, width = 3.8, minZ = 54.45, maxZ = 55.0 },
  { coords = vector4(90.18803, -227.8382, 54.46, 251.0), length = 0.8, width = 3.8, minZ = 54.45, maxZ = 55.0 },

  -- Textile City
  { coords = vector4(387.2666, -828.4962, 29.18, 180.0), length = 0.8, width = 3.8, minZ = 29.15, maxZ = 29.55 },
  { coords = vector4(387.273, -831.02875, 29.18, 180.0), length = 0.8, width = 3.8, minZ = 29.15, maxZ = 29.55 },

  -- Mirror Park
  { coords = vector4(1132.918, -472.1075, 66.48922, 345.0), length = 0.8, width = 3.8, minZ = 66.48, maxZ = 66.9 },
  { coords = vector4(1131.988, -475.5923, 66.49097, 345.0), length = 0.8, width = 3.8, minZ = 66.48, maxZ = 66.9 },

  -- Vespucci Beach
  { coords = vector4(-1246.999, -1452.343, 4.135796, 35.0), length = 1.0, width = 1.5, minZ = 4.13, maxZ = 4.83 },
  { coords = vector4(-1244.849, -1450.838, 4.130699, 35.0), length = 1.0, width = 1.5, minZ = 4.13, maxZ = 4.83 },
  { coords = vector4(-1245.698, -1454.198, 4.145884 , 35.0), length = 1.0, width = 1.5, minZ = 4.13, maxZ = 4.83 },

  -- Paleto
  { coords = vector4(-25.0771, 6482.699, 31.50389, 45.0), length = 0.8, width = 3.6, minZ = 31.4, maxZ = 31.7 },
  { coords = vector4(-23.22865, 6480.872, 31.50389, 45.0), length = 0.8, width = 3.6, minZ = 31.4, maxZ = 31.7 },
}) do
  AddBoxZoneAutoname(v.coords, v.length, v.width, {
    minZ = v.minZ,
    maxZ = v.maxZ,
  }, {
    options = {
      {
        event_server = 'core:server:digital-den:stealFromSpot',
        icon = 'fa-regular fa-search',
        label = 'Search',

        groups = {
          'Civilian'
        }
      },
    },
    context_disable = true,
    distance = 3.0,
  })
end