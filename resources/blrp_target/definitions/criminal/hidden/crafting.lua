AddBoxZone('Crafting1', vector3(714.9827, -695.0734, 30.70818), 1.6, 2.5, {
  name = 'Crafting1',
  heading = 260.0,
  minZ = 30.7,
  maxZ = 31.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft Crowbar',

      item_id = 'wbody|WEAPON_CROWBAR',

      groups = {
        'Civilian',
        'LEO'
      }
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft Drill',

      item_id = 'bank_drill',

      groups = {
        'Civilian',
        'LEO'
      }
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft Wire Cutters',

      item_id = 'wire_cutters',

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

AddBoxZone('Crafting2', vector3(3602.84, 3662.73, 33.87), 0.2, 0.1, {   -- Chemical Gloves
  name="Crafting2",
  heading=350,
  minZ=33.92,
  maxZ=34.32,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-hand-paper',
      label = 'Shop',
      uid = 'chemical-gloves',
      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

AddBoxZone('Crafting3', vector3(1647.66, 4844.17, 42.03), 1.4, 2, {   -- Respirator
  name="Crafting3",
  heading=5,
  minZ=39.23,
  maxZ=43.43,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft Respirator',

      item_id = 'respirator',

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

AddBoxZone('Crafting4', vector3(-506.75, 299.71, 83.87), 1.4, 2, {   --Illegal Electronics Crafting (Hacking Phone, Laptop, Ribbon Cable)
  name="Crafting4",
  heading=355,
  minZ=81.07,
  maxZ=85.07,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft something',

      item_id = 'bank_phone',

      groups = {
        'Civilian',
        'LEO'
      }
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft something else',

      item_id = 'laptop_h',

      groups = {
        'Civilian',
        'LEO'
      },
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft the other thing',

      item_id = 'ribbon_cable',

      groups = {
        'Civilian',
        'LEO'
      },
    },
  },
  distance = 2.0,
  context_disable = true,
})

AddBoxZone('Crafting5', vector3(-334.73, 6081.61, 31.45), 1.0, 2, {   --Armor in Paleto
  name="Crafting5",
  heading=315,
  minZ=28.85,
  maxZ=32.85,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-shield',
      label = 'Craft Heavy Armor',

      item_id = 'heavy_armor',

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

AddBoxZone('Crafting6', vector3(-1061.67, -247.52, 44.02), 5.2, 2.3, {   --Racing USB
  name = "Crafting6",
  heading = 28,
  minZ = 43.02,
  maxZ = 44.62,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-usb-drive',
      label = 'CODE USB',

      item_id = 'racing_gps',

      groups = {
        'Civilian',
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

AddBoxZone('Crafting7', vector3(-1242.35, -1448.689, 4.210235), 1.4, 2, {   --Illegal Electronics Crafting at Vespucci Digiden (Adv Hacking Phone, Adv Hacking Laptop, USB Racing Dongle)
  name="Crafting7",
  minZ = 4.13,
  maxZ = 4.83,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft something',

      item_id = 'laptop_h_adv',
      notify = false,

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft something else',

      item_id = 'phone_h_adv',
      notify = false,

      groups = {
        'Civilian'
      },
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-screwdriver-wrench',
      label = 'Craft the other thing',

      item_id = 'racing_gps',
      notify = false,

      groups = {
        'Civilian'
      },
    },
  },
  distance = 2.0,
  context_disable = true,
})

for i, pickup in pairs({
  { coords = vector3(2430.17, 4962.06, 42.35), heading = 41.0, length = 1.8, width = 1.4}, -- Grandmals house
  { coords = vector3(3628.45, 3738.71, 28.69), heading = 327.0, length = 2.8, width = 2.8}, -- Humane Labs
  { coords = vector3(2504.36, -329.2, 92.99), heading = 359.0, length = 1.65, width = 2.0}, -- NOOSE
  { coords = vector3(612.07, -3077.33, 6.07), heading = 0.0, length = 3.4, width = 2.2}, -- maerywherter
}) do
  AddBoxZone('precursorPickup' .. i, pickup.coords, pickup.length, pickup.width, {
    name = 'precursorPickup' .. i,
    heading = pickup.heading,
    minZ = pickup.coords.z - 1.0,
    maxZ = pickup.coords.z + 1.0,
  }, {
    options = {
      {
        event_server = 'core:drug-system:precursorpickup',
        icon = 'fa-solid fa-box',
        label = 'Search For Crate',
        target_position = pickup.coords,
      }
    },
    distance = 3.0
  })
end