disableContext(true)

-- Mescaline
AddBoxZone('Lab1Manufacture', vector3(2486.539, 3721.538, 44.04105), 0.9, 2.3, {
  name = 'Lab1Manufacture',
  heading = 220.0,
  minZ = 43.3,
  maxZ = 44.7,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-fire-burner',
      label = 'Manufacture',

      location_id = 4,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

-- MDMA
AddBoxZone('Lab2Manufacture', vector3(1090.726, -1291.424, 16.27011), 1.2, 2.2, {
  name = 'Lab2Manufacture',
  heading = 335.0,
  minZ = 15.5,
  maxZ = 16.8,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-fire-burner',
      label = 'Manufacture',

      location_id = 7,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

-- LSD
AddBoxZone('Lab3Manufacture', vector3(-331.2446, -2445.325, 7.161469), 1.2, 2.6, {
  name = 'Lab3Manufacture',
  heading = 50.0,
  minZ = 6.4,
  maxZ = 7.4,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-fire-burner',
      label = 'Manufacture',

      location_id = 5,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

-- Heroin
--AddBoxZone('Lab4Manufacture', vector3(1953.346, 5180.024, 46.98379), 1.2, 2.6, {
--  name = 'Lab4Manufacture',
--  heading = 0.0,
--  minZ = 46.9,
--  maxZ = 48.5,
--}, {
--  options = {
--    {
--      event_server = 'core:server:hd:interactTarget',
--      icon = 'fa-regular fa-fire-burner',
--      label = 'Manufacture',
--
--      location_id = 6,
--
--      groups = {
--        'Civilian',
--        'LEO'
--      }
--    },
--  },
--  distance = 2.0,
--  context_disable = true,
--})

--[[
-- PCP
AddBoxZone('Lab5Manufacture', vector3(-527.5048, 5335.575, 74.03846), 1.2, 2.3, {
  name = 'Lab5Manufacture',
  heading = 160.0,
  minZ = 73.2,
  maxZ = 74.5,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-fire-burner',
      label = 'Manufacture',

      location_id = 8,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})
]]
--[[
AddBoxZoneAutoname(vector4(846.3304, 2123.052, 53.13187,70.0), 1.0, 1.0, {
  minZ = 52.6,
  maxZ = 54.8,
}, {
  options = {
    {
      event_server = 'core:server:drug-system:lab-knock',
      icon = 'fa-regular fa-door-closed',
      label = 'Knock x1',

      strikes = 1,
      labid = "pcp_lab",

      groups = {
        'Civilian',
        'LEO',
      }
    },
    {
      event_server = 'core:server:drug-system:lab-knock',
      icon = 'fa-regular fa-door-closed',
      label = 'Knock x2',

      strikes = 2,
      labid = "pcp_lab",

      groups = {
        'Civilian',
        'LEO',
      }
    },
    {
      event_server = 'core:server:drug-system:lab-knock',
      icon = 'fa-regular fa-door-closed',
      label = 'Knock x3',

      strikes = 3,
      labid = "pcp_lab",

      groups = {
        'Civilian',
        'LEO',
      }
    },
  },
  distance = 4.0
})



AddBoxZoneAutoname(vector4(857.0818, 2128.082, 52.69301, 0.0), 2.0, 1.0, {
  minZ = 52.5,
  maxZ = 53.1,
}, {
  options = generateAdvancedCookingOptions('pcp_lab1', 'dlf_pcp', { 'u_pcp', 'd_water' }),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(857.0765, 2123.624, 52.78541, 0.0), 2.0, 1.0, {
  minZ = 52.5,
  maxZ = 53.1,
}, {
  options = generateAdvancedCookingOptions('pcp_lab2', 'dlf_pcp', { 'u_pcp', 'd_water' }),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(851.4617, 2128.55, 52.69926, 90.0), 2.0, 1.0, {
  minZ = 52.5,
  maxZ = 53.1,
}, {
  options = generateAdvancedCookingOptions('pcp_lab3', 'dlf_pcp', { 'u_pcp', 'd_water' }),
  distance = 2.0,
})
]]

disableContext(true)
AddBoxZoneAutoname(vector4(195.5537, -1841.831, 28.04027, 130.0), 0.4, 1.35, {
  minZ = 27.0,
  maxZ = 28.5,
}, {
  options = {
    {
      event_server = 'core:server:drugshipment:knock',
      icon = 'fa-regular fa-door-closed',
      label = 'Knock',

      groups = {
        'Civilian'
      }
    },
  },
  distance = 2.0
})


AddBoxZoneAutoname(vector4(-1926.396, 1785.927, 173.243, 90.0), 1.0, 1.1, {
  minZ = 172.8,
  maxZ = 173.4,
}, {
  options = generateAdvancedCookingOptions('heroin_lab1', 'dlf_heroin', {
    "u_heroin",
  },
  {
    'Civilian',
  },false),
  distance = 2.0,
})
AddBoxZoneAutoname(vector4(-1921.201, 1787.835, 173.243, 90.0), 1.0, 1.1, {
  minZ = 172.8,
  maxZ = 173.4,
}, {
  options = generateAdvancedCookingOptions('heroin_lab2', 'dlf_heroin', {
    "u_heroin",
  },
  {
    'Civilian',
  },false),
  distance = 2.0,
})
AddBoxZoneAutoname(vector4(-1919.606, 1782.905, 173.243, 90.0), 1.0, 1.1, {
  minZ = 172.8,
  maxZ = 173.4,
}, {
  options = generateAdvancedCookingOptions('heroin_lab3', 'dlf_heroin', {
    "u_heroin",
  },
  {
    'Civilian',
  },false),
  distance = 2.0,
})