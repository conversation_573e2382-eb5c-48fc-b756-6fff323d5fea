-- Lysergic
--[[AddBoxZone('Ingredient1', vector3(172.6879, 2220.605, 89.72915), 1.6, 3.1, {
  name = 'Ingredient1',
  heading = 330.0,
  minZ = 89.7,
  maxZ = 91.5,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 9,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})]]

-- Ergot
--[[AddBoxZone('Ingredient2', vector3(2943.592, 4626.957, 48.15768), 0.7, 0.7, {
  name = 'Ingredient2',
  heading = 0.0,
  minZ = 47.7,
  maxZ = 48.8,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 10,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})]]

-- Opium
--[[AddBoxZone('Ingredient3', vector3(-2547.752, 2301.979, 32.21749), 1.6, 1.9, {
  name = 'Ingredient3',
  heading = 5.0,
  minZ = 32.2,
  maxZ = 33.7,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 11,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})]]

-- Anhydride
AddBoxZone('Ingredient4', vector3(-1923.385, 1782.959, 173.707), 0.8, 1.0, {
  name = 'Ingredient4',
  heading = 155.0,
  minZ = 173.1,
  maxZ = 173.907,
}, {
  options = {
    {
      event_server = 'core:drug-system:takeingredient',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect Anhydride',

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})
--[[
-- Safrole
AddBoxZone('Ingredient5', vector3(905.1691, 3656.605, 31.99415), 1.7, 1.8, {
  name = 'Ingredient5',
  heading = 105.0,
  minZ = 31.7,
  maxZ = 33.0,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 13,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})

-- Formaldehyde
AddBoxZone('Ingredient6', vector3(1096.55, -1522.569, 33.66909), 1.0, 1.0, {
  name = 'Ingredient6',
  heading = 105.0,
  minZ = 33.6,
  maxZ = 35.7,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 14,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.0,
  context_disable = true,
})
]]
--[[
-- Ether
AddBoxZone('Ingredient7', vector3(266.026,-2427.536,8.664), 2.0, 2.4, {
  name = 'Ingredient7',
  heading = 180.0,
  minZ = 8.0,
  maxZ = 8.8,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 15,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.5,
  context_disable = true,
})

-- Hydrochloride
AddBoxZone('Ingredient8', vector3(481.7636, -591.1329, 24.40223), 2.0, 2.0, {
  name = 'Ingredient8',
  heading = 240.0,
  minZ = 24.0,
  maxZ = 24.8,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 16,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },
  distance = 2.5,
  context_disable = true,
})
]]
--[[
-- Sodium Hydroxide
AddBoxZone('Ingredient9', vector3(-801.0715, -2787.44, 12.95735), 1.6, 3.1, {
  name = 'Ingredient9',
  heading = 305.0,
  minZ = 12.9,
  maxZ = 14.5,
}, {
  options = {
    {
      event_server = 'core:server:hd:interactTarget',
      icon = 'fa-regular fa-boxes-stacked',
      label = 'Collect',

      location_id = 24,

      groups = {
        'Civilian',
        'LEO'
      }
    },
  },

  distance = 2.5,
  context_disable = true,
})
]]
