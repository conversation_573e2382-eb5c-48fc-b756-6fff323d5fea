disableContext(true)

AddTargetModel({
  `ch_prop_ch_sec_cabinet_01b`,
  `ch_prop_ch_sec_cabinet_01f`
}, {
  options = {
    {
      event_server = 'core:server:bcsb:useLockbox',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',

      groups = {
        'Civilian'
      },

      zones = {
        'BCSBVaultArea',
      },

      filter = function()
        return GlobalState.bcsb_stage >= 6
      end,
    },
  },
  distance = 1.2
})

-- Filing cabinets
for _, coords in pairs({
  vector4(-94.68823, 6465.916, 30.62899, 45.0),
  vector4(-94.23103, 6466.356, 30.62899, 45.0),
}) do
  for i = 1, 5 do
    local offset = 6 - i

    local max = coords.z + (offset * 0.370202)
    local min = max - 0.34

    AddBoxZoneAutoname(coords, 0.61, 0.61, {
      minZ = min,
      maxZ = max,
    }, {
      options = {
        {
          event_server = 'core:server:bcsb:useDrawer',
          icon = 'fa-regular fa-magnifying-glass',
          label = 'Search',

          drawer_id = i,

          groups = {
            'Civilian'
          },

          filter = function()
            return GlobalState.bcsb_stage >= 4
          end,
        },
      },

      distance = 2.0,
    })
  end
end

-- Fingerprint search spots
for _, spot in pairs({
  { coords = vector4(-110.4801, 6475.414, 30.63314, 45.0), height = 1.7 },
  { coords = vector4(-110.7745, 6469.06, 31.56955, 45.0), width = 0.4, length = 0.4 },
  { coords = vector4(-110.1288, 6470.734, 31.58151, 45.0), width = 0.4, length = 0.3 },
  { coords = vector4(-109.7755, 6471.038, 31.58151, 45.0), width = 0.4, length = 0.3 },
  { coords = vector4(-108.0557, 6471.819, 31.53418, 45.0), length = 0.3 },
  { coords = vector4(-107.2289, 6473.044, 31.48144, 135.0), width = 0.4, length = 0.3 },
  { coords = vector4(-106.3932, 6473.764, 31.43291, 45.0) },
  { coords = vector4(-106.7057, 6472.984, 31.43418, 45.0), width = 0.3, length = 0.3 },
  { coords = vector4(-100.6819, 6474.495, 30.63314, 45.0), height = 1.7 },
  { coords = vector4(-104.7794, 6479.363, 31.43521, 45.0) },
  { coords = vector4(-105.7176, 6478.375, 31.46216, 45.0) },
  { coords = vector4(-98.1645, 6466.08, 31.43521, 45.0) },
  { coords = vector4(-97.2275, 6467.068, 31.46216, 45.0) },
  { coords = vector4(-104.0325, 6459.986, 31.4829, 45.0) },
  { coords = vector4(-103.8105, 6460.404, 31.43521, 45.0) },
  { coords = vector4(-102.8735, 6461.393, 31.46216, 45.0) },
  { coords = vector4(-107.1693, 6461.816, 30.63314, 45.0), height = 1.7 },
}) do
  local width = spot.width or 0.5
  local length = spot.length or 0.5
  local height = spot.height or 0.2

  if width == 0 then
    width = 0.5
  end

  if length == 0 then
    length = 0.5
  end

  AddBoxZoneAutoname(spot.coords, length, width, {
    minZ = spot.coords.z,
    maxZ = spot.coords.z + height,
  }, {
    options = {
      {
        event_server = 'core:server:bcsb:searchSpot',
        icon = 'fa-regular fa-magnifying-glass',
        label = 'Inspect',

        groups = {
          'Civilian'
        },

        filter = function()
          return GlobalState.bcsb_stage >= 3
        end,
      },
    },

    distance = 2.0,
  })
end

-- Card swipe
AddBoxZoneAutoname(vector4(-296.6575, 6390.816, 30.999, 305.0), 0.3, 0.3, {
  minZ = 30.85,
  maxZ = 31.15,
}, {
  options = {
    {
      event_server = 'core:server:bcsb:useSwipe',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',
    },
  },

  distance = 2.5,
})

-- Phone room
AddBoxZoneAutoname(vector4(-294.5928, 6395.511, 29.80841, 305.0), 0.9, 0.9, {
  minZ = 29.8,
  maxZ = 31.8,
}, {
  options = {
    {
      event_server = 'blrp_hack10:server:openUI',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Rack 1',

      site = 'PALBSA29',
      equipment = 'DS0',
    },
  },

  distance = 2.5,
})

AddBoxZoneAutoname(vector4(-294.0614, 6394.752, 29.80841, 305.0), 0.9, 0.9, {
  minZ = 29.8,
  maxZ = 31.8,
}, {
  options = {
    {
      event_server = 'blrp_hack10:server:openUI',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Rack 2',

      site = 'PALBSA29',
      equipment = 'DS1',
    },
  },

  distance = 2.5,
})

AddBoxZoneAutoname(vector4(-293.5198, 6393.979, 29.80841, 305.0), 0.9, 0.9, {
  minZ = 29.8,
  maxZ = 31.8,
}, {
  options = {
    {
      event_server = 'blrp_hack10:server:openUI',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Rack 3',

      site = 'PALBSA29',
      equipment = 'DS2',
    },
  },

  distance = 2.5,
})

AddBoxZoneAutoname(vector4(-292.9818, 6393.211, 29.80841, 305.0), 0.9, 0.9, {
  minZ = 29.8,
  maxZ = 31.8,
}, {
  options = {
    {
      event_server = 'blrp_hack10:server:openUI',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Rack 4',

      site = 'PALBSA29',
      equipment = 'DS3',
    },
  },

  distance = 2.5,
})

AddBoxZoneAutoname(vector4(-292.4391, 6392.436, 29.6516, 305.0), 1.3, 1.0, {
  minZ = 29.8,
  maxZ = 32.4,
}, {
  options = {
    {
      event_server = 'core:server:bcsb:inhibit',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',
    },
  },

  distance = 2.5,
})

-- Phone
AddBoxZoneAutoname(vector4(-113.0956, 6472.596, 31.73782, 45.0), 0.4, 0.4, {
  minZ = 31.7,
  maxZ = 31.9,
}, {
  options = {
    {
      event_server = 'blrp_phone:server:startCallFromTarget',
      icon = 'fa-regular fa-phone-office',
      label = 'Use Phone',

      source_number = '911-2157',
    },
  },

  distance = 2.5,
})

-- Front security keypad
AddBoxZoneAutoname(vector4(-109.6319, 6461.569, 31.80594, 225.0), 0.2, 0.4, {
  minZ = 31.8,
  maxZ = 32.2,
}, {
  options = {
    {
      event_server = 'core:server:bcsb:useKeypad',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',
    },
    {
      event_server = 'core:server:bcsb:reset',
      icon = 'fa-solid fa-power-off',
      label = 'Reset Alarm',

      groups = {
        'LEO',
      },

      filter = function()
        return GlobalState.bcsb_stage > 1
      end
    },
  },

  distance = 2.5,
})

--[[
-- Code lock back hallway (unused)
AddBoxZoneAutoname(vector4(-99.09076, 6473.232, 31.98204, 315.0), 0.2, 0.25, {
  minZ = 31.85,
  maxZ = 32.15,
}, {
  options = {
    {
      event_server = 'unused',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',
    },
  },

  distance = 2.5,
})
]]

-- Fingerprint pad back hallway
AddBoxZoneAutoname(vector4(-93.75833, 6468.21, 31.84568, 135.0), 0.2, 0.3, {
  minZ = 31.65,
  maxZ = 32.05,
}, {
  options = {
    {
      event_server = 'core:server:bcsb:useFingerprintPad',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',

      filter = function()
        return GlobalState.bcsb_stage >= 3
      end,
    },
  },

  distance = 2.5,
})

-- Security room keyboard 1
AddBoxZoneAutoname(vector4(-92.11889, 6464.379, 31.42655, 230.0), 0.3, 0.5, {
  minZ = 31.4,
  maxZ = 31.6,
}, {
  options = {
    {
      event_server = 'core:server:bcsb:useKeyboard',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',

      id = 1,

      filter = function()
        return GlobalState.bcsb_stage >= 4 or GlobalState.is_dev
      end,
    },
  },

  distance = 2.5,
})

-- Security room keyboard 2
AddBoxZoneAutoname(vector4(-94.50427, 6462.546, 31.42655, 140.0), 0.3, 0.5, {
  minZ = 31.4,
  maxZ = 31.6,
}, {
  options = {
    {
      event_server = 'core:server:bcsb:useKeyboard',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',

      id = 2,

      filter = function()
        return GlobalState.bcsb_stage >= 4 or GlobalState.is_dev
      end,
    },
  },

  distance = 2.5,
})

-- Vault keypad
AddBoxZoneAutoname(vector4(-101.9514, 6462.894, 32.11906, 225.0), 0.3, 0.5, {
  minZ = 31.8,
  maxZ = 32.5,
}, {
  options = {
    {
      event_server = 'core:server:bcsb:useVaultKeypad',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Use',

      filter = function()
        return GlobalState.bcsb_stage > 1
      end,
    },
    {
      event_server = 'core:server:bcsb:policeMoveDoor',
      icon = 'fa-solid fa-door-open',
      label = 'Open Vault Door',
      state = 'open',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:bcsb:policeMoveDoor',
      icon = 'fa-solid fa-door-closed',
      label = 'Close Vault Door',
      state = 'close',

      groups = {
        'LEO',
      }
    },
  },

  distance = 2.5,
})
