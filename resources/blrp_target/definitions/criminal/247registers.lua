local interior_coords = {
  { coords = vector3(549.031, 2671.358, 42.156),   name = '24/7 Harmony'       },
  { coords = vector3(1959.984, 3739.966, 32.343),  name = '24/7 Sandy Shores'  },
  { coords = vector3(2678.032, 3279.453, 55.241),  name = '24/7 Senora Fwy'    },
  { coords = vector3(1696.538, 4923.908, 42.063),  name = '24/7 Grapeseed'     },
  { coords = vector3(1727.913, 6415.287, 35.037),  name = '24/7 Braddock Pass' },
  { coords = vector3(160.586, 6641.629, 31.698),   name = '24/7 Paleto Bay'    },
  { coords = vector3(-2539.334, 2313.841, 33.410), name = '24/7 <PERSON><PERSON>ancudo'  },
  { coords = vector3(1820.936, 3755.140, 33.477),  name = '24/7 Market'        },
  { coords = vector3(1165.923, 2710.773, 38.157),  name = "<PERSON><PERSON>'s Liquor"     },
  { coords = vector3(1392.903, 3606.487, 34.980),  name = 'Liquor Ace'         },
  { coords = vector3(-161.078, 6321.394, 31.586),  name = 'Del Vecchio Liquor' },
}

AddTargetModel({
  `prop_till_01`,
}, {
  options = {
    {
      event_server = 'core:server:convenience-registers:smash',
      icon = 'fa-regular fa-money-bill-simple-wave',
      label = 'Smash',

      filter_pass_value = true,
      filter = function(entity, coords)
        return register_filter(entity, coords)
      end,

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'core:server:convenience-registers:inspect',
      icon = 'fa-regular fa-search',
      label = 'Inspect',

      filter_pass_value = true,
      filter = function(entity, coords)
        return register_filter(entity, coords, true)
      end,

      groups = {
        'LEO',
      }
    },
  },
  distance = 2.0
})

function register_filter(entity, coords, bypass_crowbar)
  local interior_id = GetInteriorFromEntity(PlayerPedId())

  if not interior_id or interior_id == 0 then
    return false
  end

  local room_key = GetRoomKeyFromEntity(PlayerPedId())
  local room_id = GetInteriorRoomIndexByHash(interior_id, room_key)

  if not room_id or room_id == -1 then
    return false
  end

  local room_name = GetInteriorRoomName(interior_id, room_id)

  if
    room_name ~= 'V_39_ShopRm' and
    room_name ~= 'V_66_ShopRm' and
    room_name ~= 'V_68_GasRm' and
    room_name ~= 'liquor_front' and
    room_name ~= 'tm_gm_col' and
    room_name ~= 'room01_store'
  then
    return false
  end

  local found_interior = false
  local player_coords = GetEntityCoords(PlayerPedId())

  for _, interior_data in pairs(interior_coords) do
    if #(player_coords - interior_data.coords) < 15.0 then
      found_interior = interior_data.name
      break
    end
  end

  if not found_interior then
    return false
  end

  if bypass_crowbar then
    return found_interior
  end

  local w_, weapon_hash = GetCurrentPedWeapon(PlayerPedId(), true)

  if weapon_hash == `WEAPON_CROWBAR` then
    return found_interior
  end

  return false
end
