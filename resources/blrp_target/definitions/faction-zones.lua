-- LSFD Computers Pillbox

local lsfd_computers = {
  -- Paleto Bay

  {
    coords = vector3(-256.1091, 6328.01, 32.44391),
    length = 0.5,
    width = 0.7,
    heading = 0.0
  },
  {
    coords = vector3(-258.3152, 6327.864, 32.44463),
    length = 0.5,
    width = 0.7,
    heading = 20.0
  },
}

for _, lsfd_computer in ipairs(lsfd_computers) do
  AddBoxZone('LSFDComputer' .. _, lsfd_computer.coords, lsfd_computer.length, lsfd_computer.width, {
    name = 'LSFDComputer' .. _,
    heading = lsfd_computer.heading,
    minZ = lsfd_computer.coords.z - 0.3,
    maxZ = lsfd_computer.coords.z + 0.3,
  }, {
    options = {
      -- Clock in / out
      {
        event_server = 'vrp:server:openMedicalComputer',
        icon = 'fas fa-computer-classic',
        label = 'LSFD Computer',

        zones = {
          common_zones.Hospital_Paleto,
        },

        groups = {
          'LSFD'
        }
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'LSFD',

        icon = 'fas fa-clock',
        label = 'Clock in - LSFD',

        zones = {
          common_zones.Hospital_Paleto,
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'LSFD+TMU_Internal',

        icon = 'fas fa-clock',
        label = 'Clock in - LSFD (TMU)',

        zones = {
          common_zones.Hospital_Paleto,
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',

        zones = {
          common_zones.Hospital_Paleto,
        },
      },
    },
    distance = 2.0
  })
end
