--debugPolys(true)

-- Downtown Job Center
AddBoxZoneAutoname(vector4(-269.358, -955.124, 31.227, 206.439), 0.6, 0.8, {
    minZ = 30.22,
    maxZ = 32.04,
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Taxi',

        icon = 'fas fa-clock',
        label = 'Clock in - Taxi',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Truck Driver',

        icon = 'fas fa-clock',
        label = 'Clock in - Truck Driver',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Delivery',

        icon = 'fas fa-clock',
        label = 'Clock in - Delivery',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Tow Truck',

        icon = 'fas fa-clock',
        label = 'Clock in - Towing',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Postal Worker',

        icon = 'fas fa-clock',
        label = 'Clock in - Go postal',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Garbage Collection',

        icon = 'fas fa-clock',
        label = 'Clock in - Garbage',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Gardening',

        icon = 'fas fa-clock',
        label = 'Clock in - Gardening',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Mechanic',

        icon = 'fas fa-clock',
        label = 'Clock in - Mechanic',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 3.0
  })

  -- Sandy job Center
  AddBoxZoneAutoname(vector4(1695.488, 3782.003, 34.767, 221.830), 0.6, 0.8, {
    minZ = 33.5,
    maxZ = 35.5
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Taxi',

        icon = 'fas fa-clock',
        label = 'Clock in - Taxi',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Truck Driver',

        icon = 'fas fa-clock',
        label = 'Clock in - Truck Driver',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Delivery',

        icon = 'fas fa-clock',
        label = 'Clock in - Delivery',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Tow Truck',

        icon = 'fas fa-clock',
        label = 'Clock in - Towing',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Postal Worker',

        icon = 'fas fa-clock',
        label = 'Clock in - Go postal',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Garbage Collection',

        icon = 'fas fa-clock',
        label = 'Clock in - Garbage',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Gardening',

        icon = 'fas fa-clock',
        label = 'Clock in - Gardening',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Mechanic',

        icon = 'fas fa-clock',
        label = 'Clock in - Mechanic',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Lawyer',

        icon = 'fas fa-clock',
        label = 'Clock in - Lawyer',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Miner',

        icon = 'fas fa-clock',
        label = 'Clock in - Miner',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 3.0
  })

  AddBoxZoneAutoname(vector4(-148.079, 6306.251, 31.756, 139.538), 0.6, 0.8, {
    minZ = 30.75,
    maxZ = 32.55
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Taxi',

        icon = 'fas fa-clock',
        label = 'Clock in - Taxi',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Truck Driver',

        icon = 'fas fa-clock',
        label = 'Clock in - Truck Driver',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Delivery',

        icon = 'fas fa-clock',
        label = 'Clock in - Delivery',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Tow Truck',

        icon = 'fas fa-clock',
        label = 'Clock in - Towing',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Postal Worker',

        icon = 'fas fa-clock',
        label = 'Clock in - Go postal',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Garbage Collection',

        icon = 'fas fa-clock',
        label = 'Clock in - Garbage',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Gardening',

        icon = 'fas fa-clock',
        label = 'Clock in - Gardening',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Mechanic',

        icon = 'fas fa-clock',
        label = 'Clock in - Mechanic',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Lawyer',

        icon = 'fas fa-clock',
        label = 'Clock in - Lawyer',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 3.0
  })

  -- Tow yard LS
  AddBoxZoneAutoname(vector4(-192.5211, -1161.918, 23.50149, 170.0), 0.5, 0.5, {
    minZ = 23.49,
    maxZ = 23.54,
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Tow Truck',

        icon = 'fas fa-clock',
        label = 'Clock in - Towing',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
        groups = {
          'Tow Truck',
        }
      },
    },
    distance = 3.0
  })

  -- FIB clockin
  AddBoxZoneAutoname(vector4(154.9013, -738.2993, 242.2386, 160.0), 0.6, 0.5, {
    minZ = 241.95,
    maxZ = 242.45,
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'FIB',

        icon = 'fas fa-clock',
        label = 'Clock in - FIB',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 3.0
  })

  -- Mine Grapeseed
  AddBoxZoneAutoname(vector4(1634.58, 4795.68, 46.89, 0.0), 0.8, 0.8, {
    minZ = 45.89,
    maxZ = 47.09
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Miner',

        icon = 'fas fa-clock',
        label = 'Clock in - Miner',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 3.0
  })

  --Post Clock In
  AddBoxZoneAutoname(vector4(78.550, 111.914, 81.168, 228.683), 0.6, 0.8, {
    minZ = 80.168,
    maxZ = 82.168,
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Postal Worker',
        icon = 'fas fa-clock',
        label = 'Clock in - Go Postal',
        groups = { 'Civilian' },
      },
  --  {
  --    event_server = 'core:target-backhaul:server:postalClothes',
  --    icon = 'fas fa-tshirt',
  --    label = 'Work Uniform',
  --    groups = { 'Postal Worker' },
  --  },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',
        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 3.0
  })

  -- Garbage Clock in

  AddBoxZoneAutoname(vector4(2041.699, 3187.196, 45.169, 235.426), 0.6, 0.8, {
    minZ = 44.169,
    maxZ = 46.169
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Garbage Collection',
        icon = 'fas fa-clock',
        label = 'Clock in - Garbage Collection',
        groups = { 'Civilian' },
      },
  --   {
  --    event_server = 'core:target-backhaul:server:postalClothes',
  --      icon = 'fas fa-tshirt',
  --   label = 'Work Uniform',
  --    groups = { 'Postal Worker' },
  --  },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',
        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 3.0
  })