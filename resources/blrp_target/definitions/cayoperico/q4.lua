--[[
AddTargetModel({
  `v_ilev_body_parts`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-search',
      label = 'Search',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
    }
  },
  distance = 2.0
})

AddBoxZone('CayoLaptop', vector3(5266.497, -5431.105, 140.7575), 0.5, 0.5, {
  name = 'CayoLaptop',
  heading = 155.0,
  minZ = 140.6,
  maxZ = 141.1,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-circle-arrow-right',
      label = 'Use',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
    },
  },
  distance = 3.0
})

-- Stage 3 components

AddBoxZone('CayoGlobe', vector3(5015.477, -5752.459, 27.8444), 1.3, 1.3, {
  name = 'CayoGlobe',
  heading = 330.0,
  minZ = 27.8,
  maxZ = 29.3,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'globe',
    },
  },
  distance = 3.0
})

AddBoxZone('CayoKeypad', vector3(5009.66, -5758.911, 28.93), 0.3, 0.3, {
  name = 'CayoKeypad',
  heading = 330.0,
  minZ = 28.7,
  maxZ = 29.2,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'keypad',
    },
  },
  distance = 3.0,
  context_disable = true,
})


AddTargetModel({
  `h4_prop_door_safe_01`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'safe',
    }
  },
  distance = 0.75
})

-- Obelisks

AddBoxZone('CayoObelisk1', vector3(4273.732, -4364.488, 22.52279), 1.5, 1.5, {
  name = 'CayoObelisk1',
  heading = 30.0,
  minZ = 21.5,
  maxZ = 22.8,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'obelisk1',
    },
  },
  distance = 3.0,
  context_disable = true,
})

AddBoxZone('CayoObelisk2', vector3(5324.35, -4882.799, 16.14284), 1.5, 1.5, {
  name = 'CayoObelisk2',
  heading = 30.0,
  minZ = 14.1,
  maxZ = 16.5,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'obelisk2',
    },
  },
  distance = 3.0,
  context_disable = true,
})

AddBoxZone('CayoObelisk3', vector3(4593.767, -4872.868, 18.00115), 1.5, 1.5, {
  name = 'CayoObelisk3',
  heading = 0.0,
  minZ = 16.7,
  maxZ = 18.2,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'obelisk3',
    },
  },
  distance = 3.0,
  context_disable = true,
})

AddBoxZone('CayoObelisk4', vector3(5583.21, -5802.597, 12.15926), 1.5, 1.5, {
  name = 'CayoObelisk4',
  heading = 0.0,
  minZ = 10.9,
  maxZ = 12.4,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'obelisk4',
    },
  },
  distance = 3.0,
  context_disable = true,
})

AddBoxZone('CayoQ4Loot', vector3(5004.061, -5753.174, 14.74676), 1.0, 1.0, {
  name = 'CayoQ4Loot',
  heading = 58.0,
  minZ = 14.4,
  maxZ = 15.2,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-eye',
      label = 'Inspect',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      quest_id = '04',
      quest_component = 'lootbox',
    },
  },
  distance = 3.0,
  context_disable = true,
})
]]
