disableContext(true)

--[[
AddBoxZone('CayoBusLockers', vector3(4989.411, -5206.015, 1.587319), 0.8, 1.6, {
  name = 'CayoBusLockers',
  heading = 125.0,
  minZ = 1.5,
  maxZ = 3.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Cayo Perico',
      },
    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'fa-regular fa-tshirt',
      label = 'Clothing Store',

      groups = {
        'Cayo Perico',
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Cayo Perico',
      component_id = 'custom3',
      component_perm = 'extra_b',
    },

  },
  distance = 3.0
})

AddBoxZone('CayoBusPrisA', vector3(4964.54346, -5761.104, 19.9295883), 0.6, 0.85, {
  name = 'CayoBusPrisA',
  heading = 115.0,
  minZ = 19.9,
  maxZ = 20.65,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Cayo Perico',
      component_id = 'custom4',
      component_perm = 'extra_b',
    },

  },
  distance = 3.0
})

AddBoxZone('CayoBusPrisB', vector3(4986.69727, -5763.737, 19.95636), 0.6, 0.85, {
  name = 'CayoBusPrisB',
  heading = 85.0,
  minZ = 19.9,
  maxZ = 20.65,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Cayo Perico',
      component_id = 'custom5',
      component_perm = 'extra_b',
    },

  },
  distance = 3.0
})

]]

--------------------------------------------------------------------------------
------------------------------------ CARTEL ------------------------------------
--------------------------------------------------------------------------------

AddBoxZone('CayoBusCartelA', vector3(4967.082, -5601.14355, 22.6910324), 1.3, 1.7, {
  name = 'CayoBusCartelA',
  heading = 165.0,
  minZ = 22.6,
  maxZ = 24.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Cayo Perico',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 1000.0,
        named = 'CP23CartelStorage1',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('CayoBusCartelB', vector3(5002.95557, -5711.41, 18.9635353), 1.3, 1.7, {
  name = 'CayoBusCartelB',
  heading = 130.0,
  minZ = 18.9,
  maxZ = 20.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Cartel Management',

      business_name = 'Cayo Perico',
      component_id = 'management',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Cayo Perico',
      },
    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'fa-regular fa-tshirt',
      label = 'Clothing Store',

      groups = {
        'Cayo Perico',
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Cayo Perico',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 1000.0,
        named = 'CP23CartelStorage2',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('CayoBusCartelC', vector3(5197.1084, -5133.113, 2.25328326), 1.1, 2.3, {
  name = 'CayoBusCartelC',
  heading = 78.0,
  minZ = 2.5,
  maxZ = 3.6,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Cayo Perico',
      },
    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'fa-regular fa-tshirt',
      label = 'Clothing Store',

      groups = {
        'Cayo Perico',
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Cayo Perico',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 1000.0,
        named = 'CP23CartelStorage3',
      },
    },
  },
  distance = 3.0
})
