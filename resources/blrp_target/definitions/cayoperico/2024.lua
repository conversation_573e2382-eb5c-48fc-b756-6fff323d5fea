--[[
disableContext(true)

AddBoxZoneAutoname(vector4(4993.515, -5198.968, 1.522293, 305.0), 0.8, 1.1, {
  minZ = 1.5,
  maxZ = 3.3,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Buy Supplies',

      uid = 'cp24-q1-supplies',
    },
  },
  distance = 3.0
})

AddTargetModel({
  `bl_cayo_chest_01a`,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:lootChest',
      icon = 'far fa-search',
      label = 'Search',
    },
  },
  distance = 6.0
})

AddBoxZoneAutoname(vector4(5603.977, -5670.099, 9.88141, 40.0), 0.9, 1.9, {
  minZ = 9.0,
  maxZ = 10.0,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:searchLighthouseCrate',
      icon = 'far fa-search',
      label = 'Search',
    },
  },
  distance = 3.0
})

AddTargetModel({
  `prop_box_wood01a`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:searchLootable',
      icon = 'fa-regular fa-search',
      label = 'Search',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      freeze = true,
    }
  },
  distance = 2.0
})

AddTargetModel({
  `prop_vintage_pump`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:useGasPump',
      icon = 'fa-regular fa-gas-pump',
      label = 'Use',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      freeze = true,
    }
  },
  distance = 2.0
})

AddBoxZoneAutoname(vector4(4482.24658, -4593.163, 4.55403233, -22.0), 0.8, 1.0, {
  minZ = 4.5,
  maxZ = 5.2,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:takeRewardCrate',
      icon = 'fas fa-arrow-up',
      label = 'Take',

      filter = function()
        return (exports.blrp_core:me().get('c24quest:Completion:stage') or 1) == 2
      end
    },
  },
  distance = 3.0
})

AddTargetModel({
  `prop_beach_fire`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:cookFire',
      icon = 'fa-regular fa-fire',
      label = 'Cook',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      freeze = true,
    }
  },
  distance = 2.0
})
]]