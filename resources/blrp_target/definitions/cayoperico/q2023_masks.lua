--[[

disableContext(true)

AddBoxZone('CP23Q1_01', vector3(4473.7876, -4595.31738, 4.50743246), 1.8, 1.0, {
  heading = 20.0,
  minZ = 4.5,
  maxZ = 6.0,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:inspectTent',
      icon = 'fa-regular fa-search',
      label = 'Search',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:Masks:stage') or 1) <= 2 and exports.blrp_zones:IsInsideMatchedZone('CayoQ104')
      end,
    },
  },
  distance = 2.0
})

AddBoxZone('CP23Q1_02', vector3(4576.5459, -5141.04053, 2.00332165), 0.5, 0.5, {
  heading = 0.0,
  minZ = 1.8,
  maxZ = 2.5,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:takeShell',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:Masks:stage') or 1) == 5 and IsIplActive('h4_bl_ipl_conch')
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CP23Q1_03', vector3(5210.11719, -5391.8501, 57.8733406), 0.75, 0.75, {
  heading = 0.0,
  minZ = 57.0,
  maxZ = 58.5,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:takeStone',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:Masks:stage') or 1) == 13 and IsIplActive('h4_bl_ipl_stone')
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CP23Q1_04', vector3(4479.30566, -4596.58545, 4.65830755), 2.0, 2.0, {
  heading = 20.0,
  minZ = 4.5,
  maxZ = 6.0,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:takeDynamite',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',

      filter = function()
        local stage = (exports.blrp_core:me().get('cp23quest:Masks:stage') or 1)

        return stage == 14 or stage == 15
      end
    },
  },
  distance = 3.0
})

]]
