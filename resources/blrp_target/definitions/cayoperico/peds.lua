--[[
AddTargetModel({
  `s_m_m_fieldworker_01`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-comment-dots',
      label = 'Talk',

      zones = {
        'CayoPed01', -- In blrp_zones
      },

      quest_id = '01',
    },
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-comment-dots',
      label = 'Talk',

      zones = {
        'CayoPed03', -- In blrp_zones
      },

      quest_id = '03',
    },
  },
  distance = 2.0
})

AddTargetModel({
  `g_m_m_cartelguards_02`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:talkToQuestPed',
      icon = 'fa-regular fa-comment-dots',
      label = 'Talk',

      zones = {
        'CayoPed02', -- In blrp_zones
      },

      quest_id = '02',
    },
  },
  distance = 2.0
})
]]

AddTargetModel({
  `a_c_panther`,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:pantherHarvest',
      icon = 'fas fa-shovel',
      label = 'Harvest',

      filter = function(entity, coords)
        return GetEntityHealth(entity) == 0
      end,
    },
  },
  distance = 2.5
})
