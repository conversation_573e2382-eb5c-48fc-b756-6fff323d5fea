--[[

disableContext(true)

AddBoxZone('CP23Q3_01', vector3(2969.328, 5212.981, 131.0887), 0.5, 0.5, {
  heading = 0.0,
  minZ = 131.0,
  maxZ = 131.4,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:searchPlane',
      icon = 'fa-regular fa-search',
      label = 'Search',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:FlyUS:stage') or 1) == 2
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CP23Q3_02', vector3(3803.43, -4735.235, -1.767787), 0.5, 0.5, {
  heading = 0.0,
  minZ = -1.9,
  maxZ = -1.5,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:searchPlane',
      icon = 'fa-regular fa-search',
      label = 'Search',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:FlyUS:stage') or 1) == 3
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CP23Q3_03', vector3(-560.5651, -1689.374, 20.25282), 0.5, 0.5, {
  heading = 0.0,
  minZ = 20.0,
  maxZ = 20.4,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:searchPlane',
      icon = 'fa-regular fa-search',
      label = 'Search',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:FlyUS:stage') or 1) == 4
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CP23Q3_04', vector3(5497.408, -5641.476, 22.19317), 0.5, 0.5, {
  heading = 0.0,
  minZ = 22.0,
  maxZ = 22.4,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:searchPlane',
      icon = 'fa-regular fa-search',
      label = 'Search',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:FlyUS:stage') or 1) == 5
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CP23Q3_05', vector3(1940.367, 3548.306, 39.57777), 0.5, 0.5, {
  heading = 0.0,
  minZ = 39.3,
  maxZ = 39.7,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:searchPlane',
      icon = 'fa-regular fa-search',
      label = 'Search',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:FlyUS:stage') or 1) == 6
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CP23Q3_06', vector3(-897.274, -2983.719, 14.3027), 2.5, 2.5, {
  heading = 60.0,
  minZ = 12.3,
  maxZ = 15.3,
}, {
  options = {
    {
      event_server = 'blrp_cayo:quest:burn',
      icon = 'fa-regular fa-fire',
      label = 'Light Fire',

      filter = function()
        return (exports.blrp_core:me().get('cp23quest:FlyUS:stage') or 1) == 8
      end
    },
  },
  distance = 3.0
})

]]
