--[[

AddTargetModel({
  `prop_beach_fire`
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:cookFire',
      icon = 'fa-regular fa-fire',
      label = 'Cook',

      zones = {
        'CayoExclusionInner', -- In blrp_zones
      },

      freeze = true,
    }
  },
  distance = 2.0
})

AddBoxZone('CayoPostOpContainer', vector3(-513.212, -2848.867, 6.422493), 1.0, 3.0, {
  name = 'CayoPostOpContainer',
  heading = 225.0,
  minZ = 5.0,
  maxZ = 8.0,
}, {
  options = {
    {
      event_server = 'blrp_cayo:server:takeStoragePostOp',
      icon = 'fa-regular fa-container-storage',
      label = 'Take Cargo',
    },
  },
  distance = 3.0
})

]]
