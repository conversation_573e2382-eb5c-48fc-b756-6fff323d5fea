--for k, skull in ipairs({
--  vector3(-2155.111, 2681.514, 2.736713),
--  vector3(-2164.395, 2657.938, 2.020111),
--  vector3(-2096.948, 2661.826, 1.942975),
--  vector3(-2094.734, 2687.532, 2.303839),
--  vector3(-2127.087, 2690.206, 2.096335),
--  vector3(-2166.461, 2697.528, 2.024307),
--}) do
--  local wh = 1.0
--
--  if k == 1 then
--    wh = 1.5
--  end
--
--  AddBoxZone('HalloweenSkull' .. k, skull, wh, wh, {
--    name = 'HalloweenSkull' .. k,
--    heading = 0.0,
--    minZ = skull.z - 0.5,
--    maxZ = skull.z + 0.5,
--  }, {
--    options = {
--      {
--        event_server = 'blrp_halloween:pickupSkull',
--        icon = 'fas fa-arrow-up',
--        label = 'Take',
--      },
--    },
--    distance = 4.0
--  })
--end
--
--local vending_machines = {
--  { coords = vector3(-2198.038, 2678.343, 4.391903), product = 'clth_hw23_mask_a1' },
--  { coords = vector3(-2198.362, 2676.505, 4.353750), product = 'clth_hw23_mask_a2' },
--  { coords = vector3(-2198.719, 2674.480, 4.379759), product = 'clth_hw23_mask_b1' },
--  { coords = vector3(-2199.047, 2672.624, 4.353897), product = 'clth_hw23_mask_b2' },
--  { coords = vector3(-2199.364, 2670.826, 4.318375), product = 'clth_hw23_mask_b3' },
--}
--
--for k, vending_machine in ipairs(vending_machines) do
--  AddBoxZone('HalloweenVending' .. k, vending_machine.coords, 2.0, 1.5, {
--    name = 'HalloweenVending' .. k,
--    heading = -100.0,
--    minZ = vending_machine.coords.z - 2.0,
--    maxZ = vending_machine.coords.z,
--  }, {
--    options = {
--      {
--        event_server = 'blrp_halloween:redeemPrize',
--        icon = 'fas fa-coin',
--        label = 'Redeem',
--        item_id = vending_machine.product
--      }
--    },
--    distance = 3.0,
--  })
--end
