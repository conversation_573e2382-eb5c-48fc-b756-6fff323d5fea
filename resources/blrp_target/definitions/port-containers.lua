local containers = {
  { id = 1,  model = `prop_container_05mb`, coords = vector3(824.3901, -3098.872, 10.4820), heading = -90 },
  { id = 2,  model = `prop_container_05mb`, coords = vector3(822.2559, -3074.553, 7.71470), heading =  90 },
  { id = 3,  model = `prop_container_03a`,  coords = vector3(837.6259, -3093.277, 10.4788), heading = -90 },
  { id = 4,  model = `prop_container_03mb`, coords = vector3(850.8469, -3082.637, 10.5065), heading = -90 },
  { id = 5,  model = `prop_container_03b`,  coords = vector3(837.3631, -3082.583, 13.3309), heading =  90 },
  { id = 6,  model = `prop_container_01mb`, coords = vector3(851.6387, -3098.882, 4.88570), heading =  90 },
  { id = 7,  model = `prop_container_01mb`, coords = vector3(902.8792, -3085.389, 4.90580), heading =  90 },
  { id = 8,  model = `prop_container_03a`,  coords = vector3(900.9003, -3082.734, 10.4938), heading = -90 },
  { id = 9,  model = `prop_container_03a`,  coords = vector3(916.9121, -3101.401, 13.2993), heading = -90 },
  { id = 10, model = `prop_container_03a`,  coords = vector3(905.5289, -3093.340, 10.4871), heading =  90 },
  { id = 11, model = `prop_container_03b`,  coords = vector3(930.1616, -3082.805, 10.5090), heading = -90 },
  { id = 12, model = `prop_container_05a`,  coords = vector3(946.8386, -3096.102, 13.3110), heading = -90 },
  { id = 13, model = `prop_container_03a`,  coords = vector3(946.2171, -3028.895, 10.4780), heading = -90 },
  { id = 14, model = `prop_container_03a`,  coords = vector3(901.2581, -3028.928, 7.66850), heading = -90 },
  { id = 15, model = `prop_container_01d`,  coords = vector3(930.9241, -2981.816, 4.89330), heading = -90 },
  { id = 16, model = `prop_container_03b`,  coords = vector3(961.4201, -2989.938, 7.58210), heading =  90 },
  { id = 17, model = `prop_container_03b`,  coords = vector3(933.6097, -2976.538, 10.4837), heading =  90 },
  { id = 18, model = `prop_container_03b`,  coords = vector3(1014.332, -2987.196, 10.4921), heading =  90 },
  { id = 19, model = `prop_container_03b`,  coords = vector3(1040.192, -2973.518, 10.5157), heading =  90 },
  { id = 20, model = `prop_container_03b`,  coords = vector3(1002.184, -2978.990, 13.3264), heading =  90 },
  { id = 21, model = `prop_container_03b`,  coords = vector3(1057.760, -2992.436, 13.3097), heading =  90 },
  { id = 22, model = `prop_container_01g`,  coords = vector3(1041.214, -3031.600, 4.88400), heading =  90 },
  { id = 23, model = `prop_container_01g`,  coords = vector3(999.5804, -3037.084, 4.85820), heading =  90 },
  { id = 24, model = `prop_container_03mb`, coords = vector3(1002.016, -3026.205, 10.5084), heading =  90 },
  { id = 25, model = `prop_container_01g`,  coords = vector3(999.3162, -3090.608, 4.88540), heading = -90 },
  { id = 26, model = `prop_container_03mb`, coords = vector3(1052.766, -3077.468, 13.3177), heading = -90 },
  { id = 27, model = `prop_container_03mb`, coords = vector3(1052.688, -3101.413, 13.3233), heading = -90 },
  { id = 28, model = `prop_container_03mb`, coords = vector3(1097.694, -3037.172, 7.67530), heading = -90 },
  { id = 29, model = `prop_container_01d`,  coords = vector3(1114.358, -3031.570, 4.88090), heading = -90 },
  { id = 30, model = `prop_container_05mb`, coords = vector3(1146.585, -3044.646, 10.4942), heading =  90 },
  { id = 31, model = `prop_container_03mb`, coords = vector3(1153.792, -3023.663, 10.5015), heading = -90 },
  { id = 32, model = `prop_container_03mb`, coords = vector3(1131.128, -2984.301, 10.4927), heading =  90 },
  { id = 33, model = `prop_container_03b`,  coords = vector3(1103.266, -2994.867, 7.67340), heading =  90 },
  { id = 34, model = `prop_container_03a`,  coords = vector3(1098.126, -2968.711, 13.3258), heading = -90 },
  { id = 35, model = `prop_container_03mb`, coords = vector3(1159.038, -2970.753, 10.5068), heading =  90 },
  { id = 36, model = `prop_container_03b`,  coords = vector3(905.4971, -2984.579, 7.66492), heading =  90 },

  --Merryweather docks
  { id = 37, model = `prop_container_05mb`, coords = vector3(628.2859, -2958.330, 7.8577), heading =  90 },
  { id = 38, model = `prop_container_03a`, coords = vector3(621.3587, -2966.309, 10.6777), heading =  -90 },
  { id = 39, model = `prop_container_03b`, coords = vector3(612.704, -2966.30029, 10.6777554), heading =  90 },
  { id = 40, model = `prop_container_03a`, coords = vector3(578.2358, -2958.49, 10.6744347), heading =  90 },
  { id = 41, model = `prop_container_05mb`, coords = vector3(563.720154, -2969.055, 10.6744347), heading =  -90 },
  { id = 42, model = `prop_container_03mb`, coords = vector3(533.3919, -2959.96851, 16.3160667), heading =  -90 },
  { id = 43, model = `prop_container_03a`, coords = vector3(552.103333, -2958.065, 13.4949083), heading =  90 },
  { id = 44, model = `prop_container_01d`, coords = vector3(610.0273, -2968.878, 5.034836), heading =  90 },
  { id = 45, model = `prop_contr_03b_ld`, coords = vector3(635.3756, -2958.08569, 5.044006), heading =  90 },
  { id = 46, model = `prop_container_03b`, coords = vector3(575.9404, -3002.73657, 10.681076), heading =  -90 },
  { id = 47, model = `prop_container_03mb`, coords = vector3(564.6227, -2994.828, 10.681076), heading =  90 },
  { id = 48, model = `prop_container_03b`, coords = vector3(534.7544, -3000.1106, 13.5018044), heading =  -90 },
  { id = 49, model = `prop_container_03mb`, coords = vector3(546.896851, -2995.06152, 13.5018044), heading =  -90 },
  { id = 50, model = `prop_container_05mb`, coords = vector3(504.490845, -2995.07, 10.6798792), heading =  90 },
  { id = 50, model = `prop_container_03mb`, coords = vector3(490.21106, -3000.38477, 10.6798792), heading =  90 },
  { id = 51, model = `prop_container_03a`, coords = vector3(476.177856, -2968.881, 10.67375), heading =  90 },
  { id = 52, model = `prop_container_03mb`, coords = vector3(491.00647, -2966.30737, 10.6791134), heading =  90 },
  { id = 53, model = `prop_container_05mb`, coords = vector3(505.791, -2960.989, 11.670), heading =  90 },
}

for container_id, container_data in ipairs(containers) do
  local extent_minimum, extent_maximum = GetModelDimensions(container_data.model)

  local length = math.abs(extent_minimum.y * 2) + 0.05
  local width = math.abs(extent_minimum.x * 2) + 0.05

  AddBoxZone('PortContainer' .. container_id, container_data.coords, length, width, {
    name = 'PortContainer' .. container_id,
    heading = container_data.heading,
    minZ = container_data.coords.z - extent_minimum.z,
    maxZ = container_data.coords.z + extent_maximum.z,
  }, {
    options = {
      {
        event_server = 'core:server:port-containers:initiate',
        icon = 'fas fa-container-storage',
        label = 'Break In',

        zones = {
          common_zones.PortOfLosSantos,
          common_zones.MerryweatherPort
        },

        filter = function()
          local entity_position = container_data.coords

          local ped_coords = GetEntityCoords(PlayerPedId())

          local arctan = math.atan2(ped_coords.x - entity_position.x, ped_coords.y - entity_position.y)

          local degrees = math.deg(arctan)

          if degrees < 0 then
            degrees = degrees + 360
          end

          local max_angle = 192 + container_data.heading
          local min_angle = 168 + container_data.heading

          if degrees < min_angle then
            return false
          end

          if degrees > max_angle then
            return false
          end

          return true
        end,

        container_id = container_data.id,
      },
    },
    distance = (length / 2) + 0.85
  })
end
