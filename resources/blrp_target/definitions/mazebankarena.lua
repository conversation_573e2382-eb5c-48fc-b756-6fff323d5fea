AddBoxZone('MBAClothingStore', vector3(-277.7735, -2011.721, 21.73527), 0.5, 2, {
  name = 'MBAClothingStore',
  heading = 140.0,
  minZ = 21.0,
  maxZ = 22.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'fa-regular fa-tshirt',
      label = 'Clothing Store',

    },

  },
  distance = 3.0
})

AddBoxZone('MBABarber', vector3(-277.3515, -2016.229, 21.75627), 1.3, 0.2, {
  name = 'MBABarber',
  heading = 145,
  minZ = 21.5,
  maxZ = 22.9,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  distance = 3.0,
})

AddBoxZone('MBAClothingStore2', vector3(-273.5131, -2006.787, 21.74445), 0.5, 2, {
  name = 'MBAClothingStore2',
  heading = 140.0,
  minZ = 21.0,
  maxZ = 22.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'fa-regular fa-tshirt',
      label = 'Clothing Store',

    },

  },
  distance = 3.0
})

AddBoxZone('MBABarber2', vector3(-269.0103, -2006.288, 22.03773), 1.3, 0.2, {
  name = 'MBABarber2',
  heading = 145,
  minZ = 21.5,
  maxZ = 22.9,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  distance = 3.0,
})
