local toilets = {
  { type = 'toilet', coords = vector3(335.1083, -585.7845, 42.72556), },
  { type = 'toilet', coords = vector3(333.5486, -585.1994, 42.72556), },
  { type = 'toilet', coords = vector3(331.9794, -584.6855, 42.72556), },
  { type = 'toilet', coords = vector3(330.4184, -584.0695, 42.72556), },
  { type = 'toilet', coords = vector3(341.5706, -594.0756, 42.96817), },
  { type = 'toilet', coords = vector3(474.3174, -980.0053, 30.12791), },
  { type = 'toilet', coords = vector3(473.199, -979.9922, 30.12791), },
  { type = 'toilet', coords = vector3(472.0031, -979.9691, 30.12791), },
  { type = 'toilet', coords = vector3(477.0451, -1015.266, 25.50969), },
  { type = 'toilet', coords = vector3(480.11, -1015.199, 25.50737), },
  { type = 'toilet', coords = vector3(483.1143, -1015.189, 25.56138), },
  { type = 'toilet', coords = vector3(486.2426, -1015.229, 25.50737), },
  { type = 'toilet', coords = vector3(223.903, -1656.228, 29.15131), },
  { type = 'toilet', coords = vector3(221.8406, -1658.706, 29.13334), },
  { type = 'toilet', coords = vector3(1048.726, 47.96965, 70.54091), },
  { type = 'toilet', coords = vector3(1047.23, 48.87671, 70.54091), },
  { type = 'toilet', coords = vector3(1045.725, 49.86201, 70.54091), },
  { type = 'toilet', coords = vector3(1044.292, 50.73283, 70.54091), },
  { type = 'urinal', coords = vector3(1041.579, 46.30049, 70.94066), },
  { type = 'urinal', coords = vector3(1042.461, 45.74858, 70.90274), },
  { type = 'urinal', coords = vector3(1043.31, 45.21743, 70.9222), },
  { type = 'urinal', coords = vector3(1044.163, 44.68324, 70.91917), },
  { type = 'urinal', coords = vector3(1045.012, 44.1517, 70.88183), },
  { type = 'urinal', coords = vector3(1045.884, 43.60586, 70.91415), },
  { type = 'urinal', coords = vector3(1031.733, 89.27757, 70.92022), },
  { type = 'urinal', coords = vector3(1032.252, 90.10786, 70.90459), },
  { type = 'urinal', coords = vector3(1032.791, 90.96799, 70.84641), },
  { type = 'urinal', coords = vector3(1033.323, 91.81915, 70.86293), },
  { type = 'urinal', coords = vector3(1033.847, 92.65587, 70.84071), },
  { type = 'urinal', coords = vector3(1034.384, 93.51284, 70.84925), },
  { type = 'toilet', coords = vector3(1038.838, 90.86121, 70.5406), },
  { type = 'toilet', coords = vector3(1037.906, 89.38049, 70.5406), },
  { type = 'toilet', coords = vector3(1036.993, 87.89254, 70.5406), },
  { type = 'toilet', coords = vector3(1036.064, 86.42078, 70.5406), },
  { type = 'toilet', coords = vector3(-1390.836, -1323.714, 4.3138), },
  { type = 'toilet', coords = vector3(-1389.377, -1323.973, 4.273054), },
  { type = 'toilet', coords = vector3(-1388.058, -1324.208, 4.254759), },
  { type = 'toilet', coords = vector3(-1386.653, -1324.457, 4.224793), },
  { type = 'toilet', coords = vector3(-1384.464, -1324.846, 4.288508), },
  { type = 'toilet', coords = vector3(-1385.604, -1331.111, 4.32054), },
  { type = 'toilet', coords = vector3(-1387.802, -1330.72, 4.258579), },
  { type = 'toilet', coords = vector3(-1389.157, -1330.48, 4.209602), },
  { type = 'toilet', coords = vector3(-1390.5, -1330.241, 4.219331), },
  { type = 'toilet', coords = vector3(-1391.955, -1329.983, 4.199886), },
  { type = 'toilet', coords = vector3(814.6642, -289.3764, 66.50713), },
  { type = 'toilet', coords = vector3(813.2192, -281.5371, 66.5715), },
  { type = 'toilet', coords = vector3(-1414.64, -622.1282, 29.86548), },
  { type = 'toilet', coords = vector3(-1416.096, -623.1124, 29.97058), },
  { type = 'toilet', coords = vector3(-1417.57, -624.0716, 29.97609), },
  { type = 'toilet', coords = vector3(-1419.029, -625.0127, 29.96133), },
  { type = 'toilet', coords = vector3(-1417.783, -611.8121, 29.98513), },
  { type = 'toilet', coords = vector3(-1418.739, -610.348, 29.99631), },
  { type = 'toilet', coords = vector3(-1419.685, -608.8917, 29.99848), },
  { type = 'toilet', coords = vector3(-1420.64, -607.4334, 30.01443), },
  { type = 'urinal', coords = vector3(-1416.131, -604.7064, 30.69738), },
  { type = 'urinal', coords = vector3(-1415.147, -605.2165, 30.4077), },
  { type = 'urinal', coords = vector3(-1414.67, -605.9507, 30.40944), },
  { type = 'urinal', coords = vector3(-1414.071, -606.8731, 30.41655), },
  { type = 'urinal', coords = vector3(-1413.53, -607.7063, 30.39565), },
  { type = 'urinal', coords = vector3(-1412.941, -608.6116, 30.42422), },
  { type = 'toilet', coords = vector3(-1704.947, -1130.822, 13.32166), },
  { type = 'toilet', coords = vector3(-1703.328, -1128.016, 13.25857), },
  { type = 'toilet', coords = vector3(-1628.483, -1044.681, 13.29527), },
  { type = 'toilet', coords = vector3(-1629.623, -1043.724, 13.28819), },
  { type = 'toilet', coords = vector3(-1630.797, -1042.74, 13.2437), },
  { type = 'toilet', coords = vector3(-1631.993, -1041.736, 13.22638), },
  { type = 'toilet', coords = vector3(-1634.385, -1039.729, 13.24079), },
  { type = 'toilet', coords = vector3(-1635.583, -1038.723, 13.29918), },
  { type = 'toilet', coords = vector3(-1636.779, -1037.721, 13.2984), },
  { type = 'toilet', coords = vector3(-1637.961, -1036.728, 13.29096), },
  { type = 'toilet', coords = vector3(-721.399, 271.6743, 84.08896), },
  { type = 'toilet', coords = vector3(-1202.098, -887.5541, 13.13903), },
  { type = 'toilet', coords = vector3(-1200.606, -886.5776, 13.12349), },
  { type = 'urinal', coords = vector3(-1202.219, -891.0374, 13.61648), },
  { type = 'urinal', coords = vector3(-1201.617, -891.924, 13.54443), },
  { type = 'toilet', coords = vector3(-1204.677, -889.4102, 13.12349), },
  { type = 'toilet', coords = vector3(-1206.075, -890.379, 13.12349), },
  { type = 'toilet', coords = vector3(365.1884, -1606.594, 24.89132), },
  { type = 'toilet', coords = vector3(366.4593, -1607.721, 24.87862), },
  { type = 'toilet', coords = vector3(1841.594, 2572.025, 45.305), },
  { type = 'toilet', coords = vector3(1850.97, 3691.421, 29.30468), },
  { type = 'toilet', coords = vector3(1829.818, 3691.169, 33.71074), },
  { type = 'toilet', coords = vector3(1827.675, 3689.954, 33.71029), },
  { type = 'toilet', coords = vector3(1576.369, 3605.474, -99.76595), },
  { type = 'toilet', coords = vector3(129.4067, 6646.152, 31.02691), },
  { type = 'toilet', coords = vector3(131.3925, 6644.076, 31.02685), },
  { type = 'toilet', coords = vector3(132.4813, 6642.976, 31.02792), },
  { type = 'urinal', coords = vector3(130.9888, 6643.651, 31.67746), },
  { type = 'urinal', coords = vector3(129.2755, 6645.366, 31.65329), },
  { type = 'toilet', coords = vector3(-424.5316, 5998.707, 31.18065), },
  { type = 'toilet', coords = vector3(-427.6324, 6001.824, 31.18591), },
  { type = 'toilet', coords = vector3(-425.1226, 1223.975, 325.8426), },
  { type = 'toilet', coords = vector3(-424.915, 1217.29, 325.84), },
  { type = 'toilet', coords = vector3(-426.7627, 1217.783, 325.8267), },
  { type = 'toilet', coords = vector3(-426.4523, 1211.371, 325.7664), },
  { type = 'toilet', coords = vector3(-428.2727, 1211.857, 325.7352), },
  { type = 'toilet', coords = vector3(-430.0428, 1205.705, 325.7685), },
  { type = 'toilet', coords = vector3(333.5578, -592.0555, 42.72163), },
  { type = 'toilet', coords = vector3(332.0104, -591.5898, 42.72163), },
  { type = 'toilet', coords = vector3(330.5222, -590.9695, 42.72163), },
  { type = 'toilet', coords = vector3(328.9612, -590.3805, 42.72163), },
}

if GlobalState.toilets_enabled then
  for _, v in pairs(toilets) do
    AddBoxZone('Toilet' .. _, v.coords, 1.0, 1.0, {
      heading = 0,
      minZ = v.coords.z - 0.5,
      maxZ = v.coords.z + 0.5,
    }, {
      options = {
        {
          event_server = 'blrp_core:server:basic-needs:useToilet',
          icon = 'fa-regular fa-toilet',
          label = 'Use ' .. v.type,
          toilet_type = v.type,
        }
      },
      distance = 2.0,
    })
  end
end
