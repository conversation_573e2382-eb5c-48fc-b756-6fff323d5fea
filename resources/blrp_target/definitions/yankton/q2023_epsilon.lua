--[[
disableContext(true)

AddBoxZoneAutoname(vector4(4288.56, -4731.918, 112.070244, 0), 0.5, 0.5, {
  minZ = 111.8,
  maxZ = 112.4,
}, {
  options = {
    {
      event_server = 'blrp_yankton:quest:takeHeadphones',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',

      filter = function()
        return (exports.blrp_core:me().get('ny23quest:Epsilon:stage') or 1) == 2 and IsIplActive('2023_ny_q1_s1')
      end
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(3860.551, -4958.986, 92.998, 0), 1.5, 1.5, {
  minZ = 90.8,
  maxZ = 92.5,
}, {
  options = {
    {
      event_server = 'blrp_yankton:quest:inspectAlien',
      icon = 'fa-solid fa-magnifying-glass',
      label = 'Inspect',

      filter = function()
        return (exports.blrp_core:me().get('ny23quest:Epsilon:stage') or 1) == 3
      end
    },
  },
  distance = 2.5
})

AddTargetModel({
  `prop_ny23_plant_a`,
}, {
  options = {
    {
      event_server = 'blrp_yankton:quest:harvestSeeds',
      icon = 'fas fa-shovel',
      label = 'Harvest',

      filter = function()
        return (exports.blrp_core:me().get('ny23quest:Epsilon:stage') or 1) >= 4
      end
    },
  },
  distance = 2.5
})
]]
