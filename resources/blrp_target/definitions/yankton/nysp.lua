--[[
AddBoxZone('NYSPMainDesk', vector3(5962.128, -5223.997, 85.57606), 0.7, 0.7, {
  name = 'NYSPMainDesk',
  heading = 30.0,
  minZ = 85.5,
  maxZ = 85.7,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'NYSP',

      icon = 'fas fa-clock',
      label = 'Clock in - NYSP',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },

  distance = 3.0
})

AddBoxZone('NYSPEquipmentRoom', vector3(5969.492, -5234.155, 86.12824), 0.6, 3.1, {
  name = 'NYSPEquipmentRoom',
  heading = 172.0,
  minZ = 84.6,
  maxZ = 87.0,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'NYSP Equipment',

      uid = 'leo-equipment',

      groups = {
        'NYSP',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-snowflake',
      label = 'NYSP Store',

      uid = 'leo-nysp-equipment',

      groups = {
        'NYSP',
      },
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request NYSP ID Card',
      card_type = 'id_nysp',

      groups = {
        'NYSP'
      }
    },
    {
      event_server = 'core:server:id-cards:requestFactionBadge',
      icon = 'far fa-id-card',
      label = 'Request NYSP Badge',
      card_type = 'prop_nysp_badge',

      groups = {
        'NYSP'
      }
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'NYSP Evidence Locker',

      chest_name = 'locker_nysp_evidence',
      chest_radius = 3.0,
      chest_weight = 5000.0,
      chest_permission = 'nysp.service',

      groups = {
        'NYSP'
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Lawton Junction',

      groups = {
        'NYSP',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('NYSPLocker', vector3(5961.018, -5233.559, 86.0873), 0.7, 2.8, {
  name = 'NYSPLocker',
  heading = 172.0,
  minZ = 84.6,
  maxZ = 87.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'NYSP Uniform Wardrobe',
      cloakroom_name = 'nysp',

      groups = {
        'NYSP',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'NYSP',
      },
    },
  },
  distance = 3.0
})

]]