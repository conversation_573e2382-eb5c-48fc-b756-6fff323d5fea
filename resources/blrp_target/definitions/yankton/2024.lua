--[[
for _,boilfire in pairs({
  {name="boilfire1", position=vector3(6065.971, -5301.11, 85.04297)},
  {name="boilfire2", position=vector3(6053.976, -5340.888, 84.89684)},
  {name="boilfire3", position=vector3(6032.639, -5226.11, 85.16994)},
  {name="boilfire4", position=vector3(4731.718, -5307.776, 107.3104)},
  {name="boilfire5", position=vector3(4436.361, -4905.044, 111.8352)},
  {name="boilfire6", position=vector3(3721.241, -4676.039, 114.4383)},
  {name="boilfire7", position=vector3(3136.336, -4818.317, 111.0986)}
  }) do
    
    AddBoxZone(boilfire.name, boilfire.position,1.0,1.0,{
      heading = 90.0,
      minZ = boilfire.position.z - 0.5 ,
      maxZ = boilfire.position.z + 0.5,
    }, {
      options = {
        {
          event_server = 'blrp_yankton:server:cookFire',
          icon = 'fa-regular fa-fire',
          label = 'Boil Water',
  
          table_name = boilfire.name
        },
      },
      distance = 2.5
    })
  
  end

  --gallery
  AddBoxZone('xm24_Gal1a', vector3(6088.02, -5275.72, 85.6), 1.6, 0.6, {
    heading = 350.0,
    minZ = 84.6,
    maxZ = 87.0,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_1a',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal1b', vector3(6088.19, -5273.62, 85.6), 1.8, 0.6, {
    heading = 350.0,
    minZ = 84.6,
    maxZ = 85.6,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_1b',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal1c', vector3(6088.82, -5271.32, 85.61), 1.8, 1.2, {
    heading = 355.0,
    minZ = 84.61,
    maxZ = 87.61,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_1c',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal2', vector3(6090.03, -5267.39, 85.61), 1.2, 1.6, {
    heading = 30.0,
    minZ = 84.61,
    maxZ = 87.01,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_2',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal3a', vector3(6093.6, -5267.88, 85.61), 1.2, 1.2, {
    heading = 350.0,
    minZ = 84.61,
    maxZ = 86.01,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-coin',
        label = 'Browse - Vouchers',

        uid = 'xm24gal_3a',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal3b', vector3(6096.68, -5268.38, 85.61), 1.2, 1.4, {
    heading = 350.0,
    minZ = 84.61,
    maxZ = 86.01,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-coin',
        label = 'Browse - Vouchers',

        uid = 'xm24gal_3b',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal4', vector3(6094.97, -5269.21, 85.61), 1.2, 1.8, {
    heading = 350.0,
    minZ = 84.61,
    maxZ = 85.61,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_4',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal5', vector3(6100.13, -5268.24, 85.61), 0.6, 2.0, {
    heading = 350.0,
    minZ = 84.61,
    maxZ = 87.01,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-coin',
        label = 'Browse - Vouchers',

        uid = 'xm24gal_5',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal6', vector3(6101.49, -5271.02, 85.61), 3.6, 0.8, {
    heading = 350.0,
    minZ = 84.61,
    maxZ = 87.61,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_6',
      },
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-coin',
        label = 'Browse - Vouchers',

        uid = 'xm24gal_7',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal7', vector3(6100.24, -5276.67, 85.6), 2.0, 0.8, {
    heading = 350.0,
    minZ = 84.6,
    maxZ = 87.0,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_9',
      },
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-coin',
        label = 'Browse - Vouchers',

        uid = 'xm24gal_8',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal8', vector3(6097.68, -5280.54, 85.61), 3.4, 1.2, {
    heading = 350.0,
    minZ = 84.61,
    maxZ = 85.81,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_10',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal9', vector3(6094.81, -5281.95, 85.6), 0.2, 2.6, {
    heading = 350.0,
    minZ = 85.6,
    maxZ = 87.6,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_11',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal10', vector3(6096.39, -5274.22, 85.6), 3.8, 1.8, {
    heading = 350.0,
    minZ = 84.6,
    maxZ = 86.0,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_12',
      },
    },
    distance = 2.5
  })
  
  AddBoxZone('xm24_Gal11', vector3(6092.39, -5274.29, 85.6), 3.6, 1.8, {
    heading = 355.0,
    minZ = 84.6,
    maxZ = 86.0,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-solid fa-dollar-sign',
        label = 'Browse',

        uid = 'xm24gal_13',
      },
    },
    distance = 2.5
  })

  --charity
  AddBoxZoneAutoname(vector4(6067.79, -5276.33, 85.88, 45), 1.8, 1.8, {
    minZ=84.88,
    maxZ=86.08,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-box',
          label = 'Storage',
  
          business_name = 'Blath Alainn Charity',
          component_id = 'storage',
  
          args = {
            capacity = 1000.0,
            named = 'xm24char1',
          },
        },
      },
      distance = 3.0,
      context_disable = true,
    })
  
  AddBoxZoneAutoname(vector4(6063.8, -5275.84, 85.88, 40), 0.8, 1.2, {
    minZ=84.88,
    maxZ=85.68,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-box',
          label = 'Medical Storage',
  
          business_name = 'Blath Alainn Charity',
          component_id = 'storage',
  
          args = {
            capacity = 500.0,
            named = 'xm24char2',
          },
        },
      },
      distance = 2.5,
      context_disable = true,
    })

    AddBoxZoneAutoname(vector4(6065.89, -5275.17, 85.88, 340), 1.2, 2.2, {
    minZ=84.88,
    maxZ=86.48,
    }, {
      options = {
        {
          event_server = 'core:server:restaurants:transformFood',
          icon = 'fa-regular fa-cup-straw',
          label = 'Make Hot Chocolate (x5)',
          component_perm = 'extra_a',
          food_item_id = 'xm24_hotchoc',

          groups = {
            'Blath Alainn Charity'
        },
      },
    },
    distance = 2.5,
    context_disable = true,
  })

  ]]