--[[
AddBoxZone('LSIALockers', vector3(-927.3922, -2935.211, 13.02979), 1.0, 1.8, {
  name = 'LSIALockers',
  heading = 330.0,
  minZ = 13.0,
  maxZ = 15.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'LSIA',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'FlyUS Uniform Wardrobe',
      cloakroom_name = 'FlyUS',

      groups = {
        'LSIA',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('LSIACupboard', vector3(-927.3, -2939.6, 12.92289), 1.0, 2.2, {
  name = 'LSIACupboard',
  heading = 330.0,
  minZ = 13.0,
  maxZ = 15.5,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'FlyUS Supply Cupboard',

      uid = 'flyus-cupboard',

      groups = {
        'LSIA',
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'FlyUS Storage',

      business_name = 'LSIA',
      component_id = 'storage',
    },
  },
  distance = 3.0
})

]]