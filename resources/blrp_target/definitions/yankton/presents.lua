--[[
AddBoxZone('PresentPickupLudendorff', vector3(3224.122, -4848.779, 111.2829), 3.0, 3.0, {
  name = 'PresentPickupLudendorff',
  heading = 205.0,
  minZ = 111.0,
  maxZ = 114.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Christmas Present Pickup',
}, {
  options = {
    {
      event_server = 'blrp_yankton:server:requestPresent',
      icon = 'fa-regular fa-gift',
      label = 'Take Present',
    },
  },
  distance = 3.0
})

AddBoxZone('PresentPickupLegion', vector3(177.91, -941.0, 29.44), 5.1, 2.0, {
  name = 'PresentPickupLegion',
  heading=20,
  minZ=28.04,
  maxZ=30.04,
  notifyText = '<i class="fa-regular fa-eye"></i> Christmas Present Pickup',
}, {
  options = {
    {
      event_server = 'blrp_yankton:server:requestPresent',
      icon = 'fa-regular fa-gift',
      label = 'Take Present',
    },
  },
  distance = 3.0
})

AddBoxZone('PresentPickupSandy', vector3(1970.492, 3712.068, 31.12373), 3.0, 3.0, {
  name = 'PresentPickupSandy',
  heading = 185.0,
  minZ = 31.0,
  maxZ = 34.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Christmas Present Pickup',
}, {
  options = {
    {
      event_server = 'blrp_yankton:server:requestPresent',
      icon = 'fa-regular fa-gift',
      label = 'Take Present',
    },
  },
  distance = 3.0
})

AddBoxZone('PresentPickupPaleto', vector3(-71.77, 6550.34, 31.49), 5.4, 2.5, {
  name = 'PresentPickupPaleto',
  heading=320,
  minZ=30.29,
  maxZ=32.09,
  notifyText = '<i class="fa-regular fa-eye"></i> Christmas Present Pickup',
}, {
  options = {
    {
      event_server = 'blrp_yankton:server:requestPresent',
      icon = 'fa-regular fa-gift',
      label = 'Take Present',
    },
  },
  distance = 3.0
})

AddBoxZone('PresentPickupLawton', vector3(6049.6665, -5286.99951, 84.66367), 3.0, 4.0, {
  name = 'PresentPickupLawton',
  heading = 259.0,
  minZ = 84.6,
  maxZ = 86.8,
  notifyText = '<i class="fa-regular fa-eye"></i> Christmas Present Pickup',
}, {
  options = {
    {
      event_server = 'blrp_yankton:server:requestPresent',
      icon = 'fa-regular fa-gift',
      label = 'Take Present',
    },
  },
  distance = 3.0
})

]]