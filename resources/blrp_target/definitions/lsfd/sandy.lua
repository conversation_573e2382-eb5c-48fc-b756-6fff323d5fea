AddBoxZone('LSFDLockerSandy1', vector3(1784.044, 3652.711, 34.92052), 0.5, 3.4, {
  name = 'LSFDLockerSandy1',
  heading = 30.0,
  minZ = 34.0,
  maxZ = 35.8,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSFD Uniform Wardrobe',
      cloakroom_name = 'emergency'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('LSFDLockerSandy2', vector3(1784.5, 3650.811, 34.99696), 0.7, 1.3, {
  name = 'LSFDLockerSandy2',
  heading = 30.0,
  minZ = 34.0,
  maxZ = 35.8,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Locker',

      chest_name = 'locker_storage_char_id_7',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supply',
      uid = 'ems-equipment',
      groups = { 'LSFD' },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supervisor Shop',
      uid = 'ems-supervisor',
      groups = { 'ems_rank5' },
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request LSFD ID Card',
      card_type = 'id_lsfd',
      groups = { 'LSFD' },
    },
  },
  distance = 3.0
})

local emsShopCoords = {
  vector3(1772.337, 3655.413, 34.33365),
  vector3(1774.85, 3651.154, 34.40436),
  vector3(1777.305, 3646.934, 34.34391),
}

for i, coords in ipairs(emsShopCoords) do
  AddBoxZone('EMSShopSandy' .. i, coords, 0.75, 2.3, {
    name = 'EMSShopSandy' .. i,
    heading = 210.0,
    minZ = 34.0,
    maxZ = 34.9,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'far fa-prescription-bottle-alt',
        label = 'EMS Drug Cabinet',
        uid = 'ems-drugs',
        groups = { 'LSFD' },
      },
    },
    distance = 2.5
  })
end

AddBoxZone('LSFDCanteenSandy', vector3(1786.656, 3650.825, 35.78652), 0.7, 1.3, {
  name = 'LSFDCanteenSandy',
  heading = 30.0,
  minZ = 35.3,
  maxZ = 36.2,
}, {
  options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'far fa-burger-soda',
        label = 'Hospital Food',
        uid = 'ems-food',
        groups = { 'LSFD' },
      },
  },
  distance = 3.0
})
