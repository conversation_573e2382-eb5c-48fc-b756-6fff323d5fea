AddBoxZone('EMSEquipmentMorgue', vector3(263.55, -1362.69, 24.54), 1, 1.8, {
  name = 'EMSEquipmentMorgue',
  heading = 320,
  minZ = 23.39,
  maxZ = 25.39
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supply',

      uid = 'ems-equipment',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supervisor Shop',

      uid = 'ems-supervisor',

      groups = {
        'ems_rank5',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Pillbox',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Locker',

      chest_name = 'locker_storage_char_id_morgue',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSFD Uniform Wardrobe',
      cloakroom_name = 'emergency'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 2.0
})

AddBoxZone('LSFDMorgueComputer', vector3(261.07, -1359.58, 24.54), 0.6, 0.2, {
  name = 'LSFDMorgueComputer',
  heading = 347,
  minZ = 24.34,
  maxZ = 24.84
}, {
  options = {
    -- Clock in / out
    {
      event_server = 'vrp:server:openMedicalComputer',
      icon = 'fas fa-computer-classic',
      label = 'LSFD Computer',

      groups = {
        'LSFD'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSFD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSFD',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSFD+TMU_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSFD (TMU)',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0
})
