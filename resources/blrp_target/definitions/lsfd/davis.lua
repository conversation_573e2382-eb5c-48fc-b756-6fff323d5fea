AddBoxZone('LSFDComputerDavis', vector3(199.2194, -1640.336, 29.87672), 0.4, 0.7, {
  name = 'LSFDComputerDavis',
  heading = 310.0,
  minZ = 29.5,
  maxZ = 30.2,
}, {
  options = {
    -- Clock in / out
    {
      event_server = 'vrp:server:openMedicalComputer',
      icon = 'fas fa-computer-classic',
      label = 'LSFD Computer',

      groups = {
        'LSFD'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSFD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSFD',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSFD+TMU_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSFD (TMU)',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0
})

AddBoxZone('LSFDEquipmentDavis', vector3(195.6235, -1645.767, 30.83916), 0.5, 2.6, {
  name = 'LSFDEquipmentDavis',
  heading = 50.0,
  minZ = 28.000,
  maxZ = 30.83916,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supply',

      uid = 'ems-equipment',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supervisor Shop',

      uid = 'ems-supervisor',

      groups = {
        'ems_rank5',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-burger-soda',
      label = 'Hospital Food',

      uid = 'ems-food',
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request LSFD ID Card',
      card_type = 'id_lsfd',

      groups = {
        'LSFD'
      }
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Davis Fire',

      groups = {
        'LSFD'
      },
    },
  },
  distance = 2.0
})

AddBoxZone('LSFDLockerDavis1', vector3(217.2565, -1650.302, 31.09375), 0.5, 2.2, {
  name = 'LSFDLockerDavis1',
  heading = 50.0,
  minZ = 28.0,
  maxZ = 31.09375,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Locker',

      chest_name = 'locker_lsfd_davis_char_id',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSFD Uniform Wardrobe',
      cloakroom_name = 'emergency'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('LSFDLockerDavis2', vector3(220.4291, -1651.114, 31.09375), 1.0, 5.2, {
  name = 'LSFDLockerDavis2',
  heading = 320.0,
  minZ = 28.0,
  maxZ = 31.09375,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Locker',

      chest_name = 'locker_lsfd_davis_char_id',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSFD Uniform Wardrobe',
      cloakroom_name = 'emergency'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 7.0
})

AddBoxZone('LSFDBarberDavis', vector3(224.03604125977,-1654.2633056641,30.204730987549), 1.3, 1.5, {
  name = 'LSFDBarberDavis',
  heading = -47.18,
  minZ = 28.7,
  maxZ = 31.7,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  distance = 3.0,
})

AddBoxZone('DavisLSFDFridge', vector3(204.7501, -1638.774, 33.54696), 0.81, 0.9, {
  name = 'DavisLSFDFridge',
  heading = 120.0,
  minZ = 33.2,
  maxZ = 34.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Fridge',

      chest_name = 'locker_lsfd_fridge',
      chest_radius = 3.0,
      chest_weight = 50.0,
      chest_permission = 'emergency.shop',
    },
  },
  distance = 2.0
})
