for idx, coords in pairs({
  vector3(313.389, -586.577, 43.543),
  vector3(312.2863, -579.9784, 43.50743),
  vector3(311.1172, -582.9597, 43.49929),
	vector3(351.0473, -582.1792, 43.59923),
}) do
  AddBoxZone('LSFDComputerPillbox' .. idx, coords, 0.7, 0.7, {
    name = 'LSFDComputerPillbox' .. idx,
    heading = 0.0,
    minZ = 43.07,
    maxZ = 44.0,
  }, {
    options = {
      -- Clock in / out
      {
        event_server = 'vrp:server:openMedicalComputer',
        icon = 'fas fa-computer-classic',
        label = 'LSFD Computer',

        groups = {
          'LSFD'
        }
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'LSFD',

        icon = 'fas fa-clock',
        label = 'Clock in - LSFD',
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'LSFD+TMU_Internal',

        icon = 'fas fa-clock',
        label = 'Clock in - LSFD (TMU)',
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-computer-classic',
        label = 'TMU Business Management',

        business_name = 'LEO TMU',
        component_id = 'management',
      },
    },
    distance = 2.0
  })
end

RegisterNetEvent('blrp_target:lsfd:washHands', function()

  exports['mythic_progbar']:Progress({
    name = 'lsfd_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end


    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

for idx, coords in pairs({
  vector3(314.2554, -594.4175, 43.23277),
  vector3(321.1297, -596.5098, 43.23753),
  vector3(326.2674, -598.3107, 43.09429),
	vector3(341.0307, -587.0562, 43.32814),
  vector3(339.6764, -586.559, 43.32984),
}) do
  AddBoxZone('LSFDHandWash' .. idx, coords, 0.7, 0.7, {
    name = 'LSFDHandWash' .. idx,
    heading = 0.0,
    minZ = 43.07,
    maxZ = 44.0,
  }, {
    options = {
      {
        event_client = 'blrp_target:lsfd:washHands',
        icon = 'far fa-sink',
        label = 'Wash Hands',
      },
    },
    distance = 2.0
  })
end





AddBoxZone('LSFDLowerComputer', vector3(347.967, -587.045, 28.847), 0.7, 0.7, {
  name = 'LSFDLowerComputer',
  minZ = 28,
  maxZ = 30,
}, {
  options = {
    -- Clock in / out
    {
      event_server = 'vrp:server:openMedicalComputer',
      icon = 'fas fa-computer-classic',
      label = 'LSFD Computer',

      groups = {
        'LSFD'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSFD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSFD',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSFD+TMU_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSFD (TMU)',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'TMU Business Management',

      business_name = 'LEO TMU',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZone('LSFDLockerPillbox1', vector3(307.3023, -601.6487, 43.38987), 0.5, 3.2, {
  name = 'LSFDLockerPillbox1',
  heading = 253.769,
  minZ = 42.0,
  maxZ = 44.7772,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Locker',

      chest_name = 'locker_storage_char_id_6',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSFD Uniform Wardrobe',
      cloakroom_name = 'emergency'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('LSFDLockerPillbox2', vector3(304.8395, -600.3066, 43.43295), 0.5, 3.2, {
  name = 'LSFDLockerPillbox2',
  heading = 253.769,
  minZ = 42.0,
  maxZ = 44.7772,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Locker',

      chest_name = 'locker_storage_char_id_6',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSFD Uniform Wardrobe',
      cloakroom_name = 'emergency'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('EMSFoodPillbox', vector3(312.705, -596.6932, 43.5346), 0.5, 1.0, {
  name = 'EMSFoodPillbox',
  heading = 251.173,
  minZ = 42.0,
  maxZ = 44.7,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-burger-soda',
      label = 'Hospital Food',

      uid = 'ems-food',

      groups = {
        'LSFD'
      },
    },
  },
  distance = 2.0
})

AddBoxZone('EMSEquipmentPillbox', vector3(325.3339, -590.8564, 43.76941), 0.5, 2.0, {
  name = 'EMSEquipmentPillbox',
  heading = 74.404,
  minZ = 42.0,
  maxZ = 44.7,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supply',

      uid = 'ems-equipment',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supervisor Shop',

      uid = 'ems-supervisor',

      groups = {
        'ems_rank5',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'TMU Supply',

      uid = 'ems-tmu',

      groups = {
        'LEO TMU',
      },
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request LSFD ID Card',
      card_type = 'id_lsfd',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Pillbox',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLSFDItemRecovery',
      icon = 'far fa-box',
      label = 'LSFD Vehicle Item Recovery',

      groups = {
        'LSFD'
      },
    },
  },
  distance = 2.0
})

AddBoxZone('EMSEquipmentPillboxLower', vector3(305.4729, -577.5176, 28.76383), 1.0, 2.2, {
  name = 'EMSEquipmentPillboxLower',
  heading = 160.0,
  minZ = 27.0,
  maxZ = 29.0,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supply',

      uid = 'ems-equipment',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supervisor Shop',

      uid = 'ems-supervisor',

      groups = {
        'ems_rank5',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'TMU Supply',

      uid = 'ems-tmu',

      groups = {
        'LEO TMU',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'Pharmacy Tech Locker',

      groups = {
        'Pharmacy Tech',
      },

      chest_name = 'locker_storage_char_id_pharmtech',
      chest_radius = 3.0,
      chest_weight = 20.0,
      chest_permission = 'pharmacy.service',
      chest_properties = {
        whitelisted_categories = {
          'drug_precursors',
          'addiction_treatment_drugs',
          'pharmacy_locker_allowed',
        },
      },
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request LSFD ID Card',
      card_type = 'id_lsfd',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-prescription-bottle-alt',
      label = 'EMS Drug Cabinet',

      uid = 'ems-drugs',
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Pillbox',

      groups = {
        'LSFD'
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLSFDItemRecovery',
      icon = 'far fa-box',
      label = 'LSFD Vehicle Item Recovery',

      groups = {
        'LSFD'
      },
    },
  },
  distance = 2.0
})

AddBoxZone('EMSDrugsPillbox1', vector3(327.6012, -591.1047, 43.45024), 1.0, 0.5, {
  name = 'EMSDrugsPillbox1',
  heading = 159.33,
  minZ = 42.0,
  maxZ = 44.7,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-prescription-bottle-alt',
      label = 'EMS Drug Cabinet',

      uid = 'ems-drugs',
    },
  },
  distance = 2.0
})

AddBoxZone('EMSDrugsPillbox2', vector3(347.8721, -581.5652, 43.5475), 0.5, 1.0, {
  name = 'EMSDrugsPillbox2',
  heading = 159.33,
  minZ = 42.0,
  maxZ = 44.7,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-prescription-bottle-alt',
      label = 'EMS Drug Cabinet',

      uid = 'ems-drugs',
    },
  },
  distance = 2.0
})

AddBoxZone('LSFDPubPillbox1', vector3(348.44, -577.82, 43.28), 0.8, 0.6, {
  name = 'LSFDPubPillbox1',
  heading = 340,
  minZ = 42.08,
  maxZ = 44.08
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'fa-regular fa-person-dress',
      label = 'Gowns',
      cloakroom_name = 'hospital'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('ChemicalSupply', vector3(299.8011, -579.1693, 29.97363), 0.5, 1.0, {
  name = 'ChemicalSupply',
  heading = 90.0,
  minZ = 29.73,
  maxZ = 30.0,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-prescription-bottle-alt',
      label = 'Chemical Supplies',

      uid = 'chemical-supply',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Injector/Container Supplies',

      uid = 'drug-equipment-supply',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    },
  },
  distance = 2.0
})
AddBoxZone('CASLaptopPillbox', vector3(305.0078, -574.4452, 29.07542),0.5,0.3,{
  name = 'CASLaptop2',
  heading = 45.0,
  minZ = 28.7,
  maxZ = 29.2,
}, {
  options = {
    {
      event_server = 'core:server:pharmacy:caslookup',
      icon = 'far fa-prescription-bottle-alt',
      label = 'CAS Lookup',

      uid = 'drug-lookup-pillbox',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    },
    {
      event_server = 'core:server:pharmacy:solutionlookup',
      icon = 'far fa-prescription-bottle-alt',
      label = 'Solution Lookup',

      uid = 'solution-lookup-pillbox',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    }
  },
  distance = 2.0
})
AddBoxZoneAutoname(vector4(305.0583, -581.0308, 28.76383, 90.0), 1.0, 1.1, {
  minZ = 28.5,
  maxZ = 28.8,
}, {
  options = generateAdvancedCookingOptions('pillbox_lab1', nil, {
    "u_naloxone",
    "u_bupropion",
    "u_lidocaine",
    "u_labetalol",
    "u_lofexidine",
    "u_valium",
    "d_water",
  },
  {
    'Pharmacy Tech',
    'ems_rank5'
  },false),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(299.1131, -578.7897, 28.76383, 90.0), 1.0, 1.1, {
  minZ = 28.5,
  maxZ = 28.8,
}, {
  options = generateAdvancedCookingOptions('pillbox_lab2', nil, {
    "u_naloxone",
    "u_bupropion",
    "u_lidocaine",
    "u_labetalol",
    "u_lofexidine",
    "u_valium",
    "d_water",
  },
  {
    'Pharmacy Tech',
    'ems_rank5'
  },false),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(301.5852, -573.6852, 28.76383, 90.0), 1.0, 1.1, {
  minZ = 28.5,
  maxZ = 28.8,
}, {
  options = generateAdvancedCookingOptions('pillbox_lab3', nil, {
    "u_naloxone",
    "u_bupropion",
    "u_lidocaine",
    "u_labetalol",
    "u_lofexidine",
    "u_valium",
    "d_water",
  },
  {
    'Pharmacy Tech',
    'ems_rank5'
  },false),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(306.4415, -575.301, 28.76383, 90.0), 1.0, 1.1, {
  minZ = 28.5,
  maxZ = 28.8,
}, {
  options = generateAdvancedCookingOptions('pillbox_lab4', nil, {
    "u_naloxone",
    "u_bupropion",
    "u_lidocaine",
    "u_labetalol",
    "u_lofexidine",
    "u_valium",
    "d_water",
  },
  {
    'Pharmacy Tech',
    'ems_rank5'
  },false),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(308.2406, -580.1528, 28.76383, 90.0), 1.0, 1.1, {
  minZ = 28.5,
  maxZ = 28.8,
}, {
  options = generateAdvancedCookingOptions('pillbox_lab5', nil, {
    "u_naloxone",
    "u_bupropion",
    "u_lidocaine",
    "u_labetalol",
    "u_lofexidine",
    "u_valium",
    "d_water",
  },
  {
    'Pharmacy Tech',
    'ems_rank5'
  },false),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(307.3387, -575.4265, 28.66841,90.0),0.3,0.3,{
  minZ = 28.5,
  maxZ = 28.8,
}, {
  options = {
    {
      event_server = 'core:server:pharmacy:wastebin',
      icon = 'far fa-prescription-bottle-alt',
      label = 'Chemical Waste Bin',

      uid = 'lab-wastebin1',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    }
  },
  distance = 2.0
})


