AddBoxZone('LSFDLockerPaleto', vector3(-253.484, 6327.593, 33.32565), 0.7, 1.8, {
  name = 'LSFDLockerPaleto',
  heading = 45.0,
  minZ = 30.0,
  maxZ = 33.32565,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'LSFD Locker',

      chest_name = 'locker_storage_char_id_8',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSFD Uniform Wardrobe',
      cloakroom_name = 'emergency'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('ChemicalSupplyPaleto', vector3(-253.5662, 6320.35, 33.55115), 0.5, 1.0, {
  name = 'ChemicalSupplyPaleto',
  heading = 90.0,
  minZ = 33.1,
  maxZ = 33.8,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-prescription-bottle-alt',
      label = 'Chemical Supplies',

      uid = 'chemical-supply',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Injector/Container Supplies',

      uid = 'drug-equipment-supply',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    },
  },
  distance = 2.0
})

AddBoxZone('CASLaptopPaleto', vector3(-250.1943, 6310.6, 32.60515),0.3,0.3,{
  name = 'CASLaptop1',
  heading = 45.0,
  minZ = 32.3,
  maxZ = 32.7,
}, {
  options = {
    {
      event_server = 'core:server:pharmacy:caslookup',
      icon = 'far fa-prescription-bottle-alt',
      label = 'CAS Lookup',

      uid = 'drug-lookup-paleto',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    },
    {
      event_server = 'core:server:pharmacy:solutionlookup',
      icon = 'far fa-prescription-bottle-alt',
      label = 'Solution Lookup',

      uid = 'solution-lookup-paleto',

      groups = {
        'Pharmacy Tech',
        'ems_rank5',
      }
    }
  },
  distance = 2.0
})


AddBoxZoneAutoname(vector4(-248.7476, 6315.816, 32.40891, 114.196), 2.5, 1.1, {
  minZ = 32.0,
  maxZ = 32.45,
}, {
  options = generateAdvancedCookingOptions('paleto_lab1', nil, {
    "u_naloxone",
    "u_bupropion",
    "u_lidocaine",
    "u_labetalol",
    "u_lofexidine",
    "u_valium",
    "d_water",
  },
  {
    'Pharmacy Tech',
    'ems_rank5'
  },false),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(-248.0224, 6315.005, 32.40891, 114.196), 2.5, 1.1, {
  minZ = 32.0,
  maxZ = 32.45,
}, {
  options = generateAdvancedCookingOptions('paleto_lab2', nil, {
    "u_naloxone",
    "u_bupropion",
    "u_lidocaine",
    "u_labetalol",
    "u_lofexidine",
    "u_valium",
    "d_water",
  },
  {
    'Pharmacy Tech',
    'ems_rank5'
  },false),
  distance = 2.0,
})
