local address = '5 Meteor St'

-- Entry
AddBoxZoneAutoname(vector4(414.7076, -217.9015, 60.12228, 340), 0.5, 5.0, {
  minZ = 59.0,
  maxZ = 62.0,
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 4th Floor',

      id = 'HAWICK-ELGIN-B',
      instance = 'Floor-4',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 3rd Floor',

      id = 'HAWICK-ELGIN-B',
      instance = 'Floor-3',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'HAWICK-ELGIN-B',
      instance = 'Floor-2',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'HAWICK-ELGIN-B',
      instance = 'Floor-1',
    },
  },

  distance = 2.5,
})

local initial_coords = vector4(407.0763, -240.8994, 51.10257, 70)
local initial_min = 50.0
local initial_max = 52.5

for i = 0, 3 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 0), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end
