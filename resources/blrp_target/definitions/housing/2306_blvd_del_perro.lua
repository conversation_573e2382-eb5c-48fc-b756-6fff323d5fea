local address = '2306 S Blvd Del Perro'

-- Entry
AddBoxZoneAutoname(vector4(-1163.852, -349.5335, 36.69495, 5), 0.5, 3.0, {
  minZ = 35.6,
  maxZ = 38.0,
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 4th Floor',

      id = 'ROCKF-APT-A',
      instance = 'Floor-4',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 3rd Floor',

      id = 'ROCKF-APT-A',
      instance = 'Floor-3',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'ROCKF-APT-A',
      instance = 'Floor-2',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'ROCKF-APT-A',
      instance = 'Floor-1',
    },
  },

  distance = 2.5,
})

local initial_coords = vector4(-1164.578, -357.9789, 36.13785, 5)
local initial_min = 35.1
local initial_max = 37.5

for i = 0, 4 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 0), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end

