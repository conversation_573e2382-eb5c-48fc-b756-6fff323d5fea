local address = '8 Popular Street'

AddBoxZoneAutoname(vector4(683.4218, -789.9597, 24.58943, 0), 0.3, 3.2, {
  minZ = 23.55,
  maxZ = 25.85,
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'POP-8',
      instance = 'Floor-1',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'POP-8',
      instance = 'Floor-2',
    },
  },

  distance = 2.5,
})

-- Floors

local initial_coords = vector4(686.6204, -790.71, 30, 0)
local initial_min = 30.00
local initial_max = 32.30

for i = 0, 1 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 0), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end
