local address = '1066 Ginger St'
AddBoxZoneAutoname(vector4(-812.47, -980.43, 14.14, 35), 3.0, 1, {
  minZ = 13.14,
  maxZ = 16.14
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'GIN-1066',
      instance = 'Floor-1',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'GIN-1066',
      instance = 'Floor-2',
    },
  },

  distance = 2.5,
})

-- Floors

local initial_coords = vector4(-816.271, -972.634, -4.000, 230.831)
local initial_min = -4.99
local initial_max = -2.61

for i = 0, 1 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 4), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end
