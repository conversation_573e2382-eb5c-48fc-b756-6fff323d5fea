local address = '100 Fantastic Pl'

-- Entry
AddBoxZoneAutoname(vector4(287.695, -1095.016, 29.50081, 270), 0.5, 5.0, {
  minZ = 28.4,
  maxZ = 31.0,
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 7th Floor',

      id = 'SKID-TEMPL-A',
      instance = 'Floor-7',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 6th Floor',

      id = 'SKID-TEMPL-A',
      instance = 'Floor-6',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 5th Floor',

      id = 'SKID-TEMPL-A',
      instance = 'Floor-5',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 4th Floor',

      id = 'SKID-TEMPL-A',
      instance = 'Floor-4',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 3rd Floor',

      id = 'SKID-TEMPL-A',
      instance = 'Floor-3',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'SKID-TEMPL-A',
      instance = 'Floor-2',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'SKID-TEMPL-A',
      instance = 'Floor-1',
    },
  },

  distance = 2.5,
})

local initial_coords = vector4(283.9568, -1116.274, 31.16663, 180)
local initial_min = 30.0
local initial_max = 32.5

for i = 0, 6 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 0), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end
