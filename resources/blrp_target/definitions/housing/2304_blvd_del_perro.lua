local address = '2304 S Blvd Del Perro'

-- Entry
AddBoxZoneAutoname(vector4(-1096.374, -325.9284, 38.02486, 175), 0.5, 5.0, {
  minZ = 36.8,
  maxZ = 39.2,
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 3rd Floor',

      id = 'ROCKF-ARCH',
      instance = 'Floor-3',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'ROCKF-ARCH',
      instance = 'Floor-2',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'ROCKF-ARCH',
      instance = 'Floor-1',
    },
  },

  distance = 2.5,
})

local initial_coords = vector4(-1066.153, -336.768, 36.19389, 265)
local initial_min = 35.1
local initial_max = 37.5

for i = 0, 2 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 0), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end

