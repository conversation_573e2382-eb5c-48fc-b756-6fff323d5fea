local address = '8358 Eclipse Boulevard'

AddBoxZoneAutoname(vector4(-310.09, 221.41, 87.93, 11), 0.6, 2.4, {
  minZ = 86.93,
  maxZ = 89.33
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'EXLIP-8358',
      instance = 'Floor-1',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'EXLIP-8358',
      instance = 'Floor-2',
    },
  },

  distance = 2.5,
})

-- Floors

local initial_coords = vector4(-309.7062, 215.4491, 86.02327, 359)
local initial_min = 85.00
local initial_max = 87.33

for i = 0, 2 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 359), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end
