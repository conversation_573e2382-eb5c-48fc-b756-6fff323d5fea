local address = '101 Popular St'

-- Entry
AddBoxZoneAutoname(vector4(797.5435, -1628.755, 31.68319, -5), 0.5, 5.0, {
  minZ = 30.4,
  maxZ = 33.0,
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'LMESA-STG',
      instance = 'Floor-2',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'LMESA-STG',
      instance = 'Floor-1',
    },
  },

  distance = 2.5,
})

local initial_coords = vector4(789.3891, -1635.632, 31.125, 355)
local initial_min = 30.1
local initial_max = 32.4

for i = 0, 2 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 0), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end

