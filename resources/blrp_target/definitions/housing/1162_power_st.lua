local address = '1162 Power Street'

AddBoxZoneAutoname(vector4(293.1657, -162.7878, 64.97089, 70), 0.5, 2.5, {
  minZ = 63.5,
  maxZ = 66.2,
}, {
  options = {
    {
      event_client = 'null',
      icon = 'far fa-apartment',
      label = address,
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 1st Floor',

      id = 'PWR-1162',
      instance = 'Floor-1',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 2nd Floor',

      id = 'PWR-1162',
      instance = 'Floor-2',
    },
    {
      event_server = 'core:server:properties:enterProcedural',
      icon = 'far fa-sort-circle-up',
      label = 'Elevator: 3rd Floor',

      id = 'PWR-1162',
      instance = 'Floor-3',
    },
  },

  distance = 2.5,
})

-- Floors

local initial_coords = vector4(294.0957, -161.669, 66.16848, 70)
local initial_min = 64.5
local initial_max = 68.3

for i = 0, 2 do
  local offset_z = 5 * i

  AddBoxZoneAutoname(initial_coords + vector4(0, 0, offset_z, 0), 0.5, 1.9, {
    minZ = initial_min + offset_z,
    maxZ = initial_max + offset_z,
  }, {
    options = {
      {
        event_client = 'null',
        icon = 'far fa-apartment',
        label = address .. ' - Floor ' .. (i + 1),
      },
      {
        event_server = 'core:server:properties:leaveProcedural',
        icon = 'far fa-sort-circle-down',
        label = 'Elevator: Ground Floor',
      },
    },

    distance = 2.5,
  })
end