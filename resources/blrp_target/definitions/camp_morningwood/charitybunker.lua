AddBoxZone('CM25BAManagement', vector3(-855.05, 2881.35, 21.74), 0.4, 0.4, {
  heading = 15.0,
  minZ = 21.34,
  maxZ = 21.94,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Blath Alainn Charity',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('CM25BACabinet', vector3(-851.95, 2882.54, 21.74), 0.6, 1.6, {
  heading = 25.0,
  minZ = 20.74,
  maxZ = 23.14,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Blath Alainn Charity',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '<PERSON><PERSON><PERSON>',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'cm25_ba_storage1',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'Supplies',

      uid = 'c24-blath',

      business_name = 'Blath Alainn Charity',
      component_id = 'storage',
      component_perm = 'management'
    },
  },
  distance = 2.5
})

for k, d in pairs({
  vector4(-859.69, 2884.86, 21.74, 20),
  vector4(-852.2, 2888.2, 21.74, 30),
}) do
  AddBoxZone('CM25BABedside' .. k, d.xyz, 0.8, 1.2, {
    heading = d.w,
    minZ = 21.34,
    maxZ = 21.94,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',

        business_name = 'Blath Alainn Charity',
        component_id = 'storage',

        args = {
          capacity = 50.0,
          named = 'cm25_ba_bedside_' .. k,
        },
      },
    },
    distance = 2.5
  })
end