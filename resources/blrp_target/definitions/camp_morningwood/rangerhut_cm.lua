AddBoxZone('CM25FactionSelect', vector3(-1101.0, 2854.34, 14.62), 0.4, 0.6, {
  name = 'CM25FactionSelect',
  heading = 25.0,
  minZ = 14.82,
  maxZ = 15.42
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Ranger',

      icon = 'fas fa-clock',
      label = 'Clock in - Civilian Ranger',

      groups = {
        'Civilian'
      }
    },

    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'LEO Ranger Management',

      business_name = 'LEO Ranger',
      component_id = 'management',
    },
  },
  distance = 3.0
})

AddBoxZone('CM25PoliceLocker', vector3(-1101.2, 2852.06, 14.62), 0.6, 0.6, {
  name = 'CM25PoliceLocker',
  heading = 20.0,
  minZ = 13.62,
  maxZ = 14.02,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'BCSO Uniform Wardrobe',
      cloakroom_name = 'bcso',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSPD Uniform Wardrobe',
      cloakroom_name = 'lspd',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'Ranger Uniform Wardrobe',
      cloakroom_name = 'ranger',

      groups = {
        'Ranger',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'LEO',
        'Ranger',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('CM25SupplyLocker', vector3(-1097.07, 2852.91, 14.62), 0.8, 1.6, {
  name = 'CM25SupplyLocker',
  heading = 35.0,
  minZ = 13.62,
  maxZ = 14.42,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-toolbox',
      label = 'Ranger Equipment',

      uid = 'ranger-equipment',

      groups = {
        'Ranger'
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-toolbox',
      label = 'Ranger Supervisor Shop',

      uid = 'supervisor-ranger-shop',

      groups = {
        'Ranger'
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Tints',

      uid = 'leo-tints',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'LSPD Supervisor Shop',

      uid = 'leo-lspd-supervisor',

      groups = {
        'LEO',
      },

      groups = {
        'police_rank5',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'BCSO Supervisor Shop',

      uid = 'leo-bcso-supervisor',

      groups = {
        'sheriff_rank5',
      }
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'Police Locker',

      chest_name = 'locker_storage_char_id_rangerhut',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',

      groups = {
        'LEO',
        'Ranger',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'CMRangerHut',

      groups = {
        'LEO',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('CM25RangerFridge', vector3(-1093.83, 2854.93, 14.62), 0.8, 0.8, {
  name = 'CM25RangerFridge',
  heading = 305.0,
  minZ = 13.62,
  maxZ = 15.62,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-refrigerator',
      label = 'Fridge',

      chest_name = 'fridge_cm25_rangerhut',
      chest_radius = 3.0,
      chest_weight = 35.0,
      chest_permission = 'ranger.employee',

      groups = {
        'LEO',
        'Ranger',
      },
    },
  },
  distance = 2.5
})