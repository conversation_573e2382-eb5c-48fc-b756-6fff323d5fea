AddBoxZone('ZancudoMilitaryEquip', vector3(-2173.1, 3256.047, 33.12953), 1.0, 0.5, {
  name = 'ZancudoMilitaryEquip',
  heading = 145.0,
  minZ = 31.8,
  maxZ = 34.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Zancudo Reception',
}, {
  options = {
    {
      event_server = 'core:server:id-cards:getZancudoId',
      icon = 'fa-regular fa-id-card',
      label = 'Request US Army ID Card',
      card_type = 'id_zancudo',
      groups = { 'Fort Zancudo' },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'Military Uniform Wardrobe',
      cloakroom_name = 'zancudo',
      groups = { 'Fort Zancudo' },
    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'far fa-tshirt',
      label = 'Clothing Store',
      groups = { 'Fort Zancudo' },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe',
      groups = { 'Fort Zancudo' },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Zancudo Equipment',

      uid = 'zancudo-equipment',

      business_name = 'Fort Zancudo',
      component_id = 'extra_a',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-user-secret',
      label = 'Zancudo Captain Equipment',

      uid = 'zancudo-equipment-cpt',

      business_name = 'Fort Zancudo',
      component_id = 'extra_b',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-rectangle-xmark',
      label = 'Zancudo MRE Supply',

      uid = 'zancudo-equipment-mre',

      business_name = 'Fort Zancudo',
      component_id = 'extra_a',
    },
  },
  distance = 2.5
})
AddTargetModel({
  `ch_prop_fingerprint_scanner_01e`,
}, {
  options = {
    {
      event_server = 'camp_morningwood:server:swipeKeycardBunker',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Swipe keycard',
      keypad_coords = 'outer',
    },
  },
  distance = 2.5,
})
AddTargetModel({
  `ch_prop_fingerprint_scanner_01c`,
}, {
  options = {
    {
      event_server = 'camp_morningwood:server:swipeKeycardBunker',
      icon = 'fa-regular fa-circle-caret-right',
      label = 'Swipe keycard',
      keypad_coords = 'inner',
    },
  },
  distance = 2.5,
})