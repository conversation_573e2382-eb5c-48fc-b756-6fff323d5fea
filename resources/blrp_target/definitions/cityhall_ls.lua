
AddBoxZone('DOLLosSantosLeft', vector3(-552.0728, -202.1048, 38.12601), 3.0, 3.75, {
  name = 'DOLLosSantosLeft',
  heading = 345.0,
  minZ = 37.2,
  maxZ = 39.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Department of Justice',
}, {
  options = {
    {
      event_server = 'core:server:prison:checkParoleTime',
      icon = 'fa-regular fa-clock-desk',
      label = 'Check Parole Time',
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request DOJ ID Card',
      card_type = 'id_doj',

      groups = {
        'Lawyer',
        'DOJ',
      },
    },
    {
      event_server = 'core:server:compensation:openChest',
      icon = 'fa-regular fa-treasure-chest',
      label = 'Open Compensation Chest'
    },
    {
      event_server = 'vrp:server:openCourtComputer',
      icon = 'fas fa-computer-classic',
      label = 'Court Computer',

      groups = {
        'DOJ',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Lawyer',

      icon = 'fas fa-clock',
      label = 'Clock in - Lawyer',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'DOJ',

      icon = 'fas fa-clock',
      label = 'Clock in - DOJ',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 3.0
})

AddBoxZone('DOLLosSantosRight', vector3(-543.2126, -197.0241, 38.12601), 3.0, 3.75, {
  name = 'DOLLosSantosRight',
  heading = 255.0,
  minZ = 37.2,
  maxZ = 39.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Department of Licensing',
}, {
  options = {
    {
      event_client = 'license_shop:client:openInterface',
      icon = 'fa-regular fa-id-card',
      label = 'Purchase Licenses',
    },
    {
      event_server = 'core:server:id-cards:requestCitizenCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Citizen ID Card',
    },
    {
      event_server = 'core:server:id-cards:requestCDLCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request CDL Card',
    },
    {
      event_server = 'core:server:id-cards:requestHuntingCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Hunting Card',
    },
    {
      event_server = 'core:server:id-cards:requestFishingCard',
      icon = 'fa-regular fa-id-card',
      label = 'Request Fishing Card',
    },
    {
      event_server = 'core:server:id-cards:generateMMHCard',
      icon = 'fa-regular fa-id-card',
      label = 'Issue Medical Marijuana Handler ID',

      groups = {
        'staff',
      }
    },
    {
      event_server = 'core:server:plateRequest',
      icon = 'fa-regular fa-rectangle-wide',
      label = 'Apply For Personalized License Plate',
    },
    {
      event_server = 'core:server:target-backhaul:purchasePinkSlip',
      icon = 'fa-regular fa-file-alt',
      label = 'Purchase Vehicle Transfer Slips'
    },
    {
      event_server = 'blrp_tablet:server:payTickets',
      icon = 'fa-regular fa-file-invoice-dollar',
      label = 'Pay Outstanding Tickets'
    },
    {
      event_server = 'core:server:compensation:openChest',
      icon = 'fa-regular fa-treasure-chest',
      label = 'Open Compensation Chest'
    },
    {
      event_server = 'core:server:target-backhaul:changeDOB',
      icon = 'fa-regular fa-computer-classic',
      label = 'Update date of birth',

      filter = function()
        return string.match(exports.blrp_core:me().get('dateofbirth'), '%-01%-01')
      end
    },
    {
      event_server = 'core:server:target-backhaul:grantPilotLicense',
      icon = 'fa-regular fa-plane',
      label = 'Grant Pilot License',

      groups = {
        'sheriff_rank5',
        'police_rank5',
        'sahp_rank2',
        'Blaine County Hawks',
      }
    },
    {
      event_server = 'core:server:target-backhaul:grantCDL',
      icon = 'fa-solid fa-truck-arrow-right',
      label = 'Grant Commercial Drivers License',

      groups = {
        'Go Truck Yourself',
      }
    },    
    {
      event_server = 'core:server:businesses:manageWildcard',
      icon = 'fa-regular fa-computer-classic',
      label = 'Business Management',

      filter = function(entity, ray_intersect_coords)
        for _, permissions in pairs(exports.blrp_core:me().get('business_perms')) do
          if permissions.management then
            return true
          end
        end

        return false
      end
    },
  },
  distance = 3.0
})

AddBoxZone('CourthouseDOCLocker', vector3(-560.4924, -228.5118, 33.26681), 0.75, 0.85, {
  name = 'CourthouseDOCLocker',
  heading = 30.0,
  minZ = 33.2,
  maxZ = 35.9,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'CityHallLosSantos',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'DOC Locker',

      chest_name = 'locker_storage_char_id_CityHallLS',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',

      groups = {
        'DOC',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'DOC Uniform Wardrobe',
      cloakroom_name = 'bailiff',

      groups = {
        'DOC',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'LEO',
      },
    },
  },
  distance = 3.0
})

AddTargetModel({
  GetHashKey('ba_prop_battle_club_computer_01'),
}, {
  options = {
    -- Clock in / out
    {
      event_server = 'vrp:server:openCourtComputer',
      icon = 'fas fa-computer-classic',
      label = 'Court Computer',

      groups = {
        'DOC',
        'DOJ',
      },

      zones = {
        'CityHallLosSantos',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Lawyer',

      icon = 'fas fa-clock',
      label = 'Clock in - Lawyer',

      groups = {
        'Civilian',
      },

      zones = {
        'CityHallLosSantos',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'DOJ',

      icon = 'fas fa-clock',
      label = 'Clock in - DOJ',

      groups = {
        'Civilian',
      },

      zones = {
        'CityHallLosSantos',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',

      zones = {
        'CityHallLosSantos',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('CityHallLSDAStorage', vector3(-535.1443, -195.0228, 43.36719), 0.5, 1.5, {
  name = 'CityHallLSDAStorage',
  heading = 120.0,
  minZ = 42.5,
  maxZ = 43.6,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'DA Storage',

      chest_name = 'locker_cityhall_office4',
      chest_radius = 3.0,
      chest_weight = 500.0,
      chest_permission = 'key.da',
    },
  },
  distance = 2.0
})

local LawClosets = {
  vector3(-538.8247, -196.9952, 43.14368),
  vector3(-538.8303, -196.9855, 37.99966),
  vector3(-533.9608, -194.1945, 37.99888),
  vector3(-529.0905, -191.4076, 37.89733),
}
AddBoxZone('CourthouseGavel', vector3(-575.691, -210.651, 39.564), 0.7, 0.5, {
  name = 'CourthouseGavel',
  heading = 150.0,
  minZ = 38.1,
  maxZ = 39.564,
}, {
  options = {
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel',

      strikes = 1,

      groups = {
        'DOJ',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 2x Strike',

      strikes = 2,

      groups = {
        'DOJ',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 3x Strike',

      strikes = 3,

      groups = {
        'DOJ',
      },
    },

  },
  distance = 2.5
})


for closet, position in pairs(LawClosets) do
  AddBoxZone('CityHallStorage' .. closet, position, 0.4, 1.6, {
    name = 'CityHallStorage' .. closet,
    heading = 15.0,
    minZ = position.z,
    maxZ = position.z + 0.5,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openChest',
        icon = 'far fa-box',
        label = 'Locker',

        chest_name = 'locker_storage_char_id_cityhall',
        chest_radius = 3.0,
        chest_weight = 150.0,
        chest_permission = 'lawoffice.storage',

        groups = {
          'DOJ',
        },
      },
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',

        groups = {
          'DOJ',
        }
      },
    },
    distance = 3.0
  })

end
