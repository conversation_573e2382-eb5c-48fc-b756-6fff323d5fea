
local food_cart_items ={
  -- Taqueria De Los Santos
  { id = "food_mexitacos", label = "Cook Mexi Tacos",group = "Taqueria De Los Santos",model = `prop_fc_tacos`},
  { id = "food_churros", label = "Cook Churros",group = "Taqueria De Los Santos",model = `prop_fc_tacos`},
  { id = "food_chipsnsalsa", label = "Prepare Chips N Salsa",group = "Taqueria De Los Santos",model = `prop_fc_tacos`},
  { id = "food_burrito", label = "Cook Burrito",group = "Taqueria De Los Santos",model = `prop_fc_tacos`},
  { id = "food_garritos", label = "Mix Garritos",group = "Taqueria De Los Santos",model = `prop_fc_tacos`},
  --Burgershot
  { id = "food_killer_doubler_burger", label = "Prepare Heart Stopper Burger", group ="Burgershot",model = `prop_fc_burgershot`},
  { id = "food_french_fires", label = "Fry French Fries", group ="Burgershot",model = `prop_fc_burgershot`},
  { id = "food_chickennuggets", label = "Fry Chicken Nuggets", group ="Burgershot",model = `prop_fc_burgershot`},
  { id = "food_chickenburger", label = "Prepare Chicken Burger", group="Burgershot",model =`prop_fc_burgershot`},
  { id = "food_bs_cola", label = "Mix Burgershot Cola", group="Burgershot",model=`prop_fc_burgershot`},
  { id = "food_killer_burger", label = "Prepare Bleeder Burger", group="Burgershot",model=`prop_fc_burgershot`},
  -- pearl neclace
  {id = "p_colcannon", label = "Prepare Calcannon", group="Pearls",model=`prop_fc_pearls`},
  {id = "p_fishtaco", label = "Prepare Fish Tacos", group="Pearls",model=`prop_fc_pearls`},
  {id = "p_firecrackershrimp", label = "Prepare Tangirine Firecracker Shrimp", group="Pearls",model=`prop_fc_pearls`},
  {id = "p_arnold_palmer", label = "Mix Arnold Palmer", group="Pearls", model=`prop_fc_pearls`},
  {id = "p_fruit_clubsoda", label = "Mix Fruit Infused Club Soda", group="Pearls", model=`prop_fc_pearls`},
  --Chihuahuahuahuahuahua dogs
  {id = "food_chi_dog", label = "Prepare Chihuahua Dog", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_chi_fries", label = "Fry Chihuahua Fries", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_cooked_bacon", label = "Cook Bacon", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_chi_dog_bacon", label = "Prepare Chihuahua Bacon Dog", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_chi_dog_foot", label = "Prepare Chihuahua Footlong", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_weenie", label = "Cook Regular Weenie", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_weenie_footlong", label = "Cook Footlong Weenie", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_corndog", label = "Cook Chihuahua Corndog", group="Chihuahua Hotdogs", model=`prop_fc_chihuahua`},
  {id = "food_chi_cola", label = 'Mix Chihuahua Cola', group = "Chihuahua Hotdogs", odel=`prop_fc_chihuahua`},
  --YellowJack
  {id = "food_breakfastburrito", label = "Prepare Breakfast Burrito", group="Yellow Jack",model =`prop_fc_yj`},
  {id = "food_spicywings", label = "Prepare Spicy Wings", group="Yellow Jack",model =`prop_fc_yj`},
  {id = "food_mozzarellasticks", label = "Fry Mozzeralla Sticks", group="Yellow Jack",model =`prop_fc_yj`},
  {id = "food_chocolatechippancake", label = "Prepare Chocolate Chip Pancake", group="Yellow Jack",model =`prop_fc_yj`},
  {id = "food_avocadotoas", label = "Prepare Avocado Toast", group="Yellow Jack",model =`prop_fc_yj`},
  {id = "food_steakandeggs", label = "Prepare Steak and Eggs", group="Yellow Jack",model =`prop_fc_yj`},
  {id = "food_chickenandwaffles", label = "Prepare Chicken and Waffles", group="Yellow Jack",model =`prop_fc_yj`},
  {id = "food_chocolatemilk", label = "Mix Choccy Milk", group="Yellow Jack",model =`prop_fc_yj`},
  -- Boos Catfe
  {id = "food_spec_eggrolls", label = "Cook Egg Rolls", group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_spec_sushi", label = "Prepare Sushi", group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_spec_ramennoodles", label = "Cook Ramen Noodles", group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_spec_bobatea", label = "Mix Boba Tea", group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_spec_matchatea", label = "Mix Matcha Tea", group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_oolong", label = "Mix Oolong Tea", group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_flan", label = 'Cook Cat Flan', group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_chickenbentobox", label = 'Cook Chicken Bento Box', group = "Boo's Catfe", model = `prop_fc_booscatfe`},
  {id = "food_kchickenc", label = "Cook Katsu Chicken Curry", group = "Boo's Catfe", model = `prop_fc_booscatfe`},


  --CoolBeans
  {id = "cb_espresso_frap", label = "Brew Espresso Frappuccino", group="Cool Beans",model=`prop_fc_coolbeans`},
  {id = "cb_honey_tea_latte", label = "Brew Honey Tea Latte", group="Cool Beans",model=`prop_fc_coolbeans`},
  {id = "cb_house", label = "Brew Cool Beans House Blend", group="Cool Beans",model=`prop_fc_coolbeans`},
  {id = "cb_sb_choc_cakep", label = "Prepare Strawberry Chocolate Cakepop", group="Cool Beans",model=`prop_fc_coolbeans`},
  {id = "cb_bb_muffin", label = "Bake Blueberry Muffin", group ="Cool Beans",model=`prop_fc_coolbeans`},
  {id = "cb_cupcake", label = "Bake Cupcake", group = "Cool Beans",model=`prop_fc_coolbeans`},
  {id = "cb_croissant", label = "Make Bacon Egg Cheese Croissant", group ="Cool Beans",model=`prop_fc_coolbeans`},
  {id = "cb_panini", label = "Make Chicken Bacon Panini", group = "Cool Beans",model=`prop_fc_coolbeans`},
}


local cart_cooking_options = {}

--Populating  Food cart items from the above table food_cart_items



for _, food_option in pairs(food_cart_items) do
  table.insert(cart_cooking_options,  {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = food_option.label,
    auto_order = true,
    food_item_id = food_option.id,

    groups = {
        food_option.group
    },

    filter = function(entity, ray_intersect_coords)
      if GetEntityModel(entity) ~= food_option.model then
        return false
      end
      return true
    end

  })
end



local house_prop_options =  {
  -- Christmas
  {
    event_server = 'blrp_yankton:server:requestPresent',
    icon = 'fa-regular fa-gift',
    label = 'Take Present',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      if GetEntityModel(entity) ~= `prop_xmas_tree_int` then
        return false
      end

      local placed_furniture_id = Entity(entity).state.placed_furniture_id

      if not placed_furniture_id or placed_furniture_id <= 0 then
        return false
      end

      return placed_furniture_id
    end
  },

  {
    event_server = 'blrp_yankton:server:requestPresent',
    icon = 'fa-regular fa-gift',
    label = 'Take Present',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      if GetEntityModel(entity) ~= `pata_christmastree` then
        return false
      end

      local placed_furniture_id = Entity(entity).state.placed_furniture_id

      if not placed_furniture_id or placed_furniture_id <= 0 then
        return false
      end

      return placed_furniture_id
    end
  },

  -- / Christmas

  -- House furniture - storage

  {
    event_server = 'blrp_housing:server:openStorageProp',
    icon = 'fa-regular fa-box',
    label = function(entity)
      local state = Entity(entity).state

      if state.placed_furniture_label then
        return 'Storage: ' .. state.placed_furniture_label
      end

      return 'Storage'
    end,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local is_storage_prop = false

      pcall(function()
        is_storage_prop = exports.housing:IsStorageProp(GetEntityModel(entity))
      end)

      if not is_storage_prop or LocalPlayer.state.instance_number == 0 then
        return false
      end

      local placed_furniture_id = Entity(entity).state.placed_furniture_id

      if not placed_furniture_id or placed_furniture_id <= 0 then
        return false
      end

      return placed_furniture_id
    end,
  },

  -- Toggle storage lock [Warehouse only]
  {
    event_client = 'blrp_housing:client:targetInteractFurniture',
    action = 'togglelock',

    icon = function(entity)
      return 'fa-regular ' .. (Entity(entity).state.placed_furniture_locked and 'fa-lock-open' or 'fa-lock')
    end,

    label = function(entity)
      return Entity(entity).state.placed_furniture_locked and 'Unlock' or 'Lock'
    end,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local is_storage_prop = false

      pcall(function()
        is_storage_prop = exports.housing:IsStorageProp(GetEntityModel(entity))
      end)

      if not is_storage_prop or LocalPlayer.state.instance_number == 0 then
        return false
      end

      local entity_state = Entity(entity).state

      local placed_furniture_id = entity_state.placed_furniture_id

      if
        not placed_furniture_id or
        placed_furniture_id <= 0 or
        not entity_state.placed_furniture_has_lock or
        not exports.housing:CurrentPropertyIsWarehouse()
      then
        return false
      end

      return placed_furniture_id
    end,
  },

  -- Storage toggle lock (owner) [Warehouse only]
  {
    event_client = 'blrp_housing:client:targetInteractFurniture',
    action = 'toggleowner',

    icon = function(entity)
      return 'fa-regular ' .. (Entity(entity).state.placed_furniture_has_lock and 'fa-user-minus' or 'fa-user-plus')
    end,

    label = function(entity)
      return Entity(entity).state.placed_furniture_has_lock and 'Remove Lock' or 'Add Lock'
    end,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local is_storage_prop = false

      pcall(function()
        is_storage_prop = exports.housing:IsStorageProp(GetEntityModel(entity))
      end)

      if not is_storage_prop or LocalPlayer.state.instance_number == 0 then
        return false
      end

      local entity_state = Entity(entity).state

      local placed_furniture_id = entity_state.placed_furniture_id

      if
        not placed_furniture_id or
        placed_furniture_id <= 0 or
        not exports.housing:CurrentPropertyIsWarehouse() or
        not exports.housing:CanModifyCurrentHouseFurniture() or
        not exports.housing:interactFurnitureEditMode()
      then
        return false
      end

      return placed_furniture_id
    end,
  },

  -- House furniture - wardrobe

  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_bl_wardrobe` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
    icon = 'far fa-tshirt',
    label = 'Clothing Store',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_bl_wardrobe` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobesmall01_a` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
    icon = 'far fa-tshirt',
    label = 'Clothing Store',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobesmall01_a` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobesmall01_b` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
    icon = 'far fa-tshirt',
    label = 'Clothing Store',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobesmall01_b` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobesmall01_c` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
    icon = 'far fa-tshirt',
    label = 'Clothing Store',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobesmall01_c` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobelarge01_a` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
    icon = 'far fa-tshirt',
    label = 'Clothing Store',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobelarge01_a` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobelarge01_b` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
    icon = 'far fa-tshirt',
    label = 'Clothing Store',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobelarge01_b` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_server = 'core:server:target-backhaul:openWardrobePersonal',
    icon = 'far fa-tshirt',
    label = 'Personal Wardrobe',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobelarge01_c` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
    icon = 'far fa-tshirt',
    label = 'Clothing Store',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `dnxprops_furniture_wardrobelarge01_c` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  -- House Hair and Makeup
  {
    event_client = 'blrp_character:openBarbershop',
    icon = 'fa-regular fa-face-awesome',
    label = 'Hair and Makeup',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `pata_2bathroom29` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_character:openBarbershop',
    icon = 'fa-regular fa-face-awesome',
    label = 'Hair and Makeup',

    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `pata_2bathroom15` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  -- House furniture - warehouse management laptop
  {
    event_server = 'blrp_housing:server:openManagementLaptop',
    icon = 'fa-regular fa-computer-classic',
    label = function()
      if exports.blrp_core:me().hasOrInheritsGroup('moderator') then
        local property_data = exports.housing:GetCurrentHouse()

        return 'Business Management (' .. (property_data.business_name or '?') .. ')'
      end

      return 'Business Management'
    end,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `xm_prop_x17_laptop_avon` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  -- House furniture - residential management laptop
  {
    event_server = 'blrp_housing:server:openResidenceManagement',
    icon = 'fa-regular fa-computer-classic',
    label = 'Residence Management',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `xm_prop_x17_laptop_mrsr` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end
  },

  -- House furniture - LEO clock in with tablet prop
  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'Sheriff',
    icon = 'fa-regular fa-computer-classic',
    label = 'Clock In - BCSO',
    auto_busy = true,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_bcso') >= 2) and not exports.blrp_core:me().hasGroup('LEO')
    end
  },
  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'Sheriff+Ranger_Internal',
    icon = 'fa-regular fa-computer-classic',
    label = 'Clock In - BCSO (Ranger)',
    auto_busy = true,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_bcso') >= 2) and not exports.blrp_core:me().hasGroup('LEO')
    end
  },

  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'LSPD',
    icon = 'fa-regular fa-computer-classic',
    label = 'Clock In - LSPD',
    auto_busy = true,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_lspd') >= 2) and not exports.blrp_core:me().hasGroup('LEO')
    end
  },

  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'LSPD+Ranger_Internal',
    icon = 'fa-regular fa-computer-classic',
    label = 'Clock In - LSPD (Ranger)',
    auto_busy = true,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_lspd') >= 2) and not exports.blrp_core:me().hasGroup('LEO')
    end
  },

  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'SAHP',
    icon = 'fa-regular fa-computer-classic',
    label = 'Clock In - SAHP',
    auto_busy = true,

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_sasp') >= 0) and not exports.blrp_core:me().hasGroup('LEO')
    end
  },

  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'LSFD',

    icon = 'fas fa-clock',
    label = 'Clock in - LSFD',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_lsfd') >= 3) and not exports.blrp_core:me().hasGroup('LSFD')
    end
  },

  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'LSFD+TMU_Internal',

    icon = 'fas fa-clock',
    label = 'Clock in - LSFD (TMU)',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_lsfd') >= 3) and not exports.blrp_core:me().hasGroup('LSFD')
    end
  },

  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'DOJ',

    icon = 'fas fa-clock',
    label = 'Clock in - DOJ',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
      and (exports.blrp_core:me().get('rank_doj') >= 2) and not exports.blrp_core:me().hasGroup('DOJ')
    end
  },

  {
    event_server = 'vrp:server:group:ch_select',
    target_group = 'Civilian',
    icon = 'fa-regular fa-computer-classic',
    label = 'Clock Out',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      return GetEntityModel(entity) == `prop_cs_tablet` and exports.housing:CanModifyCurrentHouseFurniture() and Entity(entity).state.placed_furniture_id
    end,

    groups = {
      'LEO',
      'LSFD',
      'DOJ'
    },
  },

  -- Warehouse arc furnace
  {
    event_server = 'core:server:warehouse:openFurnace',
    icon = 'fa-regular fa-fireplace',
    label = 'Arc Furnace',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      if GetEntityModel(entity) ~= `prop_bl_arcfurnace` then
        return false
      end

      local placed_furniture_id = Entity(entity).state.placed_furniture_id

      if not placed_furniture_id or placed_furniture_id <= 0 then
        return false
      end

      return placed_furniture_id
    end,
  },

  -- House furniture - controls

  {
    event_client = 'blrp_housing:client:targetInteractFurniture',
    icon = 'fa-solid fa-arrows-up-down-left-right',
    label = 'Move',
    action = 'move',

    filter = function(entity, ray_intersect_coords)
      return exports.housing:CanModifyCurrentHouseFurniture() and exports.housing:interactFurnitureEditMode() and Entity(entity).state.placed_furniture_id
    end
  },

  {
    event_client = 'blrp_housing:client:targetInteractFurniture',
    icon = 'fa-solid fa-up-from-line',
    label = 'Pick Up',
    action = 'remove',

    filter = function(entity, ray_intersect_coords)
      return exports.housing:CanModifyCurrentHouseFurniture() and exports.housing:interactFurnitureEditMode() and Entity(entity).state.placed_furniture_id
    end
  },

  -- Label storage [warehouse only]
  {
    event_client = 'blrp_housing:client:targetInteractFurniture',
    icon = 'fa-solid fa-pencil',
    label = 'Label',
    action = 'label',

    filter = function(entity, ray_intersect_coords)
      local is_storage_prop = false

      pcall(function()
        is_storage_prop = exports.housing:IsStorageProp(GetEntityModel(entity))
      end)

      return
        is_storage_prop and
        exports.housing:CurrentPropertyIsWarehouse() and
        exports.housing:CanModifyCurrentHouseFurniture() and
        exports.housing:interactFurnitureEditMode() and
        Entity(entity).state.placed_furniture_id
    end
  },

  -- Synced storages

  {
    event_server = 'core:server:synced-storage:action',
    icon = 'fa-regular fa-box',
    label = 'Storage',
    action = 'open',

    filter = function(entity, ray_intersect_coords)
      local player_coords = GetEntityCoords(PlayerPedId())
      local distance = #(player_coords - ray_intersect_coords)
      return Entity(entity).state.synced_storage and not Entity(entity).state.locked and not Entity(entity).state.drug_storage and distance < 3
    end
  },

  {
    event_server = 'core:server:synced-storage:action',
    icon = 'fa-regular fa-lock-open',
    label = 'Unlock',
    action = 'unlock',

    filter = function(entity, ray_intersect_coords)
      if GetEntityModel(entity) == `prop_container_05a` and Entity(entity).state.synced_storage and Entity(entity).state.locked then
        local player_ped = PlayerPedId()

        local player_coords = GetEntityCoords(player_ped)
        local bone_index = GetEntityBoneIndexByName(entity, 'Latch001')
        local bone_coords = GetWorldPositionOfEntityBone(entity, bone_index)

        local distance = #(player_coords - bone_coords)

        return distance < 1
      else
       return false
      end
    end
  },

  {
      event_server = 'core:server:synced-storage:action',
      icon = 'fa-regular fa-lock',
      label = 'Lock [Staff Action]',
      action = 'lock',

      groups = {
        'moderator',
      },

      filter = function(entity, ray_intersect_coords)
        return GetEntityModel(entity) == `prop_container_05a` and Entity(entity).state.synced_storage and not Entity(entity).state.locked
      end
    },

    {
      event_server = 'core:server:synced-storage:action',
      icon = 'fa-solid fa-arrow-up',
      label = 'Pickup [Staff Action]',
      action = 'take',

      groups = {
        'moderator',
      },

      filter = function(entity, ray_intersect_coords)
        return GetEntityModel(entity) == `prop_container_05a` and Entity(entity).state.synced_storage
      end,
    },

    {
      event_server = 'core:server:synced-storage:action',
      icon = 'fa-solid fa-arrow-up',
      label = 'Take',
      action = 'take',

      filter = function(entity, ray_intersect_coords)
        return Entity(entity).state.synced_storage and GetEntityModel(entity) ~= `prop_container_05a`
      end,
    },
    -- DrugCrafting
    {
      event_server = 'core:server:drug-package:action',
      icon = 'fa-regular fa-box',
      label = 'Open Package',
      action = 'open',

      filter = function(entity, ray_intersect_coords)
        return Entity(entity).state.synced_storage and not Entity(entity).state.locked and Entity(entity).state.drug_storage
      end
    },
    {
      event_server = 'core:server:drug-package:action',
      icon = 'fa-regular fa-box',
      label = 'Test Contents (LEO)',
      action = 'leotest',

      filter = function(entity, ray_intersect_coords)
        return Entity(entity).state.synced_storage and not Entity(entity).state.locked and Entity(entity).state.drug_storage and exports.blrp_core:me().hasGroup('LEO')
      end
    },



  -- Plantables
  {
    proxied = 'pPlantables.harvestPassive',
    icon = 'fas fa-shovel',
    label = 'Harvest',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      if #(GetEntityCoords(PlayerPedId()) - ray_intersect_coords) > 1.5 then
        return
      end

      local state = Entity(entity).state

      return state.plantable_passive_id
    end,
  },

  {
    event_server = 'core:server:plantables:action',
    icon = 'fa-regular fa-wave-pulse',
    label = 'Check Health',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local state = Entity(entity).state

      if not state.plantable_id then
        return
      end

      local plantable, strain_config = exports.blrp_core:GetPlantableInfo(state.plantable_id)

      if not plantable or not strain_config then
        return
      end

      return state.plantable_id
    end
  },

  {
    event_server = 'core:server:plantables:action',
    icon = 'fa-regular fa-bottle-water',
    label = 'Water',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local state = Entity(entity).state

      if not state.plantable_id then
        return
      end

      local plantable, strain_config = exports.blrp_core:GetPlantableInfo(state.plantable_id)

      if not plantable or not strain_config or strain_config.non_plant then
        return
      end

      return state.plantable_id
    end
  },


  {
    event_server = 'core:server:plantables:action',
    icon = 'fa-solid fa-bag-seedling',
    label = 'Fertilize',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local state = Entity(entity).state
      if not state.plantable_id then
        return
      end

      local plantable, strain_config = exports.blrp_core:GetPlantableInfo(state.plantable_id)

      if not plantable or not strain_config or strain_config.non_plant then
        return
      end

      return state.plantable_id
    end
  },

  {
    event_server = 'core:server:plantables:action',
    icon = 'fa-solid fa-scissors',
    label = 'Prune',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local state = Entity(entity).state

      if not state.plantable_id then
        return
      end

      local plantable, strain_config = exports.blrp_core:GetPlantableInfo(state.plantable_id)

      if not plantable or not strain_config or strain_config.non_plant then
        return
      end

      return plantable.growth > 25 and state.plantable_id
    end
  },

  {
    event_server = 'core:server:plantables:action',
    icon = 'fa-solid fa-viruses',
    label = 'Cure',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local state = Entity(entity).state

      if not state.plantable_id then
        return
      end

      local plantable, strain_config = exports.blrp_core:GetPlantableInfo(state.plantable_id)

      if not plantable or not strain_config or strain_config.non_plant then
        return
      end

      return plantable.diseased and state.plantable_id
    end
  },

  {
    event_server = 'core:server:plantables:action',
    icon = 'fa-solid fa-skull',
    label = 'Clear',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)

      local state = Entity(entity).state

      if not state.plantable_id then
        return
      end

      local plantable, strain_config = exports.blrp_core:GetPlantableInfo(state.plantable_id)

      if not plantable or not strain_config then
        return
      end

      return state.plantable_id
    end
  },

  {
    event_server = 'core:server:plantables:action',
    icon = 'fa-solid fa-person-digging',
    label = 'Harvest',

    filter_pass_value = true,
    filter = function(entity, ray_intersect_coords)
      local state = Entity(entity).state

      if not state.plantable_id then
        return
      end

      local plantable, strain_config = exports.blrp_core:GetPlantableInfo(state.plantable_id)

      if not plantable or not strain_config then
        return
      end

      return plantable.stage >= strain_config.stages and state.plantable_id
    end
  },
}

function options_unpack(target_table)
  for _, option in pairs(target_table) do
    table.insert(any_object_options, option)
  end
end

--Unpacking Entity Options here, this allows us to manipulate and add to the options of entities dynamically rather then static
options_unpack(house_prop_options)
options_unpack(cart_cooking_options)

AddTargetModel({
  'ANY_OBJECT',
},{
  options = any_object_options,
  distance = 5.0
})
