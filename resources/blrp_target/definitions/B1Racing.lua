--[[
for k, pitstorage_coords in ipairs({
  vector4(-3110.47, -236.71, 7.85, 330),
  vector4(-3116.023, -233.504, 7.85, 330),
  vector4(-3121.668, -230.244, 7.85, 330),
  vector4(-3127.274, -227.008, 7.85, 330),
  vector4(-3132.92, -223.748, 7.85, 330),
  vector4(-3138.549, -220.499, 7.85, 330),
  vector4(-3144.116, -217.284, 7.85, 330),
  vector4(-3149.792, -214.007, 7.85, 330),
  vector4(-3155.404, -210.767, 7.85, 330),
  vector4(-3161.024, -207.522, 7.85, 330),
  vector4(-3167.1, -204.738, 7.85, 330),
}) do
  AddBoxZoneAutoname(pitstorage_coords, 1.0, 2.7, {
    minZ = 6.85,
    maxZ = 8.05,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-box',
        label = 'Storage',
        table_coords = pitstorage_coords,
        prefix = 'sft_counter_',
      },
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-regular fa-shelves',
        label = 'Parts Supply',
  
        uid = 'mechanic-parts',
  
        groups = {
          'Mechanic',
        }
      }
    },
    distance = 3.0
  })
end
AddBoxZone('B1Clock', vector3(-3205.21, -174.9, 7.84), 0.4, 0.6, {
    name = 'B1Clock',
    heading = 330.0,
    minZ = 8.04,
    maxZ = 8.44,
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Mechanic',

        icon = 'fas fa-clock',
        label = 'Clock in - Mechanic',

        groups = {
          'Civilian',
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',
      }
     },
      distance = 2.0
  })
AddBoxZone('B1Wardrobe', vector3(-3199.26, -172.77, 7.84), 0.6, 1.4, {
    name = 'B1Wardrobe',
    heading = 330.0,
    minZ = 6.84,
    maxZ = 7.64,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',
      }
     },
      distance = 3.0
  })
local hands_washed = true

AddCookingZone("Event Team", "B1Cooking", vector3(-3171.42, -197.04, 7.84), 0.6, 1.2, 325.0, 7.24, 8.44, function()
  return hands_washed
end, {
  {id = "dry_burger", label = "Grill Hamburger"},
  {id = "dry_hotdog", label = "Grill Hotdog"},
},true)
]]