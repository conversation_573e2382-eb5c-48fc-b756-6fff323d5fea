AddTargetModel({
  `prop_bl_salc_terminal_247`,
  `prop_bl_salc_terminal_247m`,
  `prop_bl_salc_terminal_v_10`,
  `bl_cs4_10_salc_terminal`,
  `bl_vbm_salc_terminal`,
}, {
  options = {
    {
      event_server = 'blrp_lotto:server:buyScratchers',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Buy Scratchers',
    },
    {
      event_server = 'blrp_lotto:server:cashScratchers',
      icon = 'fa-solid fa-hand-holding-dollar',
      label = 'Cash Scratchers',
    },
    {
      event_server = 'blrp_lotto:server:buyDrawable',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Buy Super Stack Tickets',

      variant = 'sss',
    },
    {
      event_server = 'blrp_lotto:server:buyDrawable',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Buy Cash Cow Tickets',

      variant = 'bcc',
    },
    {
      event_server = 'blrp_lotto:server:cashDrawables',
      icon = 'fa-solid fa-hand-holding-dollar',
      label = 'Check Tickets',
    },
  },
  distance = 3.0
})
