-- AddBoxZone('DavisPDMainDesk', vector3(381.1243, -1595.637, 30.18285), 0.7, 2.1, {
--   name = 'DavisPDMainDesk',
--   heading = 230.0,
--   minZ = 30.0,
--   maxZ = 31.5,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:prison:checkParoleTime',
--       icon = 'fa-regular fa-clock-desk',
--       label = 'Check Parole Time',
--     },
--     {
--       event_server = 'core:server:takePoliceReport',
--       icon = 'far fa-clipboard-list',
--       label = 'Make Report',
--     },
--     {
--       event_server = 'vrp:server:group:ch_select',
--       target_group = 'LSPD',

--       icon = 'fas fa-clock',
--       label = 'Clock in - LSPD',

--       groups = {
--         'Civilian'
--       }
--     },
--     {
--       event_server = 'vrp:server:group:ch_select',
--       target_group = 'LSPD+Ranger_Internal',

--       icon = 'fas fa-clock',
--       label = 'Clock in - LSPD (<PERSON>)',

--       groups = {
--         'Civilian'
--       }
--     },
--     {
--       event_server = 'vrp:server:group:ch_select',
--       target_group = 'Sheriff',

--       icon = 'fas fa-clock',
--       label = 'Clock In - BCSO',

--       groups = {
--         'Civilian'
--       }
--     },
--     {
--       event_server = 'vrp:server:group:ch_select',
--       target_group = 'Sheriff+Ranger_Internal',

--       icon = 'fas fa-clock',
--       label = 'Clock in - BCSO (Ranger)',

--       groups = {
--         'Civilian'
--       }
--     },
--     {
--       event_server = 'vrp:server:group:ch_select',
--       target_group = 'SAHP',

--       icon = 'fas fa-clock',
--       label = 'Clock in - SAHP',

--       groups = {
--         'Civilian'
--       }
--     },
--     {
--       event_server = 'vrp:server:group:ch_select',
--       target_group = 'Civilian',

--       icon = 'fas fa-clock',
--       label = 'Clock Out',
--     },
--   },
--   distance = 3.0
-- })

-- AddBoxZone('DavisPDPoliceEquipment', vector3(365.4386, -1604.002, 27.25286), 0.6, 4.5, {
--   name = 'DavisPDPoliceEquipment',
--   heading = 230.0,
--   minZ = 24.5,
--   maxZ = 27.3,
-- }, {
--   options = {
--     {
--       event_server = 'blrp_core:server:item-store:resolveTargetConfig',
--       icon = 'far fa-raygun',
--       label = 'Police Equipment',

--       uid = 'leo-equipment',

--       groups = {
--         'LEO',
--       },
--     },
--     {
--       event_server = 'blrp_core:server:item-store:resolveTargetConfig',
--       icon = 'far fa-raygun',
--       label = 'Police Tints',

--       uid = 'leo-tints',

--       groups = {
--         'LEO',
--       },
--     },
--     {
--       event_server = 'blrp_core:server:item-store:resolveTargetConfig',
--       icon = 'far fa-raygun',
--       label = 'LSPD Supervisor Shop',

--       uid = 'leo-lspd-supervisor',

--       groups = {
--         'police_rank5',
--       }
--     },
--     {
--       event_server = 'blrp_core:server:item-store:resolveTargetConfig',
--       icon = 'far fa-raygun',
--       label = 'BCSO Supervisor Shop',

--       uid = 'leo-bcso-supervisor',

--       groups = {
--         'sheriff_rank5',
--       }
--     },
--     {
--       event_server = 'core:server:target-backhaul:openChest',
--       icon = 'far fa-box',
--       label = 'Police Locker',

--       chest_name = 'locker_storage_char_id_davis',
--       chest_radius = 3.0,
--       chest_weight = 100.0,

--       groups = {
--         'LEO',
--       },
--     },
--     {
--       event_server = 'core:server:id-cards:requestFactionCard',
--       icon = 'far fa-id-card',
--       label = 'Request LSPD ID Card',
--       card_type = 'id_lspd',

--       groups = {
--         'LSPD'
--       }
--     },
--     {
--       event_server = 'core:server:id-cards:requestFactionBadge',
--       icon = 'far fa-id-card',
--       label = 'Request LSPD Detective Badge',
--       card_type = 'prop_lspd_badge',

--       groups = {
--         'LEO INV'
--       }
--     },
--     {
--       event_server = 'core:server:id-cards:requestFactionBadge',
--       icon = 'far fa-id-card',
--       label = 'Request LSPD Badge',
--       card_type = 'prop_lspd2_badge',

--       groups = {
--         'LSPD'
--       }
--     },
--     {
--       event_server = 'core:server:target-backhaul:openLeoTrashcan',
--       icon = 'far fa-trash-alt',
--       label = 'Secure Item Disposal',
--       location = 'Davis',

--       groups = {
--         'LEO',
--       },
--     },
--   },
--   distance = 3.0
-- })

-- AddBoxZone('DavisPDLocker', vector3(361.261, -1592.544, 25.452), 1.5, 5.0, {
--   name = 'DavisPDLocker',
--   heading = 50.0,
--   minZ = 24.5,
--   maxZ = 26.5,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
--       icon = 'far fa-tshirt',
--       label = 'BCSO Uniform Wardrobe',
--       cloakroom_name = 'bcso'
--     },
--     {
--       event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
--       icon = 'far fa-tshirt',
--       label = 'LSPD Uniform Wardrobe',
--       cloakroom_name = 'lspd'
--     },
--     {
--       event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
--       icon = 'far fa-tshirt',
--       label = 'SAHP Uniform Wardrobe',
--       cloakroom_name = 'sahp'
--     },
--     {
--       event_server = 'core:server:target-backhaul:openWardrobePersonal',
--       icon = 'far fa-tshirt',
--       label = 'Personal Wardrobe'
--     },
--     {
--       event_client = 'blrp_character:openBarbershop',
--       icon = 'fa-regular fa-face-awesome',
--       label = 'Hair and Makeup',
--       cloakroom_name = 'clothingshop',
--     },
--   },
--   distance = 3.0
-- })

-- AddBoxZone('DavisPoliceComputer3', vector3(374.0498, -1605.265, 30.21858), 0.8, 1.0, {
--   name = 'DavisPoliceComputer3',
--   heading = 90.0,
--   minZ = 30.0,
--   maxZ = 30.2
-- }, {
--   options = {
--     {
--       event_server = 'core:server:police:openComputer',
--       icon = 'fas fa-computer-classic',
--       label = 'Police Computer',

--       groups = {
--         'LEO',
--       }
--     },
--     {
--       event_server = 'core:server:lawyer-tickets:generate',
--       icon = 'fas fa-fw fa-money-check-edit',
--       label = 'Write Lawyer Payment',

--       groups = {
--         'LEO',
--       }
--     },
--   },
--   distance = 3.0
-- })

-- AddBoxZone('DavisPoliceComputer1', vector3(364.263, -1602.903, 30.21293), 0.8, 1.0, {
--   name = 'DavisPoliceComputer1',
--   heading = 90.0,
--   minZ = 30.0,
--   maxZ = 30.2
-- }, {
--   options = {
--     {
--       event_server = 'core:server:police:openComputer',
--       icon = 'fas fa-computer-classic',
--       label = 'Police Computer',

--       groups = {
--         'LEO',
--       }
--     },
--     {
--       event_server = 'core:server:lawyer-tickets:generate',
--       icon = 'fas fa-fw fa-money-check-edit',
--       label = 'Write Lawyer Payment',

--       groups = {
--         'LEO',
--       }
--     },
--   },
--   distance = 3.0
-- })

-- AddBoxZone('DavisPoliceComputer2', vector3(375.9779, -1602.952, 30.2071), 0.8, 1.0, {
--   name = 'DavisPoliceComputer2',
--   heading = 90.0,
--   minZ = 30.0,
--   maxZ = 30.2
-- }, {
--   options = {
--     {
--       event_server = 'core:server:police:openComputer',
--       icon = 'fas fa-computer-classic',
--       label = 'Police Computer',

--       groups = {
--         'LEO',
--       }
--     },
--     {
--       event_server = 'core:server:lawyer-tickets:generate',
--       icon = 'fas fa-fw fa-money-check-edit',
--       label = 'Write Lawyer Payment',

--       groups = {
--         'LEO',
--       }
--     },
--   },
--   distance = 3.0
-- })
