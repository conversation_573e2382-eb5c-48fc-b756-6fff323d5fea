local hands_washed = false

AddBoxZone('SandySOPoliceReport', vector3(1829.607, 3682.061, 34.39249), 0.7, 0.7, {
  name = 'SandySOPoliceReport',
  heading = 30.0,
  minZ = 34.0,
  maxZ = 34.7,
}, {
  options = {
    {
      event_client = 'blrp_voting:client:openSheriffBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in Sheriff Election',
    },
    {
      event_server = 'core:server:prison:checkParoleTime',
      icon = 'fa-regular fa-clock-desk',
      label = 'Check Parole Time',
    },
    {
      event_server = 'core:server:takePoliceReport',
      icon = 'far fa-clipboard-list',
      label = 'Make Report',
    },
  },
  distance = 2.0
})

AddBoxZone('SandyLawyerPaymentComp', vector3(1845.096, 3688.495, 34.39645), 0.7, 0.6, {
  name = 'Sandy<PERSON>awyerPaymentComp',
  heading = 200.0,
  minZ = 34.0,
  maxZ = 34.6,
}, {
  options = {
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:lawyer-tickets:generate',
      icon = 'fas fa-fw fa-money-check-edit',
      label = 'Write Lawyer Payment',

      groups = {
        'LEO',
      }
    },
  },
  distance = 3.0
})

local computers = {
  vector3(1837.025, 3686.302, 34.39962),
  vector3(1839.094, 3680.762, 34.44047),
  vector3(1840.413, 3681.352, 34.43536),
  vector3(1839.378, 3680.221, 34.41939),
  vector3(1836.471, 3686.04, 34.42029),
}

for i, coords in ipairs(computers) do
  local zoneName = 'SandySOComputer' .. i
  AddBoxZone(zoneName, coords, 0.6, 0.8, {
    name = zoneName,
    heading = 210.0,
    minZ = 34.0,
    maxZ = 34.6,
  }, {
    options = {
      {
        event_server = 'core:server:police:openComputer',
        icon = 'fas fa-computer-classic',
        label = 'Police Computer',
        groups = {
          'LEO',
        }
      },
    },
    distance = 3.0
  })
end

local upstairs_computers = {
  vector3(1843.904, 3686.575, 39.02071),
  vector3(1845.83, 3683.195, 39.04215),
  vector3(1847.541, 3680.205, 39.02244),
}

for i, coords in ipairs(upstairs_computers) do
  local zoneName = 'SandySOComputerUS' .. i
  AddBoxZone(zoneName, coords, 0.5, 0.7, {
    name = zoneName,
    heading = 210.0,
    minZ = 38.8,
    maxZ = 39.4,
  }, {
    options = {
      {
        event_server = 'core:server:police:openComputer',
        icon = 'fas fa-computer-classic',
        label = 'Police Computer',
        groups = {
          'LEO',
        }
      },
    },
    distance = 3.0
  })
end



AddBoxZone('SandySOMainDesk', vector3(1830.281, 3680.807, 34.39249), 0.7, 0.7, {
  name = 'SandySOMainDesk',
  heading = 30.0,
  minZ = 34.0,
  maxZ = 34.7,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff',

      icon = 'fas fa-clock',
      label = 'Clock In - BCSO',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff Tow Truck',

      icon = 'fas fa-clock',
      label = 'Clock In - BCSO Tow Truck',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'SAHP',

      icon = 'fas fa-clock',
      label = 'Clock in - SAHP',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 3.0
})

AddBoxZone('SandySOPoliceEquipment1', vector3(1837.31, 3681.82, 38.93), 1.3, 3.2, {
  name = 'SandySOPoliceEquipment1',
  heading = 300,
  minZ = 37.93,
  maxZ = 40.23
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Tints',

      uid = 'leo-tints',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'BCSO Supervisor Shop',

      uid = 'leo-bcso-supervisor',

      groups = {
        'sheriff_rank5',
      }
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'Police Locker',

      chest_name = 'locker_storage_char_id_3',
      chest_radius = 3.0,
      chest_weight = 100.0,

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request BCSO ID Card',
      card_type = 'id_bcso',

      groups = {
        'Sheriff'
      }
    },
    {
      event_server = 'core:server:id-cards:requestFactionBadge',
      icon = 'far fa-id-card',
      label = 'Request BCSO Badge',
      card_type = 'prop_bcso_badge',

      groups = {
        'Sheriff'
      }
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Sandy',

      groups = {
        'LEO',
      },
    },
  },
  distance = 3.0
})

AddCookingZone('LEO', 'BCSOCrafting1', vector3(1844.643, 3681.937, 34.29311), 0.8, 0.8,30.0, 34.1, 38.17304, function()
  return hands_washed
end, {
  { id = 'food_gribblegrabble', label = 'Cook Gribble Grabble' },
  { id = 'food_meatymanwich', label = 'Cook Meaty Manwich' },
})



AddBoxZone('SandySOLocker1', vector3(1838.599, 3679.584, 39.10989), 1.0, 3.75, {
  name = 'SandySOLocker1',
  heading = 210.0,
  minZ = 38.3,
  maxZ = 40.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'BCSO Uniform Wardrobe',
      cloakroom_name = 'bcso'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSPD Uniform Wardrobe',
      cloakroom_name = 'lspd'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'SAHP Uniform Wardrobe',
      cloakroom_name = 'sahp'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('SandySOCanteen', vector3(1844.09, 3682.78, 34.33), 0.75, 0.75, {
  name = 'SandySOCanteen',
  heading = 30,
  minZ = 33.33,
  maxZ = 35.18
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-burger-soda',
      label = 'Canteen',

      uid = 'leo-canteen-sandy',

      groups = {
        'LEO',
      },
    },
  },
  distance = 2.0
})

AddBoxZone('SandySOCommandStorage', vector3(1846.304, 3678.761, 39.08427), 0.81, 0.9, {
  name = 'SandySOCommandStorage',
  heading = 120.0,
  minZ = 38.2,
  maxZ = 40.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'BCSO Command File Cabinet',

      chest_name = 'locker_bcsocommand_sandy',
      chest_radius = 3.0,
      chest_weight = 1000.0,
      chest_permission = 'sheriff.commandstorage',
    },
  },
  distance = 2.0
})

AddBoxZone('SandySOHatBox', vector3(1843.707, 3691.501, 39.64032), 0.6, 0.6, {
  name = 'SandySOHatBox',
  heading = 206.0,
  minZ = 39.4,
  maxZ = 39.9,
}, {
  options = {
    {
      event_client = 'core:client:target-backhaul:takeCowboyHat',
      icon = 'far fa-hat-cowboy',
      label = 'Take Hat'
    }
  },
  distance = 1.5
})

AddBoxZone('SandySOHatBox2', vector3(1838.881, 3674.547, 35.09076), 0.6, 0.6, {
  name = 'SandySOHatBox2',
  heading = 206.0,
  minZ = 34.8,
  maxZ = 35.3,
}, {
  options = {
    {
      event_client = 'core:client:target-backhaul:takeCowboyHat',
      icon = 'far fa-hat-cowboy',
      label = 'Take Hat'
    }
  },
  distance = 1.5
})

AddBoxZone('BSCOBarberSandy', vector3(1849.44, 3674.17, 38.93), 1.1, 0.2, {
  name = 'BSCOBarberSandy',
  heading = 300,
  minZ = 38.93,
  maxZ = 40.43,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  distance = 3.0,
})

RegisterNetEvent('blrp_target:sandyso:washHands', function()
  exports['mythic_progbar']:Progress({
    name = 'sandyso_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('SandySOSink', vector3(1844.571, 3679.352, 34.326), 0.9, 0.8, {
  name = 'SandySOSink',
  heading = 118.0,
  minZ = 34.0,
  maxZ = 34.4,
}, {
  options = {
    {
      event_client = 'blrp_target:sandyso:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        'LEO',
        'Ranger',
        'Civilian',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('SandySOEvidenceRoom', vector3(1857.167, 3680.607, 34.44648), 1.5, 0.5, {
  name = 'SandySOEvidenceRoom',
  heading = 120.0,
  minZ = 33.7,
  maxZ = 35.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Sandy',

      groups = {
        'LEO',
      },
    },
  },
  distance = 2.0
})
