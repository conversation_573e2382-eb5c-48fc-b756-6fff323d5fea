AddBoxZoneAutoname(vector4(-1194.584, -1514.521, 4.322783, 305), 0.6, 4.0, {
  minZ = 3.0,
  maxZ = 6.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'BCSO Uniform Wardrobe',
      cloakroom_name = 'bcso',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'LSPD Uniform Wardrobe',
      cloakroom_name = 'lspd',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'SAHP Uniform Wardrobe',
      cloakroom_name = 'sahp',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Tints',

      uid = 'leo-tints',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'LSPD Supervisor Shop',

      uid = 'leo-lspd-supervisor',

      groups = {
        'LEO',
      },

      groups = {
        'police_rank5',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'BCSO Supervisor Shop',

      uid = 'leo-bcso-supervisor',

      groups = {
        'sheriff_rank5',
      }
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'Police Locker',

      chest_name = 'locker_storage_char_id_vespuccicpo',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'VespucciCPO',

      groups = {
        'LEO',
      },
    },
  },
  distance = 4.0
})

AddBoxZoneAutoname(vector4(-1201.278, -1519.892, 4.247845, 35), 0.6, 0.6, {
  minZ = 4.2,
  maxZ = 4.8
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff',

      icon = 'fas fa-clock',
      label = 'Clock In - BCSO',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'SAHP',

      icon = 'fas fa-clock',
      label = 'Clock in - SAHP',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
    {
      event_server = 'core:server:police:openComputer',
      icon = 'fas fa-computer-classic',
      label = 'Police Computer',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:lawyer-tickets:generate',
      icon = 'fas fa-fw fa-money-check-edit',
      label = 'Write Lawyer Payment',

      groups = {
        'LEO',
      }
    },
  },
  distance = 3.0
})
