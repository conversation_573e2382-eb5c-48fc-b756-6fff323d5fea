AddBoxZone('PrisonFrontDesk', vector3(1840.369, 2578.782, 45.87023), 1.0, 0.5, {
  name = 'PrisonFrontDesk',
  heading = 270.0,
  minZ = 45.8,
  maxZ = 46.2,
  notifyText = '<i class="fa-regular fa-eye"></i> Bolingbroke Reception',
}, {
  options = {
    {
      event_server = 'core:server:prison:checkParoleTime',
      icon = 'fa-regular fa-clock-desk',
      label = 'Check Parole Time',
    },
    {
      event_server = 'blrp_inventory:openPrisonLockerFromDesk',
      icon = 'fa-solid fa-search',
      label = 'Prison Locker',
    },
    {
      event_server = 'core:police:pingPrison',
      icon = 'fa-solid fa-bell',
      label = 'Ping Inmates & Guards to Visitors',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'DOC',

      icon = 'fas fa-clock',
      label = 'Clock in - DOC',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'LSPD+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - LSPD (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Sheriff+Ranger_Internal',

      icon = 'fas fa-clock',
      label = 'Clock in - BCSO (Ranger)',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',

      groups = {
        'LEO',
        'DOC',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BolingbrokeBarber', vector3(1838.19, 2569.733, 46.50063), 0.2, 1.3, {
  name = 'BolingbrokeBarber',
  heading = 90.0,
  minZ = 46.0,
  maxZ = 47.0,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  distance = 3.0
})

AddBoxZone('BolingbrokeSupervisorStorage', vector3(1688.75, 2571.03, 50.33177), 0.7, 2.5, {
  name = 'BolingbrokeSupervisorStorage',
  heading = 270.0,
  minZ = 49.5,
  maxZ = 50.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'DOC Command File Cabinet',

      chest_name = 'locker_doccmd_bb',
      chest_radius = 3.0,
      chest_weight = 500.0,
      chest_permission = 'doc.supervisor',
    },
  },
  distance = 3.0
})

AddBoxZone('BolingbrokeLocker1', vector3(1834.899, 2570.043, 46.89041), 0.4, 3.2, {
  name = 'BolingbrokeLocker1',
  heading = 180.0,
  minZ = 34.0,
  maxZ = 46.89041,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'DOC Locker',

      chest_name = 'locker_storage_char_id_5',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'DOC Uniform Wardrobe',
      cloakroom_name = 'bailiff'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('BolingbrokeLocker2', vector3(1833.173, 2571.847, 46.89705), 0.4, 3.2, {
  name = 'BolingbrokeLocker2',
  heading = 90.0,
  minZ = 34.0,
  maxZ = 46.89041,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'DOC Locker',

      chest_name = 'locker_storage_char_id_5',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'DOC Uniform Wardrobe',
      cloakroom_name = 'bailiff'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('BolingbrokeLocker3', vector3(1833.165, 2575.196, 46.92486), 0.4, 3.2, {
  name = 'BolingbrokeLocker3',
  heading = 90.0,
  minZ = 34.0,
  maxZ = 46.89041,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'DOC Locker',

      chest_name = 'locker_storage_char_id_5',
      chest_radius = 3.0,
      chest_weight = 100.0,
      chest_permission = 'police.store_weapons',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'DOC Uniform Wardrobe',
      cloakroom_name = 'bailiff'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('DOCEquipmentBolingbroke', vector3(1844.365, 2573.429, 47.99726), 1.0, 2.0, {
  name = 'DOCEquipmentBolingbroke',
  heading = 180.0,
  minZ = 45.0,
  maxZ = 47.6,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'DOC Supervisor Shop',

      uid = 'leo-doc-supervisor',

      groups = {
        'doc_rank4',
        'doc_rank5',
      }
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request DOC ID Card',
      card_type = 'id_doc',

      groups = {
        'DOC'
      }
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Bolingbroke',
    },
  },
  distance = 2.5
})

AddBoxZone('DOCCommissaryBolingbroke', vector3(1786.899, 2564.63, 45.94507), 2.5, 2.5, {
  name = 'DOCCommissaryBolingbroke',
  heading = 180.0,
  minZ = 44.0,
  maxZ = 46.9,
  notifyText = '<i class="fa-regular fa-eye"></i> Commissary',
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fas fa-hand-holding-usd',
      label = 'Commissary',

      uid = 'prison-cafeteria',
    },
  },
  distance = 2.0
})
