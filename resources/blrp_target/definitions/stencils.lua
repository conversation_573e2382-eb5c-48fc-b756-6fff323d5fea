AddGangBenchOption('Vagos', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_vagos',

  business_name = 'Vagos',
  component_id = 'craft-stencil_vagos',
  component_perm = 'crafting',
})

AddGangBenchOption('Condemned MC', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_cmc',

  business_name = 'Condemned MC',
  component_id = 'craft-stencil_cmc',
  component_perm = 'crafting',
})

AddGangBenchOption('SFT Pawn', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_cartel',

  business_name = 'SFT Pawn',
  component_id = 'craft-stencil_cartel',
  component_perm = 'crafting',
})

AddGangBenchOption('Beechwood Disciples', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_bd',

  business_name = 'Beechwood Disciples',
  component_id = 'craft-stencil_bd',
  component_perm = 'crafting',
})

AddGangBenchOption('305 Mafia', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_305',

  business_name = '305 Mafia',
  component_id = 'craft-stencil_305',
  component_perm = 'crafting',
})

AddGangBenchOption('Ikonz', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_ikonz',

  business_name = 'Ikonz',
  component_id = 'craft-stencil_ikonz',
  component_perm = 'crafting',
})

AddGangBenchOption('Lost MC', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_lost',

  business_name = 'Lost MC',
  component_id = 'craft-stencil_lost',
  component_perm = 'crafting',
})

AddGangBenchOption('Trojan Armor', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_lsf',

  business_name = 'Trojan Armor',
  component_id = 'craft-stencil_lsf',
  component_perm = 'crafting',
})

AddGangBenchOption('Asian Market', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_yakuza',

  business_name = 'Asian Market',
  component_id = 'craft-stencil_yakuza',
  component_perm = 'crafting',
})

AddGangBenchOption('Ballas', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_ballas',

  business_name = 'Ballas',
  component_id = 'craft-stencil_ballas',
  component_perm = 'crafting',
})

AddGangBenchOption('The Unknown', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_aces',

  business_name = 'The Unknown',
  component_id = 'craft-stencil_aces',
  component_perm = 'crafting',
})

AddGangBenchOption('Forum Family', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_fdf',

  business_name = 'Forum Family',
  component_id = 'craft-stencil_FDF',
  component_perm = 'crafting',
})

AddGangBenchOption('Messina Crime Family', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_mcf',

  business_name = 'Messina Crime Family',
  component_id = 'craft-stencil_MCF',
  component_perm = 'crafting',
})

AddGangBenchOption('Yellers Market', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_scorpions',

  business_name = 'Yellers Market',
  component_id = 'craft-stencil_SCORPIONS',
  component_perm = 'crafting',
})

AddGangBenchOption('The Chosen Few Mc', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_tcf',

  business_name = 'The Chosen Few Mc',
  component_id = 'craft-stencil_TCF',
  component_perm = 'crafting',
})

AddGangBenchOption('El Rancho Ravens', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_collective',

  business_name = 'El Rancho Ravens',
  component_id = 'craft-stencil_collective',
  component_perm = 'crafting',
})