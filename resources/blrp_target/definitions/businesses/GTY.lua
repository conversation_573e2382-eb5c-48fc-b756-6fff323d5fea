--GO TRUCK YOURSELF
AddBoxZone('GTYClock', vector3(2673.31, 3373.25, 58.51), 0.6, 1.2, {
  name = 'GTYClockin',
  heading = 355.0,
  minZ=58.51,
  maxZ=58.91,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Truck Driver',

      icon = 'fas fa-clock',
      label = 'Clock in - Truck Driver',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.5
})

AddBoxZone('GTYManagement', vector3(2675.16, 3383.09, 58.53), 0.6, 0.4, {
    name = 'GTYManagement',
    heading = 340.0,
    minZ=58.13,
    maxZ=58.93,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-computer-classic',
        label = 'Management',
  
        business_name = 'Go Truck Yourself',
        component_id = 'management',
      },
    },
    distance = 1.5
  })

AddBoxZone('GTYSupply', vector3(2673.48, 3362.69, 57.21), 3.2, 1.2, {
  name = 'GTYSupply',
  heading = 335.0,
  minZ=56.21,
  maxZ=59.41,
}, {
  options = {
      {
          event_server = 'blrp_core:server:item-store:resolveTargetConfig',
          icon = 'fa-regular fa-shelves',
          label = 'Supply Store',
      
          uid = 'gty-supply',
      
          groups = {
            'Go Truck Yourself',
          }
      },
      {
          event_server = 'blrp_core:server:item-store:resolveTargetConfig',
          icon = 'fa-regular fa-shelves',
          label = 'Management Supply',
      
          uid = 'gty-supply-m',
          business_name = 'Go Truck Yourself',
          component_id = 'management',
      },
  },
  distance = 2.0
})
AddBoxZone('GTYStorageMain', vector3(2679.08, 3361.66, 57.21), 1.2, 3.2, {
  name = 'GTYStorageMain',
  heading = 335.0,
  minZ=56.21,
  maxZ=59.41,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Go Truck Yourself',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        uid ='1',
      },

    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',
      groups = {
        'Go Truck Yourself',
      }
    },
  },
  distance = 2.5
})