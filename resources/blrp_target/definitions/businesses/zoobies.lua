-- AddBoxZone('ZoobiesSupply', vector3(114.261, -1694.89, 131.1948), 1.0, 1.0, {
--     name = 'ZoobiesSupply',
--     heading = 200.0,
--     minZ = 131.0,
--     maxZ = 131.3,
--   }, {
--     options = {
--       {
--         event_server = 'blrp_core:server:item-store:resolveTargetConfig',
--         icon = 'fa-regular fa-dollar-sign',
--         label = 'Buy Supplies',

--         uid = 'zoobies-supply',
--       },
--     },
--     distance = 2.5
-- })

-- for k, tray_coords in ipairs({
--     vector3(100.205, -1689.666, 131.541),
--   }) do
--     AddBoxZone('ZoobiesTray' .. k, tray_coords, 1.0, 1.0, {
--       name = 'ZoobiesTray' .. k,
--       heading=330,
--       minZ=131.3,
--       maxZ=131.7,
--     }, {
--       options = {
--         {
--           event_server = 'core:server:restaurant-tables:openTarget',
--           icon = 'fa-regular fa-utensils',
--           label = 'Tray',
--           table_coords = tray_coords,
--         },
--       },
--       distance = 2.5
--     })
-- end

--[[
AddBoxZone('ZoobiesCrafting', vector3(1162.74, -449.973, 168.7142), 0.8, 1.9, {
  name = 'ZoobiesCrafting',
  heading = 200.0,
  minZ = 167.0452,
  maxZ = 168.9452,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-oven',
      label = 'Fry BD THC Donut',

      item_id = 'bd_thcdonut',

      business_name = 'Zoobies',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-oven',
      label = 'Bake THC BD Brownie',

      item_id = 'bd_thc_brownie',

      business_name = 'Zoobies',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})
]]

-- AddGangBenchOption('Zoobies', {
--   event_server = 'core:server:target-backhaul:craft',
--   icon = 'fa-regular fa-oven',
--   label = 'Bake THC BD Brownie',

--   item_id = 'bd_thc_brownie',

--   business_name = 'Zoobies',
--   component_id = 'crafting',
-- })

-- AddGangBenchOption('Zoobies', {
--   event_server = 'core:server:target-backhaul:craft',
--   icon = 'fa-regular fa-oven',
--   label = 'Fry BD THC Donut',

--   item_id = 'bd_thcdonut',

--   business_name = 'Zoobies',
--   component_id = 'crafting',
-- })
