AddBoxZone('Dynasty8Management', vector3(-724.0953, 263.5391, 84.03727), 0.8, 0.8, {
  name = 'Dynasty8Management',
  heading = 115.0,
  minZ = 84.0,
  maxZ = 84.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Dynasty 8',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZone('Dynasty8Storage', vector3(-726.2283, 261.0517, 83.95398), 0.4, 2.0, {
  name = 'Dynasty8Storage',
  heading = 115.0,
  minZ = 83.5,
  maxZ = 84.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Dynasty 8',
      component_id = 'storage',
    },
  },
  distance = 3.0
})

AddBoxZone('Dynasty8Closet', vector3(-715.8022, 266.8833, 84.05965), 0.4, 2.0, {
  name = 'Dynasty8Closet',
  heading = 115.0,
  minZ = 83.0,
  maxZ = 84.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Dynasty 8',
      }
    },
    {
      event_server = 'core:server:id-cards:getRealtorId',
      icon = 'far fa-id-card',
      label = 'Get Realtor ID Card',

      groups = {
        'Dynasty 8',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'D8 Supplies',

      uid = 'd8-supplies',

      groups = {
        'Dynasty 8',
      }
    },
  },
  distance = 3.0
})
