AddBoxZone('CRUManagement', vector3(175.8543, 6383.784, 31.36227), 0.5, 0.5, {
  name = 'CRUManagement',
  heading = 300.0,
  minZ = 31.3,
  maxZ = 31.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'LEO CRU',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('CRUStorageMain', vector3(151.1965, 6365.335, 31.77033), 0.8, 3.0, {
  name = 'CRUStorageMain',
  heading = 300.0,
  minZ = 30.7,
  maxZ = 32.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'LEO CRU',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('CRUCloset', vector3(180.0625, 6380.833, 32.47192), 0.8, 1.0, {
  name = 'CRUCloset',
  heading = 210.0,
  minZ = 31.0,
  maxZ = 32.47,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'LEO CRU',
      }
    },
  },
  distance = 2.5
})
