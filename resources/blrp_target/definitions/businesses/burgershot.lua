local hands_washed = false

RegisterNetEvent('blrp_target:burgershot:washHands', function()
  local clock_in_data = pCore.isClockedIntoTabletBusiness({ 'Burgershot' })

  if clock_in_data and not clock_in_data.clocked_in and
  (
    not GlobalState.is_dev or
    not exports.blrp_core:me().request('[DEV] Override Clock-in to wash hands?')
  )
  then
    exports.blrp_core:me().notify('You are not clocked in (through the tablet)!')
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'burgershot_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('BurgershotManagement', vector3(-1198.352, -898.8184, 13.60119), 0.9, 0.7, {
  name = 'BurgershotManagement',
  heading = 125.0,
  minZ = 13.5,
  maxZ = 14.6
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Burgershot',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('BurgershotCloset', vector3(-1180.5, -892.7845, 13.94532), 3.1, 1.0, {
  name = 'BurgershotCloset',
  heading = 125.0,
  minZ = 13.0,
  maxZ = 15.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Burgershot',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('BurgershotStorageMain', vector3(-1196.427, -900.0241, 13.75207), 0.6, 2.5, {
  name = 'BurgershotStorageMain',
  heading = 35.0,
  minZ = 13.0,
  maxZ = 14.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Burgershot',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('BurgershotStorage1', vector3(-1192.287, -898.2814, 13.91912), 1.0, 2.5, {
  name = 'BurgershotStorage1',
  heading = 305.0,
  minZ = 13.0,
  maxZ = 14.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Burgershot',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'burgershot_a'
      },
    },
  },
  distance = 3.0
})

AddBoxZone('BurgershotStorage2', vector3(-1183.549, -900.5987, 14.03058), 2.7, 1.0, {
  name = 'BurgershotStorage2',
  heading = 305.0,
  minZ = 13.0,
  maxZ = 15.3,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'burgershot-supply',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Burgershot',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        named = 'bs_fridge_b'
      },
    },
  },
  distance = 3.0
})

AddBoxZone('BurgershotBadeats', vector3(-1177.05, -897.0, 13.94985), 0.3, 0.3, {
  name = 'BurgershotBadeats',
  heading = 214.0,
  minZ = 13.85,
  maxZ = 14.1,
}, {
  options = {
    {
      event_server = 'core:server:badeats:openRestaurantMenuTarget',
      icon = 'far fa-tablet',
      label = 'Badeats Admin',
      restaurant_name = 'Burgershot',

      groups = {
        'Burgershot'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BurgershotSink', vector3(-1186.707, -903.0113, 13.67913), 0.8, 0.9, {
  name = 'BurgershotSink',
  heading = 214.0,
  minZ = 13.5,
  maxZ = 14.0,
}, {
  options = {
    {
      event_client = 'blrp_target:burgershot:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        'Burgershot'
      }
    },
  },
  distance = 2.5
})

for k, register_coords in ipairs({
  vector3(-1189.008, -894.7058, 14.09413),
  vector3(-1190.592, -895.6551, 14.1299),
  vector3(-1187.57, -893.5995, 14.13381),
}) do
  AddBoxZone('BurgershotRegister' .. k, register_coords - vector3(0.05, -0.05, 0), 0.4, 0.5, {
    name = 'BurgershotRegister' .. k,
    heading = 305.0,
    minZ = 14.0,
    maxZ = 14.4,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-general:cashRegister',
        icon = 'fa-regular fa-cash-register',
        label = 'Use Register',

        groups = {
          'Burgershot'
        }
      },
      {
        event_server = 'core:server:restaurant-general:requestRestock',
        icon = 'fa-regular fa-cash-register',
        label = 'Order Stock',

        groups = {
          'Burgershot'
        }
      },
      {
        event_server = 'core:server:target-backhaul:activatePanicAlarm',
        icon = 'fa-regular fa-triangle-exclamation',
        label = 'Trigger Panic Alarm',

        location_override = 'Burgershot',

        groups = {
          'Burgershot'
        }
      }
    },
    distance = 2.5
  })
end

AddBoxZone('BurgershotRegisterDriveThru', vector3(-1194.776, -905.1252, 13.93974), 0.4, 0.5, {
  name = 'BurgershotRegisterBurgershotRegisterDriveThru',
  heading = 260.0,
  minZ = 13.8,
  maxZ = 14.4,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',
      extended_range = true,

      groups = {
        'Burgershot'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-triangle-exclamation',
      label = 'Trigger Panic Alarm',

      location_override = 'Burgershot',

      groups = {
        'Burgershot'
      }
    }
  },
  distance = 2.5
})

for k, tray_coords in ipairs({
  vector3(-1188.472, -894.3184, 13.94627),
  vector3(-1189.513, -895.0189, 13.94627),
  vector3(-1191.318, -896.2966, 13.94627),
}) do
  AddBoxZone('BurgershotTray' .. k, tray_coords, 0.6, 0.4, {
    name = 'BurgershotTray' .. k,
    heading = 305.0,
    minZ = 13.80,
    maxZ = 14.3,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Food Tray',
        table_coords = tray_coords,
      },
    },
    distance = 2.5
  })
end

AddBoxZone('BurgershotTrayDriveThru', vector3(-1193.952, -905.439, 13.77977), 0.6, 0.4, {
  name = 'BurgershotTrayDriveThru',
  heading = 260.0,
  minZ = 13.60,
  maxZ = 14.2,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-1193.952, -905.439, 13.77977),
    },
  },
  distance = 2.5
})

AddBoxZone('BurgershotTrayWarmer', vector3(-1187.608, -897.0000, 14.19147), 1.0, 1.4, {
  name = 'BurgershotTrayWarmer',
  heading = 215.0,
  minZ = 13.80,
  maxZ = 15.0,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Warmer',
      table_coords = vector3(-1187.608, -897.0000, 14.19147),
    },
  },
  distance = 2.5
})

AddBoxZone('BurgershotTrayWarmer2', vector3(-1191.25, -903.8726, 14.23599), 1.4, 1.0, {
  name = 'BurgershotTrayWarmer2',
  heading = 215.0,
  minZ = 13.80,
  maxZ = 15.0,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Warmer',
      table_coords = vector3(-1191.25, -903.8726, 14.23599),
    },
  },
  distance = 2.5
})

AddBoxZone('BurgerShotGrill', vector3(-1186.793, -900.6704, 14.08879), 1.0, 1.5, {
  name = 'BurgerShotGrill',
  heading = 35.0,
  minZ = 13.0,
  maxZ = 14.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurants:burgershot:transformFood',
      icon = 'fa-regular fa-fire-burner',
      label = 'Grill Beef Patty',
      food_item_id = 'food_meat_patty',

      groups = {
        'Burgershot'
      },

      filter = function()
        return hands_washed
      end
    },
    {
      event_server = 'core:server:restaurants:burgershot:transformFood',
      icon = 'fa-regular fa-fire-burner',
      label = 'Grill Chicken',
      food_item_id = 'food_cooked_chicken',

      groups = {
        'Burgershot'
      },

      filter = function()
        return hands_washed
      end
    },
  },
  distance = 2.0
})

AddBoxZone('BurgerShotFrier',  vector3(-1187.554, -899.6832, 14.08879), 1.0, 1.7, {
  name = 'BurgerShotFrier',
  heading = 35.0,
  minZ = 13.0,
  maxZ = 14.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurants:burgershot:transformFood',
      icon = 'fa-regular fa-oven',
      label = 'Fry Bacon',
      food_item_id = 'food_cooked_bacon',

      groups = {
        'Burgershot'
      },

      filter = function()
        return hands_washed
      end
    },

    {
      event_server = 'core:server:restaurants:burgershot:transformFood',
      icon = 'fa-regular fa-oven',
      label = 'Fry Chicken Nuggets',
      food_item_id = 'food_chickennuggets',

      groups = {
        'Burgershot'
      },

      filter = function()
        return hands_washed
      end
    },

    {
      event_server = 'core:server:restaurants:burgershot:transformFood',
      icon = 'fa-regular fa-oven',
      label = 'Fry French Fries',
      food_item_id = 'food_french_fires',

      groups = {
        'Burgershot'
      },

      filter = function()
        return hands_washed
      end
    },
  },
  distance = 2.0
})

AddCookingZone('Burgershot', 'BurgershotPrep', vector3(-1185.869, -899.3746, 14.08879), 1.5, 1.0, 35.0, 13.0, 14.5, function()
  return hands_washed
end, {
  { id = 'food_stuffed_burger', label = 'Prepare Money Shot Burger' },
  { id = 'food_bacon_burger', label = 'Prepare Bacon Burger' },
  { id = 'food_killer_burger', label = 'Prepare Bleeder Burger' },
  { id = 'food_killer_doubler_burger', label = 'Prepare Heart Stopper Burger' },
  { id = 'food_tofuburger', label = 'Prepare Tofu Burger' },
  { id = 'food_chickenburger', label = 'Prepare Chicken Burger' },
  { id = 'food_fishburger', label = 'Prepare The Big Catch Burger' },
  { id = 'food_loaded_french_fires', label = 'Prepare Loaded French Fries' },
  --{ id = 'xmr_burgershot_f', label = 'Make Hot Chocolate Cookie' },
  --{ id = 'prop_bs_shotbox', label = 'Prepare Shotbox' },
  --{ id = 'food_ea25_bs', label = 'Prepare Bunny Nut Cookie' },
},
true)

AddBoxZone('BurgershotSafe', vector3(-1201.014, -896.0673, 13.33637), 1.2, 0.5, {
  name = 'BurgershotSafe',
  heading = 305.0,
  minZ = 12.9,
  maxZ = 14.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Burgershot',
      component_id = 'storage',

      args = {
        capacity = 100.0,
        uid = 'Safe',
        whitelisted_items = {
          'cash',
        }
      },
    },
  },
  distance = 2.5
})

AddCookingZone('Burgershot', 'BurgerShotDrinks1', vector3(-1191.49, -897.7048, 14.01835), 0.6, 2.2, 305.0, 13.5, 14.5, function()
  return true
end, {
  { id = 'food_strawberry_milkshake', label = 'Mix Strawberry Shake' },
  { id = 'food_chocolate_milkshake', label = 'Mix Chocolate Shake' },
  { id = 'food_vanilla_milkshake', label = 'Mix Vanilla Shake' },
  --{ id = 'food_peppermint_milkshake', label = 'Mix Peppermint Shake'},
  { id = 'food_bs_cola', label = 'Mix Burgershot Cola' },
},true)

AddCookingZone('Burgershot', 'BurgerShotDrinks2', vector3(-1191.082, -905.5446, 14.20363), 1.0, 0.5, 305.0, 13.5, 14.5, function()
  return true
end, {
  { id = 'food_strawberry_milkshake', label = 'Mix Strawberry Shake' },
  { id = 'food_chocolate_milkshake', label = 'Mix Chocolate Shake' },
  { id = 'food_vanilla_milkshake', label = 'Mix Vanilla Shake' },
  --{ id = 'food_peppermint_milkshake', label = 'Mix Peppermint Shake'},
  { id = 'food_bs_cola', label = 'Mix Burgershot Cola' },
},true)

if not IsDuplicityVersion() then
  Citizen.CreateThread(function()
    while true do
      Citizen.Wait(1)
      if #(GetEntityCoords(PlayerPedId()) - vector3(-1194.762, -908.425, 13.166)) < 2.5 then
        exports.blrp_core:DoWhenPressed(vector3(-1194.762, -908.425, 13.166), '9', 'Press {key} to grab food',
          function()
            TriggerServerEvent('core:server:restaurant-tables:openTarget', _, {
              table_coords = vector3(-1193.952, -905.439, 13.77977),
            })
          end)
      end
    end
  end)
end
