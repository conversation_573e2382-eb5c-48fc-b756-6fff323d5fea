AddBoxZone('LSTunersManagement', vector3(887.9651, -2099.277, 35.06796), 0.5, 0.7, {
  name = 'LSTunersManagement',
  heading = 290.0,
  minZ = 34.8,
  maxZ = 35.35,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Los Santos Tuners',
      component_id = 'management',
    },
  },
  distance = 1.5
})

--[[ AddBoxZone('LSTunersCrafting', vector3(916.6574, -2105.842, 30.40102), 0.8, 4.0, {
  name = 'LSTunersCrafting',
  heading = 265.0,
  minZ = 30.2,
  maxZ = 31.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Voucher',

      item_id = 'tuner_voucher',

      business_name = 'Los Santos Tuners',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Plushie',

      item_id = 'ch_prop_arcade_claw_plush_06n',

      business_name = 'Los Santos Tuners',
      component_id = 'crafting',
    },
  },
  distance = 1.5
})

AddBoxZone('LSTunersCrafting1', vector3(893.9155, -2133.101, 30.35355), 0.3, 0.4, {
  name = 'LSTunersCrafting1',
  heading = 175.0,
  minZ = 30.2,
  maxZ = 30.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft OBD Laptop',

      item_id = 'laptop_obd',

      business_name = 'Los Santos Tuners',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:rechargeOBD',
      icon = 'fa-regular fa-wrench',
      label = 'Recharge OBD Laptop',

      business_name = 'Los Santos Tuners',
      component_id = 'crafting',
    },
  },
  distance = 1.5
}) ]]

AddGangBenchOption('Los Santos Tuners', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Power Bank',

  item_id = 'powerbank',

  business_name = 'Los Santos Tuners',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('Los Santos Tuners', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft OBD Laptop',

  item_id = 'laptop_obd',

  business_name = 'Los Santos Tuners',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('Los Santos Tuners', {
  event_server = 'core:server:target-backhaul:rechargeOBD',
  icon = 'fa-regular fa-wrench',
  label = 'Recharge OBD Laptop',

  business_name = 'Los Santos Tuners',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddBoxZone('TunerFridge', vector3(889.2001, -2102.124, 34.84113), 0.9, 0.7, {
  name = 'TunerFridge',
  heading = -5.000,
  minZ = 34.0,
  maxZ = 35.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Los Santos Tuners',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersCloset1', vector3(883.2593, -2099.993, 31.55746), 0.6, 2.2, {
  name = 'LSTunersCloset1',
  heading = 355.0,
  minZ = 29.5,
  maxZ = 31.6,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Los Santos Tuners',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('LSTunersParts', vector3(916.9000, -2101.302, 30.75161), 3.8, 0.5, {
  name = 'LSTunersParts',
  heading = 175.000,
  minZ = 29.5,
  maxZ = 32.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Parts',

      business_name = 'Los Santos Tuners',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '1'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersStorage', vector3(885.6972, -2097.390, 34.88797), 0.8, 0.6, {
  name = 'LSTunersStorage',
  heading = -5.000,
  minZ = 34.0,
  maxZ = 35.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Los Santos Tuners',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersShopNos', vector3(914.9873, -2122.0, 31.73649), 1.3, 1.7, {
  name = 'LSTunersShopNos',
  heading = 265.0,
  minZ = 29.5,
  maxZ = 31.75,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Purchase NOS Products',

      uid = 'lst-nos',

      groups = {
        'Los Santos Tuners',
      }
    },
    {
      event_server = 'blrp_core:server:target-backhaul:refillNOSBottle',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Refill NOS Bottle',

      groups = {
        'Los Santos Tuners',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersShop1', vector3(914.9873, -2122.0, 31.73649), 1.3, 1.7, {
  name = 'LSTunersShop1',
  heading = 265.0,
  minZ = 29.5,
  maxZ = 31.75,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Purchase NOS Products',

      uid = 'lst-nos',

      groups = {
        'Los Santos Tuners',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersShop1', vector3(902.8018, -2132.793, 29.45313), 1.0, 1.1, {
  name = 'LSTunersShop1',
  heading = 265.0,
  minZ = 29.4,
  maxZ = 31.6,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Purchase Parts',

      uid = 'lst-general',

      groups = {
        'Los Santos Tuners',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersShop2', vector3(892.1878, -2131.908, 29.45313), 1.0, 1.1, {
  name = 'LSTunersShop2',
  heading = 265.0,
  minZ = 29.4,
  maxZ = 31.6,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Purchase Parts',

      uid = 'lst-general',

      groups = {
        'Los Santos Tuners',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersShop3', vector3(881.1143, -2130.925, 29.45313), 1.0, 1.1, {
  name = 'LSTunersShop3',
  heading = 265.0,
  minZ = 29.4,
  maxZ = 31.6,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Purchase Parts',

      uid = 'lst-general',

      groups = {
        'Los Santos Tuners',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('LSTunersStorage2', vector3(897.94, -2099.97, 34.89), 1.8, 0.5, {
  name = 'LSTunerstorage2',
  heading = 355,
  minZ = 33.89,
  maxZ = 35.89,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Los Santos Tuners',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 1000.0,
        uid = '3'
      },
    },
  },
  distance = 2.5
})
