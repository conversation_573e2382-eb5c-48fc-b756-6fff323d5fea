AddBoxZone('DumpShop', vector3(178.354, 2781.354, 45.96759), 0.3, 2, {
  name = 'DumpShop',
  heading = 9.0,
  minZ= 45.51,
  maxZ= 47.01,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craftCompostBag',
      icon = 'fa-regular fa-biohazard',
      label = 'Craft Compost bag',

      business_name = 'Dump and Pump Septic',
      component_id = 'craftCompostBag',
      component_perm = 'crafting',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Toilet Supplies',

      uid = 'toilet-supplies',

      groups = {
        'Dump and Pump Septic',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('DumpPumpCombo', vector3(177.8975, 2797.987, 46.08466), 0.9, 0.9, {
  name = 'DumpPumpCombo',
  heading = 10.0,
  minZ = 44.9,
  maxZ = 46.47,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Dump and Pump Septic',
      component_id = 'management',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Garbage Collection',

      icon = 'fa-solid fa-clock',
      label = 'Clock in - Garbage Collector',

      groups = {
        'Dump and Pump Septic'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0
})

AddBoxZone('DumpPumpStorage', vector3(184.3178, 2782.405, 46.01146), 0.9, 0.9, {
  name = 'DumpPumpStorage',
  heading = 10.0,
  minZ = 44.9,
  maxZ = 46.47,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Dump and Pump Septic',
      component_id = 'storage',
    },
  },
  distance = 2.0
})

AddBoxZone('DumpPumpCloset', vector3(180.7792, 2781.439, 46.1067), 0.9, 0.9, {
  name = 'DumpPumpCloset',
  heading = 10.0,
  minZ = 44.9,
  maxZ = 46.47,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Dump and Pump Septic',
      }
    },
  },
  distance = 2.0
})



AddBoxZone('DumpPumpDumpster', vector3(180.88,2796.523,45.819), 2.2, 3, {
  name = 'DumpPumpDumpster',
  heading = 10.0,
  minZ = 44,
  maxZ = 46.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openDumpPumpTrashcan',
      icon = 'fa-regular fa-biohazard',
      label = 'Dumpster',
      
      business_name = 'Dump and Pump Septic',
      component_id = 'trashinput',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:openDumpPumpTrashOutput',
      icon = 'fa-regular fa-inbox-out',
      label = 'Open Output',
      
      business_name = 'Dump and Pump Septic',
      component_id = 'trashoutput',
      component_perm = 'crafting',
    },
  },
  distance = 2.6
})
