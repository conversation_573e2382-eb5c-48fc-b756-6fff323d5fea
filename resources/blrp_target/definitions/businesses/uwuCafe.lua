local hands_washed = false

RegisterNetEvent('blrp_target:catCafe:washHands', function()
  local clock_in_data = pCore.isClockedIntoTabletBusiness({ "Bo<PERSON>'s Catfe" })

  if clock_in_data and not clock_in_data.clocked_in and
  (
    not GlobalState.is_dev or
    not exports.blrp_core:me().request('[DEV] Override Clock-in to wash hands?')
  )
  then
    exports.blrp_core:me().notify('You are not clocked in (through the tablet)!')
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'catCafe_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('catCafePet', vector3(-584.05, -1062.9, 22.7), 0.35, 0.42, {
    name = 'catCafePet',
    heading = 270.0,
    minZ = 22.3,
    maxZ = 22.7,
  }, {
    options = {
      {
        event_client = 'tg:petcat',
        icon = 'fa-regular fa-cat',
        label = 'Pet Cat',
      },
      {
        event_client = 'tg:headbuttcat',
        icon = 'fa-regular fa-face-angry-horns',
        label = 'Headbutt Cat',

        filter = function()
          local me = exports.blrp_core.me()
          return (me.get('id') == 10815)
        end
      },
    },
    distance = 3
})

AddBoxZone('catCafePet2', vector3(-575.2, -1058.158, 22.3), 0.4, 0.52, {
  name = 'catCafePet2',
  heading = 270.0,
  minZ = 22.3,
  maxZ = 22.7,
}, {
  options = {
    {
      event_client = 'tg:petcat',
      icon = 'fa-regular fa-cat',
      label = 'Pet Cat',
    },
    {
      event_client = 'tg:headbuttcat',
      icon = 'fa-regular fa-face-angry-horns',
      label = 'Headbutt Cat',

      filter = function()
        local me = exports.blrp_core.me()
        return (me.get('id') == 10815)
      end
    },
  },
  distance = 3
})

AddBoxZone('catCafeSink', vector3(-587.857, -1062.544, 22.356), 0.55, 0.62, {
  name = 'catCafeSink',
  heading = 270.0,
  minZ = 21.7,
  maxZ = 22.3,
}, {
  options = {
    {
      event_client = 'blrp_target:catCafe:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        "Boo's Catfe"
      }
    },
  },
  distance = 2.5
})

AddCookingZone("Boo's Catfe", "catCafeCooking", vector3(-591, -1056.533, 22.356), 0.55, 1, 305.0, 20, 24, function()
  return hands_washed
end, {
  {id = "food_spec_eggrolls", label = "Cook Egg Rolls"},
  {id = "food_spec_sushi", label = "Prepare Sushi"},
  {id = "food_spec_ramennoodles", label = "Cook Ramen Noodles"},
  {id = "food_flan", label = 'Cook Cat Flan'},
  {id = "food_mochi", label = 'Cook Cat Mochi'},
  {id = "food_chickenbentobox", label = 'Cook Chicken Bento Box'},
  {id = "food_kchickenc", label = "Cook Katsu Chicken Curry"},
  --{id = "xmr_boos_f", label = "Cook Cat Dumpling"},
  --{ id = 'food_ea25_boo', label = 'Cook Bunny Onigiri' },
  { id = 'food_pr25_boo', label = 'Cook Pride Pancakes' },
},true)

AddCookingZone("Boo's Catfe", "catCafeDrinkMaker", vector3(-586.835, -1061.930, 22.344), 0.55, 1, 305.0, 21.5, 23.2, function()
  return hands_washed
end, {
  {id = "food_spec_bobatea", label = "Mix Boba Tea"},
  {id = "food_spec_matchatea", label = "Mix Matcha Tea"},
  {id = "food_oolong", label = "Mix Oolong Tea"},
  --{id = "xmr_boos_d", label = "Mix Boo's Sake"},
},true)

AddBoxZone('catCafeFridge', vector3(-590.85, -1058.615, 22.344), 0.55, 1, {
  name = 'catCafeFridge',
  heading = 270.0,
  minZ = 21.7,
  maxZ = 23.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = "Boo's Catfe",
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'uwu_afridge'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('catCafeTray', vector3(-584.140, -1062.084, 22.344), 0.55, 0.6, {
  name = 'catCafeTray',
  heading = 270.0,
  minZ = 22.3,
  maxZ = 22.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-584.140, -1062.084, 22.344),
    },
  },
  distance = 2.5
})

AddBoxZone('catCafeTray2', vector3(-584.140, -1059.320, 22.344), 0.55, 0.6, {
  name = 'catCafeTray2',
  heading = 270.0,
  minZ = 22.3,
  maxZ = 22.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-584.140, -1059.320, 22.344),
    },
  },
  distance = 2.5
})

AddBoxZone('catCafeTray3', vector3(-587.321, -1059.607, 22.544), 0.55, 3, {
  name = 'catCafeTray3',
  heading = 270.0,
  minZ = 21.5,
  maxZ = 22.7,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Counter',
      table_coords = vector3(-587.321, -1059.607, 22.544),
    },
    {
      event_server = 'core:server:target-backhaul:ringBell',
      icon = 'fa-regular fa-bell',
      label = 'Ring Bell',
    },
  },
  distance = 3
})

AddBoxZone('catCafeRegister', vector3(-584, -1061.455, 22.344), 0.55, 0.3, {
  name = 'catCafeRegister',
  heading = 270.0,
  minZ = 22.3,
  maxZ = 22.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        "Boo's Catfe"
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        "Boo's Catfe"
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-triangle-exclamation',
      label = 'Trigger Panic Alarm',

      location_override = "Boo's Catfe",

      groups = {
        "Boo's Catfe"
      }
    }
  },
  distance = 2.5
})

AddBoxZone('catCafeRegister2', vector3(-584, -1058.723, 22.344), 0.55, 0.3, {
  name = 'catCafeRegister2',
  heading = 270.0,
  minZ = 22.3,
  maxZ = 22.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        "Boo's Catfe"
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-triangle-exclamation',
      label = 'Trigger Panic Alarm',

      location_override = "Boo's Catfe",

      groups = {
        "Boo's Catfe"
      }
    }
  },
  distance = 2.5
})

AddBoxZone('catCafeCloset', vector3(-586.194, -1049.832, 22.344), 0.4, 2.2, {
  name = 'catCafeCloset',
  heading = 270.0,
  minZ = 21.3,
  maxZ = 23.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        "Boo's Catfe",
      }
    }
  },
  distance = 2.5
})

AddBoxZone('catCafeDryIngredients', vector3(-588.197, -1066.066, 22.344), 0.4, 2.2, {
  name = 'catCafeDryIngredients',
  heading = 270.0,
  minZ = 21.3,
  maxZ = 23.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = "Boo's Catfe",
      component_id = 'storage',

      args = {
        capacity = 1500,
        named = 'catCafe_a'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'catfe-supply',
    },
  },
  distance = 2.5
})

AddBoxZone('catCafeDryExtraStorage', vector3(-598.350, -1062.816, 22.344), 4.8, 1.3, {
  name = 'catCafeDryExtraStorage',
  heading = 270.0,
  minZ = 21.3,
  maxZ = 23.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = "Boo's Catfe",
      component_id = 'storage',

      args = {
        capacity = 1500,
        named = 'catCafe_b'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('catCafeDrinksStorage', vector3(-587.038, -1061.774, 23.344), 0.3, 1.25, {
  name = 'catCafeDrinksStorage',
  heading = 270.0,
  minZ = 23.1,
  maxZ = 23.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Drinks Storage',

      business_name = "Boo's Catfe",
      component_id = 'storage',

      args = {
        capacity = 150,
        named = 'catCafe_c'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('catCafeManagement', vector3(-578.290, -1066.667, 26.614), 0.55, 0.1, {
  name = 'catCafeManagement',
  heading = 270.0,
  minZ = 26.6,
  maxZ = 27,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = "Boo's Catfe",
      component_id = 'management',

      component_perm = 'extra_a',
    },
  },
  distance = 3
})

AddBoxZone('catCafeBadEats', vector3(-596.144, -1052.448, 22.444), 0.55, 0.1, {
  name = 'catCafeBadEats',
  heading = 270.0,
  minZ = 22.3,
  maxZ = 22.7,
}, {
  options = {
    {
      event_server = 'core:server:badeats:openRestaurantMenuTarget',
      icon = 'far fa-tablet',
      label = 'Badeats Admin',
      restaurant_name = "Boo's Catfe",

      groups = {
        "Boo's Catfe"
      },
    },
  },
  distance = 3
})

AddBoxZone('catCafeManagerStorage', vector3(-575.545, -1066.460, 26.614), 0.55, 2.5, {
  name = 'catCafeManagerStorage',
  heading = 270.0,
  minZ = 25.4,
  maxZ = 26.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = "Boo's Catfe",
      component_id = 'storage',

      component_perm = 'extra_a',

      args = {
        capacity = 500,
        named = 'catCafe_d'
      },

    },
  },
  distance = 2.5
})
