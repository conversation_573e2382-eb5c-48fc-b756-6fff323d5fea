disableContext(true)

AddBoxZone('GTFManagement', vector3(999.7524, -2403.83, 29.92829), 1.0, 1.0, {
  name = 'GTFManagement',
  heading = 355.0,
  minZ = 29.8,
  maxZ = 30.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'LEO GTF',
      component_id = 'management',
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request GTF ID Card',
      card_type = 'id_gtf',

      groups = {
        'LEO GTF'
      }
    },
  },
  distance = 5.0
})

AddBoxZone('GTFStorage', vector3(1006.064, -2404.493, 30.84635), 0.6, 2.0, {
  name = 'GTFStorage',
  heading = 265.0,
  minZ = 28.9,
  maxZ = 30.84,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'LEO GTF',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('GTFCloset1', vector3(1018.643, -2389.261, 30.81779), 0.8, 2.2, {
  name = 'GTFCloset1',
  heading = 350.0,
  minZ = 28.9,
  maxZ = 30.81,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'LEO GTF',
      }
    },
  },
  distance = 2.0
})

AddGangBenchOption('LEO GTF', {
  event_server = 'blrp_core:server:item-store:resolveTargetConfig',
  icon = 'fa-regular fa-shelves',
  label = 'GTF Supplys',

  uid = 'leogtf-supplys',

  groups = {
    'LEO GTF',
  },
})

AddGangBenchOption('LEO GTF', {
  event_server = 'core:server:id-cards:requestFactionCard',
  icon = 'far fa-id-card',
  label = 'Request GTF ID Card',
  card_type = 'id_gtf',

  groups = {
    'LEO GTF'
  }
})
