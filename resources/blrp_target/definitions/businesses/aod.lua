AddBoxZone('AODGavel', vector3(1159.541, -404.305, 71.818), 0.7, 1.1, {
  name = 'AODGavel',
  heading = 150.0,
  minZ = 70.0,
  maxZ = 72.0,
}, {
  options = {
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel',

      strikes = 1,

      groups = {
        'Angels of Death',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 2x Strike',

      strikes = 2,

      groups = {
        'Angels of Death',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 3x Strike',

      strikes = 3,

      groups = {
        'Angels of Death',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('AODFridge', vector3(1156.987, -404.7683, 67.53913), 0.8, 0.8, {
  name = 'AODFridge',
  heading = 255.0,
  minZ = 67.5,
  maxZ = 68.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Angels of Death',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('AODManagement1', vector3(1165.6, -404.2829, 71.78638), 0.5, 0.7, {
  name = 'AODManagement1',
  heading = 165.0,
  minZ = 71.78,
  maxZ = 72.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Angels of Death',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('AODStorage2', vector3(977.6968, -104.6968, 75.57111), 1.0, 1.0, {
  name = 'AODStorage2',
  heading = 222.0,
  minZ = 73.8,
  maxZ = 75.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Angels of Death',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 250.0,
        uid = '2',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('AODStorage3', vector3(963.4761, -103.3868, 74.60331), 0.6, 1.8, {
  name = 'AODStorage3',
  heading = 150.0,
  minZ = 74.3,
  maxZ = 74.9,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Angels of Death',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '3',
      },
    },
  },
  distance = 2.5
})
AddBoxZone('AODStorage4', vector3(1163.185, -419.4399, 66.09356),1.0, 2.5, {
  name = 'AODStorage4',
  heading = 345.0,
  minZ = 66.10,
  maxZ = 68.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Angels of Death',
      component_id = 'storage',
      component_perm = 'crafting',

      args = {
        capacity = 500.0,
        uid = '4',
      },
    },
  },
  distance = 2.5
})


AddBoxZone('AODCrafting', vector3(1156.8, -425.22, 67.1), 1.8, 1.0, {
  name = 'AODCrafting',
  heading = 255,
  minZ = 66.1,
  maxZ = 67.5,
}, {
  options = {
    {
      event_server = 'core:server:item-durability:useGunRepairTarget',
      icon = 'fa-regular fa-toolbox',
      label = 'Repair Firearms',

      business_name = 'Angels of Death',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Angels of Death',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm repair kit',

      item_id = 'tool_wpn_repair',

      business_name = 'Angels of Death',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-spray-can',
      label = 'Craft Stencil',

      item_id = 'stencil_aod',

      business_name = 'Angels of Death',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('AODSafe2', vector3(1166.50, -402.5458, 71.83212), 0.3, 0.8, {
  name = 'AODSafe2',
  heading = 345.0,
  minZ = 71.0,
  maxZ = 72.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Angels of Death',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 100.0,
        uid = 'Safe',
      },
    },

  },
  distance = 2.5
})

AddBoxZone('AODSafe5', vector3(1155.258, -415.8418, 63.96633), 0.8, 0.9, {
  heading = 255.0,
  minZ = 63.9,
  maxZ = 66.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Angels of Death',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Angels of Death',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 666.0,
        uid = '5',
      },
    },
  },
  distance = 2.5
})
AddBoxZone('AODSafe4', vector3(1149.38, -419.6241, 66.58874), 1.0, 1.0, {
  name = 'AODSafe4',
  heading = 165.0,
  minZ = 66.5,
  maxZ = 68.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Angels of Death',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        uid = '2',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('AODSafe3', vector3(1150.452, -419.9115, 66.58874), 1.0, 1.0, {
  name = 'AODSafe3',
  heading = 165.0,
  minZ = 66.5,
  maxZ = 68.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Angels of Death',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        named = 'AngelsofDeathstorage1',
      },
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(1155.888, -413.4904, 63.96633, 255), 1.2, 2.1, {
  minZ = 64.7,
  maxZ = 65.7,
}, {
  options = {
		{
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Thermite Charge',

      item_id = 'thermite_charge',

      business_name = 'Angels of Death',
      component_id = 'crafting',
      component_perm = 'extra_a',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Large Storage Crate',

      item_id = 'bl_prop_gunbox',

      business_name = 'Angels of Death',
      component_id = 'crafting',
      component_perm = 'extra_a',
    },
  },
  distance = 2.5
})

AddBoxZone('AODCloset', vector3(1158.82, -418.2651, 66.08831), 1.0, 1.6, {
  name = 'AODCloset',
  heading = 345.0,
  minZ = 66.10,
  maxZ = 68.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Angels of Death',
      }
    },
  },
  distance = 2.5
})

