AddBoxZone('KushRegister', vector3(-34.85, -1665.8, 29.49), 0.6, 0.4, {
  name = 'KushRegister',
  heading = 320,
  minZ = 29.09,
  maxZ = 29.49,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'The Kush Korner',
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Kush Korner',

      groups = {
        'The Kush Korner'
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'The Kush Korner',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('KushCounter', vector3(-35.79, -1666.7, 29.49), 1.6, 0.6, {
  name = 'KushCounter',
  heading = 320,
  minZ = 29.29,
  maxZ = 29.69,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-box',
      label = 'Counter',
      table_coords = vector3(-35.79, -1666.7, 29.49),
      prefix = 'big_counter_',
    },
  },
  distance = 2.5
})

AddBoxZone('KushFridge', vector3(-33.16, -1665.39, 29.49), 0.2, 1.2, {
  name = 'KushFridge',
  heading = 320,
  minZ = 28.69,
  maxZ = 29.29,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'far fa-refrigerator',
      label = 'Fridge',

      business_name = 'The Kush Korner',
      component_id = 'storage',

      args = {
        capacity = 50.0,
        named = 'fridge_kush_korner',
        fake_fridge = true,
      },
    },
  },
  distance = 2.5
})

AddBoxZone('KushCrafting', vector3(-10.69, -1671.06, 29.49), 1.0, 2.0, {
  name = 'KushCrafting',
  heading = 320,
  minZ = 29.09,
  maxZ = 29.69,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-oven',
      label = 'Kookies & Kream THC Bar',

      item_id = 'kk_thc_bar',

      business_name = 'The Kush Korner',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-cup-straw',
      label = 'Peaches & Kream THC Drink',
        
      item_id = 'kk_thcdrink',
        
      business_name = 'The Kush Korner',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('KushStor1', vector3(-16.25, -1674.79, 29.49), 0.6, 4.8, {
  name = 'KushStor1',
  heading = 320,
  minZ = 28.49,
  maxZ = 30.89,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-cannabis',
      label = 'Storage',

      business_name = 'The Kush Korner',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        named = 'kushkorner_stor1',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('KushStor2', vector3(-33.1, -1672.19, 29.49), 0.4, 1.4, {
  name = 'KushStor2',
  heading = 320,
  minZ = 28.89,
  maxZ = 30.69,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-shelves',
      label = 'Storage',

      business_name = 'The Kush Korner',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'kushkorner_stor2',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Buy Supplies',

      uid = 'kushkorner-supply',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-lemon',
      label = 'Buy Seeds',

      uid = 'kushkorner-seeds',
      business_name = 'The Kush Korner',
      component_id = 'management',
    },
  },
  distance = 2.5
})
