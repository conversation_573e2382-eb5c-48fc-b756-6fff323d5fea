AddBoxZone('ACEManagement', vector3(760.9975, -1298.685, 26.37977), 0.9, 0.5, {
  name = 'ACEManagement',
  heading = 90.0,
  minZ = 25.7,
  maxZ = 27.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'The Unknown',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('ACEStorageMain', vector3(756.1932, -1296.786, 26.41574), 0.55, 3.25, {
  name = 'ACEStorageMain',
  heading = 90.0,
  minZ = 25.1,
  maxZ = 28.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'The Unknown',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('ACEStorageMain2', vector3(760.2652, -1269.225, 27.25374), 0.55, 3.25, {
  name = 'ACEStorageMain2',
  heading = 180.0,
  minZ = 25.2,
  maxZ = 28.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'The Unknown',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('ACECloset', vector3(753.2808, -1297.222, 26.41043), 0.55, 2.12, {
  name = 'ACESCloset',
  heading = 90.0,
  minZ = 25.9,
  maxZ = 28.8,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
      'The Unknown',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('ACECrafting', vector3(744.4635, -1300.13, 26.55199), 1.0, 2.0, {
  name = 'ACECrafting',
  heading = 270.0,
  minZ = 25.4,
  maxZ = 27.3,
}, {
  options = {
     {
       event_server = 'core:server:item-durability:useGunBreakdown',
       icon = 'fa-regular fa-toolbox',
       label = 'Break Down Firearms',

       business_name = 'The Unknown',
       component_id = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft 9mm Ammo (20)',

       item_id = 'ammo_9mm',

       business_name = 'The Unknown',
       component_id = 'crafting',
     },
    -- {
    --   event_server = 'core:server:target-backhaul:craft',
    --   icon = 'fa-regular fa-wrench',
    --   label = 'Craft EMP',

    --   item_id = 'emp',

    --   business_name = 'The Unknown',
    --   component_id = 'crafting',
    -- },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft MPX',

       item_id = 'wbody|WEAPON_SMG_MK2',
       weekly_craft_limit = 40,
       business_name = 'The Unknown',
       component_id = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft Firearm Body',

       item_id = 'firearm_body',

       business_name = 'The Unknown',
       component_id = 'crafting',
     },
		{
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Umbrella Key',

      item_id = 'umbrella_key',

      business_name = 'The Unknown',
      component_id = 'crafting',
    },
    -- {
    --   event_server = 'core:server:target-backhaul:craft',
    --   icon = 'fa-regular fa-wrench',
    --   label = 'Craft Large Storage Crate',

    --   item_id = 'bl_prop_gunbox',

    --   business_name = 'The Unknown',
    --   component_id = 'crafting',
    -- },
  },
  distance = 1.5,
  context_disable = true,
})
