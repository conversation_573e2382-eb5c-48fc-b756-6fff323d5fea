AddBoxZone('NYHouseLaptop', vector3(6029.87, -5409.93, 89.8), 0.4, 0.4, {
    name = 'NYHouseLaptop',
    heading = 355.0,
    minZ=89.2,
    maxZ=89.6,
  }, {
    options = {
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'NYSP',
  
        icon = 'fas fa-clock',
        label = 'Clock in - NYSP',
  
        groups = {
          'Civilian'
        }
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',
  
        icon = 'fas fa-clock',
        label = 'Clock Out',
      },
    },
    distance = 2.5
  })

AddBoxZoneAutoname(vector4(6025.7, -5409.54, 89.8, 355), 1.4, 0.4, {
    minZ=88.8,
    maxZ=91.2,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',
        component_perm = 'extra_a',

        groups = {
          'North Yankton',
        }
      },
    },
    distance = 3.0,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(6027.05, -5406.32, 89.8, 355), 0.4, 1.4, {
    minZ=88.8,
    maxZ=91.2,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',
        component_perm = 'extra_a',

        business_name = 'North Yankton',
        component_id = 'storage',

        args = {
          capacity = 1000.0,
          named = 'NYHouseStor1',
        },
      },
    },
    distance = 3.0,
    context_disable = true,
  })

  AddBoxZoneAutoname(vector4(6026.81, -5415.11, 89.8, 355), 0.9, 0.9, {
    minZ=88.8,
    maxZ=90.8,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-refrigerator',
          label = 'Fridge',
          component_perm = 'extra_a',
  
          business_name = 'North Yankton',
          component_id = 'storage',
  
          args = {
            capacity = 50.0,
            named = 'NYFridge',
            fake_fridge = true,
          },
        },
      },
      distance = 3.5,
    })

  -- Reindeer Stables
  AddBoxZoneAutoname(vector4(6030.65, -5302.57, 85.79, 330), 1.4, 2.2, {
    minZ=84.79,
    maxZ=85.99,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Stables Storage',
        component_perm = 'extra_b',

        business_name = 'North Yankton',
        component_id = 'storage',

        args = {
          capacity = 1000.0,
          named = 'NYStableStor1',
        },
      },
    },
    distance = 3.0,
    context_disable = true,
  })
