disableContext(true)

AddBoxZone('FDFManagement', vector3(-195.3453, -1714.172, 28.12152), 0.4, 0.4, {
  name = 'FDFManagement',
  heading = 120.0,
  minZ = 28.1,
  maxZ = 28.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Forum Family',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('FDFStorageMain', vector3(-167.1183, -1698.197, 28.94567), 1.09, 1.05, {
  name = 'FDFStorageMain',
  heading = 129.0,
  minZ = 26.9,
  maxZ = 29.17,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Forum Family',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('FDFStorage2', vector3(-199.5, -1687.5, 25.01316), 1.7, 1.7, {
  name = 'FDFStorage2',
  heading = 129.0,
  minZ = 22.9,
  maxZ = 26.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Forum Family',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})
AddBoxZone('FDFRailsCrafting',vector3(-9.858086, -1428.516, 31.25968),0.9,0.9,{
  name="FDFRailsCrafting",
  heading= 0.0,
  minZ = 30.0,
  maxZ = 31.3,
},{
  options={
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Strawberry Rails',

      item_id = 'food_cereal_a',

      business_name = 'Forum Family',
      component_id = 'crafting',
    },
  },
  distance=2.0
})


AddBoxZone('FDFCrafting', vector3(-196.8, -1691.6, 25.0311), 0.9, 3.8, {
  name = 'FDFCrafting',
  heading = 130.0,
  minZ = 25.8,
  maxZ = 26.97,
}, {
  options = {
    --[[
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Forum Family',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 9mm Ammo (20)',

      item_id = 'ammo_9mm',

      business_name = 'Forum Family',
      component_id = 'crafting',
    },
    ]]--
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Lucille',

      item_id = 'wbody|WEAPON_LUCILLE',

      business_name = 'Forum Family',
      component_id = 'crafting',
    },
--[[
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Uzi',

      item_id = 'wbody|WEAPON_MICROSMG',

      business_name = 'Forum Family',
      component_id = 'crafting',
    },}
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm Body',

      item_id = 'firearm_body',

      business_name = 'Forum Family',
      component_id = 'crafting',
    },
    ]]--
  },
  distance = 2.5,
})

AddBoxZone('FDFFridge', vector3(-179.3208, -1694.176, 27.19401), 0.9, 0.9, {
  name = 'FDFFridge',
  heading = 130.0,
  minZ = 25.1,
  maxZ = 27.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Forum Family',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('FDFCloset', vector3(-194.3417, -1717.338, 29.16481), 0.3, 3.6, {
  name = 'FDFCloset',
  heading = 130.0,
  minZ = 27.3,
  maxZ = 29.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Forum Family',
      }
    },
  },
  distance = 3.0
})
