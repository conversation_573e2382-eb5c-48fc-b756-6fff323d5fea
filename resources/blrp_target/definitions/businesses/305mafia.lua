disableContext(true)

-- Closet
AddBoxZoneAutoname(vector4(-1979.66, -501.58, 20.73, 320), 2.6, 0.6, {
  minZ = 19.73,
  maxZ = 22.73,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        '305 Mafia',
      }
    },
  },
  distance = 3.0
})


AddBoxZoneAutoname(vector4(-1985.19, -502.45, 12.18, 320), 0.55, 0.2, {
  minZ = 12.28,
  maxZ = 12.63,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = '305 Mafia',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZoneAutoname(vector4(-1986.94, -497.69, 12.18, 321), 2.0, 1, {
  minZ = 11.18,
  maxZ = 13.38,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft AK',
      
      weekly_craft_limit = 35,
      item_id = 'wbody|WEAPON_ASSAULTRIFLE',

      business_name = '305 Mafia',
      component_id = 'craft-WEAPON_ASSAULTRIFLE',
      component_perm = 'extra_b',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm Body',

      item_id = 'firearm_body',

      business_name = '305 Mafia',
      component_id = 'crafting',
      component_perm = 'extra_b',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 7.62x39 Ammo (20)',

      item_id = 'ammo_762x39',

      business_name = '305 Mafia',
      component_id = 'extra_b',
    },
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = '305 Mafia',
      component_id = 'firearm-repair',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Large Storage Crate',

      item_id = 'bl_prop_gunbox',

      business_name = '305 Mafia',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
  },
  distance = 2.5
})


AddBoxZoneAutoname(vector4(-1989.19, -500.26, 12.19, 320), 3.8, 1, {
  minZ = 11.19,
  maxZ = 12.39,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '305 Mafia',
      component_id = 'storage',

      args = {
        capacity = 3000.0,
        named = 'business4storage1',
      },
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-1983.25, -503.83, 12.19, 320), 1.0, 3.8, {
  minZ = 11.14,
  maxZ = 12.54,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '305 Mafia',
      component_id = 'storage',
      component_perm = 'extra_c',

      args = {
        capacity = 500.0,
        uid = '2',
      },
    },
  },
  distance = 2.5
})
