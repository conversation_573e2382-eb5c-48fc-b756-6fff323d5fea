AddBoxZone('TrojanArmorManagement', vector3(-301.793, -1286.348, 31.06974), 0.4, 0.4, {
  name = 'TrojanArmorManagement',
  heading = 88.0,
  minZ = 31.0,
  maxZ = 31.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Trojan Armor',
      component_id = 'management',
    },
  },
  distance = 1.5
})

AddBoxZone('TrojanArmorStorageMain', vector3(-307.65, -1280.766, 31.91203), 2.0, 2.0, {
  name = 'TrojanArmorStorageMain',
  heading = 0.0,
  minZ = 30.3,
  maxZ = 31.95,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Steel Storage',

      business_name = 'Trojan Armor',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('TrojanArmorStorage2', vector3(-317.3751, -1313.875, 31.73288), 2.0, 2.0, {
  name = 'TrojanArmorStorage2',
  heading = 90.0,
  minZ = 30.3,
  maxZ = 31.95,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Trojan Armor',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('TrojanArmorStorage3', vector3(-319.597, -1295.05, 31.45918), 2.2, 2.0, {
  name = 'TrojanArmorStorage3',
  heading = 0.0,
  minZ = 30.3,
  maxZ = 31.95,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Armor Storage',

      business_name = 'Trojan Armor',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = 'Armor',
        whitelisted_items = {
          'light_armor',
          'medium_armor',
          'heavy_armor',
          'makeshift_armor',
          'trojan_armor',
        },
      },
    },
  },
  distance = 2.5
})

AddBoxZone('TrojanArmorStorage4', vector3(-310.0535, -1280.877, 31.16131), 2.0, 2.0, {
  name = 'TrojanArmorStorage4',
  heading = 0.0,
  minZ = 30.3,
  maxZ = 31.95,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Fabric Storage',

      business_name = 'Trojan Armor',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '3',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('TrojanArmorStorage5', vector3(-318.77, -1302.35, 31.26), 2.2, 1.6, {
  name = 'TrojanArmorStorage5',
  heading = 0.0,
  minZ = 30.16,
  maxZ = 32.36
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Trojan Armor',
      component_id = 'storage',

      args = {
        capacity = 1250.0,
        uid = '4',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('TrojanArmorCloset1', vector3(-307.7069, -1315.718, 31.52734), 1.0, 1.5, {
  name = 'TrojanArmorCloset1',
  heading = 90.0,
  minZ = 30.3,
  maxZ = 32.7,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Trojan Armor',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('TrojanArmorCloset2', vector3(-301.8933, -1282.607, 32.24105), 0.7, 1.0, {
  name = 'TrojanArmorCloset2',
  heading = 0.0,
  minZ = 30.3,
  maxZ = 32.7,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Trojan Armor',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('TrojanArmorCrafting', vector3(-298.8721, -1280.614, 31.43395), 0.6, 1.0, {
  name = 'TrojanArmorCrafting',
  heading = 90.0,
  minZ = 30.95,
  maxZ = 31.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Generic Fabric (2)',

      item_id = 'generic_fabric',

      business_name = 'Trojan Armor',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Trojan Armor',

      item_id = 'trojan_armor',

      business_name = 'Trojan Armor',
      component_id = 'crafting',
    },
  },
  distance = 1.5
})

-------------------------
---- STOCKABLE STORE ----
-------------------------

AddBoxZoneAutoname(vector4(-303.675, -1293.344, 31.246, 260.0), 0.7, 0.7, {
  minZ = 30.4,
  maxZ = 32.1,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Shop',
      uid = 'trojan-storefront',
    },
    {
      event_server = 'core:server:trojan:route',
      icon = 'fa-regular fa-box',
      label = 'Stock',
      action = 'stock',

      business_name = 'Trojan Armor',
      component_id = 'storefront-stock',
      component_perm = 'extra_c',
    },
    {
      event_server = 'core:server:trojan:route',
      icon = 'fa-regular fa-computer-classic',
      label = 'Manage Stock Prices',
      action = 'prices',

      business_name = 'Trojan Armor',
      component_id = 'storefront-prices',
      component_perm = 'extra_c',
    },
    {
      event_server = 'core:server:trojan:route',
      icon = 'fa-regular fa-vault',
      label = 'Safe',
      action = 'safe',

      business_name = 'Trojan Armor',
      component_id = 'storefront-safe',
      component_perm = 'extra_d',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Trojan Supply Store',
      uid = 'trojan-supply',
        
      groups = {
        'Trojan Armor',
      },
    },
  },
  distance = 2.5,
  context_disable = true,
})
