disableContext(true)

AddBoxZone('MCFManagement', vector3(-1108.89, -1641.38, -0.36), 0.4, 0.4, {
  name = 'MCFManagement',
  heading = 305.0,
  minZ = -0.76,
  maxZ = -0.16,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Messina Crime Family',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('MCFStorageMain', vector3(-1124.1, -1637.11, -0.36), 2.4, 1.4, {
  name = 'MCFStorageMain',
  heading = 305.0,
  minZ = -1.16,
  maxZ = 0.24,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Messina Crime Family',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 3000.0,
        uid = '1',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('MCFCloset1', vector3(-1116.9, -1631.81, -0.36), 0.8, 1.0, {
  name = 'MCFCloset1',
  heading = 305.0,
  minZ = -1.36,
  maxZ = 0.44,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Messina Crime Family',
      }
    },
  },
  distance = 3.0
})

AddBoxZone('MCFStor2', vector3(-1117.45, -1630.99, -0.36), 0.8, 1, {
  name = 'MCFStor2',
  heading = 305.0,
  minZ = -1.36,
  maxZ = 0.44,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Messina Crime Family',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'MCFStor2',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('MCFSafe', vector3(-1115.06, -1628.49, -0.36), 0.8, 0.2, {
  name = 'MCFSafe',
  heading = 305.0,
  minZ = -1.36,
  maxZ = 0.24,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Messina Crime Family',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 50.0,
        named = 'MCFSafe',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('MCFFridge', vector3(-1097.23, -1635.12, -0.36), 0.6, 0.8, {
  name = 'MCFFridge',
  heading = 305.0,
  minZ = -1.36,
  maxZ = 0.24,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Messina Crime Family',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('MCFCounter', vector3(-1099.41, -1634.7, -0.36), 0.8, 2.4, {
  name = 'MCFCounter',
  heading = 305,
  minZ = -0.36,
  maxZ = -0.16,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-cocktail',
      label = 'Counter',
      table_coords = vector3(-1099.41, -1634.7, -0.36),
    },
  },
  distance = 2.5,
})

AddBoxZone('MCFPoker', vector3(-1096.93, -1643.23, -0.36), 1.0, 2.2, {
  name = 'MCFPoker',
  heading = 305.0,
  minZ = -0.76,
  maxZ = -0.36,
}, {
  options = {
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular fa-cards',
      label = "View Dealer's Hand",
      table_name = 'messinablackjack'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 1's Hand",
      table_name = 'messinablackjack1'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 2's Hand",
      table_name = 'messinablackjack2'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'messinablackjack3'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'messinablackjack4'
    },
  },
  distance = 2.5
})

AddBoxZone('MCFCrafting', vector3(-1122.43, -1639.11, -0.36), 1.6, 0.8, {
  name = 'MCFCrafting',
  heading = 305.0,
  minZ = -1.36,
  maxZ = -0.16,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Gusenberg',

      item_id = 'wbody|WEAPON_GUSENBERG',
      weekly_craft_limit = 40,
      business_name = 'Messina Crime Family',
      component_id = 'crafting',
      component_perm = 'extra_a',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft .45 Automatic Ammo (20)',

      item_id = 'ammo_45auto',

      business_name = 'Messina Crime Family',
      component_id = 'crafting',
      component_perm = 'extra_a',
    },
		{
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Messina Crime Family',
      component_id = 'crafting',
      component_perm = 'extra_b',
    },
		{
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm Body',

      item_id = 'firearm_body',

      business_name = 'Messina Crime Family',
      component_id = 'crafting',
      component_perm = 'extra_b',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Large Storage Crate',

      item_id = 'bl_prop_gunbox',

      business_name = 'Messina Crime Family',
      component_id = 'crafting',
      component_perm = 'extra_b',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft GPS Transceiver',

      item_id = 'gps_transceiver',

      business_name = 'Messina Crime Family',
      component_id = 'crafting',
      component_perm = 'extra_b',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Advanced Lockpick',

      item_id = 'lockpick_adv',

      business_name = 'Messina Crime Family',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Pocketwatch',

      item_id = 'mcf_pocketwatch',

      business_name = 'Messina Crime Family',
      component_id = 'crafting',
      component_perm = 'extra_b',
    },
  },
  distance = 2.0
})
