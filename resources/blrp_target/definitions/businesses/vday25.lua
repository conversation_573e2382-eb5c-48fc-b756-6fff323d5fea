--[[
AddBoxZoneAutoname(vector4(-1453.675, 203.9162, 56.52042, 80), 1.5, 1.0, {
    minZ=56.0,
    maxZ=56.7,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-box',
          label = 'Storage',
  
          business_name = 'Spellbound Occult',
          component_id = 'storage',
  
          args = {
            capacity = 100.0,
            named = 'vdaysb',
          },
        },
        {
          event_server = 'core:server:restaurant-tables:openTarget',
          icon = 'fa-regular fa-box',
          label = 'Table',
          table_coords = vector3(-1453.675, 203.9162, 56.52042),
        },
      },
      distance = 2.5,
      context_disable = true,
    })

AddBoxZoneAutoname(vector4(-1444.308, 210.2777, 57.80913, 120), 2.0, 1.0, {
    minZ=56.9,
    maxZ=58.8,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-box',
          label = 'Storage',
  
          business_name = 'Chihuahua Hotdogs',
          component_id = 'storage',
  
          args = {
            capacity = 100.0,
            named = 'vdaych',
          },
        },
      },
      distance = 2.5,
      context_disable = true,
    })

AddBoxZone('vday25gal', vector3(-1434.058, 206.9657, 57.30708), 0.5, 1.0, {
    name = 'vday25gal',
    heading = 165.0,
    minZ=56.9,
    maxZ=57.7,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-refrigerator',
          label = 'Fridge',
  
          business_name = 'Galaxy',
          component_id = 'storage',
  
          args = {
            capacity = 100.0,
            named = 'vdaygal',
          },
        },
      },
      distance = 2.5,
      context_disable = true,
    })

AddBoxZoneAutoname(vector4(-1437.761, 200.1978, 57.19115, 120), 1.0, 1.0, {
    minZ=56.8,
    maxZ=57.3,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-box',
          label = 'Storage',
  
          business_name = 'Wanderlust',
          component_id = 'storage',
  
          args = {
            capacity = 100.0,
            named = 'vdaywl',
          },
        },
      },
      distance = 2.5,
      context_disable = true,
    })
    
    for _, vday_mirrors in pairs({
      {name="vmirror1", position=vector3(-1503.004, 135.7025, 55.75891)},
      {name="vmirror2", position=vector3(-1507.727, 136.3225, 55.72905)},
      {name="vmirror3", position=vector3(-1508.654, 137.2057, 55.65545)},
    }) do
  AddBoxZone(vday_mirrors.name, vday_mirrors.position, 0.4, 0.4, {
      heading=125,
      minZ = vday_mirrors.position.z - 1.2,
      maxZ = vday_mirrors.position.z + 1.2,
    }, {
      options = {
        {
          event_client = 'blrp_character:openBarbershop',
          icon = 'fa-regular fa-face-awesome',
          label = 'Hair and Makeup',
          cloakroom_name = 'clothingshop',
        },
      },
      distance = 3.0,
    })
  end
  
  for _, gal_trays in pairs({
      {name="galtray1", position=vector3(-1434.5, 206.1658, 57.97495)},
      {name="galtray2", position=vector3(-1435.841, 207.6018, 57.97495)},
      {name="galtray3", position=vector3(-1435.833, 209.5958, 58.0074)},
      {name="galtray4", position=vector3(-1434.496, 210.8697, 57.97493)},
    }) do
  AddBoxZone(gal_trays.name, gal_trays.position, 1.0, 1.0, {
      heading=120,
      minZ = gal_trays.position.z - 0.3,
      maxZ = gal_trays.position.z + 0.3,
    }, {
      options = {
        {
          event_server = 'core:server:restaurant-tables:openTarget',
          icon = 'fa-regular fa-cocktail',
          label = 'Counter',
          table_coords = gal_trays.position,
        },
      },
      distance = 3.0,
    })
  end
  
  for _, vday_slots in pairs({
      {name="slots1", position=vector3(-1480.321, 183.7161, 56.82236)},
      {name="slots2", position=vector3(-1480.876, 184.7663, 56.78134)},
      {name="slots3", position=vector3(-1481.399, 185.742, 56.79263)},
      {name="slots4", position=vector3(-1469.232, 197.0499, 56.78505)},
      {name="slots5", position=vector3(-1435.462, 214.4344, 57.91616)},
    }) do
  AddBoxZone(vday_slots.name, vday_slots.position, 1.0, 1.0, {
      heading=120,
      minZ = vday_slots.position.z - 1.3,
      maxZ = vday_slots.position.z + 1.3,
    }, {
      options = {
        {
          event_server = 'blrp_core:server:vday-slots:resolveTargetConfig',
          icon = 'fa-solid fa-arrows-spin',
          label = 'Play Slots',
        },
      },
      distance = 3.0,
    })
  end
  --]]