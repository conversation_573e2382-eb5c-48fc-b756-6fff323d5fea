
AddBoxZone('JenkinsManagement', vector3(2013.19, 4993.37, 41.21), 0.6, 0.4, {
  name = 'JenkinsManagement',
  heading = 330.0,
  minZ = 41.01,
  maxZ = 41.41,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Jenkins Farms',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('JenkinsCrafting', vector3(2140.73, 4970.32, 41.41), 1.0, 2.0, {
  name = 'JenkinsCrafting',
  heading = 315.0,
  minZ = 41.01,
  maxZ = 41.81,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-crate-apple',
      label = 'Prepare Pig Feed',

      item_id = 'hunting_bait_7',

      business_name = 'Jenkins Farms',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-crate-apple',
      label = 'Prepare Cow Feed',

      item_id = 'hunting_bait_8',

      business_name = 'Jenkins Farms',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-crate-apple',
      label = 'Prepare Chicken Feed',

      item_id = 'hunting_bait_9',

      business_name = 'Jenkins Farms',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('JenkinsFreezer', vector3(2000.26, 4984.12, 41.55), 2.0, 1, {
  name = 'JenkinsFreezer',
  heading = 310.0,
  minZ = 40.95,
  maxZ = 42.15,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Freezer',

      business_name = 'Jenkins Farms',
      component_id = 'storage',
      component_perm = 'general',

      args = {
        capacity = 1000.0,
        named = 'JenkinsMeatFreezer'
      },
    },
  },
  distance = 2.5
})

for _,grainsilos in pairs({
{ name = 'GrainSilo_1', coords = vector4(1994.98, 5021.52, 40.14, 315) }}) do
AddBoxZoneAutoname(grainsilos.coords, 5.8, 5.8, {
  minZ = 38.74,
  maxZ = 43.14,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Silo Storage',

      business_name = 'Jenkins Farms',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        uid = grainsilos.name,
        whitelisted_items = {
          'food_tomato',
          'tomato_seed',
          'food_raw_corn',
          'corn_seed',
          'jute_seed',
          'jute_fibers',
        },
      },
    },
  },
  distance = 7.5
})
end

AddBoxZone('JenkinsGrinder', vector3(1998.59, 4980.47, 41.63), 0.4, 0.4, {
  name = 'JenkinsGrinder',
  heading = 310.0,
  minZ = 41.43,
  maxZ = 42.03,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:GrinderInput',
      icon = 'fa-regular fa-box',
      label = 'Open Input',

      business_name = 'Jenkins Farms',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:GrinderFunction',
      icon = 'fa-regular fa-wrench',
      label = 'Turn on Grinder',

      item_id = 'food_raw_meat_patty',

      business_name = 'Jenkins Farms',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:GrinderOutput',
      icon = 'fa-regular fa-box',
      label = 'Open Output',

      business_name = 'Jenkins Farms',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})