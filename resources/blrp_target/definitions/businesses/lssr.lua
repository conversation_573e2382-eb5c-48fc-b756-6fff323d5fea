
-- Management
AddBoxZoneAutoname(vector4(-982.56, -247.75, 38.47, 30), 1.0, 0.2, {
    minZ = 38.07,
    maxZ = 38.87,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-computer-classic',
        label = 'Management',
  
        business_name = 'Los Santos Sound Rentals',
        component_id = 'management',
      },
    },
    distance = 2.5
  })

-- Storage
  AddBoxZoneAutoname(vector4(-974.39, -257.9, 38.47, 30), 1.8, 1, {
    minZ = 37.47,
    maxZ = 40.87,
    }, {
      options = {
        {
          event_server = 'core:server:businesses:custom:targetInteract',
          icon = 'fa-regular fa-box',
          label = 'Storage',
  
          business_name = 'Los Santos Sound Rentals',
          component_id = 'storage',
          component_perm = 'extra_a',
  
          args = {
            capacity = 1000.0,
            named = 'LSSRMainStorage',
          },
        },
      },
      distance = 3.1,
      context_disable = true,
    })

-- <PERSON><PERSON>
    AddBoxZone('RadioLSCounter', vector3(-997.0, -259.36, 39.04), 0.6, 1.8, {
      name = 'RadioLSCounter',
      heading = 325,
      minZ = 39.24,
      maxZ = 39.44,
    }, {
      options = {
        {
          event_server = 'core:server:restaurant-tables:openTarget',
          icon = 'fa-regular fa-box',
          label = 'Counter',
          table_coords = vector3(-997.0, -259.36, 39.04),
        },
      },
      distance = 2.5,
    })