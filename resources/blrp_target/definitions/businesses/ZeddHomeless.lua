
local function IsHighRankPolice()
	for _, group in pairs({
    'police_rank4',
    'sheriff_rank4',
    'sahp_rank2',
		'FIB'
  }) do
    if exports.blrp_core:me().hasOrInheritsGroup(group) then
      return true
    end
  end
	return false
end



AddBoxZone('ZeddTentStorage', vector3(171.98, -1202.09, 29.3), 2.2, 1, {
    name = 'ZeddTentStorage',
    heading = 0.0,
    minZ = 29.0,
    maxZ = 30.0,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openChest',
        icon = 'fa-regular fa-box',
        label = 'Tent Storage',
        
        chest_radius = 3.0,
        chest_weight = 100.0,
				chest_name = 'ZeddTentStorage',

        filter = function(entity, ray_intersect_coords)
            
        if exports.blrp_core:me().get('id') == 14826 or IsHighRankPolice() then
						return true
					end
					return false
				end,
      },
    },
    distance = 2.5
  })