
AddBoxZone('StPetersSupply', vector3(-235.7288, 6201.632, 134.5712), 0.8, 1.4, {
    name = 'StPetersSupply',
    heading = 90.0,
    minZ = 134.0,
    maxZ = 135.0,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'far fa-utensils',
        label = 'St Peters Pub Supply',
  
        uid = 'stpeters-supply',
      },
    },
    distance = 2.5,
  })


  for k, tray_coords in ipairs({
    vector3(-236.0308, 6202.808, 134.1788),
  }) do
    AddBoxZone('StPetersTray' .. k, tray_coords, 0.6, 1.0, {
      name = 'StPetersTray' .. k,
      heading=90,
      minZ=134.0,
      maxZ=135.0,
    }, {
      options = {
        {
          event_server = 'core:server:restaurant-tables:openTarget',
          icon = 'fa-regular fa-utensils',
          label = 'Tray',
          table_coords = tray_coords,
        },
      },
      distance = 2.5
    })
  end