AddBoxZone('WanderlustManagement', vector3(-1236.29, -1440.04, 3.87), 0.2, 1, {
  name = 'WanderlustManagement',
  heading = 30.0,
  minZ = 4.27,
  maxZ = 4.67,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Wanderlust',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('WanderlustSupply', vector3(-1233.11, -1437.34, 4.37), 1.2, 0.4, {
    name = 'WanderlustSupply',
    heading = 35.0,
    minZ = 3.37,
    maxZ = 5.37,
  }, {
    options = {
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-regular fa-dollar-sign',
        label = 'Buy Supplies',

        uid = 'wanderlust-supply',

        groups = {
          'Wanderlust'
        },
      },
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',
  
        groups = {
          'Wanderlust',
        }
      },
    },
    distance = 2.5
})

AddBoxZone('WanderlustStorage', vector3(-1235.24, -1440.71, 4.37), 1.6, 0.2, {
  name = 'WanderlustStorage',
  heading = 305.0,
  minZ = 3.37,
  maxZ = 4.17,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Wanderlust',
      component_id = 'storage',

      args = {
        capacity = 250,
        named = 'WanderlustMainStor',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('WanderlustSafe', vector3(-1233.5, -1436.49, 4.37), 0.6, 0.6, {
  name = 'WanderlustSafe',
  heading = 305.0,
  minZ = 4.37,
  maxZ = 4.77,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Wanderlust',
      component_id = 'storage',
      component_perm = 'management',

      args = {
        capacity = 100,
        named = 'WanderlustSafe',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('WanderlustRegister', vector3(-1233.7, -1441.34, 4.37), 0.8, 0.4, {
  name = 'WanderlustRegister',
  heading = 305.0,
  minZ = 4.17,
  maxZ = 4.77,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        'Wanderlust'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Wanderlust',

      groups = {
        'Wanderlust'
      }
    },
  },
  distance = 2.5
})

AddBoxZone('WanderlustCounter', vector3(-1234.17, -1442.22, 4.37), 0.4, 2.0, {
  name = 'WanderlustCounter',
  heading = 35.0,
  minZ = 4.37,
  maxZ = 4.57,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-box',
      label = 'Counter',
      table_coords = vector3(-1234.17, -1442.22, 4.37),
      prefix = 'sft_counter_',
    },
  },
  distance = 2.5
})

AddBoxZone('WanderlustCraft', vector3(-1235.3, -1440.55, 4.37), 1.2, 0.4, {
  name = 'WanderlustCraft',
  heading = 305.0,
  minZ = 4.17,
  maxZ = 4.37,
}, {
  options = {
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-cup-straw',
      label = 'Make Dark Academia Tea',
      food_item_id = 'food_dark_acad_tea',
      groups = {
        'Wanderlust',
      }
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-cup-straw',
      label = 'Make Lavender Haze',
      food_item_id = 'food_lav_haze',
      groups = {
        'Wanderlust',
      }
    },
  },
  distance = 2.5
})