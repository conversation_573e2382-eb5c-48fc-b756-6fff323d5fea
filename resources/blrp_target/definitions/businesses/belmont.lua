disableContext(true)

AddBoxZone('BelmontManagement', vector3(-1338.513, -1138.867, 17.33868), 0.4, 0.4, {
  heading = 110.0,
  minZ = 17.3,
  maxZ = 17.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'The Belmont',
      component_id = 'management',
    },
  },
  distance = 1.5
})

AddBoxZone('BelmontStorageMain', vector3(-1333.864, -1128.424, 0.0), 0.8, 2.0, {
  heading = 180.0,
  minZ = -1.0,
  maxZ = 1.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'The Belmont',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontStorage2', vector3(-1343.045, -1134.469, 4.180807), 0.6, 2.0, {
  heading = 180.0,
  minZ = 3.0,
  maxZ = 4.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'The Belmont',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontStorage3', vector3(-1344.123, -1141.256, 4.180407), 0.6, 1.8, {
  heading = 90.0,
  minZ = 3.0,
  maxZ = 4.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'The Belmont',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '3',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontStorage4', vector3(-1334.225, -1139.988, 17.37828), 0.6, 2.0, {
  heading = 90.0,
  minZ = 16.5,
  maxZ = 18.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'The Belmont',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '4',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontCloset', vector3(-1331.721, -1135.564, 0.0), 1.2, 0.8, {
  heading = 180.0,
  minZ = -1.0,
  maxZ = 1.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'The Belmont',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('BelmontCloset2', vector3(-1338.553, -1133.234, 16.51814), 0.8, 0.8, {
  heading = 180.0,
  minZ = 16.5,
  maxZ = 18.7,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'The Belmont',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('BelmontSupply1', vector3(-1344.622, -1134.533, 4.21775), 0.6, 0.6, {
  name = 'BelmontSupply1',
  heading = -15.0,
  minZ = 2.3,
  maxZ = 6.3,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-cocktail',
      label = 'Belmont Supply',

      groups = {
        'The Belmont',
      },

      uid = 'belmont-supply',
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontSupply2', vector3(-1347.511, -1141.567, 4.502978), 0.6, 0.6, {
  name = 'BelmontSupply2',
  heading = -15.0,
	minZ = 2.3,
  maxZ = 6.3,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-cocktail',
      label = 'Belmont Supply',

      groups = {
        'The Belmont',
      },

      uid = 'belmont-supply',
    },
  },
  distance = 2.5
})

AddCookingZone('The Belmont', 'BelmontPrep1', vector3(-1341.26, -1134.56, 4.14), 0.6, 1.4, 0.0, 3.94, 4.54, function()
  return true
end, {
  { id = 'bm_amarettosour', label = 'Mix Amaretto Sour' },
  { id = 'bm_limoncello', label = 'Mix Limoncello' },
  { id = 'bm_seltz', label = 'Mix Seltz Limone'},
  { id = 'bm_oldfashioned', label = 'Mix Old Fashioned' },
  { id = 'bm_bellini', label = 'Mix Bellini' },
  { id = 'bm_nuts', label = 'Make Belmont Nuts'}
},true)

AddCookingZone('The Belmont', 'BelmontPrep2', vector3(-1345.08, -1141.82, 4.14), 0.8, 1.4, 0.0, 3.94, 4.74, function()
  return true
end, {
  { id = 'bm_amarettosour', label = 'Mix Amaretto Sour' },
  { id = 'bm_limoncello', label = 'Mix Limoncello' },
  { id = 'bm_seltz', label = 'Mix Seltz Limone'},
  { id = 'bm_oldfashioned', label = 'Mix Old Fashioned' },
  { id = 'bm_bellini', label = 'Mix Bellini' },
  { id = 'bm_nuts', label = 'Make Belmont Nuts'}
},true)

AddBoxZone('BelmontTray', vector3(-1345.615, -1140.157, 4.180407), 0.6, 0.5, {
  name = 'BelmontTray',
  heading = 167.0,
	minZ= 3.83,
  maxZ= 5.03,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-1345.615, -1140.157, 4.180407),
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontTray2', vector3(-1347.758, -1140.123, 4.180407), 0.6, 0.5, {
  name = 'BelmontTray2',
  heading = 167.0,
	minZ= 3.83,
  maxZ= 5.03,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-1347.758, -1140.123, 4.180407),
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontTray3', vector3(-1344.257, -1132.519, 4.180407), 0.6, 0.5, {
  name = 'BelmontTray3',
  heading = 167.0,
  minZ= 3.83,
  maxZ= 5.03,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-1344.257, -1132.519, 4.180407),
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontTray4', vector3(-1340.853, -1132.488, 4.180407), 0.6, 0.5, {
  name = 'BelmontTray4',
  heading = 167.0,
	minZ= 3.83,
  maxZ= 5.03,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-1340.853, -1132.488, 4.180407),
    },
  },
  distance = 2.5
})

for _,belmont_tables in pairs({
  {name="belmonttable1", position=vector3(-1333.142, -1138.002, 3.942218)},
  {name="belmonttable2", position=vector3(-1334.288, -1133.08, 3.945736)},
  {name="belmonttable3", position=vector3(-1334.27, -1129.798, 3.945438)},
  {name="belmonttable4", position=vector3(-1348.936, -1136.488, 3.926596)},
  {name="belmonttable5", position=vector3(-1347.99, -1133.44, 4.14)},
  {name="belmonttable6", position=vector3(-1348.19, -1139.38, 22.08)}
  }) do

    AddBoxZone(belmont_tables.name, belmont_tables.position,1.0,1.0,{
      heading = 90.0,
      minZ = belmont_tables.position.z - 0.5,
      maxZ = belmont_tables.position.z + 0.5,
    }, {
      options = {
        {
          event_server = 'core:server:restaurant-tables:openTarget',
          icon = 'fa-regular fa-utensils',
          label = 'Table',
          table_coords = belmont_tables.position,
        },
      },
      distance = 2.5
    })

  end

  for _,patio_tables in pairs({
    {name="belmonttable7", position=vector3(-1340.334, -1123.936, 4.354916)},
    {name="belmonttable8", position=vector3(-1335.422, -1122.386, 4.3169)},
    {name="belmonttable9", position=vector3(-1330.684, -1121.49, 4.316952)}
    }) do

      AddBoxZone(patio_tables.name, patio_tables.position,2.0,2.0,{
        heading = 90.0,
        minZ = patio_tables.position.z - 0.5,
        maxZ = patio_tables.position.z + 0.5,
      }, {
        options = {
          {
            event_server = 'core:server:restaurant-tables:openTarget',
            icon = 'fa-regular fa-utensils',
            label = 'Table',
            table_coords = patio_tables.position,
          },
        },
        distance = 2.5
      })

    end

AddBoxZone('BelmontBoard1', vector3(-1347.29, -1136.59, 17.52), 6.6, 1.9, {
  name = 'BelmontBoard1',
  heading = 0,
	minZ= 17.12,
  maxZ= 17.72,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Table',
      table_coords = vector3(-1347.29, -1136.59, 17.52),
    },
  },
  distance = 8.5
})

AddBoxZone('BelmontRegister', vector3(-1343.995, -1141.566, 4.203407), 0.7, 0.55, {
  name = 'BelmontRegister',
  heading = 266.0,
	minZ= 3.83,
  maxZ= 5.03,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        'The Belmont'
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'The Belmont'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'The Belmont Hotel',

      groups = {
        'The Belmont'
      }
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontRegister2', vector3(-1345.87, -1133.463, 4.180407), 0.7, 0.55, {
  name = 'BelmontRegister2',
  heading = 266.0,
	minZ= 3.83,
  maxZ= 5.03,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',
      groups = {
        'The Belmont'
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'The Belmont'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'The Belmont Hotel',

      groups = {
        'The Belmont'
      }
    },
  },
  distance = 2.5
})

--cardtables
AddBoxZone('blackjacktable1', vector3(-1344.794, -1138.773, 0), 1.0, 1.0, {
  name = 'BelmontBlackjack1',
  heading = 0,
	minZ=-0.26,
  maxZ=0.14,
}, {
  options = {
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular fa-cards',
      label = "View Dealer's Hand",
      table_name = 'blackjackdealera'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 1's Hand",
      table_name = 'blackjackp1a'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 2's Hand",
      table_name = 'blackjackp2a'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp3a'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp4a'
    },
  },
  distance = 2.5
})

AddBoxZone('blackjacktable2', vector3(-1340.616, -1138.490, 0), 1.0, 1.0, {
  name = 'BelmontBlackjack2',
  heading = 0,
	minZ=-0.26,
  maxZ=0.14,
}, {
  options = {
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular fa-cards',
      label = "View Dealer's Hand",
      table_name = 'blackjackdealerb'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 1's Hand",
      table_name = 'blackjackp1b'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 2's Hand",
      table_name = 'blackjackp2b'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp3b'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp4b'
    },
  },
  distance = 2.5
})

AddBoxZone('blackjacktable3', vector3(-1344.922, -1131.110, 0), 1.0, 1.0, {
  name = 'BelmontBlackjack3',
  heading = 0,
	minZ=-0.26,
  maxZ=0.14,
}, {
  options = {
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular fa-cards',
      label = "View Dealer's Hand",
      table_name = 'blackjackdealerc'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 1's Hand",
      table_name = 'blackjackp1c'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 2's Hand",
      table_name = 'blackjackp2c'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp3c'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp4c'
    },
  },
  distance = 2.5
})

AddBoxZone('blackjacktable4', vector3(-1340.781, -1130.960, 0), 1.0, 1.0, {
  name = 'BelmontBlackjack4',
  heading = 0,
	minZ=-0.26,
  maxZ=0.14,
}, {
  options = {
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular fa-cards',
      label = "View Dealer's Hand",
      table_name = 'blackjackdealerd'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 1's Hand",
      table_name = 'blackjackp1d'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 2's Hand",
      table_name = 'blackjackp2d'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp3d'
    },
    {
      event_server = 'blrp_core:card_game:viewcards',
      icon = 'fa-regular',
      label = "View Player 3's Hand",
      table_name = 'blackjackp4d'
    },
  },
  distance = 2.5
})

AddBoxZone('BelmontCrafting', vector3(-1332.911, -1136.296, 1.089), 1.5,1.7,{
  name = 'BelmontCrafting',
  heading = 266.0,
  minZ = -1.0,
  maxZ = 1.1,
},{
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Bronze coin',

      item_id = 'bronzecoin',

      business_name = 'The Belmont',
      component_id = 'crafting',

    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Silver coin',

      item_id = 'silvercoin',

      business_name = 'The Belmont',
      component_id = 'crafting',

    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Gold coin',

      item_id = 'goldcoin',

      business_name = 'The Belmont',
      component_id = 'crafting',

    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Diamond coin',

      item_id = 'diamondcoin',

      business_name = 'The Belmont',
      component_id = 'crafting',

    }
  },
  distance = 2.0
})
