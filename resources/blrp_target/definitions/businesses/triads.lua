AddBoxZoneAutoname(vector4(-654.188354, -1229.206421, 11.724072, 120), 2.0, 1, {
  minZ = 11.00,
  maxZ = 12.40,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-sword',
      label = 'Craft Triad Sword',

      item_id = 'wbody|WEAPON_DRAGON_KATANA_BLUE',

      business_name = 'Triads',
      component_id = 'crafting',
      component_perm = 'extra_a',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft AP Pistol',

      item_id = 'wbody|WEAPON_APPISTOL',
      weekly_craft_limit = 40,

      business_name = 'Triads',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm Body',

      item_id = 'firearm_body',

      business_name = 'Triads',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 9mm Ammo (20)',

      item_id = 'ammo_9mm',

      business_name = 'Triads',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Triads',
      component_id = 'firearm-repair',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Large Storage Crate',

      item_id = 'bl_prop_gunbox',

      business_name = 'Triads',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-653.062561, -1230.987183, 11.724072, 120), 3.8, 1, {
  minZ = 11.00,
  maxZ = 12.40,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Triads',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 1750.0,
        named = 'TriadsMLOStorage1',
      },
    },
  },
  distance = 2.5
})

-- AddGangBenchOption('Triads', {
--   event_server = 'core:server:target-backhaul:craft',
--   icon = 'fa-regular fa-sword',
--   label = 'Craft Triad Sword',

--   item_id = 'wbody|WEAPON_DRAGON_KATANA_BLUE',

--   business_name = 'Triads',
--   component_id = 'crafting',
-- })

--  AddGangBenchOption('Triads', {
--    event_server = 'core:server:target-backhaul:craft',
--    icon = 'fa-regular fa-wrench',
--    label = 'Craft AP Pistol',
--      weekly_craft_limit = 40,
--    item_id = 'wbody|WEAPON_APPISTOL',

--    business_name = 'Triads',
--    component_id = 'crafting',
--  })

-- AddGangBenchOption('Triads', {
--   event_server = 'core:server:target-backhaul:craft',
--   icon = 'fa-regular fa-wrench',
--   label = 'Craft 9mm Ammo (20)',

--   item_id = 'ammo_9mm',

--   business_name = 'Triads',
--   component_id = 'crafting',
-- })

-- AddGangBenchOption('Triads', {
--   event_server = 'core:server:target-backhaul:craft',
--   icon = 'fa-regular fa-wrench',
--   label = 'Craft Firearm Body',

--   item_id = 'firearm_body',

--   business_name = 'Triads',
--   component_id = 'crafting',
-- })

-- AddGangBenchOption('Triads', {
--   event_server = 'core:server:item-durability:useGunBreakdown',
--   icon = 'fa-regular fa-toolbox',
--   label = 'Break Down Firearms',

--   business_name = 'Triads',
--   component_id = 'firearm-repair',
--   component_perm = 'crafting',
-- })

-- AddGangBenchOption('Triads', {
--   event_server = 'core:server:target-backhaul:craft',
--   icon = 'fa-regular fa-wrench',
--   label = 'Craft Large Storage Crate',

--   item_id = 'bl_prop_gunbox',

--   business_name = 'Triads',
--   component_id = 'crafting',
--   component_perm = 'crafting',
-- })