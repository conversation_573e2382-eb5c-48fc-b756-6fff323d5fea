
AddBoxZone('CondemnedGavel', vector3(2571.4, 4656.2, 35.5), 0.7, 0.5, {
  name = 'CondemnedGavel',
  heading = 150.0,
  minZ = 34.0,
  maxZ = 35.5,
}, {
  options = {
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel',

      strikes = 1,

      groups = {
        'Condemned MC',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 2x Strike',

      strikes = 2,

      groups = {
        'Condemned MC',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 3x Strike',

      strikes = 3,

      groups = {
        'Condemned MC',
      },

    },


  },
  distance = 2.5
})

AddBoxZone('CondemnedMCManagement', vector3(2572.094, 4655.859, 34.88108), 0.5, 0.5, {
  name = 'CondemnedMCManagement',
  heading = 285.0,
  minZ = 34.88,
  maxZ = 35.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Condemned MC',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZone('CondemnedMCStorageMain', vector3(2558.402, 4673.014, 34.31623), 0.9, 3.0, {
  name = 'CondemnedMCStorageMain',
  heading = 134.0,
  minZ = 33.5,
  maxZ = 34.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Condemned MC',
      component_id = 'storage',
    },
  },
  distance = 3.0
})

AddBoxZone('CondemnedMCStorage2', vector3(2569.233, 4657.598, 34.53861), 0.8, 0.8, {
  name = 'CondemnedMCStorage2',
  heading = 44.0,
  minZ = 34.0,
  maxZ = 35.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Condemned MC',
      component_id = 'storage',
      component_perm = 'extra_c',

      args = {
        capacity = 250.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('CondemnedMCFridge', vector3(2551.230, 4664.996, 34.0463), 0.8, 1.7, {
  name = 'CondemnedMCFridge',
  heading = 44.0,
  minZ = 34.0,
  maxZ = 36.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Condemned MC',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('CondemnedMCCloset1', vector3(2565.083, 4651.03, 34.06062), 1.0, 1.6, {
  name = 'CondemnedMCCloset1',
  heading = 224.0,
  minZ = 34.0,
  maxZ = 36.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Condemned MC',
      }
    },
  },
  distance = 2.0
})

AddBoxZone('CondemnedMCCloset2', vector3(2561.203, 4651.974, 34.06062), 1.0, 2.5, {
  name = 'CondemnedMCCloset2',
  heading = 134.0,
  minZ = 34.0,
  maxZ = 36.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Condemned MC',
      }
    },
  },
  distance = 3.0
})

-- AddBoxZone('CondemnedMCCrafting', vector3(2576.027, 4676.304, 34.31704), 1.0, 2.75, {
--   name = 'CondemnedMCCrafting',
--   heading = 225.0,
--   minZ = 34.0,
--   maxZ = 35.0,
-- }, {
--   options = {
--     -- {
--     --   event_server = 'core:server:item-durability:useGunRepairTarget',
--     --   icon = 'fa-regular fa-toolbox',
--     --   label = 'Repair Firearms',

--     --   business_name = 'Condemned MC',
--     --   component_id = 'firearm-repair',
--     --   component_perm = 'crafting',
--     -- },
--     -- {
--     --   event_server = 'core:server:item-durability:useGunBreakdown',
--     --   icon = 'fa-regular fa-toolbox',
--     --   label = 'Break Down Firearms',

--     --   business_name = 'Condemned MC',
--     --   component_id = 'firearm-repair',
--     --   component_perm = 'crafting',
--     -- },
--     -- {
--     --   event_server = 'core:server:target-backhaul:craft',
--     --   icon = 'fa-regular fa-wrench',
--     --   label = 'Craft Amatex',

--     --   item_id = 'dynamite_st',

--     --   business_name = 'Condemned MC',
--     --   component_id = 'explosive-crafting',
--     --   component_perm = 'extra_a',
--     -- },
--     -- {
--     --   event_server = 'core:server:target-backhaul:craft',
--     --   icon = 'fa-regular fa-wrench',
--     --   label = 'Craft ANFO',

--     --   item_id = 'dynamite_fe',

--     --   business_name = 'Condemned MC',
--     --   component_id = 'explosive-crafting',
--     --   component_perm = 'extra_a',
--     -- },
--     -- {
--     --   event_server = 'core:server:target-backhaul:craft',
--     --   icon = 'fa-regular fa-wrench',
--     --   label = 'Craft Nitroglycerin',

--     --   item_id = 'dynamite_al',

--     --   business_name = 'Condemned MC',
--     --   component_id = 'explosive-crafting',
--     --   component_perm = 'extra_a',
--     -- },
--     -- {
--     --   event_server = 'core:server:target-backhaul:craft',
--     --   icon = 'fa-regular fa-wrench',
--     --   label = 'Craft Semtex',

--     --   item_id = 'dynamite_ti',

--     --   business_name = 'Condemned MC',
--     --   component_id = 'explosive-crafting',
--     --   component_perm = 'extra_a',
--     -- },
--     -- {
--     --   event_server = 'core:server:target-backhaul:craft',
--     --   icon = 'fa-regular fa-wrench',
--     --   label = 'Craft Timed C4',

--     --   item_id = 'timed_explosive',

--     --   business_name = 'Condemned MC',
--     --   component_id = 'explosive-crafting',
--     --   component_perm = 'crafting',
--     -- },
--   },
--   distance = 3.0
-- })
