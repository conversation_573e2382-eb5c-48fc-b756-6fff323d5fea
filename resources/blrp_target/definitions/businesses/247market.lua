AddBoxZone('247MarketFridge0', vector3(1800.248, 3764.423, 34.64722), 0.7, 3.4, {
  name = '247MarketFridge0',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.7,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-refrigerator',
      label = 'Alcohol',

      uid = '247market-alcodrinks',
    },
  },
  distance = 4.0
})


AddBoxZone('247MarketFridge1', vector3(1801.636, 3762.409, 33.59122), 0.85, 1.55, {
  name = '247MarketFridge1',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.8,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-refrigerator',
      label = 'Pre-prepared Ingredients',

      uid = '247market-prepared',
    },
  },
  distance = 4.0
})

AddBoxZone('247MarketFridge2', vector3(1802.386, 3761.056, 33.59122), 0.85, 1.55, {
  name = '247MarketFridge2',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.8,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-refrigerator',
      label = 'Non-alcoholic Drinks',

      uid = '247market-nonalcodrinks',
    },
  },
  distance = 4.0
})

AddBoxZone('247MarketFridge3', vector3(1803.135, 3759.704, 33.59122), 0.85, 1.55, {
  name = '247MarketFridge3',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.8,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fresh Meat',

      uid = '247market-meat',
    },
  },
  distance = 4.0
})

AddBoxZone('247Market1', vector3(1814.44, 3763.126, 33.92512), 1.0, 4.3, {
  name = '247Market1',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.2,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Canned Goods',

      uid = '247market-canned',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Condiments',

      uid = '247market-condiments',
    },
  },
  distance = 4.0
})

AddBoxZone('247Market2', vector3(1812.236, 3762.039, 33.89874), 1.0, 4.3, {
  name = '247Market2',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.2,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Dessert Ingredients',

      uid = '247market-dessert',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Bar Fixtures',

      uid = '247market-bar',
    },
  },
  distance = 4.0
})

AddBoxZone('247Market3', vector3(1810.047, 3760.837, 33.90376), 1.0, 4.3, {
  name = '247Market3',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.2,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Dry Foods',

      uid = '247market-dry',
    },
  },
  distance = 4.0
})

AddBoxZone('247MarketProduce', vector3(1817.3, 3765.455, 32.47675), 1.5, 4.3, {
  name = '247MarketProduce',
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.0,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-eggplant',
      label = 'Fresh Produce',

      uid = '247market-produce',
    },
  },
  distance = 4.0
})

AddTargetModel({
  `prop_recyclebin_04_c`,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openYellerRecycling',
      icon = 'fa-regular fa-recycle',
      label = 'Open Recycling',
    },
  },
  distance = 2.5
})

AddBoxZone('247RecyleBench', vector3(1817.0, 3746.21, 33.48), 1, 2.0, {
  name = '247RecyleBench',
  heading = 25,
  minZ=33.28,
  maxZ=33.68,
}, {
  options = {
    {
      event_server = 'core:server:247market:useCrateBreakdown',
      icon = 'fa-regular fa-recycle',
      label = 'Break Down Grocery Crates',
    
      business_name = 'Yellers Market',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Plastic Bottle',

      item_id = 'empty_bottle',

      business_name = 'Yellers Market',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('247MarketSales', vector3(1822.602, 3750.635, 33.477), 3.75, 3.5, {
  heading = 120.0,
  minZ = 32.5,
  maxZ = 34.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Sell Produce',
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Sell Produce',

      uid = '247market-sales',
    },
  },
  distance = 3.0
})

AddBoxZone('247MarketRegister', vector3(1820.66, 3755.71, 33.48), 0.4, 0.45, {
  name = '247MarketRegister',
  heading = 300,
  minZ = 33.48,
  maxZ = 33.88,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'Yellers Market',
      }
    },
  },
  distance = 2
})

AddBoxZoneAutoname(vector4(2516.715, 4094.49, 38.47306, 245), 1.0, 2.0, {
  minZ = 37.48,
  maxZ = 38.58,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:craft',
        icon = 'fa-regular fa-wrench',
        label = 'Craft Sprunk Ice 6-Pack (High Grade Meth)',
  
        item_id = 'ice_sprunk_pack',
  
        business_name = 'Yellers Market',
        component_id = 'crafting',
      },
    --  {
    --    event_server = 'core:server:restaurants:transformFood',
    --    icon = 'fa-regular fa-cup-straw',
    --    label = 'Prepare Crystal Nog (Death Meth)',
    --    component_id = 'crafting',
    --    food_item_id = 'xmr_247_d',

    --    groups = {
    --      'Yellers Market'
    --    },
    --  },
  },
  distance = 2.5,
  context_disable = true,
})

AddBoxZone('247MarketStorageMain', vector3(2515.658, 4098.721, 39.37432), 0.7, 1.0, {
  name = '247MarketStorageMain',
  heading = 335.0,
  minZ = 37.7,
  maxZ = 39.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Yellers Market',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('247MarketStorage2', vector3(1821.474, 3754.434, 33.34958), 0.3, 1.0, {
  name = '247MarketStorage2',
  heading = 210.0,
  minZ = 32.5,
  maxZ = 34.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Yellers Market',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        uid = '2'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-dollar-sign',
      label = '24/7 Supply',

      uid = '247market-supply',
    },
  },
  distance = 2.5
})

AddBoxZone('247MarketManagement', vector3(1811.955, 3744.109, 33.22116), 0.7, 1.0, {
  name = '247MarketManagement',
  heading = 335.0,
  minZ = 33.2,
  maxZ = 34.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Yellers Market',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('247MarketCloset', vector3(1808.648, 3749.856, 33.50956), 0.5, 2.0, {
  name = '247MarketCloset',
  heading = 300.0,
  minZ = 32.5,
  maxZ = 34.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Yellers Market',
      }
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(1803.87, 3758.35, 33.48, 120), 0.85, 1.55, {
  minZ = 32.5,
  maxZ = 34.8,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-refrigerator',
        label = 'fish freezer',

        business_name = 'Yellers Market',
        component_id = 'storage',

        args = {
          capacity = 500.0,
          named = 'FishFreezer1',
          fake_fridge = true,
        },
      },
    },
    distance = 2.5,
  })

  AddBoxZone('247Counter', vector3(1821.39, 3756.11, 33.48), 0.6, 1.2, {
    name = '247Counter',
    heading = 30,
    minZ = 33.28,
    maxZ = 33.68,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-box',
        label = 'Counter',
        table_coords = vector3(1821.39, 3756.11, 33.48),
      },
    },
    distance = 2.5,
  })


