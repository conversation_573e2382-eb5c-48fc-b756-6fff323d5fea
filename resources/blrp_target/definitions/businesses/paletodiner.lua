local hands_washed = false

RegisterNetEvent('blrp_target:paletodiner:washHands', function()
  local clock_in_data = pCore.isClockedIntoTabletBusiness({ "Paleto Diner" })

  if clock_in_data and not clock_in_data.clocked_in and
  (
    not GlobalState.is_dev or
    not exports.blrp_core:me().request('[DEV] Override Clock-in to wash hands?')
  )
  then
    exports.blrp_core:me().notify('You are not clocked in (through the tablet)!')
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'paletodiner_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('paletodinerSink', vector3(78.14, 6580.62, 31.43), 0.8, 0.7, {
  name = 'paletodinerSink',
  heading = 315.0,
  minZ = 30.43,
  maxZ = 31.63,
}, {
  options = {
    {
      event_client = 'blrp_target:paletodiner:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        "Paleto Diner"
      }
    },
  },
  distance = 2.5
})

AddCookingZone("Paleto Diner", 'paletodinerCooking', vector3(79.3, 6581.84, 31.43), 0.8, 0.6, 315, 31.03, 31.63, function()
  return hands_washed
end, {
  {id = "food_spec_fgm", label = "Cook Fried Green Maters"},
  {id = "food_spec_bacuncraklins", label = "Candied Bacun Cracklins"},
  {id = "food_spec_chikunbugger", label = "Cook BBQ Chikun Little Buggerz"},
  {id = "food_steaktaters", label = 'Cook Steak n\' Taters'},
  {id = "food_venisongumbo", label = 'Cook Venison Gumbo'},
  {id = "food_porkchopssuc", label = 'ried porkchops n’ succotash'},
  --{id = "xmr_diner_f", label = 'Make Holiday Crack'},
},true)

AddCookingZone("Paleto Diner", 'paletodinerCooking2', vector3(75.8, 6581.01, 31.43), 0.8, 1.6, 315, 30.63, 31.83, function()
  return hands_washed
end, {
  {id = "food_spec_fgm", label = "Cook Fried Green Maters"},
  {id = "food_spec_bacuncraklins", label = "Candied Bacun Cracklins"},
  {id = "food_spec_chikunbugger", label = "Cook BBQ Chikun Little Buggerz"},
  {id = "food_steaktaters", label = 'Cook Steak n\' Taters'},
  {id = "food_venisongumbo", label = 'Cook Venison Gumbo'},
  {id = "food_porkchopssuc", label = 'ried porkchops n’ succotash'},
  --{id = "xmr_diner_f", label = 'Make Holiday Crack'},
},true)

AddCookingZone("Paleto Diner", "paletodinerDrinkMaker", vector3(81.06, 6583.52, 31.43), 1, 0.6, 315, 31.43, 32.03, function()
  return hands_washed
end, {
  {id = "food_spec_rootbeer", label = "Mix Root Beer"},
  {id = "food_spec_sweet_tea", label = "Mix Sweet Tea"},
  {id = "food_spec_materjuice", label = "Mix Mater Juice"},
  --{id = "xmr_diner_d", label = "Mix Eggnog"},
},true)

AddBoxZone('paletodinerFridge', vector3(77.19, 6585.2, 31.43), 1.6, 1, {
  name = 'paletodinerFridge',
  heading = 317.0,
  minZ = 30.43,
  maxZ = 32.83,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = "Paleto Diner",
      component_id = 'storage',

      args = {
        capacity = 750.0,
        named = 'paletodinerfridge'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('paletodinermeatprocessing', vector3(76.62, 6580.3, 31.43), 0.4, 0.6, {
  name = 'paletodinermeatprocessing',
  heading = 326,
  minZ = 31.03,
  maxZ = 31.43,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-knife',
      label = 'Chop Deer Meat',

      item_id = 'food_diced_venison',

      business_name = 'Paleto Diner',

      component_id = 'crafting',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-knife',
      label = 'Chop Boar Meat',

      item_id = 'food_sliced_boar',

      business_name = 'Paleto Diner',

      component_id = 'crafting',
      component_perm = 'crafting',
    },
  },
  distance = 3.5
})

AddBoxZone('paletodinerTray', vector3(79.36, 6579.65, 31.43), 0.8, 0.8, {
  name = 'paletodinerTray',
  heading = 316,
  minZ = 31.23,
  maxZ = 31.63,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(79.36, 6579.65, 31.43),
    },
  },
  distance = 2.5
})

AddBoxZone('paletodinerRegister', vector3(78.44, 6578.82, 31.43), 0.45, 0.5, {
  name = 'paletodinerRegister',
  heading = 315,
  minZ = 31.43,
  maxZ = 31.83,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        "Paleto Diner"
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        "Paleto Diner"
      }
    }, 
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-triangle-exclamation',
      label = 'Trigger Panic Alarm',

      location_override = "Paleto Diner",

      groups = {
        "Paleto Diner"
      }
    },
    {
      event_server = 'core:server:badeats:openRestaurantMenuTarget',
      icon = 'far fa-tablet',
      label = 'Badeats Admin',
      restaurant_name = "Paleto Diner",

      groups = {
        "Paleto Diner"
      },
    },
  },
  distance = 2.5
})

AddBoxZone('paletodinerCloset', vector3(72.31, 6587.39, 31.43), 0.4, 2.0, {
  name = 'paletodinerCloset',
  heading = 45,
  minZ = 30.43,
  maxZ = 32.63,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        "Paleto Diner",
      }
    }
  },
  distance = 2.5
})

AddBoxZone('paletodinerDryIngredients', vector3(78.95, 6582.56, 31.43), 1.2, 0.4, {
  name = 'paletodinerDryIngredients',
  heading = 315.0,
  minZ = 32.23,
  maxZ = 33.23,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = "Paleto Diner",
      component_id = 'storage',

      args = {
        capacity = 750,
        named = 'paletodiner_a'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('paletodinerDryExtraStorage', vector3(78.78, 6581.26, 31.43), 0.8, 0.8, {
  name = 'paletodinerDryExtraStorage',
  heading = 316,
  minZ = 30.43,
  maxZ = 31.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = "Paleto Diner",
      component_id = 'storage',

      args = {
        capacity = 750,
        named = 'paletodiner_b'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('paletodinerDrinksStorage', vector3(79.91, 6582.34, 31.43), 0.8, 0.6, {
  name = 'paletodinerDrinksStorage',
  heading = 315,
  minZ = 30.43,
  maxZ = 31.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = "Paleto Diner",
      component_id = 'storage',

      args = {
        capacity = 750,
        named = 'paletodiner_c'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'paletodiner-supply',

      groups = {
        "Paleto Diner"
      }
    },
  },
  distance = 2.5
})

AddBoxZone('paletodinerManagement', vector3(76.35, 6590.46, 31.43), 0.8, 0.2, {
  name = 'paletodinerManagement',
  heading = 43,
  minZ = 31.23,
  maxZ = 31.83,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = "Paleto Diner",
      component_id = 'management',

      component_perm = 'extra_a',
    },
  },
  distance = 3
})

AddBoxZone('paletodinerManagerStorage', vector3(75.57, 6586.28, 31.43), 0.6, 1.2, {
  name = 'paletodinerManagerStorage',
  heading = 315,
  minZ = 30.43,
  maxZ = 31.43,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = "Paleto Diner",
      component_id = 'storage',

      component_perm = 'extra_a',

      args = {
        capacity = 500,
        named = 'paletodiner_d'
      },

    },
  },
  distance = 2.5
})
