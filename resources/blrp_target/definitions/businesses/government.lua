-- Commissioner

<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>('SAGovCommissionerStorage', vector3(1705.312, 3791.677, 33.74802), 0.7, 1.3, {
  heading = 34.1,
  minZ = 33.74,
  maxZ = 34.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'San Andreas Government',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 1000.0,
        uid = 'A1',
      },
    },
  },
  distance = 1.75
})

AddBoxZone('SAGovCommissionerWardrobe', vector3(1702.011, 3789.542, 35.14914), 0.7, 1.7, {
  heading = 34.1,
  minZ = 33.74,
  maxZ = 36.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'San Andreas Government',
      }
    },
  },
  distance = 1.75
})

AddBoxZone('SAGovCommissionerStorage2', vector3(1540.145, 2220.703, 77.62173), 0.6, 1.5, {
  heading = 270.0,
  minZ = 76.5,
  maxZ = 78.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'San Andreas Government',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 1000.0,
        uid = 'A2',
      },
    },
  },
  distance = 2.0
})

AddBoxZone('SAGovCommissionerWardrobe2', vector3(1542.912, 2235.718, 78.01157), 0.7, 2.0, {
  heading = 270.0,
  minZ = 77.0,
  maxZ = 79.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'San Andreas Government',
      }
    },
  },
  distance = 2.0
})

-- Mayor

AddBoxZone('SAGovMayorStorage', vector3(-525.2874, -189.3727, 44.56925), 0.7, 1.5, {
  name = 'SAGovMayorStorage',
  heading = 300.0,
  minZ = 42.37,
  maxZ = 44.57,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'San Andreas Government',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 1000.0,
        uid = 'B1',
      },
    },
  },
  distance = 2.0
})

AddBoxZone('SAGovMayorWardrobe', vector3(-527.4645, -185.4492, 44.56925), 0.7, 1.5, {
  name = 'SAGovMayorWardrobe',
  heading = 300.0,
  minZ = 42.37,
  maxZ = 44.57,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'San Andreas Government',
      }
    },
  },
  distance = 2.0
})

-- Governor Office

AddBoxZone('SAGovGovernorStorage1', vector3(-591.6484, -718.3953, 121.5501), 1.4, 2.2, {
  name = 'SAGovGovernorStorage1',
  heading = 360.0,
  minZ = 120.6,
  maxZ = 121.57,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'San Andreas Government',
      component_id = 'storage',
      component_perm = 'extra_c',

      args = {
        capacity = 1000.0,
        uid = 'C1',
      },
    },
  },
  distance = 2.0
})

AddBoxZone('SAGovGovernorWardrobe1', vector3(-600.772, -709.749, 121.605), 4.0, 4.0, {
  name = 'SAGovGovernorWardrobe1',
  heading = 360.0,
  minZ = 120.0,
  maxZ = 123.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'San Andreas Government',
      }
    },
  },
  context_disable = true,
  distance = 4.0
})

-- Governor House

AddBoxZone('SAGovGovernorStorage2', vector3(-799.5141, 176.5631, 71.88088), 0.7, 1.5, {
  name = 'SAGovGovernorStorage2',
  heading = 200.0,
  minZ = 71.8,
  maxZ = 73.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'San Andreas Government',
      component_id = 'storage',
      component_perm = 'extra_c',

      args = {
        capacity = 1000.0,
        uid = 'C2',
      },
    },
  },
  distance = 2.0
})

AddBoxZone('SAGovGovernorWardrobe2', vector3(-811.694, 175.144, 76.745), 2.0, 3.0, {
  name = 'SAGovGovernorWardrobe2',
  heading = 110.0,
  minZ = 75.8,
  maxZ = 78.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'San Andreas Government',
      }
    },
  },
  context_disable = true,
  distance = 4.0
})
