--[[
AddBoxZone('305AutobodyManagement', vector3(-1430.387, -454.8969, 35.73009), 0.4, 0.4, {
  name = '305AutobodyManagement',
  heading = 30.0,
  minZ = 35.7,
  maxZ = 36.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = '305 Autobody',
      component_id = 'management',
    },
  },
  distance = 3.0
})

AddBoxZone('305AutobodyStorage1', vector3(-1409.791, -448.7712, 37.15146), 0.8, 1.8, {
  name = '305AutobodyStorage1',
  heading = 212.0,
  minZ = 35.0,
  maxZ = 37.2,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Parts Supply',

      uid = 'mechanic-parts',

      groups = {
        '305 Autobody',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '305 Autobody',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('305AutobodyJobSelect', vector3(-1426.906, -458.4258, 35.7115), 0.45, 0.5, {
  name = '305AutobodyJobSelect',
  heading = 216.0,
  minZ = 35.7,
  maxZ = 36.15,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Tow Truck',

      icon = 'fa-solid fa-clock',
      label = 'Clock in - Tow Truck',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Mechanic',

      icon = 'fa-solid fa-clock',
      label = 'Clock in - Mechanic',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fa-solid fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 3.0
})

AddBoxZone('305AutobodyCloset', vector3(-1426.027, -457.2849, 36.85043), 1.0, 1.3, {
  name = '305AutobodyCloset',
  heading = 122.0,
  minZ = 35.0,
  maxZ = 36.8,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        '305 Autobody',
      }
    },
  },
  distance = 3.0
})
]]
