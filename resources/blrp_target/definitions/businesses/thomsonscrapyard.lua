-- AddBoxZone('ScrapyardManagement', vector3(2329.59, 3047.2854, 47.14553), 0.8, 2.4, {
--   name = 'ScrapyardManagement',
--   heading = 270.0,
--   minZ = 47.0,
--   maxZ = 49.8,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:openWardrobePersonal',
--       icon = 'fa-regular fa-tshirt',
--       label = 'Personal Wardrobe',

--       groups = {
--         'Thomson Scrapyard',
--       }
--     },
--     {
--       event_server = 'core:server:businesses:custom:targetInteract',
--       icon = 'fa-regular fa-computer-classic',
--       label = 'Management',

--       business_name = 'Thomson Scrapyard',
--       component_id = 'management',
--     },
--     {
--       event_server = 'core:server:target-backhaul:getDailyDistributor',
--       icon = 'fa-regular fa-computer-classic',
--       label = 'Get Daily Export Distributor',

--       business_name = '<PERSON> Scrapyard',
--       component_id = 'null',
--       component_perm = 'extra_b',
--     },

--   },
--   distance = 2.5
-- })

-- for idx, storage_data in ipairs({
--   { coords = vector3(2342.687, 3045.509, 49.120224), heading = 180.0, named = 'cs4_sy_Aluminum', component_perm = 'extra_c', }, -- Aluminum
--   { coords = vector3(2341.314, 3045.662, 49.120224), heading = 180.0, named = 'cs4_sy_Copper', component_perm = 'extra_c', }, -- Copper
--   { coords = vector3(2342.85938, 3045.53955, 47.85914), heading = 180.0, named = 'cs4_sy_Gold', component_perm = 'extra_c', }, -- Gold
--   { coords = vector3(2339.41968, 3045.509, 49.120224), heading = 180.0, named = 'cs4_sy_Platinum', component_perm = 'extra_c', }, -- Platinum
--   { coords = vector3(2332.88672, 3045.509, 49.120224), heading = 180.0, named = 'cs4_sy_Nitrate', component_perm = 'extra_c', }, -- Nitrate
--   { coords = vector3(2334.79053, 3045.662, 49.120224), heading = 180.0, named = 'cs4_sy_Wood', component_perm = 'extra_c', }, -- Wood
--   { coords = vector3(2333.059, 3045.53955, 47.85914), heading = 180.0, named = 'cs4_sy_Plastic', component_perm = 'extra_c', }, -- Plastic
--   { coords = vector3(2331.51367, 3045.662, 47.85914), heading = 180.0, named = 'cs4_sy_Glue', component_perm = 'extra_c', }, -- Glue
--   { coords = vector3(2339.592, 3045.53955, 47.85914), heading = 180.0, named = 'cs4_sy_Pipe', component_perm = 'extra_c', }, -- Steel Pipe
--   { coords = vector3(2338.04663, 3045.662, 47.85914), heading = 180.0, named = 'cs4_sy_Gears', component_perm = 'extra_c', }, -- Gears
--   { coords = vector3(2336.16357, 3045.509, 49.120224), heading = 180.0, named = 'cs4_sy_Springs', component_perm = 'extra_c', }, -- Springs
--   { coords = vector3(2331.51367, 3045.662, 49.120224), heading = 180.0, named = 'cs4_sy_Oil', component_perm = 'extra_c', }, -- Oil
--   { coords = vector3(2334.79053, 3045.662, 47.85914), heading = 180.0, named = 'cs4_sy_Charcoal', component_perm = 'extra_c', }, -- Charcoal
--   { coords = vector3(2338.04663, 3045.662, 49.120224), heading = 180.0, named = 'cs4_sy_Steel', component_perm = 'extra_c', }, -- Steel
--   { coords = vector3(2341.314, 3045.662, 47.85914), heading = 180.0, named = 'cs4_sy_Titanium', component_perm = 'extra_c', }, -- Titanium
--   { coords = vector3(2336.336, 3045.53955, 47.85914), heading = 180.0, named = 'cs4_sy_Electronics', component_perm = 'extra_c', }, -- Electronics
--   { coords = vector3(2332.33936, 3050.393, 49.120224), heading = 0.0, named = 'cs4_sy_Tape', component_perm = 'extra_c', }, -- Tape
--   { coords = vector3(2332.16919, 3050.34766, 47.85914), heading = 0.0, named = 'cs4_sy_Glass', component_perm = 'extra_c', }, -- Glass Shards
--   { coords = vector3(2336.97534, 3050.24463, 49.120224), heading = 0.0, named = 'cs4_sy_Outbound1', }, -- Storage 1
--   { coords = vector3(2336.97534, 3050.24463, 47.85914), heading = 0.0, named = 'cs4_sy_Outbound2', }, -- Storage 2 
--   { coords = vector3(2333.701, 3050.24463, 49.120224), heading = 0.0, named = 'cs4_sy_Misc1', component_perm = 'extra_c', }, -- Misc 1
--   { coords = vector3(2333.701, 3050.24463, 47.85914), heading = 0.0, named = 'cs4_sy_Misc2', component_perm = 'extra_c', }, -- Misc 2
--   { coords = vector3(2335.61377, 3050.393, 49.120224), heading = 0.0, named = 'cs4_sy_Misc3', component_perm = 'extra_c', }, -- Misc 3 -- needs number
--   { coords = vector3(2335.4436, 3050.34766, 47.85914), heading = 0.0, named = 'cs4_sy_Misc4', component_perm = 'extra_c', }, -- Misc 4 --- needs number
-- }) do
--   AddBoxZone('ThomsonStorage' .. idx, storage_data.coords, 0.5, 1.3, {
--     name = 'ThomsonStorage' .. idx,
--     heading = storage_data.heading,
--     minZ = storage_data.coords.z - 0.5,
--     maxZ = storage_data.coords.z + 0.5,
--   }, {
--     options = {
--       {
--         event_server = 'core:server:businesses:custom:targetInteract',
--         icon = 'far fa-box',
--         label = 'Storage',

--         business_name = 'Thomson Scrapyard',
--         component_id = 'storage',
--         component_perm = storage_data.component_perm,

--         args = {
--           capacity = 1000.0,
--           named = storage_data.named
--         },
--       }
--     },
--     distance = 3.0,
--   })
-- end

-- AddBoxZone('ScrapyardWorkbench', vector3(2329.91, 3049.75, 48.15), 0.5, 1.5, {
--   name = 'ScrapyardWorkbench',
--   heading = 80.0,
--   minZ= 47.15,
--   maxZ= 48.75,
-- }, {
--   options = {
--     --[[{
--       event_server = 'core:server:target-backhaul:craft',
--       icon = 'fa-regular fa-wrench',
--       label = 'Craft Power Bank',

--       item_id = 'powerbank',

--       business_name = 'Thomson Scrapyard',
--       component_id = 'crafting',
--     },
--     {
--       event_server = 'core:server:target-backhaul:makeAluminumSheet',
--       icon = 'fa-regular fa-hammer-crash',
--       label = 'Make Aluminum Sheets',

--       groups = {
--         'Thomson Scrapyard',
--         'LEO',
--       }
--     },
--     {
--       event_server = 'core:server:target-backhaul:makePlate',
--       icon = 'fa-regular fa-hammer-crash',
--       label = 'Manufacture License Plate',

--       groups = {
--         'Thomson Scrapyard',
--         'LEO',
--       }
--     },]]--
--   },
--   distance = 3.0,
-- })

-- AddBoxZone('ThomsonTrashDumpster', vector3(2354.314, 3035.834, 47.02058), 4.75, 2.5, {
--   name = 'ThomsonTrashDumpster',
--   heading = 135.0,
--   minZ = 47.0,
--   maxZ = 49.5,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:openScrapyardTrashInput',
--       icon = 'fa-regular fa-inbox-in',
--       label = 'Open Input',
--     },
--     {
--       event_server = 'core:server:target-backhaul:openScrapyardTrashOutput',
--       icon = 'fa-regular fa-inbox-out',
--       label = 'Open Output',

--       business_name = 'Thomson Scrapyard',
--       component_id = 'recycleroutput',
--       component_perm = 'extra_a',
--     },
--   },
--   distance = 3.0,
-- })

-- -------------------------
-- ---- MATERIAL EXPORT ----
-- -------------------------

-- AddBoxZone('ScrapyardExport1', vector3(1736.215, 3330.231, 40.21017), 2.9, 2.9, {
--   name = 'ScrapyardExport1',
--   heading = 15.0,
--   minZ = 40.2,
--   maxZ = 42.5,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:sellToDistributor',
--       icon = 'fa-regular fa-box-check',
--       label = 'Export Materials',

--       groups = {
--         'Thomson Scrapyard',
--       },
--     },
--   },
--   distance = 3.5
-- })

-- AddBoxZone('ScrapyardExport2', vector3(2146.547, 4777.9, 39.97714), 2.9, 2.9, {
--   name = 'ScrapyardExport2',
--   heading = 15.0,
--   minZ = 39.9,
--   maxZ = 42.5,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:sellToDistributor',
--       icon = 'fa-regular fa-box-check',
--       label = 'Export Materials',

--       groups = {
--         'Thomson Scrapyard',
--       },
--     },
--   },
--   distance = 3.5
-- })

-- AddBoxZone('ScrapyardExport3', vector3(3805.014, 4442.641, 3.051531), 2.9, 2.9, {
--   name = 'ScrapyardExport3',
--   heading = 15.0,
--   minZ = 3.05,
--   maxZ = 5.05,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:sellToDistributor',
--       icon = 'fa-regular fa-box-check',
--       label = 'Export Materials',

--       groups = {
--         'Thomson Scrapyard',
--       },
--     },
--   },
--   distance = 3.5
-- })

-- AddBoxZone('ScrapyardExport4', vector3(-98.0617752, 6499.332, 30.4293442), 2.9, 2.9, {
--   name = 'ScrapyardExport4',
--   heading = 60.0,
--   minZ = 30.4,
--   maxZ = 33.0,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:sellToDistributor',
--       icon = 'fa-regular fa-box-check',
--       label = 'Export Materials',

--       groups = {
--         'Thomson Scrapyard',
--       },
--     },
--   },
--   distance = 3.5
-- })

-- AddBoxZone('ScrapyardExport5', vector3(1181.85034, -2997.16479, 4.889572), 2.9, 8.0, {
--   name = 'ScrapyardExport5',
--   heading = 357.0,
--   minZ = 4.88,
--   maxZ = 7.5,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:target-backhaul:sellToDistributor',
--       icon = 'fa-regular fa-box-check',
--       label = 'Export Materials',

--       groups = {
--         'Thomson Scrapyard',
--       },
--     },
--   },
--   distance = 9.0
-- })
