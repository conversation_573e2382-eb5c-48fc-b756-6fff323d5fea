for k, tray_coords in ipairs({
  vector3(4902.062, -4942.404, 3.402),
  vector3(4906.032, -4940.350, 3.402),
}) do
  AddBoxZone('CayoTray' .. k, tray_coords, 0.6, 1.0, {
    name = 'CayoTray' .. k,
    heading=330,
    minZ=tray_coords.z-0.1,
    maxZ=tray_coords.z+0.2,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Tray',
        table_coords = tray_coords,
      },
    },
    distance = 2.5
  })
end