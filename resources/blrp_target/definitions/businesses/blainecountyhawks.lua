AddBoxZone('BCHManagement', vector3(2121.5708, 4784.508, 40.38488), 1.0, 1.5, {
  name = 'BCHManagement',
  heading = 300.0,
  minZ = 40.0,
  maxZ = 42.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Blaine County Hawks',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('BlaineCountyHawksStorage', vector3(2121.904, 4783, 40.84412), 0.4, 1.0, {
  name = 'BlaineCountyHawksStorage',
  heading = 300.0,
  minZ = 40.80,
  maxZ = 41.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Blaine County Hawks',
      component_id = 'storage',
    },
  },
  distance = 3.0
})


AddBoxZone('BlaineCountyHawksLocker', vector3(2117.918, 4783.99, 41.17321), 0.4, 1.6, {
  name = 'BlaineCountyHawksLocker',
  heading = 15.0,
  minZ = 41.10,
  maxZ = 41.30,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Blaine County Hawks',
      }
    },
  },
  distance = 3.0
})