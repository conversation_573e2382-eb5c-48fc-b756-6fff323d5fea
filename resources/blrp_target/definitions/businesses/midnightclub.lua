AddBoxZone('MidnightClubCloset', vector3(918.0, -1796.024, 22.77853), 1.0, 2.0, {
  name = 'MidnightClubCloset',
  heading = 85.0,
  minZ = 21.2,
  maxZ = 23.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Midnight Club',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('midnightclubshop', vector3(946.3409, -1744.544, 20.0347), 0.8, 1.8, {
  name = 'midnightclubshop',
  heading=350,
  minZ=20.03,
  maxZ=21.23,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Parts Supply',

      uid = 'thepit-supply',

      groups = {
        'Midnight Club',
      },  
    },
  },
  distance = 2.5
})

AddBoxZone('MidnightClubStorageMain', vector3(915.4008, -1749.64, 24.84943), 3.0, 6.0, {
  name = 'MidnightClubStorageMain',
  heading = 355.0,
  minZ = 22.0,
  maxZ = 24.85,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Midnight Club',
      component_id = 'storage',
    },
  },
  distance = 5.0
})
