AddBoxZone('BoathouseManagement', vector3(1530.821, 3776.027, 37.89389), 0.5, 0.5, {
  name = 'BoathouseManagement',
  heading = 85.0,
  minZ = 37.8,
  maxZ = 38.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'The Boathouse',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('BoathouseStorageMain', vector3(1513.357, 3772.254, 33.51882), 1.0, 2.1, {
  name = 'BoathouseStorageMain',
  heading = 195.0,
  minZ = 33.5,
  maxZ = 36.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'The Boathouse',
      component_id = 'storage',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-store',
      label = 'Boathouse Supply Closet',

      uid = 'boathouse-supply',

      groups = {
        'The Boathouse',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('BoathouseStorageSafe', vector3(1527.255, 3775.985, 37.92057), 1.0, 1.0, {
  name = 'BoathouseStorageSafe',
  heading = 134.0,
  minZ = 37.1,
  maxZ = 38.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'The Boathouse',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 250.0,
        uid = 'Safe',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BoathouseFridge', vector3(1526.623, 3791.428, 37.1509), 0.8, 0.8, {
  name = 'BoathouseFridge',
  heading = 300.0,
  minZ = 37.1,
  maxZ = 39.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'The Boathouse',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('BoathouseCloset1', vector3(1517.689, 3778.9, 37.144), 0.8, 1.5, {
  name = 'BoathouseCloset1',
  heading = 43.0,
  minZ = 37.1,
  maxZ = 39.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'The Boathouse',
      }
    },
  },
  distance = 2.5
})
AddBoxZone('BoathousePanic', vector3(1520.18, 3787.38, 34.51), 0.5, 0.5, {
  name = 'BHPanic',
  heading = 357,
  minZ = 34.41,
  maxZ = 34.71,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'The Boat House',

      groups = {
        'The Boathouse'
      }
    }
  },

  distance = 2.0
})
