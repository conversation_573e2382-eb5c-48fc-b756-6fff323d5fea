
AddBoxZone('LostMCManagement', vector3(-62.32717, 6384.079, 31.41035), 0.6, 0.8, {
  name = 'LostMCManagement',
  heading = 225.0,
  minZ = 31.4,
  maxZ = 32.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Lost MC',
      component_id = 'management',
    },
  },
  distance = 2.0
})
AddBoxZone('LostGavel', vector3(-57.1, 6393, 28.033), 0.7, 1.1, {
  name = 'LostGavel',
  heading = 150.0,
  minZ = 27,
  maxZ = 28.033,
}, {
  options = {
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel',

      strikes = 1,

      groups = {
        'Lost MC',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 2x Strike',

      strikes = 2,

      groups = {
        'Lost MC',
      },
    },
    {
      event_server = 'core:server:gavels:custom:targetInteract',
      icon = 'fa-regular fa-gavel',
      label = 'Use Gavel 3x Strike',

      strikes = 3,

      groups = {
        'Lost MC',
      },
    },
  },
  distance = 2.5
})


AddBoxZone('LostMCStorageMain', vector3(-60.79222, 6391.626, 28.51825), 1.0, 0.9, {
  name = 'LostMCStorageMain',
  heading = 133.5,
  minZ = 27.0,
  maxZ = 28.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Lost MC',
      component_id = 'storage',
      component_perm = 'extra_c',

      args = {
        capacity = 250.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('LostMCStorage2', vector3(-43.73593, 6384.48, 31.92512), 1.5, 2.0, {
  name = 'LostMCStorage2',
  heading = 45.0,
  minZ = 30.6,
  maxZ = 32.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Lost MC',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('LostMCStorage3', vector3(-61.42706, 6379.489, 32.07087), 0.2, 0.7, {
  name = 'LostMCStorage3',
  heading = 315.0,
  minZ = 31.6,
  maxZ = 32.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'Lost MC',
      component_id = 'storage',
      component_perm = 'extra_c',

      args = {
        capacity = 25.0,
        uid = 'Safe'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('LostMCFridge', vector3(-52.77624, 6385.09, 31.82063), 0.8, 1.4, {
  name = 'LostMCFridge',
  heading = 225.0,
  minZ = 30.5,
  maxZ = 31.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Lost MC',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('LostMCCrafting', vector3(-43.8737, 6388.129, 27.81626), 0.9, 1.7, {
  name = 'LostMCCrafting',
  heading = 45.0,
  minZ = 27.8,
  maxZ = 28.8,
}, {
  options = {
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Lost MC',
      component_id = 'crafting',
    },

     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft TEC-9',

       item_id = 'wbody|WEAPON_MACHINEPISTOL',
       weekly_craft_limit = 30,
       business_name = 'Lost MC',
       component_id = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft 9mm Ammo (20)',

       item_id = 'ammo_9mm',

       business_name = 'Lost MC',
       component_id = 'crafting',
     },

    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm Body',

      item_id = 'firearm_body',

      business_name = 'Lost MC',
      component_id = 'craft-firearm_body',
      component_perm = 'extra_d',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Large Storage Crate',

      item_id = 'bl_prop_gunbox',

      business_name = 'Lost MC',
      component_id = 'crafting',
      component_perm = 'extra_d',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Body Fixer',

      item_id = 'mech_body_fixer',

      business_name = 'Lost MC',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Diagnose Tool',

      item_id = 'mech_diagnose_tool',

      business_name = 'Lost MC',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Engine Wrench',

      item_id = 'mech_engine_wrench',

      business_name = 'Lost MC',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Fuel Tools',

      item_id = 'mech_fuel_tools',

      business_name = 'Lost MC',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Repair Parts (25)',

      item_id = 'repair_parts',

      business_name = 'Lost MC',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('LostMCCloset', vector3(-44.21349, 6381.057, 29.13535), 0.7, 2.0, {
  name = 'LostMCCloset',
  heading = 225.0,
  minZ = 27.0,
  maxZ = 29.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Lost MC',
      }
    },
  },
  distance = 3.0
})
