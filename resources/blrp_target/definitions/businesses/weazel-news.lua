AddBoxZoneAutoname(vector4(-594.2422, -927.996, 28.04606, 90.0), 0.8, 1.7, {
  minZ = 27.1,
  maxZ = 29.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Weazel News',
      component_id = 'storage',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-basket-shopping-simple',
      label = 'Supplies',

      uid = 'weazelnews-supply',
    }
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-582.9764, -929.708, 28.08294, 240.0), 0.5, 0.7, {
  minZ = 27.9,
  maxZ = 28.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Weazel News',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-583.708, -939.104, 23.870, 180.0), 1.0, 2.0, {
  minZ = 22.2,
  maxZ = 25.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Weazel News',
      }
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-594.2751, -932.9043, 28.06808, 90.0), 0.6, 2.0, {
  minZ = 28.0,
  maxZ = 28.5,
}, {
  options = {
    {
      event_server = 'core:server:weazel:openDistribution',
      icon = 'fa-regular fa-newspaper',
      label = 'Newspaper Distribution',

      business_name = 'Weazel News',
      component_id = 'distribution',
      component_perm = 'extra_a',
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-593.11, -929.91, 23.87, 0.0), 4.0, 2.2, {
  minZ = 22.87,
  maxZ = 24.27
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'News Person',

      icon = 'fas fa-clock',
      label = 'Clock in - News Person',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.5
})

