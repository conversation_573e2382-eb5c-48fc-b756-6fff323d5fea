--debug<PERSON>olys(true)
AddBoxZone('SFTPawnStor1', vector3(405.21, 324.58, 103.13), 5.0, 0.6, {
  name = 'SFTPawnStor1',
  heading = 340.0,
  minZ = 102.13,
  maxZ = 105.73,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'SFT Pawn',
      component_id = 'storage',

      args = {
        capacity = 350.0,
        named = 'sft_storage1'
      },
    },
  },
  distance = 3.0
})

AddBoxZone('SFTPawnStor2', vector3(413.53, 328.64, 103.13), 0.6, 5.0, {
  name = 'SFTPawnStor2',
  heading = 340.0,
  minZ = 102.13,
  maxZ = 105.73,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'SFT Pawn',
      component_id = 'storage',

      args = {
        capacity = 350.0,
        named = 'sft_storage2'
      },
    },
  },
  distance = 3.0
})

AddBoxZone('SFTStorageBack', vector3(418.37, 335.19, 102.51), 1.4, 0.6, {
  name = 'SFTStorageBack',
  heading = 340.0,
  minZ = 101.51,
  maxZ = 103.51,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'SFT Pawn',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 1500.0,
        named = 'sftbackrooom_storage'
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'SFT Pawn',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Store',

      groups = {
        'SFT Pawn',
      },

      uid = 'pawn-supply',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-cards-blank',
      label = 'TCG Store',

      groups = {
        'SFT Pawn|extra_b',
      },

      uid = 'pawn-tcg',
    },
  },
  distance = 3.0
})

AddBoxZone('SFTSafe', vector3(408.93, 335.9, 103.13), 1, 1, {
  name = 'SFTSafe',
  heading = 345.0,
  minZ = 102.13,
  maxZ = 103.93,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-vault',
      label = 'Safe',

      business_name = 'SFT Pawn',
      component_id = 'storage',
      component_perm = 'management',

      args = {
        capacity = 100.0,
        uid = 'Safe',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('SFTFridge', vector3(416.26, 328.78, 103.13), 0.8, 0.8, {
  name = 'SFTFridge',
  heading = 345.0,
  minZ = 102.13,
  maxZ = 103.93,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'SFT Pawn',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

for k, register_coords in ipairs({
  vector4(405.08, 318.66, 103.13, 5),
  vector4(407.44, 326.03, 103.13, 355),
  vector4(413.71, 326.9, 103.13, 35),
  vector4(413.6, 318.89, 103.13, 330),
}) do
  AddBoxZoneAutoname(register_coords, 0.5, 0.5, {
    minZ = 102.93,
    maxZ = 103.53,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-general:cashRegister',
        icon = 'fa-regular fa-cash-register',
        label = 'Use Register',

        groups = {
          'SFT Pawn'
        },
      },
      {
        event_server = 'core:server:target-backhaul:activatePanicAlarm',
        icon = 'fa-regular fa-triangle-exclamation',
        label = 'Trigger Panic Alarm',

        location_override = 'SFT Pawn Shop, Clinton Ave.',

        groups = {
          'SFT Pawn'
        }
      }
    },
    distance = 3.0
  })
end

for k, counter_coords in ipairs({
  vector4(405.61, 320.0, 103.13, 345),
  vector4(406.89, 324.28, 103.13, 340),
  vector4(415.26, 326.33, 103.13, 255),
  vector4(414.14, 320.35, 103.13, 340),
}) do
  AddBoxZoneAutoname(counter_coords, 2.0, 1.2, {
    minZ = 102.93,
    maxZ = 103.33,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-box',
        label = 'Counter',
        table_coords = counter_coords,
        prefix = 'sft_counter_',
      },
      {
        event_server = 'core:server:target-backhaul:openSFTDisplayCabinet',
        icon = 'fa-regular fa-square',
        label = 'Display Case',
      }
    },
    distance = 3.0
  })
end

AddBoxZone('SFTManagement', vector3(406.83, 332.53, 103.13), 0.8, 0.2, {
  name = 'SFTManagement',
  heading = 350.0,
  minZ = 102.93,
  maxZ = 103.53,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'SFT Pawn',
      component_id = 'management',
    },
  },
  distance = 2.0
})
