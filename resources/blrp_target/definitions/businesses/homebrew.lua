AddBoxZone('HomebrewManagement', vector3(1236.412, -396.9927, 68.86667), 0.5, 0.7, {
  name = 'HomebrewManagement',
  heading = 75.0,
  minZ = 68.8,
  maxZ = 69.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Homebrew Cafe',
      component_id = 'management',
    },
  },
  distance = 2.5
})

local hands_washed = false

RegisterNetEvent('blrp_target:homebrew:washHands', function()
  local clock_in_data = pCore.isClockedIntoTabletBusiness({ 'Homebrew Cafe' })

  if clock_in_data and not clock_in_data.clocked_in then
    exports.blrp_core:me().notify('You are not clocked in (through the tablet)!')
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'homebrew_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('HomebrewSink', vector3(1238.969, -395.6538, 68.3992), 0.8, 0.9, {
  name = 'HomebrewSink',
  heading = 255.0,
  minZ = 68.3,
  maxZ = 69.0,
}, {
  options = {
    {
      event_server = 'core:server:restaurants:homebrew:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        'Homebrew Cafe'
      }
    },
  },
  distance = 2.5
})

AddBoxZone('HomebrewFridge', vector3(1237.653, -400.8748, 68.77719), 0.8, 4.0, {
  name = 'HomebrewFridge',
  heading = 255.0,
  minZ = 67.8,
  maxZ = 69.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'far fa-refrigerator',
      label = 'Fridge',

      business_name = 'Homebrew Cafe',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        named = 'fridge_homebrew_a',
        fake_fridge = true,
      },
    },

    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-utensils',
      label = 'Food Supply',

      uid = 'homebrew-food-supply',

      groups = {
        'Homebrew Cafe'
      },
    },
  },
  distance = 2.5
})

-- Fryer

AddCookingZone('Homebrew Cafe', 'HomebrewFryer', vector3(1235.67, -402.9179, 68.75906), 0.8, 1.0, 345.0, 67.8, 69.0, function()
  return hands_washed
end, {
  { id = 'hb_coco_bread', label = 'Cook Coco Bread' },
  { id = 'food_fried_plantain', label = 'Cook Fried Plantain' },
  { id = 'food_jerk_chickenwings', label = 'Cook Jerk Chicken Wings' },
  { id = 'hb_fries', label = 'Cook Sweet Potato Fries' },
})

-- Grill

AddCookingZone('Homebrew Cafe', 'HomebrewGrill', vector3(1235.4, -400.5318, 68.99712), 0.6, 0.8, 255.0, 68.8, 69.1, function()
  return hands_washed
end, {
  { id = 'hb_fish', label = 'Cook Ackee & Salt Fish' },
  { id = 'food_beef_patty', label = 'Cook Beef Patty' },
  { id = 'hb_jerk_chx', label = 'Cook Jerk Chicken Meal' },
  { id = 'hb_jerk_tofu', label = 'Cook Jerk Tofu Meal' },
  { id = 'hb_goat', label = 'Cook Curried Goat' },
  { id = 'hb_oxtail', label = 'Cook Oxtails' },
  { id = 'hb_pasta', label = 'Cook Rasta Pasta' },
  { id = 'food_vegan_patty', label = 'Cook Vegan Patty' },
  { id = 'hb_cookie', label = 'Make BD Cookie' },
  { id = 'hb_balls', label = 'Make Tamarind Balls' },
})

AddBoxZone('HomebrewDrinks', vector3(1234.599, -401.4877, 69.0), 0.8, 1.1, {
  name = 'HomebrewDrinks',
  heading = 255.0,
  minZ = 69.0,
  maxZ = 70.2,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-martini-glass-citrus',
      label = 'Drink Supply',

      uid = 'homebrew-drink-supply',

      groups = {
        'Homebrew Cafe'
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'far fa-refrigerator',
      label = 'Drink Storage',

      business_name = 'Homebrew Cafe',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        named = 'fridge_homebrew_b',
        fake_fridge = true,
      },
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-martini-glass',
      label = 'Mix Sorrel',

      food_item_id = 'hb_sorrel',
      suppress_anim = true,

      groups = {
        'Homebrew Cafe'
      },

      filter = function()
        return hands_washed
      end
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-martini-glass',
      label = 'Mix Banana Karribean Kiss',

      food_item_id = 'hb_karib_kiss_ban',
      suppress_anim = true,

      groups = {
        'Homebrew Cafe'
      },

      filter = function()
        return hands_washed
      end
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-martini-glass',
      label = 'Mix Blueberry Karribean Kiss',

      food_item_id = 'hb_karib_kiss_blu',
      suppress_anim = true,

      groups = {
        'Homebrew Cafe'
      },

      filter = function()
        return hands_washed
      end
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-martini-glass',
      label = 'Mix Strawberry Karribean Kiss',

      food_item_id = 'hb_karib_kiss_str',
      suppress_anim = true,

      groups = {
        'Homebrew Cafe'
      },

      filter = function()
        return hands_washed
      end
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-martini-glass',
      label = 'Mix Dirty BanANNA',

      food_item_id = 'hb_dirty_ban',
      suppress_anim = true,

      groups = {
        'Homebrew Cafe'
      },

      filter = function()
        return hands_washed
      end
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-martini-glass',
      label = 'Mix Raras GnT',

      food_item_id = 'hb_raras_gt',
      suppress_anim = true,

      groups = {
        'Homebrew Cafe'
      },

      filter = function()
        return hands_washed
      end
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-martini-glass',
      label = 'Mix Rum Punch',

      food_item_id = 'hb_rum_punch',
      suppress_anim = true,

      groups = {
        'Homebrew Cafe'
      },

      filter = function()
        return hands_washed
      end
    },
  },
  distance = 2.5
})

---- Non-food components

AddBoxZone('HomebrewStorageMain', vector3(1221.077, -405.9373, 70.4521), 1.0, 1.5, {
  name = 'HomebrewStorageMain',
  heading = 255.0,
  minZ = 67.9,
  maxZ = 70.45,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Homebrew Cafe',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('HomebrewStorage2', vector3(1222.934, -400.4695, 69.82677), 0.7, 1.0, {
  name = 'HomebrewStorage2',
  heading = 345.0,
  minZ = 67.9,
  maxZ = 69.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Homebrew Cafe',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '2',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('HomebrewCloset1', vector3(1223.033, -404.9949, 69.90062), 1.0, 2.5, {
  name = 'HomebrewCloset1',
  heading = 165.0,
  minZ = 67.8,
  maxZ = 69.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Homebrew Cafe',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('HomebrewRegister', vector3(1236.257, -397.7181, 68.981), 0.5, 0.5, {
  name = 'HomebrewRegister',
  heading = 75.0,
  minZ = 68.9,
  maxZ = 69.3,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        'Homebrew Cafe'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Homebrew Cafe',

      groups = {
        'Homebrew Cafe'
      }
    },
    {
      event_server = 'core:server:badeats:openRestaurantMenuTarget',
      icon = 'far fa-tablet',
      label = 'Badeats Admin',
      restaurant_name = 'Homebrew Cafe',

      groups = {
        'Homebrew Cafe'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('HomebrewTray', vector3(1235.204, -398.9068, 69.09517), 0.8, 2.0, {
  name = 'HomebrewTray',
  heading = 75.0,
  minZ = 69.0,
  maxZ = 69.3,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(1235.204, -398.9068, 69.09517),
    },
    {
      event_server = 'core:server:auction-tickets:buyTicket',
      icon = 'fa-regular fa-ticket',
      label = 'Buy Raffle Ticket ($2000)',
      auction_id = 'bd_homebrew',
    },
  },
  distance = 3.0
})
