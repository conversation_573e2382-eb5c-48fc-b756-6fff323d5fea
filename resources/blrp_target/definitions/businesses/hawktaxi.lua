AddBoxZone('HawkTaxiManagement', vector3(907.0557, -150.0022, 73.95764), 1.2, 0.8, {
  name = 'HawkTaxiManagement',
  heading = 150.0,
  minZ = 73.9,
  maxZ = 74.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Big Hawk Taxi',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZone('HawkTaxiStorage', vector3(899.4481, -169.2525, 74.12313), 1.2, 2.0, {
  name = 'HawkTaxiStorage',
  heading = 237.0,
  minZ = 73.8,
  maxZ = 75.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Big Hawk Taxi',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        named = 'u55510business',
      },
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Hawk Taxi Voucher',

      item_id = 'hawk_taxi_voucher',

      business_name = 'Big Hawk Taxi',
      component_id = 'crafting',
    },
  },
  distance = 3.0
})

AddBoxZone('HawkTaxiCloset', vector3(894.0496, -163.176, 77.95947), 1.0, 2.5, {
  name = 'HawkTaxiCloset',
  heading = 240.0,
  minZ = 76.0,
  maxZ = 78.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Big Hawk Taxi',
      }
    },
  },
  distance = 2.5
})
