-- Management
AddBoxZoneAutoname(vector4(-283.5912, 6142.494, 32.11069, 45.0), 0.5, 0.5, {
  minZ = 32.1,
  maxZ = 32.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Rocky Road Towing',
      component_id = 'management',
    },
  },
  distance = 2.0,
})

-- Clock in desk 1
AddBoxZoneAutoname(vector4(-281.77, 6143.77, 32.718, 134.085), 0.2, 1.5, {
  minZ = 32.3,
  maxZ = 33.4,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Tow Truck',

      icon = 'fas fa-clock',
      label = 'Clock in - Tow Truck',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0,
})
--Managers storage
AddBoxZoneAutoname(vector4(-281.9, 6141.7, 31.2, 135.0), 0.8, 0.6, {
  minZ = 30.9,
  maxZ = 32.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Managers Storage',

      business_name = 'Rocky Road Towing',
      component_id = 'storage',
      component_perm = 'extra_a',

      args = {
        capacity = 500.0,
        uid = 'RRTOW2',
      },
    },
  },
  distance = 2.0,
})



-- Closet / Storage / Shop
AddBoxZoneAutoname(vector4(-281.376, 6140.431, 32.35041, 45.0), 0.5, 2.0, {
  minZ = 31.3,
  maxZ = 33.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Rocky Road Towing',
      component_id = 'storage',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-store',
      label = 'Equipment Store',

      uid = 'rockyroadtowing-supply',

      groups = {
        'Rocky Road Towing',
      }
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-store',
      label = 'Manager Store',

      uid = 'rockyroadtowing-2',

      business_name = 'Rocky Road Towing',
      component_id = 'management',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Rocky Road Towing',
      }
    },
  },
  distance = 2.0,
})

-- Fridge
AddBoxZoneAutoname(vector4(-277.5244, 6142.776, 32.4594, 135.0), 0.8, 0.6, {
  minZ = 31.3,
  maxZ = 32.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Rocky Road Towing',
      component_id = 'fridge',
    },
  },
  distance = 2.0,
})
