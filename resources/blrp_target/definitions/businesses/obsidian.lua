AddBoxZone('ObsidianRegister', vector3(325.8, 180.76, 103.59), 0.5, 0.3, {
  name = 'ObsidianRegister',
  heading = 340,
  minZ = 103.39,
  maxZ = 103.59,
}, {
  options = {
		{
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'Obsidian Tattoos',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('ObsidianComputer', vector3(325.52, 179.78, 103.59), 0.7, 0.5, {
name = 'ObsidianComputer',
heading = 0,
minZ = 103.39,
maxZ = 103.99,
}, {
options = {
	{
		event_server = 'core:server:businesses:custom:targetInteract',
		icon = 'fa-regular fa-computer-classic',
		label = 'Management',

		business_name = 'Obsidian Tattoos',
		component_id = 'management',
	},
},
distance = 1.5
})

AddBoxZone('ObsidianShelvingUpper', vector3(323.39, 178.3, 103.59), 0.6, 2.2, {
  name = 'ObsidianShelving',
  heading = 340,
  minZ = 102.59,
  maxZ = 103.59,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Obsidian Tattoos',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'obsidian_upper'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supply Shop',

      uid = 'obsidian-supplies',

      groups = {
        'Obsidian Tattoos',
      },
    },
  },
  distance = 1.5
})

AddBoxZone('ObsidianShelvingLower', vector3(322.08, 183.32, 98.35), 0.8, 2.8, {
  name = 'ObsidianShelving',
  heading = 340,
  minZ = 97.35,
  maxZ = 98.35,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Obsidian Tattoos',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'obsidian_Lower'
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supply Shop',

      uid = 'obsidian-supplies',

      groups = {
        'Obsidian Tattoos',
      },
    },
  },
  distance = 1.5
})

AddBoxZone('ObsidianMidnightClub', vector3(947.857, -1833.072, 21.036), 4, 3, {
  name = 'ObsidianMidnightClub',
  heading = 270,
  minZ = 20.5,
  maxZ = 22.5,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supply Shop',

      uid = 'obsidian-supplies',

      groups = {
        'Obsidian Tattoos',
      },
    },
  },
  distance = 3
})