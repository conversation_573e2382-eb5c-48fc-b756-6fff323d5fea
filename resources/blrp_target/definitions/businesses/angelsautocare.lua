AddBoxZone('AACClockIn', vector3(992.93, -137.04, 74.07), 1.2, 1, {
  name = 'AACClockIn',
  heading = 330.0,
  minZ = 73.82,
  maxZ = 74.42,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Mechanic',

      icon = 'fa-solid fa-clock',
      label = 'Clock in - Mechanic',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Tow Truck',

      icon = 'fa-solid fa-clock',
      label = 'Clock in - Tow Truck',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fa-solid fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0,
})


AddBoxZone('AACCrafting', vector3(1003.514, -128.3523, 74.28888), 1.2, 1.8, {
  name = 'AACCrafting',
  heading = 240.0,
  minZ = 73.5,
  maxZ = 74.5,
}, {
  options = {
{
  event_server = 'blrp_core:server:item-store:resolveTargetConfig',
  icon = 'fa-regular fa-shelves',
  label = 'Parts Supply',

  uid = 'mechanic-parts',

  groups = {
    'Angels Autocare',
  }
},
{
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Body Fixer',

  item_id = 'mech_body_fixer',

  business_name = 'Angels Autocare',
  component_id = 'crafting',
},
{
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Diagnose Tool',

  item_id = 'mech_diagnose_tool',

  business_name = 'Angels Autocare',
  component_id = 'crafting',
},
{
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Engine Wrench',

  item_id = 'mech_engine_wrench',

  business_name = 'Angels Autocare',
  component_id = 'crafting',
},
{
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Fuel Tools',

  item_id = 'mech_fuel_tools',

  business_name = 'Angels Autocare',
  component_id = 'crafting',
},
{
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Repair Parts (25)',

  item_id = 'repair_parts',

  business_name = 'Angels Autocare',
  component_id = 'crafting',
},
{
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Squeegee',

  item_id = 'mech_squeegee',

  business_name = 'Angels Autocare',
  component_id = 'crafting',
},
},
distance = 2.5
})











AddBoxZone('AODStorageMain', vector3(988.6356, -138.2995, 75.35961), 0.7, 1.1, {
    name = 'AODStorageMain',
    heading = 150.0,
    minZ = 73.0,
    maxZ = 74.8,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',
  
        business_name = 'Angels Autocare',
        component_id = 'storage',
  
        args = {
          capacity = 500.0,
          uid ='1',
        },
  
      },
    },
    distance = 2.5
  })

  AddBoxZone('AACManagement', vector3(988.7592, -135.6507, 73.88107), 0.4, 0.4, {
    name = 'AACManagement',
    heading = 60.0,
    minZ = 73.8,
    maxZ = 74.2,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-computer-classic',
        label = 'Management',
  
        business_name = 'Angels Autocare',
        component_id = 'management',
      },
    },
    distance = 1.5
  })

  AddBoxZone('AACCloset', vector3(992.5771, -134.7172, 73.56224), 1.2, 0.8, {
    name = 'AACCloset',
    heading = 330.0,
    minZ = 73.0,
    maxZ = 75.1,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',
  
        groups = {
          'Angels Autocare',
        }
      },
    },
    distance = 1.5
  })