disableContext(true)

AddBoxZone('RedwoodManagement1', vector3(2902.15, 4411.502, 50.29931), 0.7, 0.7, {
  name = 'RedwoodManagement1',
  heading = 200.0,
  minZ = 50.2,
  maxZ = 50.9,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Redwood Cigarettes',
      component_id = 'management',
    },
		{
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Buy Supplies',

      uid = 'redwoods-supply',
    },
  },
  distance = 2.5
})

AddBoxZone('RedwoodManagement2', vector3(2879.01, 4417.132, 49.18961), 0.7, 0.7, {
  name = 'RedwoodManagement2',
  heading = 200.0,
  minZ = 49.1,
  maxZ = 50.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Redwood Cigarettes',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('RedwoodStorage1', vector3(2899.851, 4412.149, 50.77734), 0.7, 0.7, {
  name = 'RedwoodStorage1',
  heading = 200.0,
  minZ = 49.3,
  maxZ = 50.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Redwood Cigarettes',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('RedwoodStorage2', vector3(2872.204, 4419.339, 49.53841), 1.5, 1.8, {
  name = 'RedwoodStorage2',
  heading = 205.0,
  minZ = 48.0,
  maxZ = 50.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Redwood Cigarettes',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('RedwoodStorage3', vector3(2874.660,4419.389,49.53841), 1.5, 1.8, {
  name = 'RedwoodStorage3',
  heading = 205.0,
  minZ = 48.0,
  maxZ = 50.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Redwood Cigarettes',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '3'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('RedwoodCloset1', vector3(2898.717, 4413.821, 49.81055), 3.0, 5.5, {
  name = 'RedwoodCloset1',
  heading = 205.0,
  minZ = 49.2,
  maxZ = 51.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Redwood Cigarettes',
      }
    },
  },
  distance = 4.0
})

AddBoxZone('RedwoodCloset2', vector3(2895.829, 4420.566, 49.81057), 3.0, 5.5, {
  name = 'RedwoodCloset2',
  heading = 205.0,
  minZ = 49.2,
  maxZ = 51.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Redwood Cigarettes',
      }
    },
  },
  distance = 4.0
})
