
AddBoxZone('MisfitsUpper', vector3(-1192.748, -215.408, 42.616), 1.0, 3.2, {
  name = 'MisfitsUpper',
  heading = 152.0,
  minZ = 42.0,
  maxZ = 44.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Stairway',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MisfitsLower',
      icon = 'far fa-sort-circle-down',
      label = 'Stairs: Ground Floor',
    }
  },
  distance = 3.0
})

AddBoxZone('MisfitsLower', vector3(-1192.748, -215.408, 37.945), 1.0, 3.2, {
  name = 'MisfitsLower',
  heading = 152.0,
  minZ = 37.0,
  maxZ = 39.9,
  notifyText = '<i class="fa-regular fa-eye"></i> Stairway',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MisfitsUpper',
      icon = 'far fa-sort-circle-down',
      label = 'Stairs: Top Floor',
    }
  },
  distance = 3.0
})

for i, pickup in pairs({
  [1] = vector3(1247.352, -3121.833, 6.200302),
  [2] = vector3(1271.978, -3245.895, 6.309997),
  [3] = vector3(1271.939, -3258.596, 6.249413),
  [4] = vector3(1052.376, -3045.047, 6.322173),
}) do
  AddBoxZone('SafrolePickup' .. i, pickup, 1.0, 3.2, {
    name = 'SafrolePickup' .. i,
    heading = 152.0,
    minZ = 6.0,
    maxZ = 6.6,
  }, {
    options = {
      {
        event_server = 'core:drug-system:safrolepickup',
        icon = 'fa-solid fa-box',
        label = 'Search Crate',
        target_position = pickup,

        groups = {
          'Misfitz',
          'LEO',
        }
      }
    },
    distance = 3.0
  })
end

AddDrugLabOption('Misfitz', {
  'u_mdma',
})

AddGangBenchOption('Misfitz', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_misfitz',

  business_name = 'Misfitz',
  component_id = 'craft-stencil_misfitz',
  component_perm = 'crafting',
})
