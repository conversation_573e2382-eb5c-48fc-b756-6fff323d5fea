AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Black Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_01' },

  additional_recipe = {
    ['dye_black'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Blue Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_02' },

  additional_recipe = {
    ['dye_blue'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Green Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_03' },

  additional_recipe = {
    ['dye_green'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Pink Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_04' },

  additional_recipe = {
    ['dye_red'] = 1,
    ['dye_white'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Purple Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_05' },

  additional_recipe = {
    ['dye_purple'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Light Purple Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_06' },

  additional_recipe = {
    ['dye_purple'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Red Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_07' },

  additional_recipe = {
    ['dye_red'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Ultra Blue Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_08' },

  additional_recipe = {
    ['dye_blue'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Urban Masked Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_09' },

  additional_recipe = {
    ['dye_black'] = 1,
    ['dye_white'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft White Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_10' },

  additional_recipe = {
    ['dye_white'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Yellow Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_11' },

  additional_recipe = {
    ['dye_yellow'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Orange Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_13' },

  additional_recipe = {
    ['dye_orange'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft MK Textures Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_14' },

  additional_recipe = {
    ['dye_purple'] = 1,
    ['dye_white'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Valentines 2025 Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_15' },

  additional_recipe = {
    ['dye_red'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft B1 LSIA 2025 Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_16' },

  additional_recipe = {
    ['dye_red'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft The Collective Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_17' },

  additional_recipe = {
    ['dye_red'] = 1,
    ['dye_green'] = 1,
    ['dye_black'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Chihuahua Hotdogs Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_18' },

  additional_recipe = {
    ['dye_red'] = 1,
    ['dye_green'] = 1,
    ['dye_orange'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft GTY Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_19' },

  additional_recipe = {
    ['dye_red'] = 1,
    ['dye_black'] = 1,
    ['dye_purple'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Misfitz Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_20' },

  additional_recipe = {
    ['dye_red'] = 1,
    ['dye_white'] = 1,
    ['dye_black'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Reapers Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_21' },

  additional_recipe = {
    ['dye_blue'] = 1,
    ['dye_black'] = 2,
    ['dye_white'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Rocky Road Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_22' },

  additional_recipe = {
    ['dye_black'] = 2,
    ['dye_white'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Tequi-la-la Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_23' },

  additional_recipe = {
    ['dye_black'] = 1,
    ['dye_yellow'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft The Vault Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_24' },

  additional_recipe = {
    ['dye_black'] = 2,
    ['dye_yellow'] = 1,
    ['dye_red'] = 1,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})

AddGangBenchOption('MK Textures', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Vendetta Bat',

  item_id = 'wbody|WEAPON_BAT',
  gun_components = { 'comp_sk_bat_bl_25' },

  additional_recipe = {
    ['dye_black'] = 1,
    ['dye_white'] = 1,
    ['dye_red'] = 2,
  },

  business_name = 'MK Textures',
  component_id = 'crafting',
  component_perm = 'crafting',
})