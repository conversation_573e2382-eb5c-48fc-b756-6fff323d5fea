AddBoxZone('HayesManagement', vector3(471.5311, -1310.972, 29.2622), 0.4, 0.4, {
  name = 'HayesManagement',
  heading = 200.0,
  minZ = 29.2,
  maxZ = 29.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Hayes Auto',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('HayesClosetStorage', vector3(479.0465, -1326.865, 29.30334), 0.8, 1.8, {
  name = 'HayesClosetStorage',
  heading = 300.0,
  minZ = 27.9,
  maxZ = 30.1,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'hayes-supply',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Hayes Auto',
      component_id = 'storage',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Hayes Auto',
      }
    },
  },
  distance = 2.5
})
