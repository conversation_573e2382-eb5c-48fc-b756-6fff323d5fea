debugPolys(true)

AddBoxZoneAutoname(vector4(979.02, 41.31, 72.65, 3.9), 1.0, 1.4, {
  minZ = 71.7,
  maxZ = 72.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Diamond Casino',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        uid = '1'
      },
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(976.5929, 39.54655, 72.69451, 72.0), 1.1, 1.2, {
  minZ = 71.7,
  maxZ = 72.7,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-id-card',
      label = 'Casino Supply',

      uid = 'casino-employees',

      groups = {
        'Diamond Casino',
      }
    },
  },
  distance = 2.5
})
