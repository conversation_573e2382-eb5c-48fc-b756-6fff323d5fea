AddTargetModel({
  `blrp_casino_table_generic`,
}, {
  options = {
    {
      proxied = 'pCasino.openCardTable',
      icon = 'fa-regular fa-cards',
      label = 'view cards',
      filter = function()
        return exports.blrp_core:me().hasGroup('Diamond Casino') or exports.blrp_core:IsSittingAtCardTable()
      end,
    },
    {
      event_client = 'core:client:casino:trySitAtCardTable',
      icon = 'fa-regular fa-chair-office',
      label = 'Sit Down - Player 1',
      player_number = 1,

      filter = function()
        return not exports.blrp_core:IsSittingAtCardTable()
      end,
    },
    {
      event_client = 'core:client:casino:trySitAtCardTable',
      icon = 'fa-regular fa-chair-office',
      label = 'Sit Down - Player 2',
      player_number = 2,

      filter = function()
        return not exports.blrp_core:IsSittingAtCardTable()
      end,
    },
    {
      event_client = 'core:client:casino:trySitAtCardTable',
      icon = 'fa-regular fa-chair-office',
      label = 'Sit Down - Player 3',
      player_number = 3,

      filter = function()
        return not exports.blrp_core:IsSittingAtCardTable()
      end,
    },
    {
      event_client = 'core:client:casino:trySitAtCardTable',
      icon = 'fa-regular fa-chair-office',
      label = 'Sit Down - Player 4',
      player_number = 4,

      filter = function()
        return not exports.blrp_core:IsSittingAtCardTable()
      end,
    },
    {
      event_client = 'core:client:casino:trySitAtCardTable',
      icon = 'fa-regular fa-chair-office',
      label = 'Sit Down - Player 5',
      player_number = 5,

      filter = function()
        return not exports.blrp_core:IsSittingAtCardTable()
      end,
    },
  },
  distance = 2.5
})
