debugPolys(true)

AddBoxZoneAutoname(vector4(989.3333, 46.37954, 72.80589, 240.0), 3.0, 3.0, {
  minZ = 71.6,
  maxZ = 74.3,
  notifyText = '<i class="fa-regular fa-eye"></i> Cashier',
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-sack-dollar',
      label = 'Exchange Chips',

      uid = 'casino-exchange',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-cards',
      label = 'Purchase Items',

      uid = 'casino-shop',
    },
    {
      event_server = 'core:server:casino:cashInVoucher',
      icon = 'fa-regular fa-hand-holding-dollar',
      label = 'Cash In Voucher',
    },
    {
      event_server = 'core:server:casino:clockIn',
      icon = 'fas fa-clock',
      label = 'Clock in - Floor Staff',

      groups = {
        'Diamond Casino'
      },

      filter = function()
        return not exports.blrp_core:me().hasGroup('Diamond Casino Floor')
      end
    },
    {
      event_server = 'core:server:casino:clockOut',
      icon = 'fas fa-clock',
      label = 'Clock Out - Floor Staff',

      groups = {
        'Diamond Casino Floor'
      }
    },
  },
  distance = 3.0
})

AddBoxZoneAutoname(vector4(989.5837, 58.15, 72.96479, 0.0), 0.3, 2.7, {
  minZ = 71.6,
  maxZ = 74.3,
}, {
  options = {
    {
      event_server = 'core:server:casino:spinLuckyWheel',
      icon = 'fa-regular fa-arrows-spin',
      label = 'Spin Wheel (5000 chips)',

      filter = function()
        local wheel_mode = GlobalState.casino_wheel_mode

        return wheel_mode == 'normal' or wheel_mode == 'lucky'
      end,
    },
    {
      event_server = 'core:server:casino:spinLuckyWheel',
      icon = 'fa-regular fa-arrows-spin',
      label = 'Lucky Spin (10000 chips)',

      golden_spin = true,

      filter = function()
        return GlobalState.casino_wheel_mode == 'lucky'
      end,
    },
  },
  distance = 2.0
})
