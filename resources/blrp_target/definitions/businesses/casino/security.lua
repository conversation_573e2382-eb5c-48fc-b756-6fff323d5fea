debugPolys(true)

AddBoxZoneAutoname(vector4(1012.974, 19.21863, 72.38517, 148.0), 0.9, 0.9, {
  minZ = 71.6,
  maxZ = 73.2,
}, {
  options = {
    {
      proxied = 'pCasino.issueFirearm',
      icon = 'fa-solid fa-gun',
      label = 'Issue Firearm ($8,000)',

      business_name = 'Diamond Casino',
      component_perm = 'extra_b',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Security Store',

      uid = 'casino-security',

      business_name = 'Diamond Casino',
      component_perm = 'extra_b',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-solid fa-box',
      label = 'General Storage',

      business_name = 'Diamond Casino',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 1000.0,
        uid = 'securityGeneral',
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-solid fa-box',
      label = 'Ammunition Storage',

      business_name = 'Diamond Casino',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 25.0,
        max_slots = 1,
        uid = 'securityAmmo',
        whitelisted_items = {
          'ammo_9mm'
        }
      },
    },
  },
  distance = 2.5,
})

AddBoxZoneAutoname(vector4(1017.519, 21.92976, 72.4528, 148.0), 0.5, 0.5, {
  minZ = 72.4,
  maxZ = 72.9,
}, {
  options = {
    {
      proxied = 'pCasino.issueSecurityId',
      icon = 'fa-solid fa-id-card-clip',
      label = 'Issue Security ID',

      business_name = 'Diamond Casino',
      component_perm = 'extra_b',
    },
  },
  distance = 2.5,
})

AddBoxZoneAutoname(vector4(1014.803, 24.25308, 72.41334, 148.0), 4.0, 2.0, {
  minZ = 72.3,
  maxZ = 73.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-triangle-exclamation',
      label = 'Trigger Panic Alarm',

      location_override = 'Diamond Casino & Resort',
      location_specific = 'Security Office',

      groups = {
        'Diamond Casino',
      },
    },
    {
      event_client = 'securitycams:client:openCamset',
      icon = 'fa-solid fa-camera-cctv',
      label = 'CCTV Cameras',
      camset_id = 1,

      groups = {
        'Diamond Casino',
        'LEO',
      }
    },
    {
      proxied = 'pCasino.issueCoa',
      icon = 'fa-solid fa-file-certificate',
      label = 'Print DOJ COA',

      business_name = 'Diamond Casino',
      component_perm = 'extra_b',
    },
  },
  distance = 2.5,
})
