AddBoxZone('PeachFarmFoodPrep', vector3(1534.479, 2229.147, 180.0277), 0.8, 2.5, {
  name = 'PeachFarmFoodPrep',
  heading = 90.0,
  minZ = 180.0,
  maxZ = 180.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-fire-burner',
      label = 'Make Peach Cobbler',

      food_item_id = 'food_peachcobbler',

      groups = {
        'Civilian',
      }
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-fire-burner',
      label = 'Make Peach Pancake',

      food_item_id = 'food_peachpancake',

      groups = {
        'Civilian',
      }
    },
    {
      event_server = 'core:server:restaurants:transformFood',
      icon = 'fa-regular fa-fire-burner',
      label = 'Make Peach Tea',

      food_item_id = 'food_peach_tea',

      groups = {
        'Civilian',
      }
    },
  },
  distance = 2.5,
})
