
AddGangBenchOption('Nice Dreams Ice Cream',{ 
    event_server = 'core:server:restaurant-general:requestRestock',
    icon = 'fa-regular fa-cash-register',
    label = 'Order Stock',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Barfs',
    auto_order = true,
    
    food_item_id = 'food_icecream_barfs',
    
    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Candybox',
    auto_order = true,

    food_item_id = 'food_icecream_candybox',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Creamy Chufty',
    auto_order = true,
      
    food_item_id = 'food_icecream_creamy_chufty',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Serpopsicle',
    auto_order = true,
      
    food_item_id = 'food_serpopsicle',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Chilldo',
    auto_order = true,

    food_item_id = 'food_icecream_chilldo',
    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Cherrypopper',
    auto_order = true,

    food_item_id = 'food_icecream_cherrypopper',
    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Chocolate Starfish',
    auto_order = true,

    food_item_id = 'food_icecream_chocolate_starfish',
    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Icecream Cone',
    auto_order = true,

    food_item_id = 'food_spec_icecreamcone',
    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Metorite',
    auto_order = true,

    food_item_id = 'food_icecream_metorite',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Orange O Tang',
    auto_order = true,

    food_item_id = 'food_icecream_orage_o_tang',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Ps&Qs',
    auto_order = true,

    food_item_id = 'food_icecream_ps&qs',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Sweet Nothings',
    auto_order = true,

    food_item_id = 'food_icecream_sweetnothings',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Uder Milken',
    auto_order = true,

    food_item_id = 'food_icecream_udermilken',

    groups = {
        'Nice Dreams Ice Cream'
    },
})

AddGangBenchOption('Nice Dreams Ice Cream', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-solid fa-ice-cream',
    label = 'Make Earthquakes',
    auto_order = true,

    food_item_id = 'food_icecream_earthquakes',

    groups = {
        'Nice Dreams Ice Cream'
    },
})


