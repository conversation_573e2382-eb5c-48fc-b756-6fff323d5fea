local hands_washed = false

RegisterNetEvent('blrp_target:coolbeans:washHands', function()
  local clock_in_data = pCore.isClockedIntoTabletBusiness({ 'Cool Beans' })

  if clock_in_data and not clock_in_data.clocked_in and
  (
    not GlobalState.is_dev or
    not exports.blrp_core:me().request('[DEV] Override Clock-in to wash hands?')
  )
  then
    exports.blrp_core:me().notify('You are not clocked in (through the tablet)!')
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'coolbeans_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('CoolbeansSink', vector3(-1198.45, -1146.032, 7.74293), 0.8, 0.9, {
  name = 'CoolbeansSink',
  heading = 200.0,
  minZ = 7.7,
  maxZ = 8.2,
}, {
  options = {
    {
      event_client = 'blrp_target:coolbeans:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        'Cool Beans'
      }
    },
  },
  distance = 2.5
})

AddBoxZone('CoolbeansSink2', vector3(-1200.076, -1139.584, 7.742926), 0.8, 0.9, {
  name = 'CoolbeansSink2',
  heading = 200.0,
  minZ = 7.7,
  maxZ = 8.2,
}, {
  options = {
    {
      event_client = 'blrp_target:coolbeans:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        'Cool Beans'
      }
    },
  },
  distance = 2.5
})

---

AddBoxZone('CoolbeansRegister', vector3(-1201.555, -1137.05, 8.128263), 0.7, 0.55, {
  name = 'CoolbeansRegister',
  heading = 20.0,
  minZ = 7.8,
  maxZ = 8.3,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        'Cool Beans'
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'Cool Beans'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Cool Beans Cafe',

      groups = {
        'Cool Beans'
      }
    },
    {
      event_server = 'core:server:badeats:openRestaurantMenuTarget',
      icon = 'far fa-tablet',
      label = 'Badeats Admin',
      restaurant_name = 'Cool Beans',

      groups = {
        'Cool Beans'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('CoolbeansManagement', vector3(-1183.846, -1138.065, 7.631453), 0.5, 0.7, {
  name = 'CoolbeansManagement',
  heading = 345.0,
  minZ = 7.63,
  maxZ = 8.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Cool Beans',
      component_id = 'management',
    },
  },
  distance = 3.0
})

AddBoxZone('CoolbeansFridge', vector3(-1196.485, -1141.579, 7.950), 1.0, 1.0, {
  name = 'CoolbeansFridge',
  heading = 290.0,
  minZ = 6.9,
  maxZ = 8.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'far fa-refrigerator',
      label = 'Fridge',

      business_name = 'Cool Beans',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        named = 'fridge_coolbeans_a',
        fake_fridge = true,
      },
    },
  },
  distance = 2.5
})
AddBoxZone('Coolbeans', vector3(-1186.526, -1141.206, 7.5), 0.8, 0.8, {
  name = 'Coolbeans',
  heading = 30.0,
  minZ = 7.2,
  maxZ = 8.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Cool Beans',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '1'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('CoolbeansFridge2', vector3(-1202.507, -1137.367, 8.053492), 1.1, 1.5, {
  name = 'CoolbeansFridge2',
  heading = 20.0,
  minZ = 6.9,
  maxZ = 8.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'far fa-refrigerator',
      label = 'Fridge',

      business_name = 'Cool Beans',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        named = 'fridge_coolbeans_b',
        fake_fridge = true,
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Supply Shop',

      uid = 'coolbeans-supply',
      business_name = 'Cool Beans',
      component_id = 'management',

    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'More Cups',

      uid = 'coolbeans-supply2',
      business_name = 'Cool Beans',
      component_id = 'management',

    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Extra Cups',

      uid = 'coolbeans-supply3',
      business_name = 'Cool Beans',
      component_id = 'management',

    },
  },
  distance = 2.5
})
AddBoxZone('BeansTray', vector3(-1199.65, -1136.31, 7.83), 0.6, 0.5, {
  name = 'BeansTray',
  heading = 250.0,
  minZ= 7.83,
  maxZ= 8.03,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-1199.65, -1136.31, 7.83),
    },
  },
  distance = 2.5
})
AddBoxZone('BeansTray2', vector3(-1202.15, -1137.3, 7.83), 0.6, 0.5, {
  name = 'BeansTray2',
  heading = 250.0,
  minZ= 8.03,
  maxZ= 8.23,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-1202.15, -1137.3, 7.83),
    },
  },
  distance = 2.5
})

local mix_options = {
  {
    event_client = 'event:stub',
    icon = 'fa-regular fa-sink',
    label = 'Go wash your hands!',

    groups = {
      'Cool Beans',
    },

    filter = function()
      return not hands_washed
    end
  }
}

local item_idx = 2

for _, mix_option in pairs({
  { id = 'cb_caramel_apple', label = 'Make Caramel Apple Spice' },
  { id = 'cb_espresso_frap', label = 'Make Espresso Frappuccino' },
  { id = 'cb_honey_tea_latte', label = 'Make Honey Tea Latte' },
  { id = 'cb_house', label = 'Mix House Blend' },
  { id = 'food_latte', label = 'Mix Latte'},
  { id = 'cb_iced_mocha', label = 'Mix Iced Mocha Cappuccino' },
  --{ id = 'xmr_coolbean_d', label = 'Mix Sugar Cookie Latte' },
}) do
  mix_options[item_idx] = {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = mix_option.label,

    food_item_id = mix_option.id,
    auto_order = true,
    groups = {
      'Cool Beans',
    },

    filter = function()
      return hands_washed
    end
  }

  item_idx = item_idx + 1
end

AddBoxZone('CoolBeansDrinkMix', vector3(-1198.691, -1139.04, 7.742926), 0.8, 1.0, {
  name = 'CoolBeansDrinkMix',
  heading = 200.0,
  minZ = 7.7,
  maxZ = 8.5,
}, {
  options = mix_options,
  distance = 2.5,
})

local cook_options = {
  {
    event_client = 'event:stub',
    icon = 'fa-regular fa-sink',
    label = 'Go wash your hands!',

    groups = {
      'Cool Beans'
    },

    filter = function()
      return not hands_washed
    end
  }
}

local item_idx = 2

for _, food_option in pairs({
  { id = 'cb_croissant', label = 'Make Bacon Egg Cheese Croissant' },
  { id = 'cb_panini', label = 'Make Chicken Bacon Panini' },
  { id = 'cb_lox_bagel', label = 'Make Lox Bagel' },
  { id = 'cb_bb_muffin', label = 'Make Blueberry Muffin' },
  { id = 'cb_cupcake', label = 'Make Cupcake' },
  { id = 'cb_v_cupcake', label = 'Make Valentines Cupcake'},
  { id = 'cb_classic_cakep', label = 'Make Classic Cake Pop' },
  { id = 'cb_sb_choc_cakep', label = 'Make Strawberry Chocolate Cake Pop' },
  --{ id = 'xmr_coolbean_f', label = 'Make Hot Coco Cupcake' },
  --{ id = 'food_ea25_cb', label = 'Make Bunny Cupcake' },
}) do
  cook_options[item_idx] = {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = food_option.label,

    food_item_id = food_option.id,
    auto_order = true,
    groups = {
      'Cool Beans'
    },

    filter = function()
      return hands_washed
    end
  }

  item_idx = item_idx + 1
end

AddBoxZone('CoolBeansFoodPrep', vector3(-1200.073, -1140.443, 7.764608), 0.6, 1.2, {
  name = 'CoolBeansFoodPrep',
  heading = 20.0,
  minZ = 7.7,
  maxZ = 8.1,
}, {
  options = cook_options,
  distance = 2.5,
})
