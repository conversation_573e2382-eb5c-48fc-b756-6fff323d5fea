-- Business components

AddBoxZone('YellowJackCloset', vector3(1951.444, 3834.144, 34.576), 0.7, 1.1, {
  name = 'YellowJackCloset',
  heading = 29.0,
  minZ = 34.5,
  maxZ = 36.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Yellow Jack',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('YellowJackStorageMain', vector3(1946.0, 3836.647, 35.23283), 0.7, 0.9, {
  name = 'YellowJackStorageMain',
  heading = 209.0,
  minZ = 34.5,
  maxZ = 36.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Yellow Jack',
      component_id = 'storage',

      args = {
        capacity = 1500,
        named = 'u77826business',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('YellowJackCrafting', vector3(1963.362, 3818.271, 31.38558), 0.9, 1.5, {
  name = 'YellowJackCrafting',
  heading = 120.0,
  minZ = 31.3,
  maxZ = 33.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-toolbox',
      label = 'Crafting',

      business_name = 'Yellow Jack',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

-- Trevor's Trailer

AddBoxZone('YellowJackTrlrCloset', vector3(1968.844, 3814.126, 33.91789), 0.7, 1.6, {
  name = 'YellowJackTrlrCloset',
  heading = 120.0,
  minZ = 32.4,
  maxZ = 35.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      business_name = 'Yellow Jack',
      component_id = 'custom99',
      component_perm = 'extra_b',
    },
  },
  distance = 2.5
})

AddBoxZone('YellowJackTrlrFridge', vector3(1972.54, 3820.231, 33.42612), 0.9, 1.0, {
  name = 'YellowJackTrlrFridge',
  heading = 210.0,
  minZ = 32.4,
  maxZ = 34.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Yellow Jack',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 350.0,
        named = 'yj_trlr_fridge',
        fake_fridge = true,
      },
    },
  },
  distance = 2.5
})

AddBoxZone('YellowJackTrlrStorage', vector3(1978.823, 3820.0, 33.65697), 0.4, 1.7, {
  name = 'YellowJackTrlrStorage',
  heading = 300.0,
  minZ = 32.4,
  maxZ = 33.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Yellow Jack',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 1500.0,
        named = 'yj_trlr_storage',
      },
    },
  },
  distance = 2.5
})


-- Bar components

local hands_washed = false

RegisterNetEvent('blrp_target:yellowjack:washHands', function()
  local clock_in_data = pCore.isClockedIntoTabletBusiness({ 'Yellow Jack' })

  if clock_in_data and not clock_in_data.clocked_in and
  (
    not GlobalState.is_dev or
    not exports.blrp_core:me().request('[DEV] Override Clock-in to wash hands?')
  )
  then
    exports.blrp_core:me().notify('You are not clocked in (through the tablet)!')
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'yellowjack_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('YellowJackSink', vector3(1942.671, 3842.647, 31.185), 0.8, 1.4, {
  name = 'YellowJackSink',
  heading = 118.0,
  minZ = 31.185,
  maxZ = 32.3,
}, {
  options = {
    {
      event_client = 'blrp_target:yellowjack:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        'Yellow Jack'
      }
    },
  },
  distance = 2.5
})

AddBoxZone('YellowJackStorage1', vector3(1944.867, 3838.876, 32.15113), 0.8, 0.8, {
  name = 'YellowJackStorage1',
  heading = 118.0,
  minZ = 31.2,
  maxZ = 33.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Yellow Jack',
      component_id = 'storage',

      args = {
        capacity = 350.0,
        named = 'yj_lower_fridge_1',
        fake_fridge = true,
      },
    },
  },
  distance = 3.0
})

AddBoxZone('YellowJackStorage2', vector3(1943.844, 3844.624, 31.22454), 0.8, 1.2, {
  name = 'YellowJackStorage2',
  heading = 208.0,
  minZ = 31.224,
  maxZ = 32.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Yellow Jack',
      component_id = 'storage',

      args = {
        capacity = 350.0,
        named = 'yj_lower_fridge_3',
        fake_fridge = true,
      },
    },
  },
  distance = 3.0
})

AddBoxZone('YellowJackStorage3', vector3(1944.924, 3845.223, 31.22454), 0.8, 1.2, {
  name = 'YellowJackStorage3',
  heading = 208.0,
  minZ = 31.224,
  maxZ = 32.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Yellow Jack',
      component_id = 'storage',

      args = {
        capacity = 350.0,
        named = 'yj_lower_fridge_2',
        fake_fridge = true,
      },
    },
  },
  distance = 3.0
})

AddBoxZone('YellowJackRegister', vector3(1947.98, 3844.094, 32.30494), 0.45, 0.4, {
  name = 'YellowJackRegister',
  heading = 208.0,
  minZ = 32.3,
  maxZ = 32.6,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        'Yellow Jack'
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'Yellow Jack'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Yellow Jack Inn',

      groups = {
        'Yellow Jack'
      }
    },
    {
      event_server = 'core:server:badeats:openRestaurantMenuTarget',
      icon = 'far fa-tablet',
      label = 'Badeats Admin',
      restaurant_name = 'Yellow Jack Inn',

      groups = {
        'Yellow Jack'
      },
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Yellow Jack',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('YellowJackFoodSupply', vector3(1942.75, 3843.93, 32.18), 0.8, 1.4, {
  name = 'YellowJackSupply',
  heading = 30.0,
  minZ=32.58,
  maxZ=33.78,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-utensils',
      label = 'Yellow Jack Food Supply',

      uid = 'yellowjack-supply',
    },
  },
  distance = 2.5,
})

local cook_options = {
  {
    event_client = 'event:stub',
    icon = 'fa-regular fa-sink',
    label = 'Go wash your hands!',

    groups = {
      'Yellow Jack'
    },

    filter = function()
      return not hands_washed
    end
  }
}

local item_idx = 2

for _, food_option in pairs({
  { id = 'food_avocadotoast', label = 'Cook Avocado Toast' },
  { id = 'food_spec_baconneggs', label = 'Cook Bacon and Eggs' },
  { id = 'food_bananapancake', label = 'Cook Banana Pancake' },
  { id = 'food_blueberrypancake', label = 'Cook Blueberry Pancake' },
  { id = 'food_breakfastburrito', label = 'Cook Breakfast Burrito' },
  { id = 'food_cherrypie', label = 'Cook Cherry Pie' },
  { id = 'food_chickenandwaffles', label = 'Cook Chicken and Waffles' },
  { id = 'food_chocolatechippancake', label = 'Cook Chocolate Chip Pancake' },
  { id = 'food_englishbreakfast', label = 'Cook English Breakfast' },
  { id = 'food_fishnchips', label = 'Cook Fish N Chips' },
  { id = 'food_frenchtoast', label = 'Cook French Toast' },
  { id = 'food_mozzarellasticks', label = 'Cook Mozzarella Sticks' },
  { id = 'food_mildwings', label = 'Cook Mild Wings' },
  { id = 'food_onionrings', label = 'Cook Onion Rings' },
  { id = 'food_peachpancake', label = 'Cook Peach Pancake' },
  { id = 'food_potatoskins', label = 'Cook Potato Skins' },
  { id = 'food_spicywings', label = 'Cook Spicy Wings' },
  { id = 'food_steakandeggs', label = 'Cook Steak and Eggs' },
  { id = 'food_tofuscramble_b', label = 'Cook Tofu Scramble Breakfast' },
  { id = 'food_whiskeypancake', label = 'Cook Whiskey Pancake' },
  --{ id = 'xmr_yj_f', label = 'Cook Hot Cocoa Pancakes' },
  --{ id = 'food_ea25_yj', label = 'Cook Deviled Eggs' },
}) do
  cook_options[item_idx] = {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = food_option.label,

    food_item_id = food_option.id,
    auto_order = true,
    groups = {
      'Yellow Jack'
    },

    filter = function()
      return hands_washed
    end
  }

  item_idx = item_idx + 1
end

local toaster_options = {
  {
    event_client = 'event:stub',
    icon = 'fa-regular fa-sink',
    label = 'Go wash your hands!',

    groups = {
      'Yellow Jack'
    },

    filter = function()
      return not hands_washed
    end
  }
}

local item_idx = 2

for _, toast_option in pairs({
  { id = 'food_toast', label = 'Cook Toast' },
  { id = 'food_waffle', label = 'Cook Waffle' },
}) do
  toaster_options[item_idx] = {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-bread-slice',
    label = toast_option.label,

    food_item_id = toast_option.id,
    auto_order = true,
    groups = {
      'Yellow Jack'
    },

    filter = function()
      return hands_washed
    end
  }

  item_idx = item_idx + 1
end

AddBoxZone('YellowJackGrill', vector3(1945.439, 3840.939, 32.08035), 0.4, 0.7, {
  name = 'YellowJackGrill',
  heading = 298.0,
  minZ = 32.0,
  maxZ = 32.4,
}, {
  options = cook_options,
  distance = 2.5,
})

AddBoxZone('YellowJackToaster', vector3(1943.09, 3844.05, 32.18), 0.6, 0.4, {
  name = 'YellowJackToaster',
  heading = 30.0,
  minZ=32.18,
  maxZ=32.58,
}, {
  options = toaster_options,
  distance = 2.5,
})

local mix_options = {
  {
    event_client = 'event:stub',
    icon = 'fa-regular fa-sink',
    label = 'Go wash your hands!',

    groups = {
      'Yellow Jack'
    },

    filter = function()
      return not hands_washed
    end
  }
}

local item_idx = 2

for _, mix_option in pairs({
  { id = 'food_chocolatemilk', label = 'Mix Choccy Milk' },
  { id = 'food_expresso', label = 'Mix Espresso' },
  { id = 'food_jaegerbomb', label = 'Mix Jager Bomb' },
  { id = 'yj_coffee', label = 'Brew YJ Coffee' },
  { id = 'food_peach_tea', label = 'Mix Peach Tea' },
  { id = 'drink_sandytea', label = 'Mix Sandy Sweet Tea' },
  { id = 'food_yjingoke', label = 'Mix YJ N\' Coke' },
  { id = 'food_yjoj', label = "Mix Fresh Squeezed OJ"},
  --{ id = 'xmr_yj_d', label = "Brew Irish Coffee" },
}) do
  mix_options[item_idx] = {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = mix_option.label,

    food_item_id = mix_option.id,
    auto_order = true,
    groups = {
      'Yellow Jack'
    },

    filter = function()
      return true
    end
  }

  item_idx = item_idx + 1
end

AddBoxZone('YellowJackDrinkMix', vector3(1946.443, 3843.676, 31.62367), 1.0, 1.4, {
  name = 'YellowJackDrinkMix',
  heading = 208.0,
  minZ = 31.2,
  maxZ = 32.2,
}, {
  options = mix_options,
  distance = 2.5,
})

AddBoxZone('YellowJackFoodTray', vector3(1946.781, 3843.351, 32.247), 0.8, 2.4, {
  name = 'YellowJackFoodTray',
  heading = 209.0,
  minZ = 32.2,
  maxZ = 32.4,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(1946.781, 3843.351, 32.247),
    },
  },
  distance = 2.5,
})
