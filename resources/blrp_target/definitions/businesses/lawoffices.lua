AddBoxZone('LawOfficesManagement', vector3(-230.4166, -770.4696, 34.06651), 0.5, 0.5, {
  name = 'LawOfficesManagement',
  heading = 105.0,
  minZ = 34.0,
  maxZ = 34.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Los Santos Law Offices',
      component_id = 'management',
    },
  },
  distance = 2.0
})

local LawClosets = {
  vector3(-227.2712, -783.9314, 34.20903),
  vector3(-214.447, -787.4627, 33.64953),
  vector3(-210.7463, -777.2951, 33.631),
}

for closet, position in pairs(LawClosets) do
  AddBoxZone('LawOfficesStorage'..closet, position, 0.4, 1.6, {
    name = 'LawOfficesStorage'..closet,
    heading = 15.0,
    minZ = position.z,
    maxZ = position.z+1.0,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openChest',
        icon = 'far fa-box',
        label = 'Locker',

        chest_name = 'locker_storage_char_id_lawoffice',
        chest_radius = 3.0,
        chest_weight = 150.0,
        chest_permission = 'lawoffice.storage',

        groups = {
          'Lawyer',
        },
      },
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',

        groups = {
          'Los Santos Law Offices',
        }
      },
    },
    distance = 3.0
  })

end

AddBoxZone('LawOfficesCanteen', vector3(-212.6592, -783.1976, 34.2584), 0.8, 1.2, {
  name = 'LawOfficesCanteen',
  heading = 300.0,
  minZ = 34.1584,
  maxZ = 34.3584,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-burger-soda',
      label = 'Naughty Shelf',

      uid = 'doj-canteen-court',

      groups = {
        'DOJ',
        'Lawyer',
        'Los Santos Law Offices',
      },
    },
  },
  distance = 2.0
})