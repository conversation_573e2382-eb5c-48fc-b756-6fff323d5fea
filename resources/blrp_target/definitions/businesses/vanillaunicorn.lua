AddBoxZone('VUStorageMain', vector3(109.36, -1303.9, 30.3275), 0.6, 2.4, {
  name = 'VUStorageMain',
  heading = 300.0,
  minZ = 27.9,
  maxZ = 28.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vanilla Unicorn',
      component_id = 'storage',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Vanilla Unicorn',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('VUStorage2', vector3(93.61625, -1290.379, 29.21244), 0.8, 0.8, {
  name = 'VUStorage2',
  heading = 30.0,
  minZ = 28.2,
  maxZ = 29.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vanilla Unicorn',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('VUStorage4', vector3(130.5, -1282.026, 28.79696), 0.8, 0.8, {
  name = 'VUStorage4',
  heading = 30.0,
  minZ = 28.2,
  maxZ = 29.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vanilla Unicorn',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        uid = '4'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('VUStorage5', vector3(136.1, -1289.55, 29.27), 0.6, 1.6, {
  name = 'VUStorage5',
  heading = 30.0,
  minZ = 28.27,
  maxZ = 29.27,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vanilla Unicorn',
      component_id = 'storage',
      component_perm = 'management',

      args = {
        capacity = 25.0,
        uid = '5'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('VUManagement', vector3(96.17, -1293.11, 29.26), 0.4, 0.8, {
  name = 'VUManagement',
  heading = 300.0,
  minZ = 29.06,
  maxZ = 29.66,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Vanilla Unicorn',
      component_id = 'management',
    },
  },
  distance = 3.5
})

AddBoxZone('VUHairDresser', vector3(108.8239, -1307.141, 29.71225), 1.6, 5.0, {
  name = 'VUHairDresser',
  heading = 210.0,
  minZ = 28.9,
  maxZ = 31.0,
}, {
  options = {
    {
      event_client = 'blrp_character:openBarbershop',
      icon = 'fa-regular fa-face-awesome',
      label = 'Hair and Makeup',
      cloakroom_name = 'clothingshop',
    },
  },
  distance = 3.0,
})

-- Bar stuff

local hands_washed = false

RegisterNetEvent('blrp_target:vanillaunicorn:washHands', function()
  exports['mythic_progbar']:Progress({
    name = 'vanillaunicorn_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('VUSink', vector3(130.4187, -1286.362, 28.26709), 1.0, 1.4, {
  name = 'VUSink',
  heading = 120.0,
  minZ = 28.2,
  maxZ = 29.5,
}, {
  options = {
    {
      event_client = 'blrp_target:vanillaunicorn:washHands',
      icon = 'fa-regular fa-sink',
      label = 'Wash Hands',

      groups = {
        'Vanilla Unicorn'
      }
    },
  },
  distance = 2.5
})

AddBoxZone('VURegister', vector3(129.1596, -1284.944, 29.40989), 0.5, 0.45, {
  name = 'VURegister',
  heading = 120.0,
  minZ = 29.3,
  maxZ = 29.8,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        'Vanilla Unicorn',
      },

      filter = function()
        return hands_washed
      end,
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'Vanilla Unicorn'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'fa-regular fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Vanilla Unicorn',

      groups = {
        'Vanilla Unicorn',
      }
    }
  },
  distance = 2.5
})

AddBoxZone('VUStorage3', vector3(132.7345, -1285.379, 28.2579), 0.8, 1.4, {
  name = 'VUStorage3',
  heading = 300.0,
  minZ = 28.2,
  maxZ = 29.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Vanilla Unicorn',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        named = 'vu_right_fridge_1',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-utensils',
      label = 'Bar Supply',

      uid = 'vanillaunicorn-supply',

      groups = {
        'Vanilla Unicorn',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('VUBarCounter', vector3(127.85, -1283.0, 29.280), 1.0, 4.0, {
  name = 'VUBarCounter',
  heading = 121.0,
  minZ = 29.2,
  maxZ = 29.7,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Table',

      table_coords = vector3(127.789, -1282.861, 29.280),
    },
  },
  distance = 2.5
})

AddCookingZone('Vanilla Unicorn', 'VUDrinks1', vector3(130.5413, -1281.548, 29.35699), 0.8, 1.2, 121.0, 29.0, 29.7, function()
  return true -- return hands_washed
end, {
  { id = 'drink_vu_kitty', label = 'Mix Creamy Kitty Shake' },
  { id = 'drink_vu_sprunk', label = 'Mix Sprunk Me Daddy' },
  --{ id = 'drink_vu_pkiss', label = 'Mix Peach Kiss' },
  --{ id = 'drink_vu_punch', label = 'Mix Bum Bum Punch' },
  { id = 'drink_frenchkiss', label = 'Mix French Kiss'},
  { id = 'drink_walkofshame', label = 'Mix Walk Of Shame'},
  { id = 'drink_popmycherry', label = 'Mix Pop My Cherry'},
  { id = 'drink_pinkpanties', label = 'Mix Pink Panties'},
  { id = 'food_rustyscrew', label = 'Mix Rusty Screw' },
  { id = 'food_lovepotion', label = 'Mix Love Potion' },
  { id = 'food_wetdream', label = 'Mix Wet Dream' },
  --{ id = 'food_dirtypalmer', label = 'Mix Dirty Palmer' },
  { id = 'food_rimjobshot', label = 'Mix Rimjob Shot' },
  { id = 'food_creamsicle', label = 'Mix Creamsicle' },
 --{ id = 'food_eyecandy', label = 'Mix Eye Candy' },
  { id = 'food_pantydropper', label = 'Mix Panty Dropper' },
  { id = 'food_unicornshot', label = 'Mix Unicorn Shot' },
  { id = 'food_palmer', label = 'Mix Palmer' },
  { id = 'food_sweet_tea', label = 'Mix Sweet Tea'},
  { id = 'strawberry_milk', label = 'Make Chocolate Strawberry'},
  { id = 'strawberry_white', label = 'Make White Choc Strawberry'},
  { id = 'food_cinnamonroll', label = 'Make Cinnomon Roll'},
  { id = 'food_popcorn', label = 'Make Popcorn'},
},true)

AddTargetModel({
  `prop_strip_pole_01`,
}, {
  options = {
    {
      event_client = 'core:client:vu:stripDance',
      icon = 'fas fa-arrow-up',
      label = 'Dance 1',

      dance_id = 1,

      groups = {
        'Vanilla Unicorn',
      },
    },
    {
      event_client = 'core:client:vu:stripDance',
      icon = 'fas fa-arrow-up',
      label = 'Dance 2',

      dance_id = 2,

      groups = {
        'Vanilla Unicorn',
      },
    },
    {
      event_client = 'core:client:vu:stripDance',
      icon = 'fas fa-arrow-up',
      label = 'Dance 3',

      dance_id = 3,

      groups = {
        'Vanilla Unicorn',
      },
    },
  },
  distance = 3.0
})

for _, vu_tables in pairs({
  {name="vutable1", position=vector3(125.828, -1286.817, 29.27478)},
  {name="vutable2", position=vector3(124.2021, -1284.015, 29.27478)},
  {name="vutable3", position=vector3(121.9931, -1286.981, 27.67864)},
  {name="vutable4", position=vector3(120.9484, -1285.173, 27.67864)},
  {name="vutable5", position=vector3(116.5017, -1291.402, 27.67864)},
  {name="vutable6", position=vector3(119.9455, -1296.761, 28.68759)},
  {name="vutable7", position=vector3(123.4028, -1294.869, 28.68759)},
}) do
  AddBoxZone(vu_tables.name, vu_tables.position, 0.8, 0.8, {
    heading=120,
    minZ = vu_tables.position.z - 0.2,
    maxZ = vu_tables.position.z + 0.2,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-cocktail',
        label = 'Table',
        table_coords = vu_tables.position,
      },
    },
    distance = 2.5
  })
end

for _, podium_coords in ipairs({
  vector3(117.5032, -1283.025, 27.88403),
  vector3(112.7809, -1283.09, 27.88288),
}) do
  AddBoxZone('VUCashPodium' .. _, podium_coords, 1.3, 1.3, {
    name = 'VUCashPodium' .. _,
    heading=90,
    minZ=27.5,
    maxZ=27.9,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openVUCashPodium',
        icon = 'fas fa-dollar-sign',
        label = 'Podium',
      },
    },
    distance = 2.5
  })
end

AddBoxZone('VUatm1', vector3(113.58, -1293.15, 28.26), 0.5, 0.7, {
  name = 'VUatm1',
  heading = 30.0,
  minZ = 27.66,
  maxZ = 28.86,
}, {
  options = {
    {
      event_server = 'blrp_banking:server:open',
      icon = 'far fa-dollar-sign',
      label = 'Access ATM',
      access_type = 'atm',
    },
    {
      event_server = 'blrp_core:atm-hack:server:insertBankCard',
      icon = 'far fa-credit-card',
      label = 'Insert Bank Card',

      filter = function(_, ray_intersect_coords)
        local has_item, quantity = exports.blrp_core:me().hasItem('bank_card_a') or exports.blrp_core:me().hasItem('bank_card_b')
        return has_item
      end
    },
  },
  distance = 2.5,
  check_upright = false,
  check_on_foot = true
})

AddBoxZone('VUatm2', vector3(131.38, -1295.87, 29.27), 0.5, 0.7, {
  name = 'VUatm2',
  heading = 300.0,
  minZ = 28.87,
  maxZ = 29.87,
}, {
  options = {
    {
      event_server = 'blrp_banking:server:open',
      icon = 'far fa-dollar-sign',
      label = 'Access ATM',
      access_type = 'atm',
    },
    {
      event_server = 'blrp_core:atm-hack:server:insertBankCard',
      icon = 'far fa-credit-card',
      label = 'Insert Bank Card',

      filter = function(_, ray_intersect_coords)
        local has_item, quantity = exports.blrp_core:me().hasItem('bank_card_a') or exports.blrp_core:me().hasItem('bank_card_b')
        return has_item
      end
    },
  },
  distance = 2.5,
  check_upright = false,
  check_on_foot = true
})

AddBoxZone('VUatm3', vector3(130.57, -1296.35, 29.27), 0.5, 0.7, {
  name = 'VUatm3',
  heading = 300.0,
  minZ = 28.87,
  maxZ = 29.87,
}, {
  options = {
    {
      event_server = 'blrp_banking:server:open',
      icon = 'far fa-dollar-sign',
      label = 'Access ATM',
      access_type = 'atm',
    },
    {
      event_server = 'blrp_core:atm-hack:server:insertBankCard',
      icon = 'far fa-credit-card',
      label = 'Insert Bank Card',

      filter = function(_, ray_intersect_coords)
        local has_item, quantity = exports.blrp_core:me().hasItem('bank_card_a') or exports.blrp_core:me().hasItem('bank_card_b')
        return has_item
      end
    },
  },
  distance = 2.5,
  check_upright = false,
  check_on_foot = true
})