AddBoxZone('LSCManagement', vector3(-339.9617, -156.5112, 44.48635), 0.5, 0.7, {
  name = 'LSCManagement',
  heading = 190.0,
  minZ = 44.4,
  maxZ = 45.1,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Los Santos Customs',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('LSCStorage1', vector3(-337.1018, -160.1366, 44.94792), 1.0, 2.0, {
  name = 'LSCStorage1',
  heading = 355.0,
  minZ = 43.0,
  maxZ = 46.0,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'lsc-supply',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Los Santos Customs',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('LSCStorage2', vector3(-345.1473, -156.2118, 44.52151), 1.0, 1.0, {
  name = 'LSCStorage2',
  heading = 30.0,
  minZ = 43.5,
  maxZ = 44.55,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Los Santos Customs',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})


AddBoxZone('LSCCrafting', vector3(-348.8562, -173.373, 38.81307), 1.2, 2.1, {
  name = 'LSCCrafting',
  heading = 25.0,
  minZ = 38.80,
  maxZ = 39.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Air Freshener',

      item_id = 'Mosleyfresh',

      business_name = 'Los Santos Customs',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Racewars 3 Ticket',

      item_id = 'ticket_rw_30',

      business_name = 'Los Santos Customs',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

AddBoxZone('LSCCloset1', vector3(-341.4804, -162.8231, 44.14521), 1.0, 1.0, {
  name = 'LSCCloset1',
  heading = 180.0,
  minZ = 43.8,
  maxZ = 45.8,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Los Santos Customs',
      }
    },
  },
  distance = 2.5
})
