AddBoxZone('TequiLaLaStorageMain', vector3(-568.5711, 290.7736, 79.29344), 0.6, 1.5, {
  name = 'TequiLaLaStorageMain',
  heading = 175.0,
  minZ = 78.2,
  maxZ = 80.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'TequiLaLa',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('TequiLaLaStorageBarLower', vector3(-563.0864, 286.1996, 81.1764), 1.4, 0.8, {
  name = 'TequiLaLaStorageBarLower',
  heading = 175.0,
  minZ = 81.1,
  maxZ = 82.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'TequiLaLa',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        uid = 'BarLower',
        fake_fridge = true
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-utensils',
      label = 'Food Supply',

      uid = 'tequilala-supply',

      groups = {
        'TequiLaLa'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('TequiLaLaStorageBarUpper', vector3(-566.2753, 285.1314, 84.3764), 1.4, 0.8, {
  name = 'TequiLaLaStorageBarUpper',
  heading = 175.0,
  minZ = 84.3,
  maxZ = 85.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'TequiLaLa',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        uid = 'BarUpper',
        fake_fridge = true
      },
    },
  },
  distance = 2.5
})

AddBoxZone('TequiLaLaManagement', vector3(-562.9767, 287.4797, 82.29747), 0.4, 0.4, {
  name = 'TequiLaLaManagement',
  heading = 80.0,
  minZ = 82.25,
  maxZ = 82.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'TequiLaLa',
      component_id = 'management',
    },
  },
  distance = 1.5
})

AddBoxZone('TequiLaLaCloset', vector3(-562.9092, 280.0565, 84.0163), 1.2, 0.8, {
  name = 'TequiLaLaCloset',
  heading = 265.0,
  minZ = 82.0,
  maxZ = 84.02,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'TequiLaLa',
      }
    },
  },
  distance = 1.5
})

for k, tray_coords in ipairs({
  vector3(-560.8517, 287.3915, 82.27589),
  vector3(-560.9517, 286.0715, 82.27589),
  vector3(-561.0917, 284.7715, 82.27589),
}) do
AddBoxZone('TequiLaLaTray' .. k, tray_coords, 0.4, 0.6, {
  name = 'TequiLaLaTray' .. k,
  heading = 85.0,
  minZ = 82.2,
  maxZ = 82.5,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = tray_coords,
    },
  },
  distance = 2.5
})
end

AddBoxZone('TequiLaLaTray2a', vector3(-564.1741, 285.5679, 85.49525), 0.4, 0.6, {
  name = 'TequiLaLaTray2a',
  heading = 85.0,
  minZ = 85.43,
  maxZ = 85.63,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-utensils',
      label = 'Food Tray',
      table_coords = vector3(-564.1741, 285.5679, 85.49525),
    },
  },
  distance = 2.5
})
AddBoxZone('TequiLaLaRegister', vector3(-561.24, 285.41, 82.18), 0.4, 0.45, {
  name = 'TequiLaLaRegister',
  heading = 355,
  minZ = 82.08,
  maxZ = 82.48,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'TequiLaLa',
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'TequiLaLa',
      }
    },
  },
  distance = 1.5
})
AddBoxZone('TequiLaLaRegister2', vector3(-561.02, 288.04, 82.18), 0.4, 0.45, {
  name = 'TequiLaLaRegister2',
  heading = 355,
  minZ = 82.08,
  maxZ = 82.48,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'TequiLaLa',
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'TequiLaLa',
      }
    },
  },
  distance = 1.5
})
AddBoxZone('TequiLaLaRegister3', vector3(-564.31, 286.28, 85.38), 0.4, 0.45, {
  name = 'TequiLaLaRegister3',
  heading = 355,
  minZ = 85.28,
  maxZ = 85.68,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'TequiLaLa',
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'TequiLaLa',
      }
    },
  },
  distance = 1.5
})

AddCookingZone('TequiLaLa', 'TequiLaLaCrafting1', vector3(-566.2753, 286.9814, 84.3764), 1.4, 0.9, 175.0, 84.3, 85.6, function()
  return true
end, {
  { id = 'drink_thors_hammer', label = "Mix Thor's Hammer" },
  { id = 'drink_adios_shot', label = "Mix Adios Shot" },
  { id = 'drink_wakeup_juice', label = "Mix Wake up juice" },
  { id = 'drink_odins_blood', label = "Mix Odin's Blood" },
  { id = 'drink_darkangels_blood', label = "Mix Dark Angel's Blood" },
  { id = 'drink_blood_moon', label = "Mix Blood Moon" },
  { id = 'drink_jethros_julep', label = "Mix Jethro's Julep" },
  { id = 'drink_condemnedlight', label = "Mix Condemned Lightning" },
  { id = 'drink_sharkattack', label = "Mix Shark Attack" },
  { id = 'drink_lsspecial', label = "Mix LS Special" },
  { id = 'drink_crystle_dream', label = "Mix Crystal Dream" },
  { id = 'drink_sinister_slayer', label = "Mix Sinister Slayer" },
  { id = 'drink_aviationfuel', label = "Mix Aviation Fuel" },
  { id = 'drink_demonsnake', label = "Mix Demon Sake" },
},true)

AddCookingZone('TequiLaLa', 'TequiLaLaCrafting2', vector3(-563.1404, 284.5446, 81.1764), 1.4, 0.8, 175.0, 81.1, 82.4, function()
  return true
end, {
  { id = 'drink_thors_hammer', label = "Mix Thor's Hammer" },
  { id = 'drink_adios_shot', label = "Mix Adios Shot" },
  { id = 'drink_wakeup_juice', label = "Mix Wake up juice" },
  { id = 'drink_odins_blood', label = "Mix Odin's Blood" },
  { id = 'drink_darkangels_blood', label = "Mix Dark Angel's Blood" },
  { id = 'drink_blood_moon', label = "Mix Blood Moon" },
  { id = 'drink_jethros_julep', label = "Mix Jethro's Julep" },
  { id = 'drink_condemnedlight', label = "Mix Condemned Lightning" },
  { id = 'drink_sharkattack', label = "Mix Shark Attack" },
  { id = 'drink_lsspecial', label = "Mix LS Special" },
  { id = 'drink_crystle_dream', label = "Mix Crystal Dream" },
  { id = 'drink_sinister_slayer', label = "Mix Sinister Slayer" },
  { id = 'drink_aviationfuel', label = "Mix Aviation Fuel" },
  { id = 'drink_demonsnake', label = "Mix Demon Sake" },
},true)
