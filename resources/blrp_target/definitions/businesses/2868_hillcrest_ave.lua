AddBoxZoneAutoname(vector4(-762.61, 613.13, 136.53, 20), 0.7, 1.8, {
    minZ = 136.13,
    maxZ = 136.93,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-computer-classic',
        label = 'Management',

        business_name = '2868 Hillcrest Ave',
        component_id = 'management',
      },
    },
    distance = 2.5,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(-766.47, 611.26, 140.33, 15), 3.2, 0.4, {
  minZ = 139.53,
  maxZ = 141.73,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',

        groups = {
          '2868 Hillcrest Ave',
        }
      },
      {
        event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
        icon = 'far fa-tshirt',
        label = 'Clothing Store',


        groups = {
          '2868 Hillcrest Ave',
        }
      },
    },
    distance = 6.0,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(-761.91, 619.41, 136.53, 20), 3.4, 0.4, {
  minZ = 135.53,
  maxZ = 138.53,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openWardrobePersonal',
        icon = 'fa-regular fa-tshirt',
        label = 'Personal Wardrobe',

        groups = {
          '2868 Hillcrest Ave',
        }
      },
      {
        event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
        icon = 'far fa-tshirt',
        label = 'Clothing Store',


        groups = {
          '2868 Hillcrest Ave',
        }
      },
    },
    distance = 6.0,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(-764.44, 620.71, 136.53, 110), 1.6, 0.6, {
  minZ = 135.53,
  maxZ = 137.93,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',

        business_name = '2868 Hillcrest Ave',
        component_id = 'storage',

        args = {
          capacity = 1000.0,
          named = 'Home2Storage1',
        },
      },
    },
    distance = 5.3,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(-768.88, 615.37, 143.93, 110), 3.2, 0.8, {
  minZ = 143.13,
  maxZ = 143.93,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',

        business_name = '2868 Hillcrest Ave',
        component_id = 'storage',

        args = {
          capacity = 500.0,
          named = 'Home2Storage2',
        },
      },
    },
    distance = 3.5,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(-773.57, 613.74, 140.37, 110), 3.2, 0.6, {
  minZ = 139.57,
  maxZ = 140.37,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',

        business_name = '2868 Hillcrest Ave',
        component_id = 'storage',

        args = {
          capacity = 500.0,
          named = 'Home2Storage3',
        },
      },
    },
    distance = 3.1,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(-758.87, 615.05, 144.14, 20), 1.8, 0.6, {
  minZ = 143.54,
  maxZ = 145.34,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'fridge',

        business_name = '2868 Hillcrest Ave',
        component_id = 'storage',

        args = {
          capacity = 25.0,
          named = 'Home2Storage4',
          fake_fridge = true,
        },
      },
    },
    distance = 3.1,
    context_disable = true,
  })

AddBoxZoneAutoname(vector4(-773.07, 614.04, 143.73, 19), 0.6, 2.1, {
  minZ = 142.93,
  maxZ = 143.73,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',

        business_name = '2868 Hillcrest Ave',
        component_id = 'storage',
        component_perm = 'extra_a',

        args = {
          capacity = 1000.0,
          named = 'Home2Storage5',
        },
      },
    },
  distance = 3.1,
  context_disable = true,
})

  -- door
AddBoxZoneAutoname(vector4(-754.87, 619.93, 142.86, 20), 2.2, 1.0, {
  minZ = 141.86,
  maxZ = 143.88,
  notifyText = '<i class="fa-regular fa-eye"></i> 2868 Hillcrest Avenue - Door'
  }, {
    options = {
      {
        event_client = 'core:client:teleports:requestFromTargetHouse',
        icon = 'fa-solid fa-door-open',
        label = '2868 Hillcrest Avenue Door',
        door_delay = true,
        destination = '2868HillcrestAveinside',
      },
    },
    distance = 1.0
  })

AddBoxZoneAutoname(vector4(-755.39, 620.53, 144.14, 110), 0.2, 1.4, {
  minZ = 143.15,
  maxZ = 145.55,
  notifyText = '<i class="fa-regular fa-eye"></i> 2868 Hillcrest Avenue - Door'
  }, {
    options = {
      {
        event_client = 'core:client:teleports:requestFromTargetHouse',
        icon = 'fa-solid fa-door-open',
        label = '2868 Hillcrest Avenue Door',
        door_delay = true,
        destination = '2868HillcrestAveoutside',
      },
    },
    distance = 2.5
  })
