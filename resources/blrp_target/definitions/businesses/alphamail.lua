AddBoxZoneAutoname(vector4(1202.42, -3252.88, 7.1, 0.0), 0.4, 0.4, {
  minZ = 6.7,
  maxZ = 7.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'AlphaMail',
      component_id = 'management',
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Postal Worker',

      icon = 'fas fa-clock',
      label = 'Clock in - Postal Worker',

      groups = {
        'Civilian',
      },
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(1199.06, -3252.48, 7.1, 0.0), 0.8, 1.4, {
  minZ = 6.1,
  maxZ = 8.1,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supply Store',

      uid = 'alphamail-supply',

      groups = {
        'AlphaMail',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'AlphaMail',
      },
    },
  },
  distance = 2.5
})
