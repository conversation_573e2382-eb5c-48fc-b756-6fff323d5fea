AddBoxZone('Home1Management', vector3(1251.22, -500.09, 69.71), 0.7, 0.5, {
  name = 'Home1Management',
  heading = 349,
  minZ = 69.51,
  maxZ = 70.11
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = '194 East Mirror Dr',
      component_id = 'management',
    },
  },
  distance = 2.5,
  context_disable = true,
})


AddBoxZone('Home1Closet1', vector3(1246.64, -501.99, 69.71), 1.35, 0.7, {
  name = 'Home1Closet1',
  heading = 347,
  minZ = 68.71,
  maxZ = 71.01
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        '194 East Mirror Dr',
      }
    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'far fa-tshirt',
      label = 'Clothing Store',


      groups = {
        '194 East Mirror Dr',
      }
    },
  },
  distance = 6.0,
  context_disable = true,
})

AddBoxZone('Home1Closet2', vector3(1251.29, -503.11, 69.73), 1.3, 1.1, {
  name = 'Home1Closet2',
  heading = 347,
  minZ = 68.68,
  maxZ = 70.98
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        '194 East Mirror Dr',
      }
    },
    {
      event_client = 'blrp_clothingstore:client:setVisibleFromTarget',
      icon = 'far fa-tshirt',
      label = 'Clothing Store',


      groups = {
        '194 East Mirror Dr',
      }
    },
  },
  distance = 6.0,
  context_disable = true,
})


AddBoxZone('Home1Storage1', vector3(1241.52, -491.38, 69.71), 1.9, 1.1, {
  name = 'HomeStorage1',
  heading = 346,
  minZ = 68.76,
  maxZ = 71.11
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '194 East Mirror Dr',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        named = 'Home1Storage1',
      },
    },
  },
  distance = 5.3,
  context_disable = true,
})


AddBoxZone('Home1Storage2', vector3(1246.95, -500.7, 69.71), 1.35, 0.75, {
  name = 'Home1Storage2',
  heading = 347,
  minZ = 68.31,
  maxZ = 69.46
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '194 East Mirror Dr',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'Home1Storage2',
      },
    },
  },
  distance = 3.5,
  context_disable = true,
})

AddBoxZone('Home1Storage3', vector3(1251.76, -501.35, 69.73), 1.45, 0.75, {
  name = 'Home1Storage3',
  heading = 347,
  minZ = 68.68,
  maxZ = 69.83
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '194 East Mirror Dr',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'Home1Storage3',
      },
    },
  },
  distance = 3.1,
  context_disable = true,
})

AddBoxZone('Home1Storage4', vector3(1243.95, -497.32, 69.71), 1.5, 1.2, {
  name = 'Home1Storage4',
  heading = 347,
  minZ = 68.76,
  maxZ = 70.71
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'fridge',

      business_name = '194 East Mirror Dr',
      component_id = 'storage',

      args = {
        capacity = 25.0,
        named = 'Home1Storage4',
      },
    },
  },
  distance = 3.1,
  context_disable = true,
})
