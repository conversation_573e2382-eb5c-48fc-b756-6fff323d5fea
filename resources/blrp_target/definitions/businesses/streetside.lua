disableContext(true)

AddBoxZone('StreetManagement', vector3(-152.2413, 168.2245, 69.33993), 0.6, 0.6, {
  name = 'StreetManagement',
  heading = 320.0,
  minZ = 69.25,
  maxZ = 69.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Street Side',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('StreetStorageMain', vector3(-149.3942, 160.5357, 68.52764), 1.2, 1.2, {
  name = 'StreetStorageMain',
  heading = 340.0,
  minZ = 68.5,
  maxZ = 70.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Street Side',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('StreetFridge', vector3(-156.131, 164.1506, 68.56161), 0.8, 0.8, {
  name = 'StreetFridge',
  heading = 340.0,
  minZ = 68.5,
  maxZ = 70.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Street Side',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('StreetCloset1', vector3(-152.0548, 161.4746, 68.63559), 0.7, 0.7, {
  name = 'StreetCloset1',
  heading = 340.0,
  minZ = 68.5,
  maxZ = 70.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Street Side',
      }
    },
  },
  distance = 2.5
})
