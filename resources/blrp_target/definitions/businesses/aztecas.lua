AddBoxZone('AztecasManagement', vector3(492.293, -1524.273, 29.07228), 0.7, 0.7, {
  name = 'AztecasManagement',
  heading = 323.0,
  minZ = 29.0,
  maxZ = 29.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Aztecas',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('AztecasStorageMain', vector3(486.2634, -1532.144, 29.00036), 1.0, 3.0, {
  name = 'AztecasStorageMain',
  heading = 140.0,
  minZ = 28.2,
  maxZ = 30.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Aztecas',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Aztecas',
      component_id = 'storage',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-spray-can',
      label = 'Spray Paint Supply',

      business_name = 'Aztecas',
      component_id = 'null',
      component_perm = 'extra_a',

      uid = 'azteca-supply',
    },
  },
  distance = 3.0
})

AddBoxZone('AztecasFridge', vector3(492.6356, -1528.888, 29.20102), 0.8, 1.4, {
  name = 'AztecasFridge',
  heading = 50.0,
  minZ = 28.5,
  maxZ = 29.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Aztecas',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})
