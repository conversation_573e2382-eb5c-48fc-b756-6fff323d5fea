local hands_washed = false



RegisterNetEvent('blrp_target:pearls:washHands', function()
  local clock_in_data = pCore.isClockedIntoTabletBusiness({ 'Pearls' })

  if clock_in_data and not clock_in_data.clocked_in and
  (
    not GlobalState.is_dev or
    not exports.blrp_core:me().request('[DEV] Override Clock-in to wash hands?')
  )
  then
    exports.blrp_core:me().notify('You are not clocked in (through the tablet)!')
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'pearls_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

for k, sink_coords in ipairs({
    vector3(-1842.6, -1191.99, 14.31),
    vector3(-1846.12, -1200.19, 14.31),
}) do
  AddBoxZone('pearlssSink' .. k, sink_coords, 0.7, 0.7, {
    name = 'PearlsSink' .. k,
    heading = 330,
    minZ = 14.1,
    maxZ = 14.4,
  }, {
    options = {
      {
        event_client = 'blrp_target:pearls:washHands',
        icon = 'far fa-sink',
        label = 'Wash Hands',

        groups = {
          'Pearls'
        }
      },
    },
    distance = 2.5
  })
end

AddBoxZone('PearlsManagement', vector3(-1839.41, -1184.39, 14.31), 0.25, 0.85, {
  name = 'PearlsManagement',
  heading=8,
  minZ=14.21,
  maxZ=14.91
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Pearls',
      component_id = 'management',
    },
  },
  distance = 2.5
})

for reg, register_coords in ipairs({
  vector3(-1835.19, -1191.71, 14.31),
  vector3(-1834.17, -1189.98, 14.31),
}) do
  AddBoxZone('pearlssRegister' .. reg, register_coords, 0.5, 0.4, {
    name = 'PearlsRegister' .. reg,
    heading=330,
    minZ=14.31,
    maxZ=14.71
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-cash-register',
      label = 'Use Register',

      groups = {
        'Pearls'
      }
    },
    {
      event_server = 'core:server:restaurant-general:requestRestock',
      icon = 'fa-regular fa-cash-register',
      label = 'Order Stock',

      groups = {
        'Pearls'
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Pearls Restaurant',

      groups = {
        'Pearls'
      }
    },
    {
      event_server = 'core:server:badeats:openRestaurantMenuTarget',
      icon = 'far fa-tablet',
      label = 'Badeats Admin',
      restaurant_name = 'Pearls',

      groups = {
        'Pearls'
      },
    },
  },
  distance = 2.5
})
end

AddBoxZone('PearlsStorage1', vector3(-1843.53, -1198.23, 14.31), 1.3, 0.4, {
  name = 'PearlsStorage1',
  heading = 330,
  minZ = 13.71,
  maxZ = 15.66
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Pearls',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        named = 'fridge_pearls_a',
        fake_fridge = true,
      },
    },
  },
  distance = 3.0
})

AddBoxZone('PearlsStorage2', vector3(-1844.3, -1199.6, 14.31), 1.3, 0.4, {
  name = 'PearlsStorage2',
  heading = 60.0,
  minZ=13.71,
  maxZ=15.66
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Pearls',
      component_id = 'storage',

      args = {
        capacity = 750.0,
        named = 'fridge_pearls_b',
        fake_fridge = true,
      },
    },
  },
  distance = 3.0
})
AddBoxZone('PearlsStorage3', vector3(-1837.763, -1190.447, 13.82819), 0.55, 0.4, {
  name = 'PearlsStorage3',
  heading=331,
  minZ=13.41,
  maxZ=14.26
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Pearls',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        named = 'fridge_pearls_c',
        fake_fridge = true,
      },
    },
  },
  distance = 3.0
})

AddBoxZone('PearlsStorage5', vector3(-1842.52, -1182.774, 14.04532), 0.55, 0.4, {
  name = 'PearlsStorage5',
  heading=331,
  minZ=13.41,
  maxZ=14.26
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Pearls',
      component_id = 'storage',

      args = {
        capacity = 250.0,
        named = 'fridge_pearls_d',
      },
    },
  },
  distance = 3.0
})

AddBoxZone('PearlsCloset', vector3(-1837.53, -1187.78, 14.31), 3.2, 0.5, {
  name = 'PearlsCloset',
  heading=330,
  minZ=13.31,
  maxZ=15.41
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Pearls',
      }
    },
  },
  distance = 2.5
})
AddBoxZone('PearlsStorage4', vector3(-1839.18, -1193.3, 14.31), 2.9, 1.35, {
  name = 'PearlsStorage4',
  heading=330,
  minZ=13.31,
  maxZ=15.51
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Pearls',
      component_id = 'storage',

      args = {
        capacity = 50.0,
        uid = '1'
      },
    },
  },
  distance = 2.5
})

local cook_options = {
    {
    event_client = 'event:stub',
    icon = 'fa-regular fa-sink',
    label = 'Go wash your hands!',

    groups = {
        'Pearls'
    },

    filter = function()
        return not hands_washed
    end
    }
}

local item_idx = 2

for _, seafood_option in pairs({
    { id = 'p_seafood_pasta', label = 'SeaFood Pasta' },
    { id = 'p_smoked_salmon_salad', label = 'Smoked Salmon Salad' },
    { id = 'p_lobster_dinner', label = 'Lobster Dinner' },
    {id = 'p_firecrackershrimp', label = 'Tangirine Firecracker Shrimp'},
    { id = 'p_caviar', label = 'Caviar' },
    { id = 'p_crab_legs', label = 'Crab Legs' },
    { id = 'p_oysters', label = 'Oyster Platter' },
    { id = 'p_fishtaco', label = 'Fish Taco'},
}) do
    cook_options[item_idx] = {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = seafood_option.label,

    food_item_id = seafood_option.id,
    auto_order = true,
    groups = {
        'Pearls'
    },

    filter = function()
        return hands_washed
    end
    }

    item_idx = item_idx + 1
end

AddBoxZone('PearlsSeaFoodPrep', vector3(-1847.51, -1194.66, 14.31), 3.5, 0.9, {
    name = 'PearlsSeaFoodPrep',
    heading = 330,
    minZ=13.31,
    maxZ=14.71
}, {
    options = cook_options,
    distance = 2.5,
})

local cook_options = {
  {
  event_client = 'event:stub',
  icon = 'fa-regular fa-sink',
  label = 'Go wash your hands!',

  groups = {
      'Pearls'
  },

  filter = function()
      return not hands_washed
  end
  }
}

local item_idx = 2

for _, food_option in pairs({
  { id = 'p_filet_mignon', label = 'Filet Mignon' },
  { id = 'p_crabless_cakes', label = 'Crabless Cakes' },
  { id = 'p_truffle_cake', label = 'Chocolate Truffle Cake' },
  { id = 'p_applebread_pudding', label = 'Apple Bread Pudding' },
  { id = 'p_creme_brulee', label = 'Creme Brulee' },
  { id = 'food_beefwellington', label = 'Cook Beef Wellington' },
  { id = 'p_colcannon', label = 'Colcannon' },
  { id = 'p_ratatouille', label = 'Ratatouille' },
  { id = 'p_capresesalad', label = 'Caprese Salad' },
  { id = 'p_banana_foster', label = 'Bananas Foster' },
  --{ id = 'xmr_pearls_f', label = 'Christmas Pudding'},
  --{ id = 'food_ea25_pearls', label = 'Carrot Cake' },
}) do
  cook_options[item_idx] = {
  event_server = 'core:server:restaurants:transformFood',
  icon = 'fa-regular fa-fire-burner',
  label = food_option.label,

  food_item_id = food_option.id,
  auto_order = true,
  groups = {
      'Pearls'
  },

  filter = function()
      return hands_washed
  end
  }

  item_idx = item_idx + 1
end

AddBoxZone('PearlsFoodPrep', vector3(-1845.25, -1196.17, 14.31), 3.0, 1.5, {
  name = 'PearlsFoodPrep',
  heading=330,
  minZ=14.11,
  maxZ=14.61
}, {
  options = cook_options,
  distance = 2.5,
})

local mix_options = {
    {
    event_client = 'event:stub',
    icon = 'fa-regular fa-sink',
    label = 'Go wash your hands!',

    groups = {
        'Pearls'
    },

    filter = function()
        return not hands_washed
    end
    }
}

local item_idx = 2

for _, mix_option in pairs({
    { id = 'p_dirty_martini', label = 'Dirty Martini' },
    { id = 'p_apple_martini', label = 'Apple Martini' },
    { id = 'p_cottoncandymarg', label = 'Cotton Candy Margarita'},
    { id = 'p_maitai', label = "Mai Tai"},
    { id = 'p_fruit_clubsoda', label = 'Fruit Infused Club Soda' },
    { id = 'p_arnold_palmer', label = 'Arnold Palmer' },
    --{ id = 'xmr_pearls_d', label = 'Cranberry Hot Toddy' },
}) do
    mix_options[item_idx] = {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = mix_option.label,

    food_item_id = mix_option.id,
    auto_order = true,
    groups = {
        'Pearls'
    },

    filter = function()
        return hands_washed
    end
    }

    item_idx = item_idx + 1
end

AddBoxZone('PearlskDrinkMix', vector3(-1836.18, -1187.5, 14.31), 0.55, 0.25, {
    heading=330,
    minZ=13.31,
    maxZ=14.31
}, {
    options = mix_options,
    distance = 2.5,
})

AddBoxZone('PearlsWine', vector3(-1835.86, -1186.97, 14.31), 0.55, 0.25, {
    name = 'PearlsWine',
    heading=330,
    minZ=13.31,
    maxZ=14.31
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Bottle of Pearl White',

      item_id = 'p_white_wine',

      business_name = 'Pearls',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Bottle of Pearl Red',

      item_id = 'p_red_wine',

      business_name = 'Pearls',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Open Bottle of Pearl White',

      item_id = 'drink_white_wine',

      business_name = 'Pearls',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Open Bottle of Pearl Red',

      item_id = 'drink_red_wine',

      business_name = 'Pearls',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})

for k, table_coords in ipairs({
  vector3(-1833.13, -1196.47, 14.31),
  vector3(-1831.19, -1193.43, 14.31),
  vector3(-1829.64, -1194.36, 14.31),
  vector3(-1831.16, -1197.56, 14.31),
  vector3(-1829.24, -1198.74, 14.31),
  vector3(-1827.29, -1199.84, 14.31),
  vector3(-1825.67, -1196.64, 14.31),
  vector3(-1824.52, -1194.66, 14.31),
  vector3(-1826.09, -1193.74, 14.31),
  vector3(-1830.06, -1191.45, 14.31),
  vector3(-1828.4, -1188.28, 14.31),
  vector3(-1826.47, -1189.45, 14.31),
  vector3(-1824.52, -1190.55, 14.31),
  vector3(-1822.56, -1191.67, 14.31),
}) do
  AddBoxZone('PearlsTables' .. k, table_coords, 1.1, 1.0, {
    name = 'PearlsTables' .. k,
    heading=330,
    minZ=14.01,
    maxZ=14.41
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Table',
        table_coords = table_coords,
      },
    },
    distance = 2.5
  })
end

for k, tray_coords in ipairs({
  vector3(-1837.2, -1188.87, 14.31),
  vector3(-1834.59, -1190.87, 14.31),
}) do
  AddBoxZone('PearlsTray' .. k, tray_coords, 0.6, 1.0, {
    name = 'PearlsTray' .. k,
    heading=330,
    minZ=14.31,
    maxZ=14.51
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Tray',
        table_coords = tray_coords,
      },
    },
    distance = 2.5
  })
end

  AddBoxZone('PublicMenuStorage1', vector3(-1826.5, -1195.17, 14.31), 0.35, 0.4, {
    name = 'PublicMenuStorage1',
    heading=331,
    minZ=14.16,
    maxZ=14.36
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Menus',
        table_coords = vector3(-1826.5, -1195.17, 14.31)
      },
    },
    distance = 2.5
  })

  AddBoxZone('PublicMenuStorage2', vector3(-1829.0, -1193.02, 14.31), 0.35, 0.85, {
    name = 'PublicMenuStorage2',
    heading=330,
    minZ=14.16,
    maxZ=14.31
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Menus',
        table_coords = vector3(-1829.0, -1193.02, 14.31)
      },
    },
    distance = 2.5
  })

  AddBoxZone('PrivateMenuStorage', vector3(-1833.67, -1187.92, 14.31), 0.55, 0.4, {
    name = 'PrivateMenuStorage',
    heading=240,
    minZ=14.06,
    maxZ=14.31
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Menus',
        table_coords = vector3(-1833.67, -1187.92, 14.31),
        groups = {
          'Pearls'
      },
      },
      {
        event_server = 'blrp_core:server:item-store:resolveTargetConfig',
        icon = 'fa-regular fa-shelves',
        label = 'Supplies',

        uid = 'pearls-supply',
      },
    },
    distance = 2.5
  })
