AddBoxZone('HuffAssociatesComputer', vector3(-230.6243, -777.8901, 34.05325), 0.5, 0.8, {
  name = 'HuffAssociatesComputer',
  heading = 72.0,
  minZ = 34.0,
  maxZ = 34.7,
}, {
  options = {
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request DOJ ID Card',
      card_type = 'id_doj',

      groups = {
        'Lawyer',
        'DOJ',
      }
    },    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Lawyer',

      icon = 'fas fa-clock',
      label = 'Clock in - Lawyer',

      groups = {
        'Civilian',
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0
})

AddBoxZone('HuffManagement', vector3(-212.4751, -774.8038, 33.90904), 0.5, 0.5, {
  name = 'HuffManagement',
  heading = 105.0,
  minZ = 33.9,
  maxZ = 34.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Huff & Associates',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZone('HuffStorage', vector3(-209.1532, -772.8358, 33.96752), 0.4, 1.0, {
  name = 'HuffStorage',
  heading = 250.0,
  minZ = 33.2,
  maxZ = 34.9,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Los Santos Law Offices',
      component_id = 'storage',
    },
  },
  distance = 3.0
})


AddBoxZone('HuffFridge', vector3(-225.7514, -767.6594, 34.19262), 0.4, 1.0, {
  name = 'HuffFridge',
  heading = 250.0,
  minZ = 33.2,
  maxZ = 34.9,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Los Santos Law Offices',
      component_id = 'fridge',
    },
  },
  distance = 3.0
})
