AddBoxZoneAutoname(vector3(-321.9768, -1301.524, 31.10574), 1.2, 2.1, {
  minZ = 31.1,
  maxZ = 31.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft EMP',

      item_id = 'emp',

      business_name = 'OTF',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Advanced Case',

      item_id = 'ex_prop_adv_case_sm',

      business_name = 'OTF',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 9mm Ammo (20)',

      item_id = 'ammo_9mm',

      business_name = 'OTF',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 7.62x39 Ammo (20)',

      item_id = 'ammo_762x39',

      business_name = 'OTF',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 7.62x54 Ammo (20)',

      item_id = 'ammo_762x54',

      business_name = 'OTF',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft .45 Automatic Ammo (20)',

      item_id = 'ammo_45auto',

      business_name = 'OTF',
      component_id = 'crafting',
    }
  },
  distance = 2.5
})