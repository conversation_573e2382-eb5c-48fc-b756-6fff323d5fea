AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = 'Cook Ceviche',
    auto_order = true,
    food_item_id = 'food_ceviche',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = 'Cook Mexi Tacos',
    auto_order = true,
    food_item_id = 'food_mexitacos',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = 'Cook Churros',
    auto_order = true,
    food_item_id = 'food_churros',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = 'Prepare Chips N Salsa',
    auto_order = true,
    food_item_id = 'food_chipsnsalsa',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = 'Cook Burrito',
    auto_order = true,
    food_item_id = 'food_burrito',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  --[[ AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = 'Cook Tamales',
    auto_order = true,
    food_item_id = 'xm23_s1_tamales',

    groups = {
      'Taqueria De Los Santos'
    },
  }) ]]

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = 'Mix Garritos',
    auto_order = true,
    food_item_id = 'food_garritos',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = 'Mix margarita',
    auto_order = true,
    food_item_id = 'food_margarita',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = 'Mix Mexi Goke',
    auto_order = true,
    food_item_id = 'food_mexicola',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = 'Mix Strawberry Margarita',
    auto_order = true,
    food_item_id = 'food_strawberrymargarita',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = 'Mix Tequila Shot',
    auto_order = true,
    food_item_id = 'food_tequilashot',

    groups = {
      'Taqueria De Los Santos'
    },
  })

  --[[ AddGangBenchOption('Taqueria De Los Santos', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-cup-straw',
    label = 'Make Atole',
    auto_order = true,
    food_item_id = 'xm23_s1_atole',

    groups = {
      'Taqueria De Los Santos'
    },
  }) ]]
  
  AddGangBenchOption('Taqueria De Los Santos',{ 
    event_server = 'core:server:restaurant-general:requestRestock',
    icon = 'fa-regular fa-cash-register',
    label = 'Order Stock',

    groups = {
      'Taqueria De Los Santos'
    }
  })