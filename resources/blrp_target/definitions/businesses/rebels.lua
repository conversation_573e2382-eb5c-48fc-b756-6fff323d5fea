AddGangBenchOption('Rebels', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Stencil',

  item_id = 'stencil_rebels',

  business_name = 'Rebels',
  component_id = 'craft-stencil_rebels',
  component_perm = 'crafting',
})

AddGangBenchOption('Rebels', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft EMP',

  item_id = 'emp',

  business_name = 'Rebels',
  component_id = 'craft-emp',
  component_perm = 'crafting',
})

AddGangBenchOption('Rebels', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Advanced Case',

  item_id = 'ex_prop_adv_case_sm',

  business_name = 'Rebels',
  component_id = 'craft-advanced-case',
  component_perm = 'crafting',
})


--- MLO stuff

disableContext(true)

-- Closet
AddBoxZoneAutoname(vector4(939.54, -1552.35, 30.74, 0), 1.1, 0.8, {
  minZ = 29.64,
  maxZ = 32.04,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Rebels',
      }
    },
  },
  distance = 3.0
})


AddBoxZoneAutoname(vector4(961.03, -1571.26, 30.74, 0), 0.4, 0.8, {
  minZ = 30.49,
  maxZ = 31.09,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Rebels',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZoneAutoname(vector4(930.86, -1534.93, 30.75, 0), 2.4, 1, {
  minZ = 29.65,
  maxZ = 31.05,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Advanced Lockpick',

      item_id = 'lockpick_adv',

      business_name = 'Rebels',
      component_id = 'crafting',
      component_perm = 'extra_c',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Advanced Lockset',

      item_id = 'adv_lock',

      business_name = 'Rebels',
      component_id = 'crafting',
      component_perm = 'extra_b',
    },
  },
  distance = 2.5
})


AddBoxZoneAutoname(vector4(928.29, -1571.34, 30.74, 0), 1.0, 4.6, {
  minZ = 29.74,
  maxZ = 32.74,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Rebels',
      component_id = 'storage',
      component_perm = 'extra_d',

      args = {
        capacity = 750.0,
        uid = '1',
      },
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(946.33, -1538.04, 30.75, 0), 1.7, 0.8, {
  minZ = 29.75,
  maxZ = 30.75,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Rebels',
      component_id = 'storage',
      component_perm = 'extra_e',

      args = {
        capacity = 1000.0,
        uid = '2',
      },
    },
  },
  distance = 2.5
})
