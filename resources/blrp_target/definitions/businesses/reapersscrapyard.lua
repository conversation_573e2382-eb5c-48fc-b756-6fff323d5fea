AddBoxZoneAutoname(vector4(-422.02, -1675.69, 19.03, 340), 1.2, 0.4, {
  minZ = 18.63,
  maxZ = 19.43
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Reapers Scrapyard',
      component_id = 'management',
    },
    {
      event_server = 'core:server:target-backhaul:getDailyDistributor',
      icon = 'fa-regular fa-computer-classic',
      label = 'Get Daily Export Distributor',

      groups = {
        'Reapers Scrapyard|extra_b',
      },
    },

  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-421.19, -1673.81, 19.03, 340), 1.6, 0.8, {
  minZ = 18.03,
  maxZ = 20.03
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Reapers Scrapyard',
      }
    },

  },
  distance = 2.5
})

for idx, storage_data in ipairs({
  { coords = vector3(-412.6251, -1674.035, 18.85065), heading = 340.0, named = 'cs4_sy_Aluminum', component_perm = 'extra_c', }, -- Aluminum
  { coords = vector3(-413.0654, -1675.245, 18.81834), heading = 340.0, named = 'cs4_sy_Copper', component_perm = 'extra_c', }, -- Copper
  { coords = vector3(-413.8076, -1677.369, 18.85849), heading = 340.0, named = 'cs4_sy_Gold', component_perm = 'extra_c', }, -- Gold
  { coords = vector3(-414.3001, -1678.722, 18.82266), heading = 340.0, named = 'cs4_sy_Platinum', component_perm = 'extra_c', }, -- Platinum
  { coords = vector3(-415.0237, -1680.71, 18.85079), heading = 340.0, named = 'cs4_sy_Nitrate', component_perm = 'extra_c', }, -- Nitrate
  { coords = vector3(-415.5467, -1682.147, 18.78641), heading = 340.0, named = 'cs4_sy_Wood', component_perm = 'extra_c', }, -- Wood
  { coords = vector3(-416.2868, -1684.181, 18.80866), heading = 340.0, named = 'cs4_sy_Plastic', component_perm = 'extra_c', }, -- Plastic
  { coords = vector3(-416.7882, -1685.558, 18.80422), heading = 340.0, named = 'cs4_sy_Glue', component_perm = 'extra_c', }, -- Glue
  { coords = vector3(-418.0393, -1688.995, 18.80658), heading = 340.0, named = 'cs4_sy_Pipe', component_perm = 'extra_c', }, -- Steel Pipe
  { coords = vector3(-418.742, -1690.926, 18.83389), heading = 340.0, named = 'cs4_sy_Gears', component_perm = 'extra_c', }, -- Gears
  { coords = vector3(-419.2706, -1692.379, 18.8056), heading = 340.0, named = 'cs4_sy_Springs', component_perm = 'extra_c', }, -- Springs
  { coords = vector3(-419.9878, -1694.349, 18.88413), heading = 340.0, named = 'cs4_sy_Oil', component_perm = 'extra_c', }, -- Oil
  { coords = vector3(-417.7073, -1676.215, 18.78598), heading = 340.0, named = 'cs4_sy_Charcoal', component_perm = 'extra_c', }, -- Charcoal
  { coords = vector3(-416.9958, -1674.26, 18.81724), heading = 340.0, named = 'cs4_sy_Steel', component_perm = 'extra_c', }, -- Steel
  { coords = vector3(-416.5076, -1672.919, 18.78821), heading = 340.0, named = 'cs4_sy_Titanium', component_perm = 'extra_c', }, -- Titanium
  { coords = vector3(-418.2203, -1677.624, 18.86614), heading = 340.0, named = 'cs4_sy_Electronics', component_perm = 'extra_c', }, -- Electronics
  { coords = vector3(-417.5157, -1687.557, 18.84686), heading = 340.0, named = 'cs4_sy_Tape', component_perm = 'extra_c', }, -- Tape
  { coords = vector3(-420.5096, -1695.782, 18.81097), heading = 340.0, named = 'cs4_sy_Glass', component_perm = 'extra_c', }, -- Glass Shards
  { coords = vector3(-424.3648, -1694.506, 18.89239), heading = 340.0, named = 'cs4_sy_Outbound1', }, -- Storage 1
  { coords = vector3(-423.844, -1693.075, 18.81347), heading = 340.0, named = 'cs4_sy_Outbound2', }, -- Storage 2
  { coords = vector3(-430.83, -1691.912, 18.84183), heading = 340.0, named = 'cs4_sy_Misc1', component_perm = 'extra_c', }, -- Misc 1
  { coords = vector3(-430.3198, -1690.51, 18.88148), heading = 340.0, named = 'cs4_sy_Misc2', component_perm = 'extra_c', }, -- Misc 2
  { coords = vector3(-429.5925, -1688.512, 18.85884), heading = 340.0, named = 'cs4_sy_Misc3', component_perm = 'extra_c', }, -- Misc 3 -- needs number
  { coords = vector3(-429.0999, -1687.159, 18.73863), heading = 340.0, named = 'cs4_sy_Misc4', component_perm = 'extra_c', }, -- Misc 4 --- needs number
}) do
  AddBoxZoneAutoname(vector4(storage_data.coords, storage_data.heading), 1.2, 0.5, {
    minZ = storage_data.coords.z - 0.7,
    maxZ = storage_data.coords.z + 0.3,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'far fa-box',
        label = 'Storage',

        business_name = 'Reapers Scrapyard',
        component_id = 'storage',
        component_perm = storage_data.component_perm,

        args = {
          capacity = 1000.0,
          named = storage_data.named
        },
      }
    },
    distance = 3.0,
  })
end

AddBoxZoneAutoname(vector4(-435.28, -1690.31, 19.03, 340), 4.00, 0.8, {
  minZ = 16.23,
  maxZ = 20.23
}, {
  options = {
  --[[ {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Power Bank',

      item_id = 'powerbank',

      business_name = 'Reapers Scrapyard',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:makeAluminumSheet',
      icon = 'fa-regular fa-hammer-crash',
      label = 'Make Aluminum Sheets',

      groups = {
        'Reapers Scrapyard',
        'LEO',
      }
    },
    {
      event_server = 'core:server:target-backhaul:makePlate',
      icon = 'fa-regular fa-hammer-crash',
      label = 'Manufacture License Plate',

      groups = {
        'Reapers Scrapyard',
        'LEO',
      }
    }, ]]
  },
  distance = 3.0,
})

AddBoxZoneAutoname(vector4(-421.71, -1722.4, 19.43, 345), 2.4, 4.8, {
  minZ = 18.23,
  maxZ = 20.43,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openScrapyardTrashInput',
      icon = 'fa-regular fa-inbox-in',
      label = 'Open Input',
    },
    {
      event_server = 'core:server:target-backhaul:openScrapyardTrashOutput',
      icon = 'fa-regular fa-inbox-out',
      label = 'Open Output',

      groups = {
        'Reapers Scrapyard|extra_a',
      },
    },
  },
  distance = 3.0,
})

-------------------------
---- MATERIAL EXPORT ----
-------------------------

AddBoxZone('ScrapyardExport1', vector3(1736.215, 3330.231, 40.21017), 2.9, 2.9, {
  name = 'ScrapyardExport1',
  heading = 15.0,
  minZ = 40.2,
  maxZ = 42.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:sellToDistributor',
      icon = 'fa-regular fa-box-check',
      label = 'Export Materials',

      groups = {
        'Reapers Scrapyard',
      },
    },
  },
  distance = 3.5
})

AddBoxZone('ScrapyardExport2', vector3(2146.547, 4777.9, 39.97714), 2.9, 2.9, {
  name = 'ScrapyardExport2',
  heading = 15.0,
  minZ = 39.9,
  maxZ = 42.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:sellToDistributor',
      icon = 'fa-regular fa-box-check',
      label = 'Export Materials',

      groups = {
        'Reapers Scrapyard',
      },
    },
  },
  distance = 3.5
})

AddBoxZone('ScrapyardExport3', vector3(3805.014, 4442.641, 3.051531), 2.9, 2.9, {
  name = 'ScrapyardExport3',
  heading = 15.0,
  minZ = 3.05,
  maxZ = 5.05,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:sellToDistributor',
      icon = 'fa-regular fa-box-check',
      label = 'Export Materials',

      groups = {
        'Reapers Scrapyard',
      },
    },
  },
  distance = 3.5
})

AddBoxZone('ScrapyardExport4', vector3(-98.0617752, 6499.332, 30.4293442), 2.9, 2.9, {
  name = 'ScrapyardExport4',
  heading = 60.0,
  minZ = 30.4,
  maxZ = 33.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:sellToDistributor',
      icon = 'fa-regular fa-box-check',
      label = 'Export Materials',

      groups = {
        'Reapers Scrapyard',
      },
    },
  },
  distance = 3.5
})

AddBoxZone('ScrapyardExport5', vector3(1181.85034, -2997.16479, 4.889572), 2.9, 8.0, {
  name = 'ScrapyardExport5',
  heading = 357.0,
  minZ = 4.88,
  maxZ = 7.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:sellToDistributor',
      icon = 'fa-regular fa-box-check',
      label = 'Export Materials',

      groups = {
        'Reapers Scrapyard',
      },
    },
  },
  distance = 9.0
})
AddBoxZone('ReapersRepair', vector3(-512.21, -1744.8, 19.41), 0.4, 0.6, {
  name = 'ReapersRepair',
  heading = 330.0,
  minZ = 19.21,
  maxZ = 19.61,
}, {
  options = {
    {
      event_server = 'core:server:item-durability:useGunRepairTarget',
      icon = 'fa-regular fa-toolbox',
      label = 'Repair Firearms',

      business_name = 'Reapers Scrapyard',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Reapers Scrapyard',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm repair kit',

      item_id = 'tool_wpn_repair',

      business_name = 'Reapers Scrapyard',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})
AddBoxZone('RRepairStorage', vector3(-511.34, -1744.04, 19.41), 1.8, 1, {
  name = 'RRepairStorage',
  heading = 325.0,
  minZ = 18.41,
  maxZ = 19.41,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Reapers Scrapyard',
      component_id = 'storage',
      component_perm = 'crafting',

      args = {
        capacity = 1000.0,
        uid = '4',
      },
    },
  },
  distance = 2.5
})
