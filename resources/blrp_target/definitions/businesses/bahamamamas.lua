AddBoxZone('BahamaManagement', vector3(-1370.541, -625.4091, 30.37849), 0.5, 0.7, {
  name = 'BahamaManagement',
  heading = 32.0,
  minZ = 30.1,
  maxZ = 30.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Bahama Mamas',
      component_id = 'management',
    },
  },
  distance = 2.5
})


AddBoxZone('BahamaFridge1', vector3(-1401.659, -597.4672, 29.99763), 0.7, 1.2, {
  name = 'BahamaFridge1',
  heading = 35.0,
  minZ = 29.3,
  maxZ = 30.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Bahama Mamas',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('BahamaFridge2', vector3(-1403.943, -598.9503, 29.93206), 0.7, 1.2, {
  name = 'BahamaFridge2',
  heading = 35.0,
  minZ = 29.3,
  maxZ = 30.3,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Bahama Mamas',
      component_id = 'storage',

      args = {
        capacity = 20.0,
        uid = 'Fridge2',
        fake_fridge = true,
      },
    },
  },
  distance = 2.5
})

AddBoxZone('BahamaCloset1', vector3(-1376.884, -634.4766, 30.47956), 1.0, 3.2, {
  name = 'BahamaCloset1',
  heading = 123.0,
  minZ = 29.3,
  maxZ = 31.5,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Bahama Mamas',
      }
    },
  },
  distance = 2.5
})


AddBoxZone('BahamaBarRegister', vector3(-1402.832, -602.5172, 30.47711), 0.4, 0.45, {
  name = 'BahamaBarRegister',
  heading = 236,
  minZ = 30.32,
  maxZ = 30.72,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'Bahama Mamas',
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Bahama Mamas',

      groups = {
        'Bahama Mamas'
      }
    },
  },
  distance = 1.5
})
AddBoxZone('BahamaBarRegister2', vector3(-1398.887, -600.1172, 30.4824), 0.4, 0.45, {
  name = 'BahamaBarRegister2',
  heading = 236,
  minZ = 30.32,
  maxZ = 30.72,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',
      groups = {
        'Bahama Mamas',
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Bahama Mamas',

      groups = {
        'Bahama Mamas'
      }
    },
  },
  distance = 1.5
})
AddBoxZone('BahamaBarRegister3', vector3(-1383.71, -592.2605, 30.4337), 0.4, 0.45, {
  name = 'BahamaBarRegister3',
  heading = 234,
  minZ = 30.32,
  maxZ = 30.72,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'Bahama Mamas',
      }
    },
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Bahama Mamas',

      groups = {
        'Bahama Mamas'
      }
    },
  },
  distance = 1.5
})

for k, tray_coords in ipairs({
  vector3(-1398.723, -601.7696, 30.40903),
  vector3(-1400.853, -603.3831, 30.40903),
}) do
  AddBoxZone('BahamaTray' .. k, tray_coords, 0.6, 1.0, {
    name = 'BahamaTray' .. k,
    heading=330,
    minZ=tray_coords.z-0.1,
    maxZ=tray_coords.z+0.2,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Tray',
        table_coords = tray_coords,
      },
    },
    distance = 2.5
  })
end
