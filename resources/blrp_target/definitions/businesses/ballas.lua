disableContext(true)
AddBoxZone('BallasManagement', vector3(-80.45846, -1814.002, 20.60791), 0.4, 0.4, {
  name = 'BallasManagement',
  heading = 255.0,
  minZ = 20.55,
  maxZ = 20.9,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Ballas',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('BallasStorageMain', vector3(2.237962, -1817.59, 29.90414), 0.5, 1.7, {
  name = 'BallasStorageMain',
  heading = 230.0,
  minZ = 28.6,
  maxZ = 30.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Ballas',
      component_id = 'storage',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Ballas',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('BallasStorage2', vector3(-78.34135, -1810.12, 21.53144), 0.8, 1.1, {
  name = 'BallasStorage2',
  heading = 318.0,
  minZ = 19.8,
  maxZ = 21.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Ballas',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '2',
      },
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Ballas',
      }
    },
  },
  distance = 2.5
})


-- AddBoxZone('BallasCrafting', vector3(-75.719, -1813.341, 20.812), 1.2, 1.8, {
--   name = 'BallasCrafting',
--   heading = 240.0,
--   minZ = 20.0,
--   maxZ = 21.2,
-- }, {
--   options = {
--     {
--       event_server = 'core:server:item-durability:useGunRepairTarget',
--       icon = 'fa-regular fa-toolbox',
--       label = 'Repair Firearms',
--
--       business_name = 'Ballas',
--       component_id = 'crafting',
--     },
--     {
--       event_server = 'core:server:item-durability:useGunBreakdown',
--       icon = 'fa-regular fa-toolbox',
--       label = 'Break Down Firearms',
--
--       business_name = 'Ballas',
--       component_id = 'crafting',
--     },
--   },
--   distance = 2.5
-- })

AddBoxZoneAutoname(vector4(-77.8622, -1811.082, 21.04604, 318.0), 0.6, 0.6, {
  minZ = 20.0,
  maxZ = 21.05,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Safe',

      business_name = 'Ballas',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '3',
      },
    },
  },
  distance = 2.5
})


AddBoxZone('BallasFridge', vector3(-2.550149, -1828.229, 26.56887), 0.7, 1.6, {
  name = 'BallasFridge',
  heading = 230.0,
  minZ = 24.3,
  maxZ = 26.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Ballas',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('BallasRegister', vector3(-6.75, -1823.35, 25.34), 0.5, 0.5, {
  heading = 50,
  minZ = 25.34,
  maxZ = 25.74,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'Ballas',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('BallasRegister2', vector3(-8.686812, -1821.769, 25.52817), 0.5, 0.5, {
  heading = 50,
  minZ = 25.34,
  maxZ = 25.74,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'Ballas',
      }
    },
  },
  distance = 1.5
})

AddBoxZone('BallasTray', vector3(-8.33, -1821.93, 25.34), 0.4, 0.4, {
  heading = 320,
  minZ = 25.34,
  maxZ = 25.74,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-solid fa-whiskey-glass-ice',
      label = 'Drink Tray',
      table_coords = vector3(-8.33, -1821.93, 25.34),
    },
  },
  distance = 2.5
})

AddBoxZone('BallasSupply', vector3(2.351423, -1817.375, 25.05812), 0.6, 1.5, {
  heading = 230.0,
  minZ = 24.0,
  maxZ = 26.0,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'ballas-supply',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Ballas',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '4',
      },
    },
  },
  distance = 2.5
})
