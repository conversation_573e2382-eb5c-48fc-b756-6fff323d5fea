--[[
  AddBoxZone('OmertaManagement', vector3(-78.61567, -801.6092, 223.0141), 0.6, 1.0, {
  name = 'OmertaManagement',
  heading = 250.0,
  minZ = 222.9,
  maxZ = 223.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Omerta Advertising',
      component_id = 'management',
    },
  },
  context_disable = true,
  distance = 2.5
})

AddBoxZone('OmertaStorage', vector3(-81.01842, -804.1269, 222.1451), 1.0, 2.1, {
  name = 'OmertaStorage',
  heading = 160.0,
  minZ = 222.0,
  maxZ = 223.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Omerta Advertising',
      component_id = 'storage',
    },
  },
  context_disable = true,
  distance = 2.5
})

AddBoxZone('OmertaCloset', vector3(-77.274, -812.248, 223.145), 1.3, 1.5, {
  name = 'OmertaCloset',
  heading = 250.0,
  minZ = 223.0,
  maxZ = 224.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Omerta Advertising',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('OmertaElevatorUpper', vector3(-76.174, -827.867, 223.145), 6.0, 4.0, {
  name = 'OmertaElevatorUpper',
  heading = 160.0,
  minZ = 222.0,
  maxZ = 225.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Maze Bank Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBankGround',
      icon = 'far fa-sort-circle-down',
      label = 'Maze Bank: ground floor',
    },
  },
  context_disable = true,
  distance = 5.0
})

AddBoxZone('OmertaElevatorLower', vector3(-79.173, -796.581, 44.227), 4.0, 4.0, {
  name = 'OmertaElevatorLower',
  heading = 190.0,
  minZ = 43.0,
  maxZ = 45.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Maze Bank Elevator',
}, {
  options = {
    {
      event_client = 'core:client:teleports:requestFromTarget',
      destination = 'MazeBank46Floor',
      icon = 'far fa-sort-circle-down',
      label = 'Maze Bank: 46th floor',
    },
  },
  context_disable = true,
  distance = 5.0
})
--]]