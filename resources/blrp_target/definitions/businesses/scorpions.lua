disableContext(true)

AddBoxZone('ScorpionsManagement', vector3(-261.1977, 1791.4, 206.1169), 0.5, 0.5, {
  heading = 0.0,
  minZ = 206.1,
  maxZ = 206.5,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Scorpions',
      component_id = 'management',
    },
  },
  distance = 1.5
})
--[[
AddBoxZone('ScorpionsCrafting', vector3(-260.404, 1794.917, 201.373), 1.2, 2.2, {
  heading = 180.0,
  minZ = 201.0,
  maxZ = 202.2,
}, {
  options = {
    [{
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Scorpion Meth',

      item_id = 'scorpionmeth',

      business_name = 'Scorpions',
      component_id = 'craft-scorpionmeth',
      component_perm = 'extra_a',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft America Freshner',

      item_id = 'airfresh_america',

      business_name = 'Scorpions',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Old Man Freshner',

      item_id = 'airfresh_oldman',

      business_name = 'Scorpions',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Sandy Strong Freshner',

      item_id = 'airfresh_sandy',

      business_name = 'Scorpions',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Squeegee',

      item_id = 'mech_squeegee',

      business_name = 'Scorpions',
      component_id = 'crafting',
    },
  },
  distance = 3.0
})
]]
AddBoxZone('ScorpionsCloset', vector3(-254.2164, 1789.922, 205.0611), 1, 0.9, {
  heading = 270.0,
  minZ = 205.0,
  maxZ = 207.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Scorpions',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('ScorpionsCloset2', vector3(-254.0238, 1794.774, 200.9598), 1, 0.9, {
  heading = 0.0,
  minZ = 200.0,
  maxZ = 203.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Scorpions',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('ScorpionsStorageBasement', vector3(-258.6728, 1794.872, 201.5276), 1.3, 2.0, {
  heading = 90.0,
  minZ = 200.9,
  maxZ = 202.2,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Scorpions',
      component_id = 'storage',

      args = {
        capacity = 5000.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('ScorpionsStorageUpper', vector3(-254.1728, 1792.619, 205.7942), 1.0, 1.0, {
  heading = 90.0,
  minZ = 204.9,
  maxZ = 206.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Scorpions',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '3'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('ScorpionsFridge', vector3(-257.201, 1790.413, 205.073), 0.8, 0.8, {
  heading = 270.0,
  minZ = 204.9,
  maxZ = 206.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Scorpions',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddTargetModel({
  `bl_2_rm1_blinds_closed`,
  `bl_2_rm1_blinds_open`,
}, {
  options = {
    {
      event_server = 'core:server:scorpions:toggleBlinds',
      icon = 'fa-regular fa-blinds',
      label = 'Toggle Blinds',
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(-257.749, 1799.270, 202.2, 90.0), 2.5, 1.1, {
  minZ = 200.1,
  maxZ = 202.6,
}, {
  options = generateAdvancedCookingOptions('scorpions_lab1', 'dlf_scorpions', { 'u_deathmeth', 'd_water' }, 'Scorpions',true),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(-260.183, 1799.471, 202.2, 90.0), 2.5, 1.1, {
  minZ = 200.1,
  maxZ = 202.6,
}, {
  options = generateAdvancedCookingOptions('scorpions_lab2', 'dlf_scorpions', { 'u_deathmeth', 'd_water' }, 'Scorpions',true),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(-260.246, 1794.730, 202.2, 90.0), 2.5, 1.1, {
  minZ = 200.1,
  maxZ = 202.6,
}, {
  options = generateAdvancedCookingOptions('scorpions_lab3', 'dlf_scorpions', { 'u_deathmeth', 'd_water' }, 'Scorpions',true),
  distance = 2.0,
})
