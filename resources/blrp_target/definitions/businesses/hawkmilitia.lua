disableContext(true)
AddBoxZone('HawkMilitiaManagement', vector3(1378.921, -2089.554, 52.47906), 0.8, 0.6, {
  name = 'HawkMilitiaManagement',
  heading = 225.0,
  minZ = 52.45,
  maxZ = 52.9,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Hawk Militia',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('HawkMilitiaStorageMain', vector3(1371.357, -2087.123, 48.14337), 0.5, 1.7, {
  name = 'HawkMilitiaStorageMain',
  heading = 230.0,
  minZ = 47.4,
  maxZ = 49.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Hawk Militia',
      component_id = 'storage',
    },
  },
  distance = 2.5
})
AddBoxZone('HawkMilitiaStorage2', vector3(1369.288, -2088.858, 48.3576), 1.0, 2.0,{
  name = "HawkMilitiaStorage2",
  heading = 230.0,
  minZ = 47.4,
  maxZ = 49.0,
},{
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Hawk Militia',
      component_id = 'storage',

      args = {
        capacity = 1000.0,
        uid = '2'
      },
    },
  },
  distance = 2.5
})

AddBoxZone('HawkMilitiaCloset', vector3(1390.929, -2080.617, 53.79598), 1.65, 0.8, {
  name = 'HawkMilitiaCloset',
  heading = 220.0,
  minZ = 51.0,
  maxZ = 53.8,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Hawk Militia',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('HawkMilitiaFridge', vector3(1386.204, -2079.868, 53.51717), 0.9, 0.9, {
  name = 'HawkMilitiaFridge',
  heading = 220.0,
  minZ = 51.6,
  maxZ = 53.51,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Hawk Militia',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

AddBoxZone('HawkMilitiaCrafting', vector3(1362.357, -2092.125, 47.40568), 0.8, 1.0, {
  name = 'HawkMilitiaCrafting',
  heading = 315.0,
  minZ = 47.0,
  maxZ = 47.9,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Hawk 6 Pack',

      item_id = 'hawk_6pack',

      business_name = 'Hawk Militia',
      component_id = 'craft-hawk_6pack',
      component_perm = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Molotov',

      item_id = 'unlit_molotov',

      business_name = 'Hawk Militia',
      component_id = 'craft-unlit_molotov',
      component_perm = 'crafting',
    },
--[[    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Thermite',

      item_id = 'thermite_charge',

      business_name = 'Hawk Militia',
      component_id = 'craft-thermite_charge',
      component_perm = 'crafting',
    },]]
    -- {
    --   event_server = 'core:server:target-backhaul:craft',
    --   icon = 'fa-regular fa-wrench',
    --   label = 'Craft 7.62x54 Ammo (20)',

    --   item_id = 'ammo_762x54',

    --   business_name = 'Hawk Militia',
    --   component_id = 'crafting',
    -- },
    -- {
    --   event_server = 'core:server:target-backhaul:craft',
    --   icon = 'fa-regular fa-wrench',
    --   label = 'Craft PKM',

    --   item_id = 'wbody|WEAPON_MG',

    --   business_name = 'Hawk Militia',
    --   component_id = 'crafting',
    -- },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft 12 gauge ammo (20)',

      item_id = 'ammo_12ga',

      business_name = 'Hawk Militia',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Double Barrel Shotgun',

      item_id = 'wbody|WEAPON_DBSHOTGUN',

      business_name = 'Hawk Militia',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Firearm Body',

      item_id = 'firearm_body',

      business_name = 'Hawk Militia',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:item-durability:useGunBreakdown',
      icon = 'fa-regular fa-toolbox',
      label = 'Break Down Firearms',

      business_name = 'Hawk Militia',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Large Storage Crate',

      item_id = 'bl_prop_gunbox',

      business_name = 'Hawk Militia',
      component_id = 'crafting',
      component_perm = 'crafting',
    },
  },
  distance = 2.5
})
