AddBoxZone('Coldstray', vector3(1960.78, 3740.95, 32.34), 0.6, 1.2, {
  name = 'Coldstray',
  heading = 30,
  minZ = 31.34,
  maxZ = 32.54,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-tables:openTarget',
      icon = 'fa-regular fa-spray-can',
      label = 'Paint Tray',
      table_coords = vector3(1960.78, 3740.95, 32.34),
    },
  },
  distance = 2.5
})

AddBoxZone('ColdsRegister', vector3(1960.15, 3741.91, 32.34), 0.4, 0.4, {
  name = 'ColdsRegister',
  heading = 35,
  minZ = 32.34,
  maxZ = 32.74,
}, {
  options = {
    {
      event_server = 'core:server:restaurant-general:cashRegister',
      icon = 'fa-regular fa-dollar-sign',
      label = 'Use Register',

      groups = {
        'Joes Corner',
      }
    },
  },
  distance = 2.5
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-solid fa-bottle-droplet',
  label = 'Craft Homemade Paint Tinner',

  item_id = 'paint_thinner_hm',

  business_name = 'Joes Corner',
  component_id = 'craft-paint_thinner',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-solid fa-can-food',
  label = 'Craft Empty Spray Can',

  item_id = 'empty_spraycan',

  business_name = 'Joes Corner',
  component_id = 'craft-empty_spraycan',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Rag',

  item_id = 'rag',

  business_name = 'Joes Corner',
  component_id = 'craft-rag',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:spraycan-breakdown:useSpraycanBreakdown',
  icon = 'fa-regular fa-toolbox',
  label = 'Break Down Spray can',

  business_name = 'Joes Corner',
  component_id = 'break-down-can',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Black Spraypaint',

  item_id = 'spraypaint_black',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_black',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft White Spraypaint',

  item_id = 'spraypaint_white',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_white',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Red Spraypaint',

  item_id = 'spraypaint_red',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_red',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Orange Spraypaint',

  item_id = 'spraypaint_orange',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_orange',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Yellow Spraypaint',

  item_id = 'spraypaint_yellow',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_yellow',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Green Spraypaint',

  item_id = 'spraypaint_green',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_green',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Blue Spraypaint',

  item_id = 'spraypaint_blue',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_blue',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft Purple Spraypaint',

  item_id = 'spraypaint_purple',

  business_name = 'Joes Corner',
  component_id = 'craft-spraypaint_purple',
  component_perm = 'crafting',
})

AddGangBenchOption('Joes Corner', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-spray-can',
  label = 'Craft CNS Stencil',

  item_id = 'stencil_cns',

  business_name = 'Joes Corner',
  component_id = 'craft-stencil-cns',
  component_perm = 'crafting',
})
