AddBoxZone('DownbadManagement', vector3(-301.619, -1070.952, 27.00227), 0.8, 1.0, {
  name = 'DownbadManagement',
  heading = 160.0,
  minZ = 27.0,
  maxZ = 27.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Downbad Apartments',
      component_id = 'management',
    },
  },
  distance = 2.0
})

AddBoxZone('DownbadStorage', vector3(-296.0349, -1072.905, 27.10858), 1.2, 2.0, {
  name = 'DownbadStorage',
  heading = 160.0,
  minZ = 26.0,
  maxZ = 28.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Downbad Apartments',
      component_id = 'storage',
    },
  },
  distance = 3.0
})

AddBoxZone('DownbadFridge', vector3(-299.2037, -1068.45, 28.00737), 1.0, 1.0, {
  name = 'DownbadFridge',
  heading = 160.0,
  minZ = 26.0,
  maxZ = 28.0,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Downbad Apartments',
      component_id = 'fridge',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-utensils',
      label = 'Security Canteen',

      uid = 'canteen-dbsec',

      groups = {
        'Downbad Apartments',
      }
    },
  },
  distance = 3.0
})

AddBoxZone('DownbadClockIn', vector3(-296.7268, -1065.42, 27.39389), 0.6, 0.8, {
  name = 'DownbadClockIn',
  heading = 290.0,
  minZ = 27.0,
  maxZ = 27.9,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Security',

      icon = 'fas fa-clock',
      label = 'Clock in - Security',

      groups = {
        'Downbad Apartments'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',

      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 2.0,
})
AddBoxZone('DownbadSupply', vector3(-295.6836, -1073.367, 27.49619), 1.0,2.0,{
  name = 'DownbadSupply',
  heading = 30.0,
  minZ = 26.0,
  maxZ = 27.7,
},{
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-walkie-talkie',
      label = 'Security Supply',

      uid = 'dbsecurity-supply',

      groups = {
        'Security',
      }
    },
  },
  distance=2.0
})

AddBoxZone('DownbadCloset', vector3(-296.1859, -1069.745, 28.02829), 0.6, 1.4, {
  name = 'DownbadClockIn',
  heading = 70.0,
  minZ = 27.0,
  maxZ = 28.2,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'Security Uniform Wardrobe',
      cloakroom_name = 'DownbadSecurity',

      groups = {
        'Security',
      }
    },

    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Downbad Apartments',
      }
    },
  },
  distance = 2.0,
})
