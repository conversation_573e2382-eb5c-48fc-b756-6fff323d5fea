
AddGangBenchOption('Vynchenzo\'s Distillery', {
    event_server = 'core:server:restaurants:transformFood',
    icon = 'fa-regular fa-fire-burner',
    label = 'Make Vynchenzos Wine',

    food_item_id = 'v_wine',

    groups = {
      'Vynchenzo\'s Distillery'
    },
})

AddGangBenchOption('Vynchenzo\'s Distillery', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Wine Opener',

  item_id = 'vinny_wopener',

  business_name = 'Vynchenzo\'s Distillery',
  component_id = 'crafting',
})

AddGangBenchOption('Vynchenzo\'s Distillery', {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Craft Wine Box',

  item_id = 'vinny_winebox',

  business_name = 'Vynchenzo\'s Distillery',
  component_id = 'crafting',
})