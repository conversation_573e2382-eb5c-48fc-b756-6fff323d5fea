disableContext(true)

AddBoxZoneAutoname(vector4(337.6327, -1980.665, 24.02454, 115.0), 0.5, 0.5, {
  minZ = 24.0,
  maxZ = 24.6,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Vagos',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZoneAutoname(vector4(325.7485, -2000.62, 25.32838, 140.0), 0.75, 4.0, {
  minZ = 23.0,
  maxZ = 25.35,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vagos',
      component_id = 'storage',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Vagos',
      }
    },
  },
  distance = 3.0
})

AddBoxZoneAutoname(vector4(338.39, -1989.28, 24.21, 320.0), 1.0, 0.6, {
  minZ = 23.21,
  maxZ = 24.41,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vagos',
      component_id = 'storage',

      args = {
        capacity = 500.0,
        uid = '3'
      },
    },
  },
  distance = 3.0
})

AddBoxZoneAutoname(vector4(473.13, -1890.21, 26.09, 25), 0.8, 0.4, {
  minZ = 25.89,
  maxZ = 26.29
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Bar',

      business_name = 'Vagos',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 20.0,
        uid = '5'
      },
    },
  },
  distance = 3.0
})

AddBoxZoneAutoname(vector4(474.31, -1896.94, 22.41, 25.0), 0.8, 1.4, {
  minZ = 21.41,
  maxZ = 24.01,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vagos',
      component_id = 'storage',
      component_perm = 'crafting',

      args = {
        capacity = 2500.0,
        uid = '2'
      },
    },
  },
  distance = 3.0
})

AddBoxZoneAutoname(vector4(475.03, -1894.18, 22.41, 25.0), 1.8, 0.6, {
  minZ = 22.21,
  maxZ = 22.61,
 }, {
   options = {
     {
       event_server = 'core:server:item-durability:useGunBreakdown',
       icon = 'fa-regular fa-toolbox',
       label = 'Break Down Firearms',

       business_name = 'Vagos',
       component_id = 'crafting',
       component_perm = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft 9mm Ammo (20)',

       item_id = 'ammo_9mm',

       business_name = 'Vagos',
       component_id = 'crafting',
       component_perm = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft Uzi',

       item_id = 'wbody|WEAPON_MICROSMG',
       weekly_craft_limit = 40,
       business_name = 'Vagos',
       component_id = 'crafting',
       component_perm = 'crafting',
     },
     {
       event_server = 'core:server:target-backhaul:craft',
       icon = 'fa-regular fa-wrench',
       label = 'Craft Firearm Body',

       item_id = 'firearm_body',

       business_name = 'Vagos',
       component_id = 'crafting',
      component_perm = 'crafting',
    },
  },
  distance = 3.0
})

AddBoxZoneAutoname(vector4(477.91, -1876.28, 26.09, 25.0), 0.4, 1.4, {
  minZ = 25.09,
  maxZ = 26.89,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Vagos',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Vagos',
      component_id = 'storage',
      component_perm = 'extra_b',

      args = {
        capacity = 50.0,
        uid = '4'
      },
    },
  },
  distance = 3.0
})

AddBoxZoneAutoname(vector4(480.96, -1876.67, 26.09, 20.0), 0.2, 1.2, {
  minZ = 25.89,
  maxZ = 26.49,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Vagos',
      component_id = 'management',
    },
  },
  distance = 3.0
})

--[[
AddBoxZoneAutoname(vector4(968.472, -1849.741, 31.614, 90.0), 2.5, 1.1, {
  minZ = 31.0,
  maxZ = 32.2,
}, {
  options = generateAdvancedCookingOptions('vagos_lab1', 'dlf_vagos', { 'u_heroin', 'd_water' }, 'Vagos',true),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(965.973, -1849.626, 31.614, 90.0), 2.5, 1.1, {
  minZ = 31.0,
  maxZ = 32.2,
}, {
  options = generateAdvancedCookingOptions('vagos_lab2', 'dlf_vagos', { 'u_heroin', 'd_water' }, 'Vagos',true),
  distance = 2.0,
})

AddBoxZoneAutoname(vector4(968.847, -1853.434, 31.614, 90.0), 2.5, 1.1, {
  minZ = 31.0,
  maxZ = 32.2,
}, {
  options = generateAdvancedCookingOptions('vagos_lab3', 'dlf_vagos', { 'u_heroin', 'd_water' }, 'Vagos',true),
  distance = 2.0,
})
]]
