--Managment Comp
AddBoxZoneAutoname(vector4(-26.69, -1104.9, 27.27, 343), 0.6, 0.5, {
  minZ = 27.17,
  maxZ = 27.77
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'PDM Auto',
      component_id = 'management',
    },
  },
  distance = 2.0
})

--Storage
AddBoxZoneAutoname(vector4(-29.52, -1109.91, 27.27, 340), 0.6, 2.6, {
  minZ = 26.27,
  maxZ = 28.37
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Supplies',

      uid = 'pdm-supply',
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'PDM Auto',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

--Fridge
AddBoxZoneAutoname(vector4(-27.4, -1087.72, 27.27, 340),  0.8, 0.75, {
  minZ = 26.22,
  maxZ = 28.17
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'PDM Auto',
      component_id = 'fridge',
    },
  },
  distance = 2.5
})

--Closet
AddBoxZoneAutoname(vector4(-26.08, -1110.06, 27.27, 340),  1.55, 1.05, {
  minZ = 26.27,
  maxZ = 28.37
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'PDM Auto',
      }
    },
  },
  distance = 2.0
})

--Panic Button
AddBoxZoneAutoname(vector4(-31.83, -1097.61, 27.27, 340),  5.2, 0.5, {
  minZ = 26.12,
  maxZ = 27.22
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:activatePanicAlarm',
      icon = 'far fa-siren-on',
      label = 'Activate Silent Alarm',

      location_override = 'Premium Deluxe Motorsport',

      groups = {
        'PDM Auto'
      }
    }
  },

  distance = 3.0,
  context_disable = true
})