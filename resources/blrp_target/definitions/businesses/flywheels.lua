AddBoxZone('FlywheelsManagement', vector3(1772.903, 3322.726, 41.24653), 0.5, 0.7, {
  name = 'FlywheelsManagement',
  heading = 240.0,
  minZ = 41.2,
  maxZ = 41.8,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Flywheels',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('FlywheelsStorageMain', vector3(1777.029, 3322.267, 41.40915), 0.5, 1.05, {
  name = 'FlywheelsStorageMain',
  heading = 210.0,
  minZ = 40.4,
  maxZ = 42.4,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = 'Flywheels',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddBoxZone('FlywheelsFridge', vector3(1764.776, 3322.539, 41.02886), 0.8, 1.8, {
  name = 'FlywheelsFridge',
  heading = 300.0,
  minZ = 40.7,
  maxZ = 41.45,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-regular fa-shelves',
      label = 'Parts Supply',

      uid = 'mechanic-parts',

      groups = {
        'Flywheels',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-refrigerator',
      label = 'Fridge',

      business_name = 'Flywheels',
      component_id = 'fridge',
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Flywheels',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('FlywheelsCrafting', vector3(1768.151, 3335.282, 41.40283), 0.8, 1.8, {
  name = 'FlywheelsCrafting',
  heading = 210.0,
  minZ = 40.7,
  maxZ = 41.45,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Body Fixer',

      item_id = 'mech_body_fixer',

      business_name = 'Flywheels',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Diagnose Tool',

      item_id = 'mech_diagnose_tool',

      business_name = 'Flywheels',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Engine Wrench',

      item_id = 'mech_engine_wrench',

      business_name = 'Flywheels',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Fuel Tools',

      item_id = 'mech_fuel_tools',

      business_name = 'Flywheels',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Repair Parts (25)',

      item_id = 'repair_parts',

      business_name = 'Flywheels',
      component_id = 'crafting',
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Squeegee',

      item_id = 'mech_squeegee',

      business_name = 'Flywheels',
      component_id = 'crafting',
    },
  },
  distance = 2.5
})
