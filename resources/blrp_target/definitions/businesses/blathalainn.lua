AddBoxZone('BAManagement', vector3(4922.264, -5296.02, 5.519324), 0.5, 0.5, {
  heading = 90.0,
  minZ = 5.5,
  maxZ = 5.7,
}, {
  options = {
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-computer-classic',
      label = 'Management',

      business_name = 'Blath Alainn Charity',
      component_id = 'management',
    },
  },
  distance = 2.5
})

AddBoxZone('BALocker', vector3(4926.16943, -5298.597, 4.67773438), 1.0, 1.7, {
  heading = 0.0,
  minZ = 4.5,
  maxZ = 7.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'fa-regular fa-tshirt',
      label = 'Personal Wardrobe',

      groups = {
        'Blath Alainn Charity',
      }
    },
    {
      event_server = 'core:server:businesses:custom:targetInteract',
      icon = 'fa-regular fa-box',
      label = 'Storage',

      business_name = '<PERSON>lath <PERSON>',
      component_id = 'storage',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'Shop',

      uid = 'c24-blath',

      business_name = 'Blath Alainn Charity',
      component_id = 'storage',
    },
  },
  distance = 2.5
})

AddGangBenchOption('Blath Alainn Charity', 
  {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Blath Alainn Welcome Bag',
  
  item_id = 'ba_giftbag_a',
  
  business_name = 'Blath Alainn Charity',
  component_id = 'crafting',
  })
AddGangBenchOption('Blath Alainn Charity', 
  {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-wrench',
  label = 'Blath Alainn Event Bag',
  
  item_id = 'ba_giftbag_b',
  
  business_name = 'Blath Alainn Charity',
  component_id = 'crafting',
  })
AddGangBenchOption('Blath Alainn Charity', 
  {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-cup-straw',
  label = 'Prepare Blath Ade',
  
  item_id = 'drink_ba_energy',
  
  business_name = 'Blath Alainn Charity',
  component_id = 'crafting',
  })
AddGangBenchOption('Blath Alainn Charity', 
  {
  event_server = 'core:server:target-backhaul:craft',
  icon = 'fa-regular fa-cup-straw',
  label = 'Prepare BA Energy Bar',
  
  item_id = 'food_ba_energy',
  
  business_name = 'Blath Alainn Charity',
  component_id = 'crafting',
  })

for k, d in pairs({
  vector4(4928.689, -5288.213, 4.672724, -75),
  vector4(4928.863, -5284.922, 4.672724, -100),
  vector4(4921.739, -5288.505, 4.672724, 90),
  vector4(4921.888, -5281.923, 4.672724, 105),
}) do
  AddBoxZone('BABedside' .. k, d.xyz, 1.0, 1.5, {
    heading = d.w,
    minZ = 4.6,
    maxZ = 5.8,
  }, {
    options = {
      {
        event_server = 'core:server:businesses:custom:targetInteract',
        icon = 'fa-regular fa-box',
        label = 'Storage',

        business_name = 'Blath Alainn Charity',
        component_id = 'storage',

        args = {
          capacity = 250.0,
          named = 'ba_bedside_' .. k,
        },
      },
    },
    distance = 2.5
  })
end
