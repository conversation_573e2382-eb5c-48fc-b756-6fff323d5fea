local units = {}

-- Populate programmatically
for _, hookies_coord in ipairs({
  vector3(-2202.867, 4244.313, 48.6511),
  vector3(-2205.301, 4242.457, 48.6511),
  vector3(-2207.758, 4240.582, 48.6511),
  vector3(-2209.111, 4237.254, 48.2840),
  vector3(-2211.565, 4235.376, 48.2840),
  vector3(-2214.010, 4233.505, 48.2840),
  vector3(-2215.598, 4230.451, 47.9636),
  vector3(-2218.046, 4228.579, 47.9636),
  vector3(-2220.498, 4226.703, 47.9636),
}) do
  table.insert(units, {
    coords = hookies_coord,
    length = 0.15,
    width = 2.8,
    heading = -142.257,
    minZ = hookies_coord.z - 1.3511,
    maxZ = hookies_coord.z + 1.2489,
    distance = 2.0,
    location_code = 'hookies',
  })
end

for _, strawberry_data in ipairs({
  { coords = vector3(-73.23759, -1196.777, 28.15783), heading = -1.288   },
  { coords = vector3(-67.06271, -1199.142, 28.16379), heading = -45.154  },
  { coords = vector3(-61.46716, -1204.748, 28.65221), heading = -45.154  },
  { coords = vector3(-56.13080, -1210.094, 28.94939), heading = -45.154  },
  { coords = vector3(-52.67406, -1216.365, 29.07335), heading = -90.795  },
  { coords = vector3(-56.30498, -1229.208, 28.92285), heading = -133.867 },
  { coords = vector3(-60.62626, -1233.856, 28.9728),  heading = -133.867 },
  { coords = vector3(-65.80644, -1239.427, 29.09918), heading = -133.867 },
  { coords = vector3(-73.78841, -1243.309, 29.23024), heading = -179.867 },
  { coords = vector3(-72.60667, -1233.612, 29.10971), heading = -128.867 },
  { coords = vector3(-67.06976, -1226.705, 28.94390), heading = -128.867 },
  { coords = vector3(-65.92917, -1211.952, 28.51159), heading = -45.154  },
  { coords = vector3(-71.19843, -1206.621, 28.14752), heading = -45.154  },
  { coords = vector3(-78.54877, -1204.434, 27.83089), heading = -1.288   },
}) do
  table.insert(units, {
    coords = strawberry_data.coords,
    length = 0.15,
    width = 6.0,
    heading = strawberry_data.heading,
    minZ = strawberry_data.coords.z - 1.5,
    maxZ = strawberry_data.coords.z + 2.5,
    distance = 2.0,
    location_code = 'strawberry',
  })
end

for _, sandy_data in ipairs({
  { coords = vector3(1732.019, 3707.271, 34.65075), heading = 200.0 },
  { coords = vector3(1726.991, 3705.443, 34.60389), heading = 200.0 },
}) do
  table.insert(units, {
    coords = sandy_data.coords,
    length = 0.15,
    width = 6.0,
    heading = sandy_data.heading,
    minZ = sandy_data.coords.z - 1.5,
    maxZ = sandy_data.coords.z + 2.5,
    distance = 2.0,
    location_code = 'sandy',
  })
end

-- Register programmatically
for unit_key, unit_data in ipairs(units) do
  AddBoxZone('StorageUnit' .. unit_key, unit_data.coords, unit_data.length, unit_data.width, {
    name = 'StorageUnit' .. unit_key,
    heading = unit_data.heading,
    minZ = unit_data.minZ,
    maxZ = unit_data.maxZ,
  }, {
    options = {
      {
        event_server = 'core:server:storage-units:openUnit',
        icon = 'far fa-warehouse',
        label = 'Open Storage',

        location_code = unit_data.location_code,
      },
    },
    distance = unit_data.distance
  })
end
