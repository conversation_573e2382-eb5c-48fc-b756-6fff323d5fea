AddBoxZone('VotingLocationLosSantos1', vector3(-542.6521, -181.2715, 38.33108), 1.0, 5.0, {
  name = 'VotingLocationLosSantos1',
  heading = 300.0,
  minZ = 38.0,
  maxZ = 40.0,
}, {
  options = {

    {
      event_client = 'blrp_voting:client:openElectionBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in San Andreas General Election',
    },


    --[[
    {
      event_client = 'blrp_voting:client:openReferendumBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in Utopia Gardens Referendum',
    },
    ]]
  },
  distance = 5.0,
  context_disable = true,
})

AddBoxZone('VotingLocationLosSantos2', vector3(-533.2562, -175.7345, 38.3273), 1.0, 5.0, {
  name = 'VotingLocationLosSantos2',
  heading = 300.0,
  minZ = 38.2,
  maxZ = 38.6,
}, {
  options = {

    {
      event_client = 'blrp_voting:client:openElectionBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in San Andreas General Election',
    },


    --[[
    {
      event_client = 'blrp_voting:client:openReferendumBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in Utopia Gardens Referendum',
    },
    ]]
  },
  distance = 5.0,
  context_disable = true,
})

AddBoxZone('VotingLocationSandyShores', vector3(1695.569, 3780.918, 33.74501), 1.2, 2.5, {
  name = 'VotingLocationSandyShores',
  heading = 215.0,
  minZ = 34.0,
  maxZ = 35.0,
}, {
  options = {
    {
      event_client = 'blrp_voting:client:openElectionBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in San Andreas General Election',
    },
    {
      event_client = 'blrp_voting:client:openSheriffBallot',
      icon = 'fa-regular fa-check-to-slot',
      label = 'Vote in Sheriff Election',
    },
  },
  distance = 5.0,
  context_disable = true,
})

-- AddBoxZone('VotingLocationMusicAwards', vector3(-297.3062, -1918.576, 30.03069), 1.0, 5.0, {
--   name = 'VotingLocationMusicAwards',
--   heading = 230.0,
--   minZ = 30.0,
--   maxZ = 31.3,
-- }, {
--   options = {
--     {
--       event_client = 'blrp_voting:client:openMusicBallot',
--       icon = 'fa-regular fa-check-to-slot',
--       label = 'Vote in Los Santos Music Awards',
--     },
--
--   },
--   distance = 5.0,
--   context_disable = true,
-- })
--
-- AddBoxZone('VotingLocationMusicAwards2', vector3(-279.7024, -1933.3, 30.03069), 1.0, 5.0, {
--   name = 'VotingLocationMusicAwards2',
--   heading = 230.0,
--   minZ = 30.0,
--   maxZ = 31.3,
-- }, {
--   options = {
--     {
--       event_client = 'blrp_voting:client:openMusicBallot',
--       icon = 'fa-regular fa-check-to-slot',
--       label = 'Vote in Los Santos Music Awards',
--     },
--
--   },
--   distance = 5.0,
--   context_disable = true,
-- })
