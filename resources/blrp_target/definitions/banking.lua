local banks = {
  { coords = vector3(-108.954, 6469.831, 31.634),    length = 1.5, width = 5.0,  heading = 45.0     }, -- BCSB
  { coords = vector3(247.6436, 223.7642, 106.8046),  length = 3.0, width = 15.0, heading = -20.003  }, -- Central Bank
  { coords = vector3(6054.512, -5249.041, 86.08332), length = 3.0, width = 6.5,  heading = 353.0,    }, -- Lawton Junction
}

for bank_id, bank in ipairs(banks) do
  AddBoxZone('Bank' .. bank_id, bank.coords, bank.length, bank.width, {
    name = 'Bank' .. bank_id,
    heading = bank.heading,
    minZ = bank.coords.z - 1.0,
    maxZ = bank.coords.z + 1.0,
    notifyText = '<i class="fa-regular fa-eye"></i> Banking',
  }, {
    options = {
      {
        event_server = 'blrp_banking:server:open',
        icon = 'fas fa-dollar-sign',
        label = 'Access Bank',
        access_type = 'bank',

        filter = function(ray_intersect_coords)
          if #(GetEntityCoords(PlayerPedId()) - ray_intersect_coords) > 2.5 then
            return false
          end

          return true
        end,
      },
    },
    distance = 15.0,
  })
end

local function banking()
  return {
    {
      event_server = 'blrp_banking:server:open',
      icon = 'fas fa-dollar-sign',
      label = 'Access Bank',
      access_type = 'bank',

      filter = function(ray_intersect_coords)
        if #(GetEntityCoords(PlayerPedId()) - ray_intersect_coords) > 2.5 then
          return false
        end

        return true
      end,
    },
  }
end


-- Legion Square
AddBoxZoneAutoname(vector4(149.0366, -1041.024, 30.17886,  159.0), 3.0, 6.5, {
   minZ = 30.17886 - 1.0,
  maxZ = 30.17886 + 1.0,
  notifyText = '<i class="fa-regular fa-eye"></i> Banking',
}, {
  options = banking(),

  distance = 15.0,
})

--- Alta
AddBoxZoneAutoname(vector4(313.7, -278.8, 54.17, 340), 1.4, 5.8, {
  minZ = 54.05,
  maxZ = 55.57,
  notifyText = '<i class="fa-regular fa-eye"></i> Banking',
}, {
  options = banking(),

  distance = 15.0,
})

-- Burton
AddBoxZoneAutoname(vector4(-351.37, -49.64, 49.04, 341), 1.4, 5.8, {
  minZ = 48.84,
  maxZ = 50.84,
  notifyText = '<i class="fa-regular fa-eye"></i> Banking',
}, {
  options = banking(),

  distance = 15.0,
})


-- Rockford Hills
AddBoxZoneAutoname(vector4(-1213.07, -330.83, 37.78, 27), 1.4, 5.8, {
  minZ = 37.58,
  maxZ = 39.58,
  notifyText = '<i class="fa-regular fa-eye"></i> Banking',
}, {
  options = banking(),

  distance = 15.0,
})

-- Chumash
AddBoxZoneAutoname(vector4(-2962.71, 482.36, 15.7, 88), 1.4, 5.8, {
  minZ = 15.5,
  maxZ = 17.5,
  notifyText = '<i class="fa-regular fa-eye"></i> Banking',
}, {
  options = banking(),

  distance = 15.0,
})


-- Harmony
AddBoxZoneAutoname(vector4(1175.49, 2706.68, 38.09, 0), 1.4, 5.8, {
  minZ = 37.89,
  maxZ = 39.89,
  notifyText = '<i class="fa-regular fa-eye"></i> Banking',
}, {
  options = banking(),

  distance = 15.0,
})


-- East Joshua Rd
AddBoxZoneAutoname(vector4(2452.67, 4060.7, 38.06, 67), 1.4, 5.8, {
  minZ = 37.86,
  maxZ = 39.86,
  notifyText = '<i class="fa-regular fa-eye"></i> Banking',
}, {
  options = banking(),

  distance = 15.0,
})


