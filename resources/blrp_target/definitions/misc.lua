AddBoxZone('OrganDonationOptions', vector3(-532.666, -194.4584, 47.22575), 1.2, 2.7, {
  name = 'OrganDonationOptions',
  heading = -57.850,
  minZ = 46.000,
  maxZ = 48.000,
}, {
  options = {
    {
      event_server = 'core:server:id-cards:changeOrganDonationStatus',
      icon = 'far fa-check',
      label = 'Opt into organ donation',

      is_donor = true,
    },
    {
      event_server = 'core:server:id-cards:changeOrganDonationStatus',
      icon = 'far fa-times',
      label = 'Opt out of organ donation',

      is_donor = false,
    },
  },
  distance = 3.0
})

AddBoxZone('Route68LiquorBulletinBoard', vector3(1165.272, 2703.32, 38.72614), 0.25, 1.0, {
  name = 'Route68LiquorBulletinBoard',
  heading = -3.24,
  minZ = 38.0,
  maxZ = 39.3,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openBulletinBoard',
      icon = 'far fa-clipboard',
      label = 'Bulletin Board',
      board_id = 'rte68-liquor'
    }
  },
  distance = 2.0
})

AddBoxZone('LSDWPCardSpot', vector3(-1403.018, -1008.843, 22.37654), 0.5, 0.5, {
  name = 'LSDWPCardSpot',
  heading = 35.0,
  minZ = 22.3,
  maxZ = 22.6,
}, {
  options = {
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request LSDWP ID Card',
      card_type = 'id_lsdwp',
    },
  },
  distance = 1.5
})

local radios = {
  ['RADIO_01_CLASS_ROCK'] = 'Los Santos Rock Radio',
  ['RADIO_02_POP'] = 'Non Stop Pop FM',
  ['RADIO_03_HIPHOP_NEW'] = 'Radio Los Santos',
  ['RADIO_04_PUNK'] = 'Channel X',
  ['RADIO_05_TALK_01'] = 'West Coast Talk Radio',
  ['RADIO_06_COUNTRY'] = 'Rebel Radio',
  ['RADIO_07_DANCE_01'] = 'Soulwax FM',
  ['RADIO_08_MEXICAN'] = 'East Los FM',
  ['RADIO_09_HIPHOP_OLD'] = 'West Coast Classics',
  ['RADIO_12_REGGAE'] = 'Blue Ark',
  ['RADIO_13_JAZZ'] = 'Worldwide FM',
  ['RADIO_14_DANCE_02'] = 'FlyLo FM',
  ['RADIO_15_MOTOWN'] = 'The Lowdown 91.1',
  ['RADIO_20_THELAB'] = 'The Lab',
  ['RADIO_16_SILVERLAKE'] = 'Radio Mirror Park',
  ['RADIO_17_FUNK'] = 'Space 103.2',
  ['RADIO_18_90S_ROCK'] = 'Vinewood Boulevard Radio',
  ['RADIO_21_DLC_XM17'] = 'Los Santos 97.8 FM',
  ['RADIO_11_TALK_02'] = 'Blaine County Radio',
  ['RADIO_22_DLC_BATTLE_MIX1_RADIO'] = 'Los Santos Underground Radio',
}

local radio_options = {}

for radio_name, radio_alias in pairs(radios) do
  table.insert(radio_options, {
    event_server = 'core:server:target-backhaul:broadcastRadioChannelChange',
    icon = 'far fa-radio',
    label = radio_alias,
    radio_name = radio_name
  })
end

for _,event_trays in pairs({
{ name='harmonychurch1', position=vector3(-291.199, 2765.77, 64.7035)},
{ name='harmonychurch2', position=vector3(-288.0038, 2763.786, 64.7035)},
{ name='harmonychurch3', position=vector3(-284.8458, 2761.738, 64.7035)}
}) do

  AddBoxZone(event_trays.name, event_trays.position, 1.0, 1.0, {
    heading=90,
    minZ = event_trays.position.z - 0.5,
    maxZ = event_trays.position.z + 0.5,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-utensils',
        label = 'Tray',
        table_coords = event_trays.position,
      },
    },
    distance = 2.5
  })
end

--AddBoxZone('FratHouseDJ', vector3(-1626.797, 3.437249, 61.78864), 0.5, 1.3, {
--  name = 'FratHouseDJ',
--  heading = 315.0,
--  minZ = 61.7,
--  maxZ = 62.0,
--}, {
--  options = radio_options,
--  distance = 1.5
--})

AddBoxZoneAutoname(vector4(-1897.46, 2053.1, 133.13, 0), 0.8, 2.0, {
  minZ = 132.93,
  maxZ = 133.53,
  }, {
    options = {
      {
        event_server = 'core:server:restaurant-tables:openTarget',
        icon = 'fa-regular fa-box',
        label = 'Table',
        table_coords = vector3(-1897.46, 2053.1, 133.13),
      },
    },
    distance = 2.5,
    context_disable = true,
  })

AddBoxZone('DorieFountain', vector3(-287.1898, 6319.437, 31.41398), 1.4, 1.4, {
  name = 'DorieFountain',
  heading = 90.0,
  minZ = 31.4,
  maxZ = 33.0,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openDorieFountain',
      icon = 'fa-regular fa-sprinkler',
      label = 'Fountain',
    },
  },
  distance = 2.5
})

-- AddBoxZone('tubesshop', vector3(-1108.93, -1693.65, 4.37), 2.6, 0.2, {
--   name = 'tubesshop',
--   heading = 35.0,
--   minZ = 4.37,
--   maxZ = 5.37,
-- }, {
--   options = {
--     {
--       event_server = 'blrp_core:server:item-store:resolveTargetConfig',
--       icon = 'fa-solid fa-dollar-sign',
--       label = 'Shop',
--       uid = 'tubes',
--     },
--   },
--   distance = 2.5
-- })
