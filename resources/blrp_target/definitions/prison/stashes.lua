local stash_locations = {
  { coords = vector3(1692.314, 2529.926, 45.16062), heading = 180.0, zMin = -0.3, zMax = 0.4, length = 0.2, width = 0.7 },
  { coords = vector3(1694.477, 2531.659, 45.21825), heading = 90.0, zMin = -0.3, zMax = 0.4, length = 0.2, width = 0.7 },
  { coords = vector3(1694.477, 2538.857, 45.21623), heading = 90.0, zMin = -0.3, zMax = 0.4, length = 0.2, width = 0.7 },
  { coords = vector3(1629.622, 2565.871, 48.29387), heading = 180.0, zMin = -0.3, zMax = 0.4, length = 0.2, width = 0.7 },
  { coords = vector3(1652.708, 2565.871, 48.28699), heading = 180.0, zMin = -0.3, zMax = 0.4, length = 0.2, width = 0.7 },
  { coords = vector3(1587.813, 2557.002, 44.82227), heading = 180.0, zMin = -0.3, zMax = 0.4, length = 0.2, width = 0.7 },
  { coords = vector3(1743.324, 2476.379, 44.75515), heading = 210.0, zMin = -0.5, zMax = 0.4, length = 0.7, width = 0.7 },
}

for stash_id, stash_location in pairs(stash_locations) do
  AddBoxZone('PrisonStash' .. stash_id, stash_location.coords, stash_location.length, stash_location.width, {
    name = 'PrisonStash' .. stash_id,
    heading = stash_location.heading,
    minZ = stash_location.coords.z + stash_location.zMin,
    maxZ = stash_location.coords.z + stash_location.zMax,
  }, {
    options = {
      {
        event_server = 'core:server:prison-ecosystem:routeCall',
        icon = 'far fa-search',
        label = 'Search',

        route = 'stashInteract',
        coords = stash_location.coords,

        groups = {
          'DOC',
          'Lifer',
          'Prisoner'
        }
      },
    },
    context_disable = true,
    distance = 1.5
  })
end
