local toilet_locations = {
  vector3(1767.821, 2502.880, 44.97598), -- Cell 1
  vector3(1764.672, 2501.081, 44.97598), -- Cell 2
  vector3(1761.552, 2499.279, 44.97598), -- Cell 3
  vector3(1755.283, 2495.633, 44.97598), -- Cell 4
  vector3(1752.125, 2493.790, 44.97598), -- Cell 5
  vector3(1748.989, 2492.007, 44.97598), -- Cell 6
  vector3(1767.821, 2502.880, 48.93102), -- Cell 7
  vector3(1764.672, 2501.081, 48.93102), -- Cell 8
  vector3(1761.552, 2499.279, 48.93102), -- Cell 9
  vector3(1758.395, 2497.427, 48.93102), -- Cell 10
  vector3(1755.283, 2495.633, 48.93102), -- Cell 11
  vector3(1752.125, 2493.790, 48.93102), -- Cell 12
  vector3(1748.989, 2492.007, 48.93102), -- Cell 13
  vector3(1758.021, 2470.564, 44.97598), -- Cell 14
  vector3(1761.173, 2472.377, 44.97598), -- Cell 15
  vector3(1764.318, 2474.178, 44.97598), -- Cell 16
  vector3(1767.414, 2475.981, 44.97598), -- Cell 17
  vector3(1770.581, 2477.841, 44.97598), -- Cell 18
  vector3(1773.725, 2479.609, 44.97598), -- Cell 19
  vector3(1776.869, 2481.477, 44.97598), -- Cell 20
  vector3(1758.021, 2470.564, 48.93102), -- Cell 21
  vector3(1761.173, 2472.377, 48.93102), -- Cell 22
  vector3(1764.318, 2474.178, 48.93102), -- Cell 23
  vector3(1767.414, 2475.981, 48.93102), -- Cell 24
  vector3(1770.581, 2477.841, 48.93102), -- Cell 25
  vector3(1773.725, 2479.609, 48.93102), -- Cell 26
  vector3(1776.869, 2481.477, 48.93102), -- Cell 20
}

for toilet_id, toilet_location in pairs(toilet_locations) do
  AddBoxZone('PrisonToilet' .. toilet_id, toilet_location, 0.8, 0.5, {
    name = 'PrisonToilet' .. toilet_id,
    heading = 30.0,
    minZ = toilet_location.z - 0.1,
    maxZ = toilet_location.z + 0.3,
  }, {
    options = {
      {
        event_server = 'core:server:prison-ecosystem:routeCall',
        icon = 'far fa-toilet',
        label = 'Use',

        route = 'toiletInteract',
        coords = toilet_location,

        groups = {
          'Lifer'
        }
      },
      {
        event_server = 'core:server:prison-ecosystem:routeCall',
        icon = 'far fa-toilet',
        label = 'Flush',

        route = 'toiletInteract',
        coords = toilet_location,

        groups = {
          'DOC'
        }
      },
    },
    context_disable = true,
    distance = 1.5
  })
end
