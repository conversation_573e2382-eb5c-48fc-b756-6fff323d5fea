prison_kitchen_hands_washed = false

AddEventHandler('blrp_target:prison:washHands', function()
  exports['mythic_progbar']:Progress({
    name = 'prison_washhands',
    duration = 7000,
    label = 'Washing Hands',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = "missheist_agency3aig_23",
      anim = "urinal_sink_loop",
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    prison_kitchen_hands_washed = true

    exports.blrp_core:me().notify('You washed your hands')
  end)
end)

AddBoxZone('PrisonSink', vector3(1779.034, 2565.126, 45.20929), 0.8, 0.9, {
  name = 'PrisonSink',
  heading = 180.0,
  minZ = 45.0,
  maxZ = 46.0,
}, {
  options = {
    {
      event_client = 'blrp_target:prison:washHands',
      icon = 'far fa-sink',
      label = 'Wash Hands',

      groups = {
        'Prisoner',
        'Lifer',
      }
    },
  },
  distance = 2.5
})

AddBoxZone('PrisonKitchenFridge', vector3(1784.588, 2564.946, 47.11909), 1.2, 1.7, {
  name = 'PrisonKitchenFridge',
  heading = 180.0,
  minZ = 44.5,
  maxZ = 47.1,
}, {
  options = {
    {
      event_server = 'core:server:prison:takeFrozenMeal',
      icon = 'fa-regular fa-refrigerator',
      label = 'Take Frozen Meal',

      groups = {
        'Prisoner',
        'Lifer',
      },

      filter = function()
        return prison_kitchen_hands_washed
      end
    },
  },
  distance = 2.5
})

AddBoxZone('PrisonKitchenMicrowave', vector3(1776.768, 2561.928, 46.25093), 0.8, 1.3, {
  name = 'PrisonKitchenMicrowave',
  heading = 90.0,
  minZ = 45.5,
  maxZ = 46.3,
}, {
  options = {
    {
      event_server = 'core:server:prison:microwaveMeal',
      icon = 'fa-regular fa-microwave',
      label = 'Heat Food',

      groups = {
        'Prisoner',
        'Lifer',
      },

      filter = function()
        return prison_kitchen_hands_washed
      end
    },
  },
  distance = 2.5
})

AddBoxZone('PrisonKitchenStove', vector3(1780.868, 2565.031, 45.59744), 1.0, 1.5, {
  name = 'PrisonKitchenStove',
  heading = 180.0,
  minZ = 44.5,
  maxZ = 45.7,
}, {
  options = {
    {
      event_server = 'core:server:prison:useStove',
      icon = 'fa-regular fa-oven',
      label = 'Use',

      groups = {
        'Lifer',
      },
    },
  },
  distance = 2.5
})
