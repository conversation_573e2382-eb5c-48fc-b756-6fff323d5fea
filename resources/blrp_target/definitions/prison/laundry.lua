AddBoxZone('PrisonClothesShelf', vector3(1587.562, 2553.085, 45.44528), 0.6, 1.4, {
  name = 'PrisonClothesShelf',
  heading = 90.0,
  minZ = 44.5,
  maxZ = 46.6,
}, {
  options = {
    {
      event_server = 'core:server:prison:takeClothes',
      icon = 'fa-solid fa-shirt',
      label = 'Take Dirty Clothes',

      groups = {
        'Prisoner',
        'Lifer'
      }
    },
    {
      event_server = 'core:server:prison:putClothes',
      icon = 'fa-light fa-shirt',
      label = 'Place Clean Clothes',

      groups = {
        'Prisoner',
        'Lifer'
      }
    },
  },
  distance = 2.5
})

for idx, coords in ipairs({
  vector3(1595.213, 2551.06, 45.11999),
  vector3(1597.18, 2551.06, 45.11999),
}) do
  AddBoxZone('PrisonIroning' .. idx, coords, 0.5, 1.7, {
    name = 'PrisonIroning' .. idx,
    heading = 90.0,
    minZ = 45.4,
    maxZ = 45.7,
  }, {
    options = {
      {
        event_server = 'core:server:prison:ironClothes',
        icon = 'fa-solid fa-shirt',
        label = 'Iron Clothes',

        groups = {
          'Prisoner',
          'Lifer'
        }
      },
    },
    distance = 2.5
  })
end

for idx, coords in ipairs({
  vector3(1593.841, 2552.223, 44.98591),
  vector3(1593.141, 2552.223, 44.98591),
  vector3(1592.442, 2552.223, 44.98591),
  vector3(1591.745, 2552.223, 44.98591),
  vector3(1591.044, 2552.223, 44.98591),
  vector3(1590.348, 2552.223, 44.98591),
  vector3(1589.65, 2552.223, 44.98591),
}) do
  AddBoxZone('PrisonDryer' .. idx, coords, 0.72, 0.7, {
    name = 'PrisonDryer' .. idx,
    heading = 180.0,
    minZ = 44.5,
    maxZ = 45.6,
  }, {
    options = {
      {
        event_server = 'core:server:prison:dryClothes',
        icon = 'fa-solid fa-dryer',
        label = 'Dry Clothes',

        groups = {
          'Prisoner',
          'Lifer'
        }
      },
    },
    distance = 2.5
  })
end

for idx, coords in ipairs({
  vector3(1588.962, 2556.404, 46.83982),
  vector3(1590.234, 2556.404, 46.83982),
  vector3(1591.603, 2556.404, 46.83982),
  vector3(1592.949, 2556.404, 46.83982),
  vector3(1594.267, 2556.404, 46.83982),
}) do
  AddBoxZone('PrisonWasher' .. idx, coords, 1.4, 1.3, {
    name = 'PrisonWasher' .. idx,
    heading = 0.0,
    minZ = 44.5,
    maxZ = 46.6,
  }, {
    options = {
      {
        event_server = 'core:server:prison:washClothes',
        icon = 'fa-solid fa-washing-machine',
        label = 'Wash Clothes',

        groups = {
          'Prisoner',
          'Lifer'
        }
      },
    },
    distance = 3.5
  })
end
