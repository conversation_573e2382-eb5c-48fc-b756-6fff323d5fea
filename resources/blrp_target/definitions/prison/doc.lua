AddBoxZone('MirrorWardrobe', vector3(1694.597, 2572.5, 46.668), 1.8, 1.6, {
  name = 'MirrorWardrobe',
  heading = 90.0,
  minZ = 46.0,
  maxZ = 46.89041,
}, {
  options = {
    {
      event_server = 'core:server:target-backhaul:openWardrobeCloakroom',
      icon = 'far fa-tshirt',
      label = 'DOC Uniform Wardrobe',
      cloakroom_name = 'bailiff'
    },
    {
      event_server = 'core:server:target-backhaul:openWardrobePersonal',
      icon = 'far fa-tshirt',
      label = 'Personal Wardrobe'
    },
  },
  distance = 3.0
})

AddBoxZone('BolingbrokeLocker3', vector3(1699.044, 2590.45, 45.911), 2.5, 0.5, {
    name = 'BolingbrokeLocker3',
    heading = 90.0,
    minZ = 34.0,
    maxZ = 46.89041,
  }, {
    options = {
      {
        event_server = 'core:server:target-backhaul:openChest',
        icon = 'far fa-box',
        label = 'D<PERSON> Locker',
    
        chest_name = 'locker_storage_char_id_5',
        chest_radius = 3.0,
        chest_weight = 100.0,
        chest_permission = 'police.store_weapons',
      },
    },
    distance = 3.0
  })

AddBoxZone('DocClockIn', vector3(1694.471, 2584.083, 45.911), 2.5, 0.13, {
  name = 'DocClockIn',
  heading = 90.0,
  minZ = 34.0,
  maxZ = 46.89041,
}, {
  options = {
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'DOC',

      icon = 'fas fa-clock',
      label = 'Clock in - DOC',

      groups = {
        'Civilian'
      }
    },
    {
      event_server = 'vrp:server:group:ch_select',
      target_group = 'Civilian',
  
      icon = 'fas fa-clock',
      label = 'Clock Out',
    },
  },
  distance = 3.0
})

AddBoxZone('DOCEquipmentBolingbroke', vector3(1696.569, 2590.43, 45.911), 0.65, 2.4, {
  name = 'DOCEquipmentBolingbroke',
  heading = 180.0,
  minZ = 45.0,
  maxZ = 46,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'Police Equipment',

      uid = 'leo-equipment',

      groups = {
        'LEO',
      }
    },
    {
      event_server = 'core:server:vehicle-target:takeEvidenceBin',
      icon = 'fa-regular fa-box',
      label = 'Take Evidence Bin',

      groups = {
        'LEO',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-raygun',
      label = 'DOC Supervisor Shop',

      uid = 'leo-doc-supervisor',

      groups = {
        'doc_rank3',
        'doc_rank4',
        'doc_rank5',
      }
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request DOC ID Card',
      card_type = 'id_doc',

      groups = {
        'DOC'
      }
    },
    {
      event_server = 'core:server:target-backhaul:openLeoTrashcan',
      icon = 'far fa-trash-alt',
      label = 'Secure Item Disposal',
      location = 'Bolingbroke',
    },
  },
  distance = 2.5
})

AddBoxZone('BolingbrokeDocSnacks', vector3(1701.026, 2583.500, 46.37134), 3, 1, {
  name = 'BolingbrokeDocSnacks',
  heading = 90.0,
  minZ = 34.0,
  maxZ = 46.89041,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fas fa-hand-holding-usd',
      label = 'DOC Snacks',

      uid = 'prison-snacks',

      groups = {
        'DOC',
        'LEO',
      }
    },
    {
      event_server = 'core:server:target-backhaul:openChest',
      icon = 'far fa-box',
      label = 'DOC Shared Storage',
  
      chest_name = 'locker_storage_shared_doc',
      chest_radius = 3.0,
      chest_weight = 500.0,
      chest_permission = 'doc.service',

      groups = {
        'DOC'
      }
    },
  },
  distance = 3.0
})

