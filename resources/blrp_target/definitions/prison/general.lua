AddTargetModel({
  `prop_cs_bin_01`,
}, {
  options = {
    {
      event_server = 'core:server:prison-ecosystem:recycleBottles',
      icon = 'fas fa-recycle',
      label = 'Recycle Bottles',
      groups = {
        'Lifer',
        'Prisoner',
      }
    },
  },
  distance = 1.5
})

AddTargetModel({
  `prop_haybale_03`,
  `prop_side_spreader`,
}, {
  options = {
    {
      event_server = 'core:server:prison-ecosystem:routeCall',
      icon = 'fas fa-search',
      label = 'Search',

      route = 'farmObjectSearch',

      groups = {
        'Lifer',
        'Prisoner',
      },
      zones = PrisonFarm,
    },
  },
  distance = 1.5
})

AddBoxZone('PrisonFarmWheat', vector3(1695.229, 2506.365, 46.0), 18.0, 18.0, {
  name = 'PrisonFarmWheat',
  heading = 0.0,
  minZ = 43.0,
  maxZ = 47.0,
}, {
  options = {
    {
      event_server = 'core:server:prison-ecosystem:takeWheat',
      icon = 'fas fa-wheat',
      label = 'Harvest',

      groups = {
        'Lifer',
        'Prisoner',
      },
      zones = PrisonFarm,
    },
  },
  context_disable = true,
  distance = 10.0
})

AddBoxZone('PrisonFarmCrafting', vector3(1706.483, 2510.742, 45.35266), 0.7, 0.7, {
  name = 'PrisonFarmCrafting',
  heading = 22.5,
  minZ = 45.3,
  maxZ = 45.45,
}, {
  options = {
    {
      event_server = 'core:server:prison-ecosystem:craft',
      icon = 'fas fa-hammer',
      label = 'Use',

      groups = {
        'Lifer',
        'Prisoner',
      },
      zones = PrisonFarm,
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Tattoo Gun',

      item_id = 'tattoo_gun',

      groups = {
        'lifer_rank2',
      },
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Black Veil',

      item_id = 'clth_pris_mask_a',

      groups = {
        'lifer_rank1',
        'lifer_rank2',
      },
    },
    {
      event_server = 'core:server:target-backhaul:craft',
      icon = 'fa-regular fa-wrench',
      label = 'Craft Green Veil',

      item_id = 'clth_pris_mask_b',

      groups = {
        'lifer_rank1',
        'lifer_rank2',
      },
    },
  },
  context_disable = true,
  distance = 10.0
})

AddBoxZone('PrisonFood', vector3(1780.95, 2559.863, 45.78379), 2.7, 3.75, {
  name = 'PrisonFood',
  heading = 0.0,
  minZ = 44.5,
  maxZ = 46.7,
  notifyText = '<i class="fa-regular fa-eye"></i> Food',
}, {
  options = {
    {
      event_server = 'core:server:prison-ecosystem:takeFood',
      icon = 'fas fa-bread-slice',
      label = 'Take Food Tray',

      groups = {
        'Lifer',
        'Prisoner',
      },
    },
    {
      event_server = 'core:server:prison:placeMeal',
      icon = 'fas fa-bread-slice',
      label = 'Put out cooked food',

      groups = {
        'Lifer',
        'Prisoner',
      },

      filter = function()
        return prison_kitchen_hands_washed
      end
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fas fa-bread-slice',
      label = 'Take Food Tray',

      uid = 'prison-food-supply',

      groups = {
        'DOC',
      },
    },
  },
  distance = 2.5
})

AddTargetModel({
  `s_m_m_prisguard_01`
}, {
  options = {
    {
      event_server = 'core:server:prison:selectReward',
      icon = 'fa-solid fa-clock',
      label = 'Reward me with reduced time',
      reward = 'time',

      groups = {
        'Prisoner',
      }
    },
    {
      event_server = 'core:server:prison:selectReward',
      icon = 'fa-solid fa-money-bill-1',
      label = 'Reward me with money',
      reward = 'cash',

      groups = {
        'Prisoner',
      }
    },
    {
      event_server = 'core:server:prison:requestCashPayout',
      icon = 'fa-solid fa-hand-holding-dollar',
      label = 'Collect money',

      groups = {
        'Prisoner',
        'Lifer',
      }
    },
  },
  distance = 2.0
})

AddBoxZone('PrisonCafeteriaSlush', vector3(1777.945, 2560.045, 46.21611), 0.7, 1.3, {
  name = 'PrisonCafeteriaSlush',
  heading = 180.0,
  minZ = 45.6,
  maxZ = 46.8,
}, {
  options = {
    {
      event_server = 'core:server:prison-ecosystem:takeSlushie',
      icon = 'fa-solid fa-glass-citrus',
      label = 'Take Slush',

      groups = {
        'Lifer',
        'Prisoner',
      },
    },
  },
  distance = 2.5
})

AddBoxZone('PrisonPharmacy', vector3(1772.896, 2578.614, 45.94278), 1.0, 2.4, {
  name = 'PrisonPharmacy',
  heading = 270.0,
  minZ = 44.8,
  maxZ = 46.0,
}, {
  options = {
    {
      event_server = 'core:server:prison:searchPharmacy',
      icon = 'fa-solid fa-search',
      label = 'Search',

      groups = {
        'Lifer',
        'Prisoner',
      },
    },
  },
  distance = 2.5
})
