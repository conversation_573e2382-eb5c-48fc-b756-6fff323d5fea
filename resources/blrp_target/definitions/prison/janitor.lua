local clocked_in_janitor = false

AddEventHandler('blrp_target:prison:clockInJanitor', function()
  exports['mythic_progbar']:Progress({
    name = 'prison_takebroom',
    duration = 2500,
    label = 'Taking Broom',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = 'pickup_object',
      anim = 'putdown_low',
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    clocked_in_janitor = true

    exports.blrp_core:me().notify('You took a broom. Go sweep some floors')
  end)
end)

AddBoxZone('PrisonJanitorCart', vector3(1772.817, 2567.732, 45.83342), 0.8, 1.5, {
  name = 'PrisonJanitorCart',
  heading = 270.0,
  minZ = 45.0,
  maxZ = 46.0,
}, {
  options = {
    {
      event_client = 'blrp_target:prison:clockInJanitor',
      icon = 'fa-solid fa-broom',
      label = 'Take Broom',

      groups = {
        'Prisoner',
        'Lifer',
      }
    },
  },
  distance = 2.5
})

local locations = {
  vector3(1768.180, 2484.234, 45.740),
  vector3(1769.833, 2495.601, 45.740),
  vector3(1753.896, 2483.973, 45.740),
  vector3(1748.578, 2483.543, 45.740),
  vector3(1750.716, 2476.568, 45.740),
  vector3(1757.099, 2497.341, 45.740),
  vector3(1766.231, 2570.267, 45.729),
  vector3(1768.663, 2597.338, 45.729),
  vector3(1769.457, 2592.175, 45.729),
  vector3(1762.982, 2592.758, 45.729),
  vector3(1777.739, 2556.529, 45.673),
  vector3(1778.358, 2547.978, 45.673),
  vector3(1784.144, 2549.132, 45.673),
  vector3(1786.582, 2556.959, 45.673),
  vector3(1782.880, 2563.407, 45.673),
  vector3(1778.675, 2561.974, 45.673),
  vector3(1770.893, 2578.509, 45.729),
  vector3(1769.596, 2583.978, 45.729),
  vector3(1771.295, 2588.178, 45.729),
  vector3(1822.615, 2594.385, 46.014),
  vector3(1827.794, 2594.958, 46.014),
  vector3(1828.949, 2588.573, 46.014),
  vector3(1828.837, 2583.320, 46.014),
  vector3(1836.767, 2594.901, 46.014),
  vector3(1843.606, 2593.737, 46.014),
  vector3(1837.249, 2588.430, 46.014),
  vector3(1842.388, 2589.132, 46.014),
  vector3(1843.217, 2582.063, 46.014),
  vector3(1838.489, 2581.263, 46.014),
  vector3(1836.765, 2573.544, 46.014),
  vector3(1839.045, 2571.541, 46.014),
  vector3(1833.998, 2577.026, 46.014),
  vector3(1834.313, 2583.688, 46.014),
  vector3(1833.986, 2588.552, 46.014),
  vector3(1589.649, 2553.868, 45.454),
  vector3(1744.034, 2481.657, 45.741),
}

for idx, coords in ipairs(locations) do
  AddBoxZone('PrisonBroom' .. idx, coords, 1.0, 1.0, {
    name = 'PrisonBroom' .. idx,
    heading = 90.0,
    minZ = coords.z - 1.0,
    maxZ = coords.z - 0.9,
  }, {
    options = {
      {
        event_server = 'core:server:prison:janitorBroom',
        icon = 'fa-solid fa-broom',
        label = 'Sweep Floor',

        groups = {
          'Prisoner',
          'Lifer'
        },

        filter = function()
          return clocked_in_janitor
        end
      },
    },
    distance = 4.0
  })
end
