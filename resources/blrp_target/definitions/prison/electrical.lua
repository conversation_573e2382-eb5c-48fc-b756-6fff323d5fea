--debugPolys(true)
local clocked_in_electrical = false
local banned_from_electrical = false


RegisterNetEvent('blrp_target:prison:clockOutElectrical', function(banned)
  clocked_in_electrical = false
  if banned then
    banned_from_electrical = true
  end
end)

RegisterNetEvent('blrp_target:prison:clockInElectrical', function(banned)


  if banned_from_electrical or banned then
    exports.blrp_core:me().notify('You have lost your electrical privilege for today!')
    banned_from_electrical = true
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'prison_electrical',
    duration = 2500,
    label = 'Grabbing Equipment',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = 'pickup_object',
      anim = 'putdown_low',
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    clocked_in_electrical = true

    exports.blrp_core:me().notify('You grabbed some equipment, go fix the electrical boxs!')
  end)
end)

-- Clock In - electrical is in licenseplates.lua blrp_target 

local electrical_boxs = {
  vector3(1636.407, 2554.159, 45.62125),
  vector3(1633.294, 2554.159, 45.65705),
  vector3(1632.697, 2565.529, 45.59013),
  vector3(1649.7, 2565.529, 45.65029),
  vector3(1655.128, 2565.529, 45.65216),
  vector3(1694.813, 2539.672, 45.74623),
  vector3(1694.794, 2532.926, 45.70937),
  vector3(1696.916, 2469.863, 45.70183),
  vector3(1745.964, 2501.497, 45.70638),
}


for idx, coords in ipairs(electrical_boxs) do
  AddBoxZone('electricalboxs' .. idx, coords, 0.7, 0.7, {
    name = 'electricalboxs' .. idx,
    heading = 90.0,
    minZ = coords.z - 0.95,
    maxZ = coords.z + 0.95,
  }, {
    options = {
      {
        event_server = 'core:server:prison:electrical',
        icon = 'fa-solid fa-bolt',
        label = 'Fix Electrical Box',

        groups = {
          'Prisoner',
          'Lifer'
        },

        filter = function()
        return clocked_in_electrical
       end
      },
    },
    context_disable = true,
    distance = 1.0
  })
end
