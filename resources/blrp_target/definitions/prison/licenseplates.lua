local clocked_in_plates = false
local banned_from_plates = false


RegisterNetEvent('blrp_target:prison:clockOutPlates', function(banned)
  clocked_in_plates = false
  if banned then
    banned_from_plates = true
  end
end)

RegisterNetEvent('blrp_target:prison:clockInPlates', function(banned)
  

  if banned_from_plates or banned then
    exports.blrp_core:me().notify('You have lost your plate privilege for today!')
    banned_from_plates = true
    return
  end

  exports['mythic_progbar']:Progress({
    name = 'prison_plates',
    duration = 2500,
    label = 'Grabbing Equipment',
    useWhileDead = false,
    canCancel = true,
    controlDisables = {
      disableMovement = true,
      disableCarMovement = false,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = 'pickup_object',
      anim = 'putdown_low',
      flags = 49,
    }
  }, function(cancelled)
    if cancelled then
      return
    end

    clocked_in_plates = true

    exports.blrp_core:me().notify('You grabbed some equipment, go make some plates!')
  end)
end)

AddBoxZone('PrisonPlateLocker', vector3(1694.99, 2454.717, 46.04599), 0.8, 1.5, {
  name = 'PrisonPlateLocker',
  heading = 270.0,
  minZ = 45.5,
  maxZ = 46.6,
}, {
  options = {
    {
      event_server = 'core:server:prison:checkBannedFromPlates',
      icon = 'fa-solid fa-clock',
      label = 'Clock In - Plate Manufacturing',

      groups = {
        'Prisoner',
        'Lifer',
      }
    },
    {
      event_server = 'core:server:prison:checkBannedFromElectrical',
      icon = 'fa-solid fa-clock',
      label = 'Clock In - Electrical',

      groups = {
        'Prisoner',
        'Lifer',
      }
    },
  },
  distance = 2.0
})

local manufacture_blank = {
  vector3(1696.805, 2464.078, 45.74523),
  vector3(1696.805, 2464.078, 45.74523),
  vector3(1706.127, 2464.078, 45.75391),
  vector3(1707.262, 2461.434, 45.72764),
  vector3(1707.262, 2453.989, 45.72475),
  vector3(1705.761, 2451.491, 45.72969),
  vector3(1701.224, 2451.491, 45.73967),
}


for idx, coords in ipairs(manufacture_blank) do
  AddBoxZone('ManufactureBlank' .. idx, coords, 1.0, 1.0, {
    name = 'ManufactureBlank' .. idx,
    heading = 90.0,
    minZ = coords.z - 0.45,
    maxZ = coords.z + 0.45,
  }, {
    options = {
      {
        event_server = 'core:server:prison:manufactureBlank',
        icon = 'fa-solid fa-user-helmet-safety',
        label = 'Machine Blank Plate',

        groups = {
          'Prisoner',
          'Lifer'
        },

        filter = function()
          return clocked_in_plates
        end
      },
      {
        event_server = 'core:server:prison:imprintBlank',
        icon = 'fa-solid fa-stamp',
        label = 'Imprint Plate',

        groups = {
          'Prisoner',
          'Lifer'
        },

        filter = function()
          return clocked_in_plates
        end
      },
    },
    distance = 1.0
  })
end

local drill_plates = {
  vector3(1698.731, 2451.234, 46.19561),
  vector3(1697.384, 2451.221, 46.18374),
  vector3(1707.586, 2456.769, 46.20127),
  vector3(1707.604, 2459.003, 46.18641),
}

for idx, coords in ipairs(drill_plates) do
  AddBoxZone('DrillPlates' .. idx, coords, 1.0, 1.0, {
    name = 'DrillPlates' .. idx,
    heading = 90.0,
    minZ = coords.z - 0.45,
    maxZ = coords.z + 0.45,
  }, {
    options = {
      {
        event_server = 'core:server:prison:drillPlate',
        icon = 'fa-solid fa-screwdriver',
        label = 'Drill Blank Plate',

        groups = {
          'Prisoner',
          'Lifer'
        },

        filter = function()
          return clocked_in_plates
        end
      },
    },
    distance = 1.0
  })
end

local assemble_plates = {
  vector3(1697.308, 2453.679, 45.78983),
  vector3(1699.028, 2453.704, 45.78983),
  vector3(1697.364, 2461.854, 45.78983),
  vector3(1698.994, 2461.817, 45.78983),
  vector3(1700.444, 2461.768, 45.78983),
  vector3(1702.057, 2461.78, 45.78983),
}

for idx, coords in ipairs(assemble_plates) do
  AddBoxZone('AssemblePlates' .. idx, coords, 1.0, 1.0, {
    name = 'AssemblePlates' .. idx,
    heading = 90.0,
    minZ = coords.z - 0.15,
    maxZ = coords.z + 0.15,
  }, {
    options = {
      {
        event_server = 'core:server:prison:finishPlate',
        icon = 'fa-light fa-input-text',
        label = 'Finish Plate',

        groups = {
          'Prisoner',
          'Lifer'
        },

        filter = function()
          return clocked_in_plates
        end
      },
    },
    distance = 4.0
  })
end
