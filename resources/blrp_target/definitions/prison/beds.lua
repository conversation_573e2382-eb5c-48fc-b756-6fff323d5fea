local bed_locations = {
  { coords = vector3(1766.304, 2500.214, 45.31393), type = "lower" }, -- Cell 1 lower
  { coords = vector3(1763.167, 2498.388, 45.31393), type = "lower" }, -- Cell 2 lower
  { coords = vector3(1760.010, 2496.578, 45.31393), type = "lower" }, -- Cell 3 lower
  { coords = vector3(1753.744, 2492.921, 45.31393), type = "lower" }, -- Cell 4 lower
  { coords = vector3(1750.589, 2491.088, 45.31393), type = "lower" }, -- Cell 5 lower
  { coords = vector3(1747.451, 2489.354, 45.31393), type = "lower" }, -- Cell 6 lower
  { coords = vector3(1766.304, 2500.214, 49.26897), type = "lower" }, -- Cell 7 lower
  { coords = vector3(1763.167, 2498.388, 49.26897), type = "lower" }, -- Cell 8 lower
  { coords = vector3(1760.010, 2496.578, 49.26897), type = "lower" }, -- Cell 9 lower
  { coords = vector3(1756.845, 2494.757, 49.26897), type = "lower" }, -- Cell 10 lower
  { coords = vector3(1753.744, 2492.921, 49.26897), type = "lower" }, -- Cell 11 lower
  { coords = vector3(1750.589, 2491.088, 49.26897), type = "lower" }, -- Cell 12 lower
  { coords = vector3(1747.451, 2489.354, 49.26897), type = "lower" }, -- Cell 13 lower
  { coords = vector3(1759.564, 2473.277, 45.31393), type = "lower" }, -- Cell 14 lower
  { coords = vector3(1762.695, 2475.077, 45.31393), type = "lower" }, -- Cell 15 lower
  { coords = vector3(1765.882, 2476.826, 45.31393), type = "lower" }, -- Cell 16 lower
  { coords = vector3(1768.967, 2478.615, 45.31393), type = "lower" }, -- Cell 17 lower
  { coords = vector3(1772.133, 2480.460, 45.31393), type = "lower" }, -- Cell 18 lower
  { coords = vector3(1775.281, 2482.319, 45.31393), type = "lower" }, -- Cell 19 lower
  { coords = vector3(1778.455, 2484.170, 45.31393), type = "lower" }, -- Cell 20 lower
  { coords = vector3(1759.553, 2473.293, 49.26598), type = "lower" }, -- Cell 21 lower
  { coords = vector3(1762.695, 2475.077, 49.26598), type = "lower" }, -- Cell 22 lower
  { coords = vector3(1765.882, 2476.826, 49.26598), type = "lower" }, -- Cell 23 lower
  { coords = vector3(1768.967, 2478.615, 49.26598), type = "lower" }, -- Cell 24 lower
  { coords = vector3(1772.133, 2480.460, 49.26598), type = "lower" }, -- Cell 25 lower
  { coords = vector3(1775.281, 2482.319, 49.26598), type = "lower" }, -- Cell 26 lower
  { coords = vector3(1778.455, 2484.170, 49.26598), type = "lower" }, -- Cell 27 lower

  { coords = vector3(1766.272, 2500.192, 46.43987), type = "upper" }, -- Cell 1 upper
  { coords = vector3(1763.098, 2498.343, 46.43987), type = "upper" }, -- Cell 2 upper
  { coords = vector3(1760.113, 2496.547, 46.43987), type = "upper" }, -- Cell 3 upper
  { coords = vector3(1753.740, 2492.973, 46.43987), type = "upper" }, -- Cell 4 upper
  { coords = vector3(1750.634, 2491.145, 46.43987), type = "upper" }, -- Cell 5 upper
  { coords = vector3(1747.376, 2489.362, 46.43987), type = "upper" }, -- Cell 6 upper
  { coords = vector3(1766.272, 2500.192, 50.39491), type = "upper" }, -- Cell 7 upper
  { coords = vector3(1763.098, 2498.343, 50.39491), type = "upper" }, -- Cell 8 upper
  { coords = vector3(1760.113, 2496.547, 50.39491), type = "upper" }, -- Cell 9 upper
  { coords = vector3(1756.862, 2494.718, 50.39491), type = "upper" }, -- Cell 10 upper
  { coords = vector3(1753.740, 2492.973, 50.39491), type = "upper" }, -- Cell 11 upper
  { coords = vector3(1750.634, 2491.145, 50.39491), type = "upper" }, -- Cell 12 upper
  { coords = vector3(1747.376, 2489.362, 50.39491), type = "upper" }, -- Cell 13 upper
  { coords = vector3(1759.513, 2473.201, 46.43987), type = "upper" }, -- Cell 14 upper
  { coords = vector3(1762.684, 2475.015, 46.43987), type = "upper" }, -- Cell 15 upper
  { coords = vector3(1765.841, 2476.802, 46.43987), type = "upper" }, -- Cell 16 upper
  { coords = vector3(1768.864, 2478.564, 46.43987), type = "upper" }, -- Cell 17 upper
  { coords = vector3(1772.075, 2480.438, 46.43987), type = "upper" }, -- Cell 18 upper
  { coords = vector3(1775.382, 2482.373, 46.43987), type = "upper" }, -- Cell 19 upper
  { coords = vector3(1778.499, 2484.158, 46.43987), type = "upper" }, -- Cell 20 upper
  { coords = vector3(1759.524, 2473.226, 50.39491), type = "upper" }, -- Cell 21 upper
  { coords = vector3(1762.684, 2475.015, 50.39491), type = "upper" }, -- Cell 22 upper
  { coords = vector3(1765.841, 2476.802, 50.39491), type = "upper" }, -- Cell 23 upper
  { coords = vector3(1768.864, 2478.564, 50.39491), type = "upper" }, -- Cell 24 upper
  { coords = vector3(1772.075, 2480.438, 50.39491), type = "upper" }, -- Cell 25 upper
  { coords = vector3(1775.382, 2482.373, 50.39491), type = "upper" }, -- Cell 26 upper
  { coords = vector3(1778.499, 2484.158, 50.39491), type = "upper" }, -- Cell 27 upper
}

for bed_id, bed_location in pairs(bed_locations) do
  AddBoxZone('PrisonBed' .. bed_id, bed_location.coords, 2.3, 0.9, {
    name = 'PrisonBed' .. bed_id,
    heading = 30.0,
    minZ = bed_location.coords.z - 0.1,
    maxZ = bed_location.coords.z + 0.3,
  }, {
    options = {
      {
        event_server = 'core:server:prison-ecosystem:routeCall',
        icon = 'far fa-bed-empty',
        label = 'Stash',

        route = 'bedInteract',
        coords = bed_location.coords,
        bed_type = bed_location.type,

        groups = {
          'Lifer'
        }
      },
      {
        event_server = 'core:server:prison-ecosystem:routeCall',
        icon = 'far fa-bed-empty',
        label = 'Search',

        route = 'bedInteract',
        coords = bed_location.coords,
        bed_type = bed_location.type,

        groups = {
          'DOC'
        }
      },
      {
        event_server = 'core:server:prison-ecosystem:routeCall',
        icon = 'far fa-bed-empty',
        label = 'Take Mattress',

        route = 'takeMattress',
        coords = bed_location.coords,
        bed_type = bed_location.type,

        groups = {
          'Lifer',
          'Prisoner'
        }
      },
    },
    context_disable = true,
    distance = 1.5
  })
end
