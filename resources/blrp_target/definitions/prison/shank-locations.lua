local shank_locations = {
  { coords = vector3(1699.128, 2473.305, 45.660), heading = -135.0, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.9 },
  { coords = vector3(1698.584, 2472.663, 45.8158), heading = -135.0, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1698.059, 2472.139, 45.73712), heading = -135.0, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1657.115, 2488.040, 46.333), heading = 144.0, zMin = -0.4, zMax = 0.4, length = 0.3, width = 1.0 },
  { coords = vector3(1667.562, 2487.496, 46.277), heading = 172.6, zMin = -0.4, zMax = 0.4, length = 0.3, width = 1.0 },
  { coords = vector3(1664.831, 2501.933, 46.150), heading = -2.407, zMin = -0.4, zMax = 0.5, length = 0.3, width = 0.5 },
  { coords = vector3(1664.245, 2502.587, 45.653), heading = -91.876, zMin = -0.4, zMax = 0.4, length = 0.2, width = 0.6 },
  { coords = vector3(1617.794, 2521.033, 46.341), heading = 136.596, zMin = -0.4, zMax = 0.4, length = 0.3, width = 1.0 },
  { coords = vector3(1616.205, 2527.735, 46.324), heading = 88.331, zMin = -0.4, zMax = 0.4, length = 0.3, width = 1.0 },
  { coords = vector3(1632.780, 2529.143, 45.237), heading = -130.167, zMin = -0.7, zMax = 0.5, length = 0.3, width = 1.5 },
  { coords = vector3(1627.934, 2538.719, 46.171), heading = -1.0, zMin = -0.4, zMax = 0.5, length = 0.3, width = 0.5 }, -- power pole tall skinny
  { coords = vector3(1627.317, 2539.399, 45.642), heading = -89.271, zMin = -0.3, zMax = 0.5, length = 0.2, width = 0.6 }, -- power pole short fat
  { coords = vector3(1628.641, 2564.720, 45.702), heading = 0.5, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1629.464, 2564.720, 45.681), heading = 0.5, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1630.307, 2564.720, 45.678), heading = 0.5, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1634.859, 2554.257, 45.678), heading = 0.5, zMin = -1.3, zMax = 0.1, length = 0.6, width = 1.6 },
  { coords = vector3(1651.242, 2564.720, 45.641), heading = 0.5, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1652.055, 2564.720, 45.570), heading = 0.5, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1652.908, 2564.720, 45.587), heading = 0.5, zMin = -1.2, zMax = 1.2, length = 0.2, width = 0.8 },
  { coords = vector3(1685.972, 2554.248, 45.672), heading = 0.5, zMin = -1.3, zMax = 0.1, length = 0.6, width = 1.6 },
  { coords = vector3(1698.786, 2533.348, 45.688), heading = 90.0, zMin = -1.3, zMax = 0.1, length = 0.6, width = 1.6 },
  { coords = vector3(1718.191, 2527.693, 46.142), heading = 120.0, zMin = -0.4, zMax = 0.5, length = 0.3, width = 0.5 },
  { coords = vector3(1717.863, 2526.859, 45.661), heading = 30.0, zMin = -0.3, zMax = 0.5, length = 0.2, width = 0.6 },
  { coords = vector3(1760.910, 2541.395, 45.638), heading = -91.067, zMin = -0.3, zMax = 0.5, length = 0.2, width = 0.6 },
  { coords = vector3(1761.514, 2540.735, 46.113), heading = -4.075, zMin = -0.4, zMax = 0.5, length = 0.3, width = 0.5 },
  { coords = vector3(1772.749, 2568.327, 45.826), heading = 0.0, zMin = -0.8, zMax = 0.1, length = 0.4, width = 0.6 }, -- Janitor cart
  { coords = vector3(1777.615, 2545.545, 45.775), heading = 90.0, zMin = -0.8, zMax = 0.1, length = 0.4, width = 0.6 }, -- Janitor cart
  { coords = vector3(1790.196, 2568.791, 45.704), heading = 90.0, zMin = -1.3, zMax = 0.1, length = 0.6, width = 1.6 },
}

for shank_id, shank_location in ipairs(shank_locations) do
  AddBoxZone('PrisonShankSearch' .. shank_id, shank_location.coords, shank_location.length, shank_location.width, {
    name = 'PrisonShankSearch' .. shank_id,
    heading = shank_location.heading,
    minZ = shank_location.coords.z + shank_location.zMin,
    maxZ = shank_location.coords.z + shank_location.zMax,
  }, {
    options = {
      {
        event_server = 'core:server:prison-ecosystem:routeCall',
        icon = 'far fa-search',
        label = 'Search',

        route = 'shankSpotSearch',
        coords = shank_location.coords,

        groups = {
          'Lifer',
          'Prisoner'
        }
      },
    },
    context_disable = true,
    distance = 1.5
  })
end
