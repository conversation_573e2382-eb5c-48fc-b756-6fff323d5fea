AddBoxZone('EMSDrugsPaleto', vector3(-260.1419, 6317.314, 34.03645), 0.7, 3.0, {
  name = 'EMSDrugsPaleto',
  heading = 45.0,
  minZ = 33.0,
  maxZ = 34.1,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-prescription-bottle-alt',
      label = 'EMS Drug Cabinet',

      uid = 'ems-drugs',
    },
  },
  distance = 2.5
})

AddBoxZone('EMSEquipmentPaleto', vector3(-260.0517, 6317.137, 32.36786), 0.9, 3.0, {
  name = 'EMSEquipmentPaleto',
  heading = 45.0,
  minZ = 31.0,
  maxZ = 32.4,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supply',

      uid = 'ems-equipment',
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-hand-holding-box',
      label = 'EMS Supervisor Shop',

      uid = 'ems-supervisor',

      groups = {
        'ems_rank5',
      },
    },
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'far fa-burger-soda',
      label = 'Hospital Food',

      uid = 'ems-food',
    },
    {
      event_server = 'core:server:id-cards:requestFactionCard',
      icon = 'far fa-id-card',
      label = 'Request LSFD ID Card',
      card_type = 'id_lsfd',

      groups = {
        'LSFD'
      }
    },
  },
  distance = 2.0
})
