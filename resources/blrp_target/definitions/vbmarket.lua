-- Peds

AddTargetModel({
  `A_M_Y_Beach_02`,
  `A_M_Y_BeachVesp_01`,
  `A_F_M_Beach_01`,
  `A_M_M_Beach_01`,
}, {
  options = {
    {
      event_server = 'blrp_core:server:item-store:resolveTargetConfig',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Shop',
      action = 'shop',

      vbmarket = true,
      filter_pass_value_as = 'uid',
      filter = function(entity, ray_intersect_coords)
        if NetworkGetEntityIsNetworked(entity) then
          return false
        end

        local store_id = Entity(entity).state.vbm_store_id

        if not store_id then
          return false
        end

        return 'vbmarket-' .. store_id
      end
    },
    {
      event_server = 'blrp_core:server:vbmarket:route',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Rent Store',
      action = 'rent',

      filter_pass_value_as = 'vbm_store_id',
      filter = function(entity, ray_intersect_coords)
        if NetworkGetEntityIsNetworked(entity) then
          return false
        end

        return Entity(entity).state.vbm_store_id
      end
    },
    {
      event_server = 'blrp_core:server:vbmarket:route',
      icon = 'fa-solid fa-dollar-sign',
      label = 'Check Lease',
      action = 'checktime',

      filter_pass_value_as = 'vbm_store_id',
      filter = function(entity, ray_intersect_coords)
        if NetworkGetEntityIsNetworked(entity) then
          return false
        end

        return Entity(entity).state.vbm_store_id
      end
    },
    {
      event_server = 'blrp_core:server:vbmarket:route',
      icon = 'fa-regular fa-box',
      label = 'Item Retrieval',
      action = 'retrieveitems',

      filter_pass_value_as = 'vbm_store_id',
      filter = function(entity, ray_intersect_coords)
        if NetworkGetEntityIsNetworked(entity) then
          return false
        end

        return Entity(entity).state.vbm_store_id
      end
    },
    {
      event_server = 'blrp_core:server:vbmarket:route',
      icon = 'fa-solid fa-circle-xmark',
      label = 'Force Vacate Store',
      action = 'vacate',

      filter_pass_value_as = 'vbm_store_id',
      filter = function(entity, ray_intersect_coords)
        if NetworkGetEntityIsNetworked(entity) then
          return false
        end

        return Entity(entity).state.vbm_store_id
      end,

      groups = {
        'moderator',
        'admin',
        'superadmin',
      },
    },
  },
  distance = 2.5
})

-- Shop components
-- A lot of math went into this logic. Here it is broken down:
-- For each component (computer, storage, safe), the code figures out where to display the component
-- the same way ytyps figure out where to put objects: based on the pivot
-- 1. The coordinates of the IPL are gathered
-- 2. The local pivot is computed based on true pivot (pivot when IPL is at 0.0 degree world rotation) and rotation of the IPL
-- 3. Local pivot is added to world coords, giving the location of the box zone

for _, ipl in pairs(exports.blrp_core:GetVBMIPLs()) do
  if ipl.set == 'blrp_customized_a' then
    AddBoxZone('VBM' .. ipl.id .. 'Computer', ipl.coords + exports.blrp_core:RotateVector(vector3(3.468886, 1.578067, -0.6600046), -ipl.rot), 0.55, 0.55, {
      heading = ipl.rot,
      minZ = 4.1,
      maxZ = 4.4,
    }, {
      options = {
        {
          event_server = 'blrp_core:server:vbmarket:route',
          icon = 'fa-regular fa-computer-classic',
          label = 'Manage Employees',
          action = 'employees',

          vbm_store_id = ipl.id,
        },
        {
          event_server = 'blrp_core:server:vbmarket:route',
          icon = 'fa-regular fa-computer-classic',
          label = 'Manage Stock Prices',
          action = 'prices',

          vbm_store_id = ipl.id,
        },
        {
          event_server = 'blrp_core:server:vbmarket:route',
          icon = 'fa-regular fa-paint-roller',
          label = 'Change Wallpaper',
          action = 'tint',

          vbm_store_id = ipl.id,
        },
      },
      distance = 2.5,
    })

    AddBoxZone('VBM' .. ipl.id .. 'Storage', ipl.coords + exports.blrp_core:RotateVector(vector3(3.284527, -2.5888505, -1.453644), -ipl.rot), 1.25, 1.0, {
      heading = ipl.rot + 90,
      minZ = 3.4,
      maxZ = 5.3,
    }, {
      options = {
        {
          event_server = 'blrp_core:server:vbmarket:route',
          icon = 'fa-regular fa-box',
          label = 'Stock',
          action = 'stock',

          vbm_store_id = ipl.id,
        },
      },
      distance = 2.5,
    })

    AddBoxZone('VBM' .. ipl.id .. 'Safe', ipl.coords + exports.blrp_core:RotateVector(vector3(4.2, -2.6, -0.5429058), -ipl.rot), 0.6, 0.7, {
      heading = ipl.rot + 90,
      minZ = 4.25,
      maxZ = 4.95,
    }, {
      options = {
        {
          event_server = 'blrp_core:server:vbmarket:route',
          icon = 'fa-regular fa-vault',
          label = 'Safe',
          action = 'safe',

          vbm_store_id = ipl.id,
        },
      },
      distance = 2.5,
    })
  end
end
