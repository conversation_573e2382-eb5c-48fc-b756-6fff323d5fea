prop_translation_table = {
  [GetHash<PERSON>ey('prop_portaloo_01a')] = 'prop_portaloo_01a',
}

local interact_config = {
  ---------------------------------------------
  --------------- PAPER SHREDDER --------------
  ---------------------------------------------
  {
    props = { 'prop_shredder_01', 'prop_shredder_01', },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:target-backhaul:useShredder',
        icon = 'fa-regular fa-shredder',
        label = 'Use Shredder',
      },
    }
  },

  ---------------------------------------------
  ------------- CIGARETTE MACHINE -------------
  ---------------------------------------------
  {
    props = { 'prop_vend_fags_01', },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:target-backhaul:buyCigarette',
        icon = 'fa-regular fa-smoking',
        label = 'Buy Cigarette',
      },
      {
        event_server = 'core:server:target-backhaul:buyCondom',
        icon = 'fa-regular fa-baby-carriage',
        label = 'Buy Condom',

        filter = function(_, ray_intersect_coords)
          return (#(vector3(125.3444, -1291.758, 29.75409) - ray_intersect_coords) < 5.0)
        end
      }
    }
  },

  ---------------------------------------------
  --------- PARKING METER / CROSSWALK ---------
  ---------------------------------------------
  {
    props = { 'prop_parknmeter_01', 'prop_parknmeter_02' },
    distance = 0.85,
    options = {
      {
        event_server = 'core:server:o-i:parking_meter:pay',
        icon = 'fas fa-coins',
        label = 'Pay Meter',
      },
      {
        event_server = 'core:server:o-i:parking_meter:check',
        icon = 'fas fa-search',
        label = 'Check Meter',
      },
      {
        event_server = 'core:server:o-i:parking_meter:rob',
        icon = 'fa-regular fa-hammer-crash',
        label = 'Break Meter',

        groups = {
          'Civilian'
        },
      },
    }
  },

  {
    props = { 'prop_traffic_01a', 'prop_traffic_01b', 'prop_traffic_01d', 'prop_traffic_02a', 'prop_traffic_02b', 'prop_traffic_03a', 'prop_traffic_03b' },
    distance = 0.75,
    options = {
      {
        event_server = 'core:server:o-i:pressCrossWalk',
        icon = 'far fa-traffic-light',
        label = 'Activate Crosswalk',
      }
    }
  },

  ---------------------------------------------
  ----------- TRASH CANS / DUmPSTERS ----------
  ---------------------------------------------

  {
    props = { 'p_dumpster_t', 'prop_cs_dumpster_01a', 'prop_dumpster_01a', 'prop_dumpster_02b', 'prop_dumpster_4a' },
    distance = 1.3,
    options = {
      {
        event_server = 'core:server:o-i:openDumpster',
        icon = 'fas fa-dumpster',
        label = 'Open'
      },
      {
        event_server = 'core:server:jobs:garbage:collect',
        icon = 'far fa-trash-arrow-up',
        label = 'Collect Garbage Bags',
        freeze = true,

        groups = {
          'Garbage Collection'
        }
      },
      {
        event_server = 'core:server:lootables:loot',
        icon = 'far fa-search',
        label = 'Search',

        groups = {
          'Civilian'
        }
      },
    }
  },

  ---------------------------------------------
  -------------- VENDING MACHINES -------------
  ---------------------------------------------

  {
    props = { 'prop_vend_soda_02' },
    distance = 1.0,
    options = {
      {
        event_server = 'core:server:o-i:vendingBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($45) Buy Sprunk',
        vending_group = 'sprunk_drink',
      }
    }
  },

  {
    props = { 'bl_sso_cellvend' },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:target-backhaul:buyCelltowaPhone',
        icon = 'fa-regular fa-phone',
        label = '($250) Buy Celltowa Phone',
      },
    }
  },

  {
    props = { 'prop_vend_soda_02a', 'prop_vend_soda_02a_bcso' },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:target-backhaul:buyRedwoodPackBCSO',
        icon = 'fa-regular fa-smoking',
        label = '($250) Buy Redwood Junior 20 Pack',

        junior_pack = true,
      },
      {
        event_server = 'core:server:target-backhaul:buyRedwoodPackBCSO',
        icon = 'fa-regular fa-smoking',
        label = '($500) Buy Redwood 20 Pack',
      },
      {
        event_server = 'core:server:target-backhaul:buyRedwoodPackBCSO',
        icon = 'fa-regular fa-smoking',
        label = '($800) Buy Redwood Gold 20 Pack',

        gold_pack = true,
      },
    }
  },

  {
    props = { 'prop_vend_snak_01', 'prop_vend_snak_01_tu' },
    distance = 1.0,
    options = {
      {
        event_server = 'core:server:o-i:vendingBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($45) Buy Chips',
        vending_group = 'chips',
      },
      {
        event_server = 'core:server:o-i:vendingBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($45) Buy Snack',
        vending_group = 'snack',
      },
    }
  },

  {
    props = { 'prop_vend_junk_01', 'sf_prop_sf_vend_drink_01a' },
    distance = 1.0,
    options = {
      {
        event_server = 'core:server:o-i:vendingBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($45) Buy Junk Energy',
        vending_group = 'junk_drink',
      },
      {
        event_server = 'core:server:o-i:vendingBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($45) Buy Junk Bar',
        vending_group = 'junk_snack',
      }
    }
  },

  {
    props = { 'prop_vend_water_01', },
    distance = 1.0,
    options = {
      {
        event_server = 'core:server:o-i:raineBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($225) Dispense Water',
      },
    },
  },

  {
    props = { 'prop_vend_soda_01' },
    distance = 1.0,
    options = {
      {
        event_server = 'core:server:o-i:vendingBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($45) Buy eCola',
        vending_group = 'ecola_drink',
      }
    }
  },

  {
    props = { 'prop_vend_coffe_01' },
    distance = 1.0,
    options = {
      {
        event_server = 'core:server:o-i:vendingBuy',
        icon = 'fa-regular fa-money-bill-wave',
        label = '($45) Buy Coffee',
        vending_group = 'coffee_drink',
      }
    }
  },

  ---------------------------------------------
  -------------------- OTHER ------------------
  ---------------------------------------------

  {
    props = {
      'prop_news_disp_01a',
      'prop_news_disp_02a',
      'prop_news_disp_02b',
      'prop_news_disp_02c',
      'prop_news_disp_02d',
      'prop_news_disp_02e',
      'prop_news_disp_03a',
      'prop_news_disp_05a',
      'prop_news_disp_06a',
    },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:weazel:takeNewspaper',
        icon = 'far fa-newspaper',
        label = '($2) Buy Latest Weazel News',
      }
    }
  },

  {
    props = {
      'prop_bin_01a',
      'prop_bin_02a',
      'prop_bin_03a',
      'prop_bin_04a',
      'prop_bin_05a',
      'prop_bin_06a',
      'prop_bin_07a',
      'prop_bin_08a',
    },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:lootables:loot',
        icon = 'far fa-search',
        label = 'Search',

        groups = {
          'Civilian'
        }
      },
      {
        event_server = 'core:server:o-i:openDumpster',
        icon = 'fas fa-dumpster',
        label = 'Open'
      },
    }
  },

  {
    props = {
      'xm_prop_x17_res_pctower',
      'prop_pc_01a',
      'v_res_pctower',
      'prop_pc_02a',
      'm23_2_int4_m232_pc',
      'prop_dyn_pc',
      'hei_prop_heist_pc_01',
      'prop_dyn_pc_02',
    },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:lootables:loot',
        icon = 'far fa-search',
        label = 'Search',

        groups = {
          'Civilian'
        }
      },
    }
  },

  {
    props = {
      'p_int_jewel_plant_01',
      'p_int_jewel_plant_02',
      'prop_fbibombplant',
      'prop_plant_int_01a',
      'prop_plant_int_01b',
      'prop_plant_int_02a',
      'prop_plant_int_02b',
      'prop_pot_plant_04c',
      'prop_pot_plant_01d',
      'prop_pot_plant_05b',
      'prop_pot_plant_03a',
      'prop_pot_plant_04a',
      'prop_pot_plant_05a',
      'prop_plant_fern_02b',
      'prop_plant_group_01',
      'prop_plant_group_02',
      'prop_pot_plant_01a',
      'prop_pot_plant_01b',
      'prop_pot_plant_01c',
      'prop_pot_plant_01e',
      'prop_pot_plant_04b',
      'prop_pot_plant_05c',
      'prop_pot_plant_05d',
      'prop_fib_plant_01',
      'prop_plant_int_04c',
      'prop_plant_int_04a',
      'prop_plant_int_04b',
      'prop_pot_plant_03b',
      'prop_plant_int_03a',
    },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:jobs:gardening:water',
        icon = 'fas fa-heart',
        label = 'Give Love',

        groups = {
          'Gardening'
        }
      }
    }
  },
  {
    props = { 'p_oil_pjack_01_s',  'p_oil_pjack_02_s', 'p_oil_pjack_03_s', },
    distance = 3.0,
    options = {
      {
        event_server = 'core:server:jobs:oil:collect',
        icon = 'far fa-oil-can',
        label = 'Collect',

        groups = {
          'Civilian'
        },
      }
    }
  },

  {
    props = { 'prop_atm_01', 'prop_atm_02', 'prop_atm_03', 'prop_fleeca_atm', },
    distance = 1.3,
    options = {
      {
        event_server = 'blrp_banking:server:open',
        icon = 'far fa-dollar-sign',
        label = 'Access ATM',
        access_type = 'atm',
      },
      {
        event_server = 'blrp_core:atm-hack:server:insertBankCard',
        icon = 'far fa-credit-card',
        label = 'Insert Bank Card',

        filter = function(_, ray_intersect_coords)
          local has_item, quantity = exports.blrp_core:me().hasItem('bank_card_a') or exports.blrp_core:me().hasItem('bank_card_b')
          return has_item
        end
      },
  },
    check_upright = false,
    check_on_foot = true
  },

  -----------------------------------------------------------------------------
  ------------------------------ FACTION RELATED ------------------------------
  -----------------------------------------------------------------------------

  -- Police Computer
  {
    props = { 'prop_monitor_01a', },
    distance = 1.5,
    options = {
      -- Medical center
      {
        event_server = 'vrp:server:openMedicalComputer',
        icon = 'fas fa-computer-classic',
        label = 'LSFD Computer',

        zones = {
          common_zones.Hospital_Sandy
        },

        groups = {
          'LSFD'
        }
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'LSFD',

        icon = 'fas fa-clock',
        label = 'Clock in - LSFD',

        zones = {
          common_zones.Hospital_Sandy,
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'LSFD+TMU_Internal',

        icon = 'fas fa-clock',
        label = 'Clock in - LSFD (TMU)',

        zones = {
          common_zones.Hospital_Sandy,
        },
      },
      {
        event_server = 'vrp:server:group:ch_select',
        target_group = 'Civilian',

        icon = 'fas fa-clock',
        label = 'Clock Out',

        zones = {
          common_zones.Hospital_Sandy,
        },
      },
    }
  },

  {
    props = { 'v_med_medwastebin', },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:target-backhaul:openLeoTrashcan',
        icon = 'fa-regular fa-biohazard',
        label = 'Hazardous Material Disposal',

        groups = {
          'LSFD',
          'LEO',
        }
      }
    }
  },

  -----------------------------------------------------------------------------
  -------------------------------- SEARCHABLES --------------------------------
  -----------------------------------------------------------------------------
  -- If you add a prop here, add the mapping of prop -> loot table
  -- in blrp_core\modules\object-interact\config.lua

  {
    props = { 'prop_barrel_01a', },
    distance = 0.8,
    options = {
      {
        event_server = 'core:server:lootables:loot',
        icon = 'far fa-search',
        label = 'Search',
        freeze = true,

        groups = {
          'Civilian'
        },
      },
    },
  },

  {
    props = { 'prop_dumpster_02a', },
    distance = 0.8,
    options = {
      {
        event_server = 'core:server:lootables:loot',
        icon = 'far fa-search',
        label = 'Search',

        groups = {
          'Civilian'
        },
      },
      {
        event_server = 'core:server:jobs:garbage:collect',
        icon = 'far fa-trash-arrow-up',
        label = 'Collect Garbage Bags',
        freeze = true,

        groups = {
          'Garbage Collection'
        }
      },
    },
  },

  {
    props = { 'prop_aircon_m_01', 'prop_aircon_m_02', 'prop_aircon_m_03', 'prop_aircon_m_04', 'prop_aircon_m_05', 'prop_aircon_m_06' },
    distance = 1.0,
    options = {
      {
        event_server = 'core:server:lootables:loot',
        icon = 'far fa-search',
        label = 'Search',

        groups = {
          'Civilian'
        },
      }
    },
  },

  {
    props = {
      'prop_woodpile_04a',
      'prop_plywoodpile_01b',
      'prop_woodpile_01b',
      'prop_cons_ply02',
      'prop_pipes_01a',
      'prop_wheelbarrow02a',
      'prop_skip_06a',
      'prop_boxpile_06a',
      'prop_barrel_03d',
    },
    distance = 1.5,
    options = {
      {
        event_server = 'core:server:lootables:loot',
        icon = 'far fa-search',
        label = 'Search',

        groups = {
          'Civilian'
        },
      },
    },
  },
}

--------------------------------------------------------------------------------
------------------------------- CONVERSION LOGIC -------------------------------
--------------------------------------------------------------------------------

for _, interact_data in ipairs(interact_config) do
  local prop_hashes = {}

  if interact_data.check_upright == nil then
    interact_data.check_upright = true
  end

  if interact_data.check_on_foot == nil then
    interact_data.check_on_foot = true
  end

  for __, prop_name in ipairs(interact_data.props) do
    local hash_key = GetHashKey(prop_name)

    table.insert(prop_hashes, hash_key)
    prop_translation_table[hash_key] = prop_name
  end

  local model_options = {
    options = interact_data.options,
    distance = interact_data.distance,
    check_on_foot = interact_data.check_on_foot,
  }

  if interact_data.check_upright then
    model_options.required_upright_value = 0.80
  end

  if interact_data.check then
    model_options.check = interact_data.check
  end

  AddTargetModel(prop_hashes, model_options)
end
