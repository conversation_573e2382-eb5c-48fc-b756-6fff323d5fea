.target-wrapper {
    position:absolute;
    top: 51vh;
    left: 51vw;
    height:30vh;
    width:auto;
    max-height: 30%;
    overflow-y: auto;
    overflow-x: hidden;
    display: none;
}

.target-eye {
    position: absolute;
    display: none;
    top: 49.3vh;
    left: 49.52vw;
    font-size: 2.0vh;
}

.target-label-wrapper {
    position: relative;

}

.target-label {
    list-style: none;

    font-family: "DM Sans";
    font-size: 1.3vh;
    font-weight: 500;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;

    color: white;
    text-transform: uppercase;
    user-select: none;
    white-space: nowrap;
}

.target-icon {
    color: rgb(30,144,255);
}

.target-box {
    background: linear-gradient(to right, rgba(19, 22, 60, 0.9), rgba(39, 41, 62, 0.6));
    padding:5px;
    text-align: start;
    border-radius: 3px;
    justify-content: center;
    margin-bottom:5px;
}

::-webkit-scrollbar {
    width: 12px; 
    height: 12px;
}

/* Styling the scrollbar track */
::-webkit-scrollbar-track {
    background-color: #27293e; /* Dark background for track */
    border-radius: 10px;
}

/* Styling the scrollbar thumb */
::-webkit-scrollbar-thumb {
    background-color: #39a0d0; /* Blue thumb color */
    border-radius: 10px;
}

/* Hover effect */
::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* Darker thumb color on hover */
}