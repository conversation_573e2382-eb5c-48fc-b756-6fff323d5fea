function generateAdvancedCookingOptions(table_id, state_key, craftable_items, group_name, is_biz, group_permission)
  local filter_function = function(flip)
    return function()
      local state = 0

      if state_key then
        state = GlobalState[state_key] or 0
      end

      local available = state & DLF_SHUT_DOWN == 0

      if flip then
        available = not available
      end

      return available
    end
  end

  local cook_options = {
    {
      event_client = 'null',
      icon = 'fa-regular fa-dumpster-fire',
      label = 'Table Destroyed',

      filter = filter_function(true),
    },

    {
      event_server = 'blrp_core:server:cooking-prepare',
      icon = 'fa-regular fa-box',
      label = 'Add/Remove Ingredients',
      ingredient_station_id = table_id,
      target_items = craftable_items,

      filter = filter_function(),
    },

    {
      event_server = 'blrp_core:server:cooking-checksolution',
      icon = 'fa-regular fa-box',
      label = 'View/Take Solution',
      ingredient_station_id = table_id,

      filter = filter_function(),
    },

    {
      event_server = 'blrp_core:server:cooking-changetemp',
      icon = 'fa-solid fa-temperature-arrow-up',
      label = 'Set heat: High',
      ingredient_station_id = table_id,
      heat_setting = 'high',

      filter = filter_function(),
    },

    {
      event_server = 'blrp_core:server:cooking-changetemp',
      icon = 'fa-solid fa-temperature-arrow-up',
      label = 'Set heat: Low',
      ingredient_station_id = table_id,
      heat_setting = 'low',

      filter = filter_function(),
    },

    {
      event_server = 'blrp_core:server:cooking-changetemp',
      icon = 'fa-solid fa-temperature-arrow-down',
      label = 'Set heat: Off',
      ingredient_station_id = table_id,
      heat_setting = 'off',

      filter = filter_function(),
    },
  }

  -- If group-specific, bind the group permission
  if group_name and is_biz then
    if not group_permission then
      group_permission = 'crafting'
    end

    for _, v in pairs(cook_options) do
      v.business_name = group_name
      v.component_id = group_permission
    end
  else
    for _, v in pairs(cook_options) do
      v.groups = group_name
    end
  end

  -- Add emergency stop, which isn't group bound ever
  table.insert(cook_options, {
    event_server = 'blrp_core:server:cooking-emergency-stop',
    icon = 'fa-regular fa-octagon',
    label = 'Emergency Stop!',
    ingredient_station_id = table_id,

    filter = filter_function(),
  })

  -- Add destroy option for police
  table.insert(cook_options, {
    event_server = 'blrp_core:server:drugs:destroyLab',
    icon = 'fa-regular fa-trash-can',
    label = 'Destroy',
    ingredient_station_id = table_id,
    state_key = state_key,

    filter = filter_function(),

    groups = {
      'LEO',
    },
  })

  return cook_options
end
