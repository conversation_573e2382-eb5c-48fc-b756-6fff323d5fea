<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal> 
  <Kits>
    <Item>
      <kitName>9891_expolgrowler_modkit</kitName>
      <id value="9891" />
      <kitType>MKT_SPECIAL</kitType>
    <visibleMods>
      <!-- Police Equipments -->
      <Item>
          <modelName>null</modelName>
          <modShopLabel>POL_SLICKTOP</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>extra_1</Item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>null</modelName>
          <modShopLabel>POL_PERFORMANCE</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_livery1</modelName>
          <modShopLabel>POL_LIV_INT</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_LIVERY_MOD</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		   <Item>
          <modelName>expolgrowler_null</modelName>
          <modShopLabel>POL_RAMB3</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>extra_4</Item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_console</modelName>
          <modShopLabel>POL_CONSOLE</modShopLabel>
          <linkedModels>
           <Item>expolgrowler_radar</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_trunk</modelName>
          <modShopLabel>POL_TRUNK</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_TRUNK</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_slight_1</modelName>
          <modShopLabel>POL_SLIGHT1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_slight_2</modelName>
          <modShopLabel>POL_SLIGHT2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_slight_3</modelName>
          <modShopLabel>POL_SLIGHT3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_cage_a</modelName>
          <modShopLabel>POL_ROLLCAGE</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_gunrack</modelName>
          <modShopLabel>POL_GUNCAGE</modShopLabel>
          <linkedModels>
          <item>grow_cage_a</item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="0" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Callsigns Digit 1 -->
        <Item>
          <modelName>expolgrowler_csign_a0</modelName>
          <modShopLabel>POL_C0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a1</modelName>
          <modShopLabel>POL_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a2</modelName>
          <modShopLabel>POL_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a3</modelName>
          <modShopLabel>POL_C3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a4</modelName>
          <modShopLabel>POL_C4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a5</modelName>
          <modShopLabel>POL_C5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a6</modelName>
          <modShopLabel>POL_C6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a7</modelName>
          <modShopLabel>POL_C7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a8</modelName>
          <modShopLabel>POL_C8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_a9</modelName>
          <modShopLabel>POL_C9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Callsigns Digit 2 -->
        <Item>
          <modelName>expolgrowler_csign_b0</modelName>
          <modShopLabel>POL_C0</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b1</modelName>
          <modShopLabel>POL_C1</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b2</modelName>
          <modShopLabel>POL_C2</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b3</modelName>
          <modShopLabel>POL_C3</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b4</modelName>
          <modShopLabel>POL_C4</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b5</modelName>
          <modShopLabel>POL_C5</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b6</modelName>
          <modShopLabel>POL_C6</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b7</modelName>
          <modShopLabel>POL_C7</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b8</modelName>
          <modShopLabel>POL_C8</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_b9</modelName>
          <modShopLabel>POL_C9</modShopLabel>
          <linkedModels />
          <turnOffBones>
          <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Callsigns Digit 3 -->
        <Item>
          <modelName>expolgrowler_csign_c0</modelName>
          <modShopLabel>POL_C0</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c1</modelName>
          <modShopLabel>POL_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c2</modelName>
          <modShopLabel>POL_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c3</modelName>
          <modShopLabel>POL_C3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c4</modelName>
          <modShopLabel>POL_C4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c5</modelName>
          <modShopLabel>POL_C5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c6</modelName>
          <modShopLabel>POL_C6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c7</modelName>
          <modShopLabel>POL_C7</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c8</modelName>
          <modShopLabel>POL_C8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>expolgrowler_csign_c9</modelName>
          <modShopLabel>POL_C9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <!-- Vanilla Modkits -->
	  <Item>
          <modelName>grow_hlite_l_1</modelName>
          <modShopLabel>CYPH_HLITE_1</modShopLabel>
          <linkedModels>
			<Item>grow_hlite_r_1</Item>
			<Item>grow_tlite_l_1</Item>
			<Item>grow_tlite_r_1</Item>
          </linkedModels>
		  <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hlite_l_2</modelName>
          <modShopLabel>CYPH_HLITE_2</modShopLabel>
          <linkedModels>
			<Item>grow_hlite_r_2</Item>
			<Item>grow_tlite_l_2</Item>
			<Item>grow_tlite_r_2</Item>
          </linkedModels>
		  <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hlite_l_3</modelName>
          <modShopLabel>CYPH_HLITE_3</modShopLabel>
          <linkedModels>
			<Item>grow_hlite_r_3</Item>
			<Item>grow_tlite_l_3</Item>
			<Item>grow_tlite_r_3</Item>
          </linkedModels>
		  <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hlite_l_4</modelName>
          <modShopLabel>CYPH_HLITE_4</modShopLabel>
          <linkedModels>
			<Item>grow_hlite_r_4</Item>
			<Item>grow_tlite_l_4</Item>
			<Item>grow_tlite_r_4</Item>
          </linkedModels>
		  <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hlite_l_5</modelName>
          <modShopLabel>CYPH_HLITE_5</modShopLabel>
          <linkedModels>
			<Item>grow_hlite_r_5</Item>
			<Item>grow_tlite_l_5</Item>
			<Item>grow_tlite_r_5</Item>
          </linkedModels>
		  <turnOffBones />
          <type>VMT_DOOR_L</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel1</modelName>
          <modShopLabel>SMOD2_STEER1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel2</modelName>
          <modShopLabel>SMOD2_STEER2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel3</modelName>
          <modShopLabel>SMOD2_STEER3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel4</modelName>
          <modShopLabel>SMOD2_STEER4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel5</modelName>
          <modShopLabel>SMOD2_STEER5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel6</modelName>
          <modShopLabel>SMOD2_STEER6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel7</modelName>
          <modShopLabel>SMOD2_STEER7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel8</modelName>
          <modShopLabel>SMOD2_STEER8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel9</modelName>
          <modShopLabel>SMOD2_STEER9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel10</modelName>
          <modShopLabel>SMOD2_STEER10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel11</modelName>
          <modShopLabel>SMOD2_STEER11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel12</modelName>
          <modShopLabel>SMOD2_STEER12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel13</modelName>
          <modShopLabel>SMOD2_STEER13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel14</modelName>
          <modShopLabel>SMOD2_STEER14</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel15</modelName>
          <modShopLabel>SMOD2_STEER15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_stwheel16</modelName>
          <modShopLabel>SMOD2_STEER16</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_mir_a1_l</modelName>
          <modShopLabel>GROW_MIRRORS_A1</modShopLabel>
          <linkedModels>
			<Item>grow_mir_a1_r</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_a</Item>
			<Item>misc_b</Item>
		  </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_mir_ac_l</modelName>
          <modShopLabel>GROW_MIRRORS_AC</modShopLabel>
          <linkedModels>
			<Item>grow_mir_ac_r</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_a</Item>
			<Item>misc_b</Item>
		  </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hcatch_b</modelName>
          <modShopLabel>GROW_HCATCH_B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hcatch_b1</modelName>
          <modShopLabel>GROW_HCATCH_B1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hcatch_b2</modelName>
          <modShopLabel>GROW_HCATCH_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hcatch_a</modelName>
          <modShopLabel>GROW_HCATCH_A</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hcatch_c</modelName>
          <modShopLabel>GROW_HCATCH_C</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hudpin_3</modelName>
          <modShopLabel>VTC_HACC_3</modShopLabel>
          <linkedModels />
          <turnOffBones	/>
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_hudpin_3a</modelName>
          <modShopLabel>VTC_HACC_4</modShopLabel>
          <linkedModels />
          <turnOffBones	/>
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_hudpin_3b</modelName>
          <modShopLabel>VTC_HACC_5</modShopLabel>
          <linkedModels />
          <turnOffBones	/>
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_hudpin_3c</modelName>
          <modShopLabel>VTC_HACC_6</modShopLabel>
          <linkedModels />
          <turnOffBones	/>
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_roof_c</modelName>
          <modShopLabel>GROW_ROOF_C</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_12</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_roof_b</modelName>
          <modShopLabel>GROW_ROOF_B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_13</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_roof_a1</modelName>
          <modShopLabel>GROW_ROOF_A1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_roof_a2</modelName>
          <modShopLabel>GROW_ROOF_A2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_roof_ac</modelName>
          <modShopLabel>GROW_ROOF_AC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_1</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_dash_1</modelName>
          <modShopLabel>GROW_DASH_1</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_p</Item>
		  </turnOffBones>
          <type>VMT_KNOB</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_dash_3</modelName>
          <modShopLabel>GROW_DASH_3</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_p</Item>
			<Item>misc_t</Item>
		  </turnOffBones>
          <type>VMT_KNOB</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_dash_2</modelName>
          <modShopLabel>GROW_DASH_2</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_p</Item>
			<Item>misc_t</Item>
		  </turnOffBones>
          <type>VMT_KNOB</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_dials1</modelName>
          <modShopLabel>GROW_DIALS1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_dials2</modelName>
          <modShopLabel>GROW_DIALS2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_dials3</modelName>
          <modShopLabel>GROW_DIALS3</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_seats_a</modelName>
          <modShopLabel>CYPH_SEATS_A</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_s</Item>
		  </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bucketa1_smod</modelName>
          <modShopLabel>SMOD_BSEAT2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		 <Item>
          <modelName>grow_bucketa2_smod</modelName>
          <modShopLabel>SMOD_BSEAT3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_bucketa3_smod</modelName>
          <modShopLabel>SMOD_BSEAT4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bucketb1_smod</modelName>
          <modShopLabel>SMOD_BSEAT5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		 <Item>
          <modelName>grow_bucketb2_smod</modelName>
          <modShopLabel>SMOD_BSEAT6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_bucketb3_smod</modelName>
          <modShopLabel>SMOD_BSEAT7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bucketc1_smod</modelName>
          <modShopLabel>SMOD_BSEAT8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		 <Item>
          <modelName>grow_bucketc2_smod</modelName>
          <modShopLabel>SMOD_BSEAT9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_bucketc3_smod</modelName>
          <modShopLabel>SMOD_BSEAT10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bucketd1_smod</modelName>
          <modShopLabel>SMOD_BSEAT11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		 <Item>
          <modelName>grow_bucketd2_smod</modelName>
          <modShopLabel>SMOD_BSEAT12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>grow_bucketd3_smod</modelName>
          <modShopLabel>SMOD_BSEAT13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_seats_f</modelName>
          <modShopLabel>CYPH_SEATS_F</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_s</Item>
		  </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace1</modelName>
          <modShopLabel>ZR350_SBRACE1</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_brace2</modelName>
          <modShopLabel>ZR350_SBRACE2</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_brace3</modelName>
          <modShopLabel>ZR350_SBRACE3</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_brace4</modelName>
          <modShopLabel>ZR350_SBRACE4</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_brace5</modelName>
          <modShopLabel>ZR350_SBRACE5</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_brace6</modelName>
          <modShopLabel>ZR350_SBRACE6</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	  <Item>
          <modelName>grow_brace8</modelName>
          <modShopLabel>ZR350_SBRACE8</modShopLabel>
          <linkedModels />
          <turnOffBones/>
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_a</modelName>
          <modShopLabel>CYPH_BRACE_A</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_b1</modelName>
          <modShopLabel>CYPH_BRACE_B1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_b2</modelName>
          <modShopLabel>CYPH_BRACE_B2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_c</modelName>
          <modShopLabel>CYPH_BRACE_C</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_d</modelName>
          <modShopLabel>CYPH_BRACE_D</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_e</modelName>
          <modShopLabel>CYPH_BRACE_E</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_g</modelName>
          <modShopLabel>CYPH_BRACE_G</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_brace_h</modelName>
          <modShopLabel>CYPH_BRACE_H</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_k</modelName>
          <modShopLabel>GROW_WING_K</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_8</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_b</modelName>
          <modShopLabel>GROW_WING_B</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_7</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_c</modelName>
          <modShopLabel>GROW_WING_C</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_6</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_d</modelName>
          <modShopLabel>GROW_WING_D</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_9</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_f</modelName>
          <modShopLabel>GROW_WING_F</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_5</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_e</modelName>
          <modShopLabel>GROW_WING_E</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_10</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_l</modelName>
          <modShopLabel>GROW_WING_L</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>mod_col_11</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_a</modelName>
          <modShopLabel>GROW_WING_A</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_a1</modelName>
          <modShopLabel>GROW_WING_A1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_ac</modelName>
          <modShopLabel>GROW_WING_AC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_h</modelName>
          <modShopLabel>GROW_WING_H</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_h1</modelName>
          <modShopLabel>GROW_WING_H1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_hc</modelName>
          <modShopLabel>GROW_WING_HC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_4</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_g</modelName>
          <modShopLabel>GROW_WING_G</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_3</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_g1</modelName>
          <modShopLabel>GROW_WING_G1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_3</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_wing_gc</modelName>
          <modShopLabel>GROW_WING_GC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTHOLDER</type>
          <bone>chassis</bone>
          <collisionBone>mod_col_3</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_vent_a</modelName>
          <modShopLabel>GROW_VENT_A</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_DOOR_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_vent_ac</modelName>
          <modShopLabel>GROW_VENT_AC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_DOOR_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_vent_b</modelName>
          <modShopLabel>GROW_VENT_B</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_DOOR_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_vent_bc</modelName>
          <modShopLabel>GROW_VENT_BC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_DOOR_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_fender_a</modelName>
          <modShopLabel>GROW_FENDER_A</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_fender_ac</modelName>
          <modShopLabel>GROW_FENDER_AC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_door_l_a</modelName>
          <modShopLabel>GROW_DOORS_A</modShopLabel>
          <linkedModels>
			<Item>grow_door_r_a</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_j</Item>
			<Item>misc_k</Item>
		  </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_door_l_b</modelName>
          <modShopLabel>GROW_DOORS_B</modShopLabel>
          <linkedModels>
			<Item>grow_door_r_b</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_j</Item>
			<Item>misc_k</Item>
		  </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_door_l_c</modelName>
          <modShopLabel>GROW_DOORS_C</modShopLabel>
          <linkedModels>
			<Item>grow_door_r_c</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_j</Item>
			<Item>misc_k</Item>
		  </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_door_l_d</modelName>
          <modShopLabel>GROW_DOORS_D</modShopLabel>
          <linkedModels>
			<Item>grow_door_r_d</Item>
          </linkedModels>
		  <turnOffBones>
         	<Item>misc_j</Item>
			<Item>misc_k</Item>
		  </turnOffBones>
          <type>VMT_INTERIOR5</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_4</modelName>
          <modShopLabel>GROW_SUN_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_4a</modelName>
          <modShopLabel>GROW_SUN_4A</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_4b</modelName>
          <modShopLabel>GROW_SUN_4B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_1a</modelName>
          <modShopLabel>CYPH_SUNS_B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_1b</modelName>
          <modShopLabel>CYPH_SUNS_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_2a</modelName>
          <modShopLabel>CYPH_SUNS_C</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_2b</modelName>
          <modShopLabel>CYPH_SUNS_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_3a</modelName>
          <modShopLabel>CYPH_SUNS_D</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_sun_3b</modelName>
          <modShopLabel>CYPH_SUNS_D2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_ac</modelName>
          <modShopLabel>GROW_EXH_AC</modShopLabel>
          <linkedModels />
          <turnOffBones>
         	<Item>misc_e</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_ad</modelName>
          <modShopLabel>GROW_EXH_AD</modShopLabel>
          <linkedModels />
          <turnOffBones>
         	<Item>misc_e</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_1</modelName>
          <modShopLabel>GROW_EXH_B</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_b</Item>
			<Item>grow_exh_b</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_1</modelName>
          <modShopLabel>GROW_EXH_BC</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_b</Item>
			<Item>grow_exh_bc</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_1</modelName>
          <modShopLabel>GROW_EXH_BD</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_b</Item>
			<Item>grow_exh_bd</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_c</modelName>
          <modShopLabel>GROW_EXH_C</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_c</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_4</modelName>
          <modShopLabel>GROW_EXH_D</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_d</Item>
			<Item>grow_exh_d</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_4</modelName>
          <modShopLabel>GROW_EXH_DC</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_d</Item>
			<Item>grow_exh_dc</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_4</modelName>
          <modShopLabel>GROW_EXH_DD</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_d</Item>
			<Item>grow_exh_dd</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_4</modelName>
          <modShopLabel>GROW_EXH_DE</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_d</Item>
			<Item>grow_exh_de</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_2</modelName>
          <modShopLabel>GROW_EXH_E</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_e</Item>
			<Item>grow_exh_e</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_2</modelName>
          <modShopLabel>GROW_EXH_EC</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_e</Item>
			<Item>grow_exh_ec</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_2</modelName>
          <modShopLabel>GROW_EXH_ED</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_e</Item>
			<Item>grow_exh_ed</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_2</modelName>
          <modShopLabel>GROW_EXH_EE</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_e</Item>
			<Item>grow_exh_ee</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_3</modelName>
          <modShopLabel>GROW_EXH_F</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_f</Item>
			<Item>grow_exh_f</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_3</modelName>
          <modShopLabel>GROW_EXH_FC</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_f</Item>
			<Item>grow_exh_fc</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_3</modelName>
          <modShopLabel>GROW_EXH_FD</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_f</Item>
			<Item>grow_exh_fd</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_exh_nodes_3</modelName>
          <modShopLabel>GROW_EXH_FE</modShopLabel>
		  <linkedModels>
         	<Item>grow_diff_f</Item>
			<Item>grow_exh_fe</Item>
		  </linkedModels>
          <turnOffBones>
         	<Item>misc_e</Item>
			<Item>misc_d</Item>
			<Item>exhaust</Item>
			<Item>exhaust_2</Item>
		  </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_bw</modelName>
          <modShopLabel>GROW_CAGE_BW</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_b</modelName>
          <modShopLabel>GROW_CAGE_B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_b2</modelName>
          <modShopLabel>GROW_CAGE_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_aw</modelName>
          <modShopLabel>GROW_CAGE_AW</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_a</modelName>
          <modShopLabel>GROW_CAGE_A</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_a2</modelName>
          <modShopLabel>GROW_CAGE_A2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_dw</modelName>
          <modShopLabel>GROW_CAGE_DW</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_d</modelName>
          <modShopLabel>GROW_CAGE_D</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_d2</modelName>
          <modShopLabel>GROW_CAGE_D2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_cw</modelName>
          <modShopLabel>GROW_CAGE_CW</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_c</modelName>
          <modShopLabel>GROW_CAGE_C</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_cage_c2</modelName>
          <modShopLabel>GROW_CAGE_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bumr_a</modelName>
          <modShopLabel>GROW_BUMR_A</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bumr_b</modelName>
          <modShopLabel>GROW_BUMR_B</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bumr_b2</modelName>
          <modShopLabel>GROW_BUMR_B2</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bumr_c</modelName>
          <modShopLabel>GROW_BUMR_C</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_bumr_c2</modelName>
          <modShopLabel>GROW_BUMR_C2</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_f</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_a1</modelName>
          <modShopLabel>GROW_SKIRT_A1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_ac</modelName>
          <modShopLabel>GROW_SKIRT_AC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_b1</modelName>
          <modShopLabel>GROW_SKIRT_B1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_b2</modelName>
          <modShopLabel>GROW_SKIRT_B2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_bc</modelName>
          <modShopLabel>GROW_SKIRT_BC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_c1</modelName>
          <modShopLabel>GROW_SKIRT_C1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_c2</modelName>
          <modShopLabel>GROW_SKIRT_C2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_skirt_cc</modelName>
          <modShopLabel>GROW_SKIRT_CC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_a</modelName>
          <modShopLabel>GROW_SPLIT_A</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_ac</modelName>
          <modShopLabel>GROW_SPLIT_AC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_d</modelName>
          <modShopLabel>GROW_SPLIT_D</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_d1</modelName>
          <modShopLabel>GROW_SPLIT_D1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_d2</modelName>
          <modShopLabel>GROW_SPLIT_D2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_dc</modelName>
          <modShopLabel>GROW_SPLIT_DC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_c</modelName>
          <modShopLabel>GROW_SPLIT_C</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_c1</modelName>
          <modShopLabel>GROW_SPLIT_C1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_c2</modelName>
          <modShopLabel>GROW_SPLIT_C2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_cc</modelName>
          <modShopLabel>GROW_SPLIT_CC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_b</modelName>
          <modShopLabel>GROW_SPLIT_B</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_b1</modelName>
          <modShopLabel>GROW_SPLIT_B1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_b2</modelName>
          <modShopLabel>GROW_SPLIT_B2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_split_bc</modelName>
          <modShopLabel>GROW_SPLIT_BC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_PLTVANITY</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_rsplit_b1</modelName>
          <modShopLabel>GROW_RSPLIT_B1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_GRILL</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_rsplit_b2</modelName>
          <modShopLabel>GROW_RSPLIT_B2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_GRILL</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_rsplit_bc</modelName>
          <modShopLabel>GROW_RSPLIT_BC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_GRILL</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_rsplit_a1</modelName>
          <modShopLabel>GROW_RSPLIT_A1</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_GRILL</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_rsplit_a2</modelName>
          <modShopLabel>GROW_RSPLIT_A2</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_GRILL</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_rsplit_ac</modelName>
          <modShopLabel>GROW_RSPLIT_AC</modShopLabel>
          <linkedModels />
		  <turnOffBones />
          <type>VMT_GRILL</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_ac</modelName>
          <modShopLabel>GROW_HOOD_AC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_f</modelName>
          <modShopLabel>GROW_HOOD_F</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_fc</modelName>
          <modShopLabel>GROW_HOOD_FC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_g</modelName>
          <modShopLabel>GROW_HOOD_G</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_gc</modelName>
          <modShopLabel>GROW_HOOD_GC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_b</modelName>
          <modShopLabel>GROW_HOOD_B</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_bc</modelName>
          <modShopLabel>GROW_HOOD_BC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_d</modelName>
          <modShopLabel>GROW_HOOD_D</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_dc</modelName>
          <modShopLabel>GROW_HOOD_DC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_e</modelName>
          <modShopLabel>GROW_HOOD_E</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_ec</modelName>
          <modShopLabel>GROW_HOOD_EC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_c1</modelName>
          <modShopLabel>GROW_HOOD_C1</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_c2</modelName>
          <modShopLabel>GROW_HOOD_C2</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_cc</modelName>
          <modShopLabel>GROW_HOOD_CC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_h</modelName>
          <modShopLabel>GROW_HOOD_H</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_hood_hc</modelName>
          <modShopLabel>GROW_HOOD_HC</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>bonnet</Item>
		  </turnOffBones> 
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_fbum_a</modelName>
          <modShopLabel>GROW_FBUM_A</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_o</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_fbum_b</modelName>
          <modShopLabel>GROW_FBUM_B</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_o</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
		<Item>
          <modelName>grow_fbum_c</modelName>
          <modShopLabel>GROW_FBUM_C</modShopLabel>
          <linkedModels />
		  <turnOffBones>
         	<Item>misc_o</Item>
		  </turnOffBones> 
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
	</visibleMods>
	<linkMods>
  <Item>
          <modelName>expolgrowler_radar</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>
  <Item>
          <modelName>grow_cage_a</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>expolgrowler_radar</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>grow_mir_a1_r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_mir_ac_r</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_door_r_a</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_door_r_b</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_door_r_c</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_door_r_d</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_diff_b</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_diff_c</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_diff_d</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_diff_e</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_diff_f</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_hlite_r_1</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_l_1</modelName>
          <bone>taillight_l</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_r_1</modelName>
          <bone>taillight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_hlite_r_2</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_l_2</modelName>
          <bone>taillight_l</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_r_2</modelName>
          <bone>taillight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_hlite_r_3</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_l_3</modelName>
          <bone>taillight_l</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_r_3</modelName>
          <bone>taillight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_hlite_r_4</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_l_4</modelName>
          <bone>taillight_l</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_r_4</modelName>
          <bone>taillight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_hlite_r_5</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_l_5</modelName>
          <bone>taillight_l</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_tlite_r_5</modelName>
          <bone>taillight_r</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_b</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_bc</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_bd</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_d</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_dc</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_dd</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_de</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_e</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_ec</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_ed</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_ee</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_f</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_fc</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_fd</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
		<Item>
          <modelName>grow_exh_fe</modelName>
          <bone>chassis</bone>
          <turnOffExtra value="false" />
		</Item>
	</linkMods>
    <statMods>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="125" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="150" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="200" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="250" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="6" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="12" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="18" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="24" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="30" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
		<Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
		</Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_01</identifier>
          <modifier value="55862314" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_01_PREVIEW</identifier>
          <modifier value="2156743178" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_02</identifier>
          <modifier value="400002352" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_02_PREVIEW</identifier>
          <modifier value="897484282" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>XM15_HORN_03</identifier>
          <modifier value="560832604" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>XM15_HORN_03_PREVIEW</identifier>
          <modifier value="314232747" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
   		<Item>
          <identifier>DLC_AW_Airhorn_01</identifier>
          <modifier value="3851180092" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_01_Preview</identifier>
          <modifier value="246182814" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>DLC_AW_Airhorn_02</identifier>
          <modifier value="3412861948" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_02_Preview</identifier>
          <modifier value="1804608241" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<Item>
          <identifier>DLC_AW_Airhorn_03</identifier>
          <modifier value="3374260066" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
		<!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
		<Item>
          <identifier>DLC_AW_Airhorn_03_Preview</identifier>
          <modifier value="2798044638" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
      </statMods>
       <slotNames>
       <Item>
          <slot>VMT_WING_L</slot>
          <name>TOP_MIR</name>
        </Item>
		<Item>
          <slot>VMT_WING_R</slot>
          <name>CMOD_SLOU_N</name>
        </Item>
		<Item>
          <slot>VMT_CHASSIS</slot>
          <name>TOP_CAGE</name>
        </Item>
        <Item>
          <slot>VMT_ICE</slot>
          <name>RAMBAR</name>
        </Item>
      </slotNames>
	  <liveryNames />
    </Item>
 </Kits>
 <Sirens>
    <Item>
      <id value="361213265446531"/>     
      <name>Exclusive_V2Lightbar</name>      
      <timeMultiplier value="1.00000000"/>
		  <lightFalloffMax value="80.00000000"/>
		  <lightFalloffExponent value="55.00000000"/>
		  <lightInnerConeAngle value="2.00000000"/>
	    <lightOuterConeAngle value="70.00000000"/>
	    <lightOffset value="0.00000000"/>
	    <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="200"/>
		  <leftHeadLight>
		    <sequencer value="0"/>
		  </leftHeadLight>
		  <rightHeadLight>
		    <sequencer value="0"/>
		  </rightHeadLight>
		  <leftTailLight>
		    <sequencer value="1431655765"/>
		  </leftTailLight>
		  <rightTailLight>
		    <sequencer value="2863311530"/>
		  </rightTailLight>
		  <leftHeadLightMultiples value="1"/>
		  <rightHeadLightMultiples value="1"/>
	    <leftTailLightMultiples value="1"/>
	    <rightTailLightMultiples value="1"/>
		  <useRealLights value="true"/>
		    <sirens>                
          <!--Siren 1 : Red-->
        <Item>
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="1.57079633" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2840947370" />
            <multiples value="3" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.00000000" />
          <lightGroup value="2" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="4" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 2 : Red-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2841029290" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 3 : Amber-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2863224406" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 4 : Amber-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2863306326" />
            <multiples value="3" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 5 : Amber-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1453938089" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 6 : Amber-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1454020009" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFFD700" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 7 : Red-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="2863224406" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFFFF0000" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 8 : Blue-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1454020009" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFF000AFF" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 9 : Blue-->
          <rotation>
            <delta value="0.00000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="3.14159300" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1431660885" />
            <multiples value="2" />
            <direction value="true" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFF000AFF" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="2" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>
        <Item>
          <!--Siren 10 : Blue-->
          <rotation>
            <delta value="-0.01000000" />
            <start value="0.00000000" />
            <speed value="3.00000000" />
            <sequencer value="4294967295" />
            <multiples value="1" />
            <direction value="false" />
            <syncToBpm value="true" />
          </rotation>
          <flashiness>
            <delta value="-1.57079633" />
            <start value="0.00000000" />
            <speed value="0.00000000" />
            <sequencer value="1431742805" />
            <multiples value="2" />
            <direction value="false" />
            <syncToBpm value="true" />
          </flashiness>
          <corona>
            <intensity value="50.00000000" />
            <size value="0.10000000" />
            <pull value="0.15000000" />
            <faceCamera value="false" />
          </corona>
          <color value="0xFF000AFF" />
          <intensity value="1.00000000" />
          <lightGroup value="1" />
          <rotate value="false" />
          <scale value="true" />
          <scaleFactor value="4" />
          <flash value="true" />
          <light value="true" />
          <spotLight value="true" />
          <castShadows value="false" />
        </Item>           
        <!--Siren 11 : Red -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2841029290"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 12 : Amber -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2863224406"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF000AFF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 13 : Amber -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1454020009"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 14 : Blue -->
        <Item>
            <rotation>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="3.14159265"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1431660885"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF000AFF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 15 : Blue-->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2863311530"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF0000FF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 16 : Blue-->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2840943274"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF0000FF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 17 : Red -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1454024021"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 18 : Blue -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1431655765"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 19 : Red -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="1431655765"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFFFF0000"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>                
        <!--Siren 20 : Blue -->
        <Item>
            <rotation>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="4294967295"/>
                <multiples value="1"/>
                <direction value="false"/>
                <syncToBpm value="true"/>
            </rotation>
            <flashiness>
                <delta value="0.00000000"/>
                <start value="0.00000000"/>
                <speed value="1.00000000"/>
                <sequencer value="2863311530"/>
                <multiples value="1"/>
                <direction value="true"/>
                <syncToBpm value="true"/>
            </flashiness>
            <corona>
                <intensity value="40.00000000"/>
                <size value="0.60000000"/>
                <pull value="0.01500000"/>
                <faceCamera value="false"/>
            </corona>
            <color value="0xFF0000FF"/>
            <intensity value="1.00000000"/>
            <lightGroup value="0"/>
            <rotate value="false"/>
            <scale value="true"/>
            <scaleFactor value="2"/>
            <flash value="true"/>
            <light value="true"/>
            <spotLight value="true"/>
            <castShadows value="false"/>
        </Item>
      </sirens>
    </Item>
  </Sirens>
</CVehicleModelInfoVarGlobal>