@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@700&display=swap");

@font-face {
    font-family: 'Cyberspace';
    src: url('./font/Cyberspace.woff2') format('woff2'),
        url('./font/Cyberspace.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

body {
  padding: 0;
  margin: 0;
  overflow: hidden;
}
* {
  font-size: 0.9vw;
}

.menu {
  position: absolute;
  background-image: url("./img/motif.png");
  background-size: 125%;
  background-repeat: no-repeat;
  background-postion: center;
  border-radius: 1vw;
  width: 45vw;
  height: 30vw;
  top: 50%;
  right: 50%;
  box-shadow: 0vw 0vw 0.5vw black;
  transform: translate(50%, -50%);
  border-left: 0.2vw solid white;
  border-top-left-radius: .3vw;
  border-bottom-left-radius: .3vw;
  border-right: 0.2vw solid white;
  border-top-right-radius: .3vw;
  border-bottom-right-radius: .3vw;
}

.menuBG {
  background: linear-gradient(315deg, rgb(0 0 255 / 50%) 26%, rgb(255 0 0 / 50%) 70%);
  width: 100%;
  height: 100%;
}

.menu h1 {
  color: white;
  font-size: 2.3vw;
  font-family: Cyberspace;
  font-weight: normal;
  position: absolute;
  top: -3vw;
  width:100%;
  text-align: center;
    text-shadow: 0vw 0vw 0.5vw black;
}

.menu h2 {
  color: white;
  font-size: 1.3vw;
  font-family: Cyberspace;
  font-weight: normal;
  position: absolute;
  bottom: -1.7vw;
  width:100%;
  text-align: center;
    text-shadow: 0vw 0vw 0.5vw black;
}
.ListContainer h1 {
  color: white;
  font-size: 2.3vw;
  font-family: Cyberspace;
  font-weight: normal;
  position: absolute;
  top: -3vw;
  width:100%;
  text-align: center;
  text-shadow: 0vw 0vw 0.5vw black;
}



.Division {
  position: absolute;
  /* background-color: white; */
  width: 100%;
  height: 70%;
  /* top: 63%; */
  top: 65%;
  right: 43.5%;
  transform: translate(50%, -50%);
}

.SelectType {
  width: 15vw;
  height: 80%;
  display: inline-table;
  margin-left: 3vw;
  border-radius: 1vw;
  transition: 0.5s;
}



.SelectType:hover {
  cursor: pointer;
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}

.SelectType h1 {
  position: relative;
  width: 100%;
  margin-left: 0;
  text-align: center;
  font-family: Cyberspace;
  font-weight: normal;
}

.JoinGame {
  background-image: url("./img/Race.png");
  background-repeat: no-repeat;
  background-position: 20%;
  background-size: cover;
}

.CreateGame {
  background-image: url("./img/Create.png");
  background-repeat: no-repeat;
  background-position: 20%;
  background-size: cover;
}


.closeMenu {
  color: #dc3545;
  text-shadow: 0vw 0vw 0.5vw rgb(0, 0, 0);
  position: absolute;
  top: -1.5vw;
  font-size: 3vw;
  right: 0.5vw;
  /* transform: translate(50%, -50%); */
  z-index: 2;
}

.closeMenu:hover {
  cursor: pointer;
  text-shadow: 0vw 0vw 1vw rgb(0, 0, 0);
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  transform: scale(1.3);
}

.closeMenuList {
  position: absolute;
  top: 23.5%;
  right: 29%;
  font-size: 3vw;
  color: #dc3545;
  transform: translate(50%, -50%);
  z-index: 2;
  text-shadow: 0vw 0vw 0.5vw rgb(0, 0, 0);
}

.closeMenuList:hover {
  cursor: pointer;
  text-shadow: 0vw 0vw 1vw rgb(0, 0, 0);
}


.centerPart{
  position: absolute;
  width: 50%;
  height: 100%;
  left: 25%;
  top: 1vw;
  z-index:1;
}


input:focus,
textarea:focus,
select:focus {
  outline: none;
}




.CreateGameMode {
  position: absolute;
  top: 8.0vw;
  width: 100%;
}

.CreateGameMode h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -2vw;
  width: 100%;
  color: rgba(255, 255, 255, 0.892);
}


.InputCreateGameMode {
  /* margin-left: 1.4vw; */
  /* width: 17vw; */
  width: 90%;
  margin-left:5%;
  outline: none;
  height: 2.5vw;
  margin-top: 1vw;
  background-color: black;
  color: #ffffff;
  border-radius: 0.5vw;
  font-family: "Poppins";
  font-size: 0.8vw;
  padding: 0.5vw 1vw 0.5vw 1vw;
}

.InputCreateGameMode:focus {
  animation: glow 800ms ease-out infinite alternate;
  background-color: #000;
  border-color: rgb(153, 51, 51);
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.2), inset 0 0 5px rgba(255, 0, 0, 0.1),
    0 2px 0 #000;
}







.CreateMap {
  position: absolute;
  top: 3.0vw;
  width: 100%;
}

.CreateMap h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -2vw;
  width: 100%;
  color: rgba(255, 255, 255, 0.892);
}


.InputCreateMap {
  /* margin-left: 1.4vw; */
  /* width: 17vw; */
  width: 90%;
  margin-left: 5%;
  outline: none;
  height: 2.5vw;
  margin-top: 1vw;
  background-color: black;
  color: #ffffff;
  border-radius: 0.5vw;
  font-family: "Poppins";
  font-size: 0.8vw;
  padding: 0.5vw 1vw 0.5vw 1vw;
}

.InputCreateMap:focus {
  animation: glow 800ms ease-out infinite alternate;
  background-color: #000;
  border-color: rgb(153, 51, 51);
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.2), inset 0 0 5px rgba(255, 0, 0, 0.1),
    0 2px 0 #000;
}













.CreateNbPlayer {
  position: absolute;
  top: 3.0vw;
  width: 100%;
}

.CreateNbPlayer h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -2vw;
  width: 100%;
  color: rgba(255, 255, 255, 0.892);
}

.InputCreateNbPlayer {
  /* margin-left: 1.4vw; */
  /* width: 17vw; */
  width: 90%;
  margin-left:5%;
  outline: none;
  height: 2.5vw;
  margin-top: 1vw;
  background-color: black;
  color: #ffffff;
  border-radius: 0.5vw;
  font-family: "Poppins";
  font-size: 0.8vw;
  padding: 0.5vw 1vw 0.5vw 1vw;
}

.InputCreateNbPlayer:focus {
  animation: glow 800ms ease-out infinite alternate;
  background-color: #000;
  border-color: rgb(153, 51, 51);
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.2), inset 0 0 5px rgba(255, 0, 0, 0.1),
    0 2px 0 #000;
}




.CreateRound {
  position: absolute;
  top: 8.0vw;
  width: 100%;
}

.CreateRound h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -2vw;
  width: 100%;
  color: rgba(255, 255, 255, 0.892);
}

.InputCreateRound {
  /* margin-left: 1.4vw; */
  /* width: 17vw; */
  width: 90%;
  margin-left:5%;
  outline: none;
  height: 2.5vw;
  margin-top: 1vw;
  background-color: black;
  color: #ffffff;
  border-radius: 0.5vw;
  font-family: "Poppins";
  font-size: 0.8vw;
  padding: 0.5vw 1vw 0.5vw 1vw;
}

.InputCreateRound:focus {
  animation: glow 800ms ease-out infinite alternate;
  background-color: #000;
  border-color: rgb(153, 51, 51);
  box-shadow: 0 0 5px rgba(255, 0, 0, 0.2), inset 0 0 5px rgba(255, 0, 0, 0.1),
    0 2px 0 #000;
}


















.CreateIMGMap {
  position: absolute;
  top: 12vw;
}




.CreateIMGMap h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -2vw;
  left: 1.25vw;
  width: 100%;
  color: rgba(255, 255, 255, 0.892);
}

.CreateIMGMap input {
  height: 15vw;
}


.CreateIMGMapBox {
  position: absolute;
  width: 22.5vw;
  height: 12.65625vw;
  border: 0.2vw white dashed;
  top: 1.2vw;
  animation: glow 800ms ease-out infinite alternate;
  /* left: 1.5vw; */
  /* background-image: url(https://cdn.discordapp.com/attachments/755368331485380658/959922752599507004/20220402230954_1.jpg); */
  background-repeat: no-repeat;
  background-size: cover;
}

.CreateIMGMapBox i {
  color: #ffffffb8;
  font-size: 5vw;
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(50%, -50%);
}

.CreateIMGMapBox:hover {
  cursor: pointer;
}



.CreateinputImage {
  position: absolute;
  top: 15vw;
  left: 2vw;
}

.CreateinputImage input {
  width: 12vw;
}

.CreateinputImage h1 {
  font-size: 1.2vw;
  position: absolute;
  top: -1vw;
}

.CreateValidate {
  position: absolute;
  bottom: -2vw;
  left: 38vw;
}

/* textarea { */
  /* width: 10vw; */
  /* height: 5vw; */
/* } */

::placeholder {
  color: white;
  opacity: 1; 
}

:-ms-input-placeholder {
  color: white;
}

::-ms-input-placeholder {
  color: white;
}

.rightSideReport {
  position: absolute;
  right: 0vw;
  width: 50%;
  height: 100%;
  top: 5vw;
  right: -1vw;
}

input[type="text"]::-webkit-search-decoration,
input[type="text"]::-webkit-search-cancel-button,
input[type="text"]::-webkit-search-results-button,
input[type="text"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}


.ValidateCreateGame {
  color: rgba(0, 255, 0, 0.899);
  text-shadow: 0vw 0vw 0.8vw white;
  position: absolute;
  bottom: -0.5vw;
  font-size: 3vw;
  right: -11vw;
}

.ValidateCreateGame:hover {
  cursor: pointer;
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  transform: scale(1.3);
}


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0; 
}

input[type="number"] {
  -moz-appearance: textfield; 
}

.ListReportPe {
  position: absolute;
  background-image: url("./img/motif.png");
  background-size: 125%;
  background-repeat: no-repeat;
  background-position: center;
  border-radius: 1vw;
  width: 45vw;
  height: 30vw;
  top: 50%;
  right: 50%;
  box-shadow: 0vw 0vw 0.5vw black;
  transform: translate(50%, -50%);
  border-left: 0.2vw solid white;
  border-top-left-radius: .3vw;
  border-bottom-left-radius: .3vw;
  border-right: 0.2vw solid white;
  border-top-right-radius: .3vw;
  border-bottom-right-radius: .3vw;
}


.bodyContLista {
  position: absolute;
  width: 90%;
  height: 80%;
  top: 45%;
  right: 50%;
  transform: translate(50%, -50%);
  overflow-y: auto;
}


table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 0.5vw;
  scroll-margin-left: 5vw;
  scroll-behavior: smooth;
}

thead span {
  color: white;
  font-size: 1vw;
  font-family: "Poppins";
  margin: 0vw 0.5vw 0vw 0.5vw;
}

thead i {
  color: white;
}

td {
  color: white;
  font-size: 0.7vw;
  font-family: "Poppins";
  text-align: center;
  padding: .5vw;
}

.LobbyName {
  border-left: 0.2vw solid white;
  border-top-left-radius: .3vw;
  border-bottom-left-radius: .3vw;
}

.GameStatus {
  border-right: 0.2vw solid white;
  border-top-right-radius: .3vw;
  border-bottom-right-radius: .3vw;
}

tbody > tr:nth-of-type(even) {
  background: linear-gradient(135deg, rgb(4 255 0 / 50%) 34%, rgba(31,5,247,0.5) 100%);
  padding: 0 .3vw;
}

tr {
    margin: none;
}
 
tbody > tr {
   background: linear-gradient(135deg, rgb(4 255 0 / 50%) 34%, rgba(31,5,247,0.5) 100%);
    padding: 0 .3vw;
}





::-webkit-scrollbar {
  width: .5vw;
  border-radius: 100vw;
  
}

::-webkit-scrollbar-track {
  background: #262626;
}

::-webkit-scrollbar-thumb {
  background: #007bff;
  height: 1vw;
}

::-webkit-scrollbar-thumb:hover {
  background: #2255a4;
  height: 1vw;
}

::-webkit-scrollbar-thumb:active {
  background: #11294f;
  height: 1vw;
}

::-webkit-scrollbar-thumb:focus {
  background: #a60000;
  height: 1vw;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance:none;
}

tr:hover {
  cursor: pointer;
}



.leftPart {
  position: absolute;
  width: 45%;
  height: 96%;
  left: 5%;
  top: 2%;
}

.rightPart {
  position: absolute;
  right: 0vw;
  width: 45%;
  height: 96%;
  top: 2%;
  right: 5%;
}

.leftPartCreate {
  position: absolute;
  width: 40%;
  height: 96%;
  left: 5%;
  top: 2%;
  z-index:80;
}

.rightPartCreate {
  position: absolute;
  right: 0vw;
  width: 40%;
  height: 96%;
  top: 2%;
  right: 5%;
  z-index:80;
}

.centerPart2{
  position: absolute;
  width: 50%;
  height: 96%;
  left: 25%;
  top: 2%;
}

.JoinRed {
  position: absolute;
  top: 5.5vw;
  width: 100%;
}

.JoinRed h1 {
  font-size: 1vw;
  text-align: left;
  position: absolute;
  top: -1vw;
  /* left: -0.5vw; */
  color: rgba(255, 255, 255, 0.892);
}

.JoinBlue {
  position: absolute;
  top: 5.5vw;
  width: 100%;
}

.JoinBlue h1 {
  font-size: 1vw;
  text-align: left;
  position: absolute;
  top: -1vw;
  /* left: -0.5vw; */
  color: rgba(255, 255, 255, 0.892);
}

.ButtonJoinRed {
 /* margin-left: 25%; */
 border-radius: 0.5vw;
 width: 45%;
 font-family: Cyberspace;
 color: white;
 height: 2vw;
 background-color: black;
}

.ButtonJoinRed:active {
 color: black;
 background-color: white;
}

.ButtonJoinRed:hover {
	box-shadow: inset 0px 0px 5px 5px red;
}

.ButtonJoinBlue {
 margin-left: 55%;
 border-radius: 0.5vw;
 width: 45%;
 font-family: Cyberspace;
 color: white;
 height: 2vw;
 background-color: black;
}

.ButtonJoinBlue:active {

 color: black;
 background-color: white;
}

.ButtonJoinBlue:hover {
	box-shadow: inset 0px 0px 5px 5px blue;
}

.JoinGameMode {
  margin-left:5%;
  position: absolute;
  top: 14.5vw;
}

.JoinGameMode h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -1.8vw;
  /* left: -0.5vw; */
  color: rgba(255, 255, 255, 0.892);
}


.JoinMap {
  margin-left:5%;
  position: absolute;
  top: 19.1vw;
}

.JoinMap h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -1.8vw;
  /* left: -0.5vw; */
  color: rgba(255, 255, 255, 0.892);
}

.JoinNbRound {
  margin-left:5%;
  position: absolute;
  top: 23.7vw;
}

.JoinNbRound h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -1.8vw;
  /* left: -0.5vw; */
  color: rgba(255, 255, 255, 0.892);
}


.JoinLobbyName {
  margin-left:10%;
  position: absolute;
  top: 14.5vw;
}

.JoinLobbyName h1 {
  font-size: 1.5vw;
  top: -1.8vw;
  width: 100%;
}

.JoinPlayerList {
  margin-left:10%;
  position: absolute;
  top: 19.1vw;
}

.JoinPlayerList h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -1.8vw;
  width: 100%;
  color: rgba(255, 255, 255, 0.892);
}

.JoinPlayerList input {
  height: 5vw;
}

.JoinInput {
  /* margin-left: 1.4vw; */
  width: 15vw;
  outline: none;
  height: 1.5vw;
  margin-top: 1vw;
  background-color: black;
  color: #ffffff;
  border-radius: 0.5vw;
  font-family: "Poppins";
  font-size: 0.8vw;
  padding: 0.5vw 1vw 0.5vw 1vw;
}

/* .JoinInput:focus { */
  /* animation: glow 800ms ease-out infinite alternate; */
  /* background-color: #000; */
  /* border-color: rgb(153, 51, 51); */
  /* box-shadow: 0 0 5px rgba(255, 0, 0, 0.2), inset 0 0 5px rgba(255, 0, 0, 0.1), */
    /* 0 2px 0 #000; */
/* } */


.JoinScreen {
  position: absolute;
  top: 0.5vw;
  width: 90%;
  left: 5%;
}

.JoinScreen h1 {
  font-size: 1.5vw;
  text-align: center;
  position: absolute;
  top: -1.8vw;
  width: 100%;
  color: rgba(255, 255, 255, 0.892);
}

/* .JoinScreen input { */
  /* height: 5vw; */
/* } */




.JoinBox {
  position: absolute;
  width: 20vw;
  height: 11.25vw;
  border: 0.2vw white dashed;
  top: 0.5vw;
  animation: glow 800ms ease-out infinite alternate;
  /* left: 1.5vw; */
  /* background-image: url(https://j.gifs.com/KkEoLn.gif); */
  /* background-image: url(https://media.discordapp.net/attachments/904763084780998686/955239506875908156/screenshot.jpg?width=1191&height=670); */
  background-repeat: no-repeat;
  background-size: cover;
}

.JoinBox i {
  color: #ffffffb8;
  font-size: 5vw;
  position: absolute;
  top: 50%;
  right: 50%;
  transform: translate(50%, -50%);
}

/* .JoinBox:hover { */
  /* cursor: pointer; */
/* } */


.ValidateJoinGame {
  color: rgba(0, 255, 0, 0.899);
  text-shadow: 0vw 0vw 0.8vw white;
  position: absolute;
  bottom: -0.5vw;
  font-size: 3vw;
  right: -11vw;
}

.ValidateJoinGame:hover {
  cursor: pointer;
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  transform: scale(1.3);
}

.ValidateStartGame {
  color: rgba(0, 255, 0, 0.899);
  text-shadow: 0vw 0vw 0.8vw white;
  position: absolute;
  bottom: 0vw;
  font-size: 2.5vw;
  right: -10.5vw;
}

.ValidateStartGame:hover {
  cursor: pointer;
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  transform: scale(1.3);
}



.SpectateGame {
  color: rgba(0, 255, 0, 0.899);
  text-shadow: 0vw 0vw 0.8vw white;
  position: absolute;
  bottom: 7.5vw;
  font-size: 2.5vw;
  right: -10.5vw;
}

.SpectateGame:hover {
  cursor: pointer;
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  transform: scale(1.3);
}

.BackListGame {
  color: rgba(0, 255, 0, 0.899);
  text-shadow: 0vw 0vw 0.8vw white;
  position: absolute;
  top: -1.75vw;
  font-size: 2.5vw;
  left: -10.5vw;
}

.BackListGame:hover {
  cursor: pointer;
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  transform: scale(1.3);
}

.ExitLobby {
  rotate: 180deg;
  color: rgba(220, 53, 70, 0.899);
  text-shadow: 0vw 0vw 0.8vw white;
  position: absolute;
  bottom: -0.5vw;
  font-size: 3vw;
  left: -11vw;
}

.ExitLobby:hover {
  cursor: pointer;
  -webkit-transform: scale(1.3);
  -moz-transform: scale(1.3);
  -ms-transform: scale(1.3);
  -o-transform: scale(1.3);
  transform: scale(1.3);
}


/* Tooltip text */
.ValidateJoinGame .tooltiptext {
  visibility: hidden;
  font-family: "Poppins";
  font-size: 0.6vw;
  width: 80px;
  background-color: #555;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;

  /* Position the tooltip text */
  position: absolute;
  z-index: 1;
  /* bottom: 125%; */
  /* left: 50%; */
  bottom: 25%;
  left: 250%;
  
  
  margin-left: -60px;

  /* Fade in tooltip */
  opacity: 0;
  transition: opacity 0.3s;
}

/* Tooltip arrow */
.ValidateJoinGame .tooltiptext::after {
  content: " ";
  position: absolute;
  top: 50%;
  right: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent #555 transparent transparent;
}

/* Show the tooltip text when you mouse over the tooltip container */
.ValidateJoinGame:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}




/* Tooltip text */
.ExitLobby .tooltiptext {
  rotate: 180deg;
  visibility: hidden;
  font-family: "Poppins";
  font-size: 0.6vw;
  width: 80px;
  background-color: #555;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;

  /* Position the tooltip text */
  position: absolute;
  z-index: 1;
  /* bottom: 125%; */
  /* left: 50%; */
  bottom: 25%;
  left: 250%;
  
  
  margin-left: -60px;

  /* Fade in tooltip */
  opacity: 0;
  transition: opacity 0.3s;
}

/* Tooltip arrow */
.ExitLobby .tooltiptext::after {
  content: " ";
  position: absolute;
  top: 50%;
  left: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent transparent #555;
}

/* Show the tooltip text when you mouse over the tooltip container */
.ExitLobby:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}




/* Tooltip text */
.ValidateStartGame .tooltiptext {
  visibility: hidden;
  font-family: "Poppins";
  font-size: 0.6vw;
  width: 80px;
  background-color: #555;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;

  /* Position the tooltip text */
  position: absolute;
  z-index: 1;
  /* bottom: 125%; */
  /* left: 50%; */
  bottom: 25%;
  left: 250%;
  
  
  margin-left: -60px;

  /* Fade in tooltip */
  opacity: 0;
  transition: opacity 0.3s;
}

/* Tooltip arrow */
.ValidateStartGame .tooltiptext::after {
  content: " ";
  position: absolute;
  top: 50%;
  right: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent #555 transparent transparent;
}

/* Show the tooltip text when you mouse over the tooltip container */
.ValidateStartGame:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}






.SpectateGame .tooltiptext {
  visibility: hidden;
  font-family: "Poppins";
  font-size: 0.6vw;
  width: 80px;
  background-color: #555;
  color: #fff;
  text-align: center;
  padding: 5px 0;
  border-radius: 6px;

  /* Position the tooltip text */
  position: absolute;
  z-index: 1;
  /* bottom: 125%; */
  /* left: 50%; */
  bottom: 25%;
  left: 250%;
  
  
  margin-left: -60px;

  /* Fade in tooltip */
  opacity: 0;
  transition: opacity 0.3s;
}

/* Tooltip arrow */
.SpectateGame .tooltiptext::after {
  content: " ";
  position: absolute;
  top: 50%;
  right: 100%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent #555 transparent transparent;
}

/* Show the tooltip text when you mouse over the tooltip container */
.SpectateGame:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}