local NUI_status = false

RegisterNUICallback('close', function()
    NUI_status = false
end)

RegisterNUICallback('change', function(data)
    NUI_status = false
    TriggerServerEvent('cd_easytime:ForceUpdate', {weather = data.weather, hours = data.time, dynamic = data.dynamic, freeze = data.freeze})
end)

RegisterNetEvent('cd_easytime:OpenUI')
AddEventHandler('cd_easytime:OpenUI', function(values)
    Open_UI(values)
end)

function Open_UI(values)
    TriggerEvent('cd_easytime:ToggleNUIFocus')
    SendNUIMessage({action = 'open', values = values})
end

function Close_UI()
    NUI_status = false
    SendNUIMessage({action = 'close'})
end

RegisterNetEvent('cd_easytime:ToggleNUIFocus')
AddEventHandler('cd_easytime:ToggleNUIFocus', function()
    NUI_status = true
    while NUI_status do
        Citizen.Wait(5)
        SetNuiFocus(NUI_status, NUI_status)
    end
    SetNuiFocus(false, false)
end)
