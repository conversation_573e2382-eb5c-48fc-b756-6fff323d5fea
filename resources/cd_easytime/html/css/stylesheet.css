:root{
    --easytime-daytime:#caf0f8!important;
    --easytime-nighttime: #070b34!important;

    --easytime-menu-background-dark:linear-gradient(180deg, #333, #323232)!important;
    --easytime-menu-background-light:linear-gradient(180deg, #fff, #f2f2f2)!important;
    --easytime-menu-text:#f3f3f3!important;

    --easytime-morning:#CAF0F8!important;
    --easytime-noon:#EDFAFD!important;
    --easytime-afternoon:#7577FA!important;
    --easytime-night:#040676!important;
}
html, body{
    user-select: none;
    background-color:transparent!important;
}
#easytime-card{
    width:35%;
    position:absolute;
    left:51%;
    top:51%;
    transform:translate(-51%, -51%);
    display:none;
    border:1px solid #333;
}
#easytime-card-body{
    position: relative;
    height:12rem;
    background-color: var(--easytime-daytime);
    overflow: hidden;
}
#easytime-sun, #easytime-moon{
    width:4rem;
    position:absolute;
}
#easytime-sun{
    bottom:calc(0px - 2rem);
    left:calc(0px - 2rem);
}
#easytime-moon{
    display:none;
    bottom:calc(0px - 4rem);
    left:calc(0px - 4rem);
}
#easytime-menu{
    background-image: var(--easytime-menu-background-dark);
    color:var(--easytime-menu-text);
}
.easytime-weather-icon{
    height:1.5rem;
    fill:#fff;
}
.easytime-morning{
    background-color:var(--easytime-morning);
}
.easytime-noon{
    background-color: var(--easytime-noon);
}
.easytime-afternoon{
    background-color: var(--easytime-afternoon);
}
.easytime-night{
    background-color:var(--easytime-night);
}
#easytime-clouds{
    width:100%;
    height:100%;
    position:absolute;
    left:0;
    top:0;
}
#easytime-stars{
    width:100%;
    height:100%;
    position:absolute;
    left:0;
    top:0;
    display:none;
}