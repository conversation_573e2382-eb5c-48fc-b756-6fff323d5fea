<?xml version="1.0" encoding="UTF - 8"?>

<CPedModelInfo__PersonalityDataList>
	<MovementModeUnholsterData>
		<Item>
			<Name>UNHOLSTER_UNARMED</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>unarmed_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H_MELEE</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>2h_melee_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_1H</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>1h_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>2h_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_MINIGUN</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>mini_holster_2h_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_UNARMED_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>unarmed_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H_MELEE_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>unarmed_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_1H_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>1h_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
		<Item>
			<Name>UNHOLSTER_2H_STEALTH</Name>
			<UnholsterClips>
				<Item>
					<Weapons>
						<Item>WEAPON_MILSPECKNIFE</Item>
					</Weapons>
					<Clip>2h_holster_unarmed</Clip>
				</Item>
			</UnholsterClips>
		</Item>
	</MovementModeUnholsterData>
	<MovementModes>
		<Item>
			<Name>DEFAULT_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_ACTION@P_M_ZERO@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId/>
								<WeaponClipFilterId/>
								<UpperBodyShadowExpressionEnabled value="false"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="true"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_ACTION@GENERIC@TRANS@UNARMED</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_STEALTH@P_M_ZERO@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId>WEAPONS@MELEE_STEALTH_1H</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="false"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_STEALTH@GENERIC@TRANS@UNARMED</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_STEALTH@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED_STEALTH</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
			<LastBattleEventHighEnergyStartTime value="0.00000000"/>
			<LastBattleEventHighEnergyEndTime value="5.00000000"/>
		</Item>
		<Item>
			<Name>MP_FEMALE_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_ACTION@MP_FEMALE@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId/>
								<WeaponClipFilterId/>
								<UpperBodyShadowExpressionEnabled value="false"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="true"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_ACTION@MP_FEMALE@UNARMED@TRANS@</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_STEALTH@P_M_ZERO@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId>WEAPONS@MELEE_STEALTH_1H</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="false"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_STEALTH@MP_FEMALE@UNARMED@TRANS</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_STEALTH@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED_STEALTH</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
			<LastBattleEventHighEnergyStartTime value="0.00000000"/>
			<LastBattleEventHighEnergyEndTime value="5.00000000"/>
		</Item>
		<Item>
			<Name>MICHAEL_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_ACTION@P_M_ZERO@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId/>
								<WeaponClipFilterId/>
								<UpperBodyShadowExpressionEnabled value="false"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="true"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_ACTION@P_M_ZERO@UNARMED@TRANS@A</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_STEALTH@P_M_ZERO@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId>WEAPONS@MELEE_STEALTH_1H</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="false"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_STEALTH@P_M_ZERO@UNARMED@TRANS@A</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_STEALTH@P_M_ZERO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED_STEALTH</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
			<LastBattleEventHighEnergyStartTime value="0.00000000"/>
			<LastBattleEventHighEnergyEndTime value="5.00000000"/>
		</Item>
		<Item>
			<Name>FRANKLIN_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_ACTION@P_M_ONE@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId/>
								<WeaponClipFilterId/>
								<UpperBodyShadowExpressionEnabled value="false"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="true"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_ACTION@P_M_ONE@UNARMED@TRANS@A</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_ACTION@P_M_ONE@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_STEALTH@P_M_ONE@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId>WEAPONS@MELEE_STEALTH_1H@P_M_ONE</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="false"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_STEALTH@P_M_ONE@UNARMED@TRANS@A</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_STEALTH@P_M_ONE@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED_STEALTH</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
			<LastBattleEventHighEnergyStartTime value="0.00000000"/>
			<LastBattleEventHighEnergyEndTime value="5.00000000"/>
		</Item>
		<Item>
			<Name>TREVOR_ACTION</Name>
			<MovementModes>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_ACTION@P_M_TWO@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId/>
								<WeaponClipFilterId/>
								<UpperBodyShadowExpressionEnabled value="false"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="true"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_ACTION@P_M_TWO@UNARMED@TRANS@A</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_ACTION@P_M_TWO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
				<Item>
					<Item>
						<Weapons>
							<Item>WEAPON_MILSPECKNIFE</Item>
						</Weapons>
						<ClipSets>
							<Item>
								<MovementClipSetId>MOVE_STEALTH@P_M_TWO@UNARMED@CORE</MovementClipSetId>
								<WeaponClipSetId>WEAPONS@MELEE_STEALTH_1H@P_M_TWO</WeaponClipSetId>
								<WeaponClipFilterId>UpperbodyAndIk_filter</WeaponClipFilterId>
								<UpperBodyShadowExpressionEnabled value="true"/>
								<UpperBodyFeatheredLeanEnabled value="false"/>
								<UseWeaponAnimsForGrip value="false"/>
								<UseLeftHandIk value="false"/>
								<IdleTransitionBlendOutTime value="0.50000000"/>
								<IdleTransitions>
									<Item>MOVE_STEALTH@P_M_TWO@UNARMED@TRANS@A</Item>
								</IdleTransitions>
								<UnholsterClipSetId>MOVE_STEALTH@P_M_TWO@HOLSTER</UnholsterClipSetId>
								<UnholsterClipData>UNHOLSTER_UNARMED_STEALTH</UnholsterClipData>
							</Item>
						</ClipSets>
					</Item>
				</Item>
			</MovementModes>
			<LastBattleEventHighEnergyStartTime value="0.00000000"/>
			<LastBattleEventHighEnergyEndTime value="5.00000000"/>
		</Item>
	</MovementModes>
</CPedModelInfo__PersonalityDataList>

