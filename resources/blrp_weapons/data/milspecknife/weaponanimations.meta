<?xml version="1.0" encoding="UTF - 8"?>

<CWeaponAnimationsSets>
	<WeaponAnimationsSets>
		<Item key="Default">
			<WeaponAnimations>
        <Item key="WEAPON_MILSPECKNIFE">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Melee1h</CoverWeaponClipSetHash>
          <MotionClipSetHash>Wpn_Switchblade_WeaponHolding</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash>Wpn_Switchblade_Strafe</MotionStrafingClipSetHash>
          <MotionStrafingStealthClipSetHash>Wpn_Switchblade_StrafeStealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash>move_strafe@knife</MotionStrafingUpperBodyClipSetHash>
          <WeaponClipSetHash>anim@melee@switchblade@holster</WeaponClipSetHash>
          <WeaponClipSetStreamedHash />
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>Wpn_Switchblade_Melee</MeleeClipSetHash>
          <MeleeVariationClipSetHash>melee@knife@streamed_variations</MeleeVariationClipSetHash>
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@1H_MELEE@KNIFE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="0.000000" />
          <AnimBlindFireRateModifier value="0.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_SWITCHBLADE" />
		      <AimGrenadeThrowNormalClipsetHash />
		      <AimGrenadeThrowAlternateClipsetHash />
        </Item>
			</WeaponAnimations>
		</Item>
		<Item key="FirstPerson">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
        <Item key="WEAPON_MILSPECKNIFE">
		      <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Melee1h</CoverWeaponClipSetHash>
          <MotionClipSetHash>anim@weapons@first_person@aim_idle@generic@melee@switchblade@shared@core</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash>move_strafe_melee_unarmed_fps</MotionStrafingClipSetHash>
          <MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
          <MotionStrafingUpperBodyClipSetHash>move_strafe@melee_knife_fps</MotionStrafingUpperBodyClipSetHash>
          <WeaponClipSetHash>anim@weapons@first_person@aim_idle@generic@melee@switchblade@shared@core</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@melee@knife@</WeaponClipSetHashStealth>
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash>melee@knife@streamed_core_fps</MeleeClipSetHash>
          <MeleeVariationClipSetHash>melee@knife@streamed_variations</MeleeVariationClipSetHash>
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@1H_MELEE@KNIFE</JumpUpperbodyClipSetHash>
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="0.000000" />
          <AnimBlindFireRateModifier value="0.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_SWITCHBLADE" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromUnholsterHash>anim@weapons@first_person@aim_idle@generic@melee@switchblade@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
	        <WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@melee@one_handed@shared@core</WeaponClipSetHashForClone>
        </Item>
			</WeaponAnimations>
		</Item>
	</WeaponAnimationsSets>
</CWeaponAnimationsSets>

