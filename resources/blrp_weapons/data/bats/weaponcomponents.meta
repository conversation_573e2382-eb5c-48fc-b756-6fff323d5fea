<?xml version="1.0" encoding="UTF - 8"?>
<CWeaponComponentInfoBlob>
	<Infos>
    <!-- XM3 bat skins (base game) -->
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3</Name>
      <Model>W_ME_Bat_XM3</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_01</Name>
      <Model>W_ME_Bat_XM3_01</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_02</Name>
      <Model>W_ME_Bat_XM3_02</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_03</Name>
      <Model>W_ME_Bat_XM3_03</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_04</Name>
      <Model>W_ME_Bat_XM3_04</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_05</Name>
      <Model>W_ME_Bat_XM3_05</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_06</Name>
      <Model>W_ME_Bat_XM3_06</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_07</Name>
      <Model>W_ME_Bat_XM3_07</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_08</Name>
      <Model>W_ME_Bat_XM3_08</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_XM3_09</Name>
      <Model>W_ME_Bat_XM3_09</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <!-- BLRP bat skins -->
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_01</Name>
      <Model>W_ME_Bat_BL_01</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_02</Name>
      <Model>W_ME_Bat_BL_02</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_03</Name>
      <Model>W_ME_Bat_BL_03</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_04</Name>
      <Model>W_ME_Bat_BL_04</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_05</Name>
      <Model>W_ME_Bat_BL_05</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_06</Name>
      <Model>W_ME_Bat_BL_06</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_07</Name>
      <Model>W_ME_Bat_BL_07</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_08</Name>
      <Model>W_ME_Bat_BL_08</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_09</Name>
      <Model>W_ME_Bat_BL_09</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_10</Name>
      <Model>W_ME_Bat_BL_10</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_11</Name>
      <Model>W_ME_Bat_BL_11</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_12</Name>
      <Model>W_ME_Bat_BL_12</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_13</Name>
      <Model>W_ME_Bat_BL_13</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_14</Name>
      <Model>W_ME_Bat_BL_14</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_15</Name>
      <Model>W_ME_Bat_BL_15</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_16</Name>
      <Model>W_ME_Bat_BL_16</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_17</Name>
      <Model>W_ME_Bat_BL_17</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_18</Name>
      <Model>W_ME_Bat_BL_18</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_19</Name>
      <Model>W_ME_Bat_BL_19</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_20</Name>
      <Model>W_ME_Bat_BL_20</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_21</Name>
      <Model>W_ME_Bat_BL_21</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_22</Name>
      <Model>W_ME_Bat_BL_22</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_23</Name>
      <Model>W_ME_Bat_BL_23</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_24</Name>
      <Model>W_ME_Bat_BL_24</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
    <Item type="CWeaponComponentVariantModelInfo">
      <Name>COMPONENT_BAT_VARMOD_BL_25</Name>
      <Model>W_ME_Bat_BL_25</Model>
      <LocName>WCT_INVALID</LocName>
      <LocDesc>WCD_INVALID</LocDesc>
      <AttachBone />
      <AccuracyModifier type="NULL" />
      <DamageModifier type="NULL" />
      <bShownOnWheel value="false" />
      <CreateObject value="false" />
      <HudDamage value="0" />
      <HudSpeed value="0" />
      <HudCapacity value="0" />
      <HudAccuracy value="0" />
      <HudRange value="0" />
      <TintIndexOverride value="2"/>
    </Item>
	</Infos>
	<InfoBlobName/>
</CWeaponComponentInfoBlob>
