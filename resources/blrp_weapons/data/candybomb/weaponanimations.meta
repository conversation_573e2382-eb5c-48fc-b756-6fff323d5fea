<?xml version="1.0" encoding="UTF-8"?>

<CWeaponAnimationsSets>
  <WeaponAnimationsSets>
    <Item key="Default">
      <Fallback />
      <WeaponAnimations>
        <Item key="WEAPON_CANDYBOMB">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Thrown_Grenade</CoverWeaponClipSetHash>
          <MotionClipSetHash>Wpn_Grenade_WeaponHolding</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>Wpn_Thrown_Grenade</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>Wpn_Thrown_Grenade_Str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPerson">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_CANDYBOMB">
          <MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Thrown_Grenade</CoverWeaponClipSetHash>
          <MotionClipSetHash>fps_projectile_motion_ball</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_idle@generic@projectile@ball@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@projectile@ball@ball_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash></FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@projectile@misc@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@projectile@ball@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
          <WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@projectile@shared@core</WeaponClipSetHashForClone>
          <FPSFidgetClipsetHashes>
           <Item>weapons@first_person@aim_idle@p_m_zero@projectile@ball@fidgets@a</Item>
           <Item>weapons@first_person@aim_idle@p_m_zero@projectile@ball@fidgets@b</Item>
           <Item>weapons@first_person@aim_idle@p_m_zero@projectile@ball@fidgets@c</Item>
         </FPSFidgetClipsetHashes>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPersonAiming">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_CANDYBOMB">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Thrown_Grenade</CoverWeaponClipSetHash>
          <MotionClipSetHash>fps_projectile_motion_ball</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_lt@generic@projectile@misc@shared@core</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@projectile@ball@ball_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@projectile@ball@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash></FPSTransitionFromLTHash>
          <FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@projectile@misc@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
          <FPSFidgetClipsetHashes>
            <Item>weapons@first_person@aim_lt@p_m_zero@projectile@misc@shared@fidgets@a</Item>
            <Item>weapons@first_person@aim_lt@p_m_zero@projectile@misc@shared@fidgets@b</Item>
            <Item>weapons@first_person@aim_lt@p_m_zero@projectile@misc@shared@fidgets@C</Item>
          </FPSFidgetClipsetHashes>
        </Item>
      </WeaponAnimations>
    </Item>
    <Item key="FirstPersonRNG">
      <Fallback>Default</Fallback>
      <WeaponAnimations>
        <Item key="WEAPON_CANDYBOMB">
          <CoverMovementClipSetHash />
          <CoverMovementExtraClipSetHash />
          <CoverAlternateMovementClipSetHash />
          <CoverWeaponClipSetHash>Cover_Wpn_Thrown_Grenade</CoverWeaponClipSetHash>
          <MotionClipSetHash>fps_projectile_motion_ball</MotionClipSetHash>
          <MotionFilterHash>BothArms_filter</MotionFilterHash>
          <MotionCrouchClipSetHash />
          <MotionStrafingClipSetHash />
          <MotionStrafingStealthClipSetHash />
          <MotionStrafingUpperBodyClipSetHash />
          <WeaponClipSetHash>weapons@first_person@aim_rng@generic@projectile@ball@</WeaponClipSetHash>
          <WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@projectile@ball@ball_str</WeaponClipSetStreamedHash>
          <WeaponClipSetHashInjured />
          <WeaponClipSetHashStealth />
          <WeaponClipSetHashHiCover />
          <AlternativeClipSetWhenBlocked />
          <ScopeWeaponClipSet />
          <AlternateAimingStandingClipSetHash />
          <AlternateAimingCrouchingClipSetHash />
          <FiringVariationsStandingClipSetHash />
          <FiringVariationsCrouchingClipSetHash />
          <AimTurnStandingClipSetHash />
          <AimTurnCrouchingClipSetHash />
          <MeleeClipSetHash />
          <MeleeVariationClipSetHash />
          <MeleeTauntClipSetHash />
          <MeleeSupportTauntClipSetHash />
          <MeleeStealthClipSetHash />
          <ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
          <JumpUpperbodyClipSetHash />
          <FallUpperbodyClipSetHash />
          <FromStrafeTransitionUpperBodyClipSetHash />
          <SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
          <SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
          <AnimFireRateModifier value="1.000000" />
          <AnimBlindFireRateModifier value="1.000000" />
          <AnimWantingToShootFireRateModifier value="-1.000000" />
          <UseFromStrafeUpperBodyAimNetwork value="false" />
          <AimingDownTheBarrel value="true" />
          <WeaponSwapData ref="SWAP_DEFAULT" />
          <AimGrenadeThrowNormalClipsetHash />
          <AimGrenadeThrowAlternateClipsetHash />
          <FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@projectile@ball@aim_trans@idle_to_idlerng</FPSTransitionFromIdleHash>
          <FPSTransitionFromRNGHash></FPSTransitionFromRNGHash>
          <FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@projectile@misc@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
        </Item>
       </WeaponAnimations>
    </Item>
  </WeaponAnimationsSets>
</CWeaponAnimationsSets>
