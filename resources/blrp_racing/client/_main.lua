tRacing = {}
T.bindInstance('racing', tRacing)

function Draw2DText(x, y, text, scale)
  -- Draw text on screen
  SetTextFont(4)
  SetTextProportional(7)
  SetTextScale(scale, scale)
  SetTextColour(255, 255, 255, 255)
  SetTextDropShadow(0, 0, 0, 0,255)
  SetTextDropShadow()
  SetTextEdge(4, 0, 0, 0, 255)
  SetTextOutline()
  SetTextEntry("STRING")
  AddTextComponentString(text)
  DrawText(x, y)
end

local_source = GetPlayerServerId(PlayerId())

race_data = nil
local blip_handles = {}

local checkpoint_handle = nil

local current_checkpoint_idx = 0
local current_checkpoint = nil
local next_checkpoint = nil
local current_lap = 1

local race_threads_running = false

local ping_correction = 0

RegisterNetEvent('blrp_racing:client:setPingCorrection', function(_ping_correction)
  ping_correction = _ping_correction
end)

RegisterNetEvent('blrp_racing:client:syncParticipants', function(join_code, participants)
  if not race_data or race_data.join_code ~= join_code then
    return
  end

  local count = 0

  for _, __ in pairs(participants) do
    count = count + 1
  end

  race_data.participants = participants
  race_data.participant_count = count

  if participants[local_source] then
    race_data.spectator = false
  else
    race_data.spectator = true
  end

  race_data.time_until_start = 2000 - ping_correction
end)

RegisterNetEvent('blrp_racing:client:syncParticipantCheckpoint', function(join_code, participant_id, checkpoint_index)
  if not race_data or race_data.join_code ~= join_code or not race_data.participants then
    return
  end

  race_data.participants[participant_id] = checkpoint_index
end)

tRacing.getCurrentRace = function()
  if not race_data then
    return
  end

  return race_data.join_code
end

tRacing.isVehicleDriver = function()
  local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

  if not vehicle or vehicle <= 0 then
    return false
  end

  return GetPedInVehicleSeat(vehicle, -1) == PlayerPedId()
end

tRacing.leaveCurrentRace = function()
  race_data = nil

  for _, blip_handle in pairs(blip_handles) do
    if DoesBlipExist(blip_handle) then
      RemoveBlip(blip_handle)
    end
  end

  if checkpoint_handle then
    DeleteCheckpoint(checkpoint_handle)
  end

  current_checkpoint_idx = 0
  current_checkpoint = nil
  next_checkpoint = nil
  current_lap = 1

  race_threads_running = false

  LocalPlayer.state:set('race_id', false, true)
end

tRacing.setCurrentRace = function(_race_data, editing)
  if not editing then
    exports.blrp_hud:SetRacingData({
      time_lap = 0,
      time_overall = 0,
      checkpoint_info = '-/-',
      lap_info = '-/-',
      position_info = '-/-',
    })

    LocalPlayer.state:set('race_id', _race_data.id, true)
  end

  race_threads_running = false

  race_data = _race_data  

  tRacing.setEditing(editing)

  current_checkpoint_idx = 0
  current_checkpoint = nil
  next_checkpoint = nil

  if editing then
    tRacing.setupCheckpoints()
    tRacing.setupBlipsEditor()
  else
    tRacing.setupBlips(true)
  end

  if not editing then
    tRacing.nextCheckpoint(true)

    Citizen.CreateThread(function()
      race_threads_running = true

      if race_data.time_until_start > 0 then
        local countdown_end_time = GetNetworkTimeAccurate() + race_data.time_until_start

        while race_threads_running and GetNetworkTimeAccurate() < countdown_end_time do
          Citizen.Wait(0)

          local diff = countdown_end_time - GetNetworkTimeAccurate()

          if diff < 5000 then
            Draw2DText(0.5, 0.4, ("~y~%d"):format(math.ceil(diff / 1000.0)), 3.0)

            local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

            if vehicle and vehicle > 0 and GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() then
              FreezeEntityPosition(vehicle, true)
            end

            diff = 0
          end

          exports.blrp_hud:SetRacingData({
            time_overall = (0 - diff),
          })
        end
      end

      if race_data then
        race_data.race_started = true
      end

      local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

      if vehicle and vehicle > 0 and GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() then
        FreezeEntityPosition(vehicle, false)
      end

      -- Separate, lower tick thread for position calculation due to potential for lots of vector math, ped retrieval going on
      Citizen.CreateThread(function()
        while race_threads_running and race_data do
          local position = 1

          for remote_source, remote_checkpoint in pairs(race_data.participants) do
            if remote_source ~= local_source then
              if current_checkpoint_idx < remote_checkpoint then
                position = position + 1
              elseif current_checkpoint_idx == remote_checkpoint then
                local remote_ped = GetPlayerPed(GetPlayerFromServerId(remote_source))

                -- local local_distance = #(GetEntityCoords(PlayerPedId()) - current_checkpoint.coords)
                -- local remote_distance = #(GetEntityCoords(remote_ped) - current_checkpoint.coords)

                local local_coords = GetEntityCoords(PlayerPedId())
                local remote_coords = GetEntityCoords(remote_ped)
                local cp_coords = current_checkpoint.coords

                local local_distance = CalculateTravelDistanceBetweenPoints(local_coords.x, local_coords.y, local_coords.z, cp_coords.x, cp_coords.y, cp_coords.z)
                local remote_distance = CalculateTravelDistanceBetweenPoints(remote_coords.x, remote_coords.y, remote_coords.z, cp_coords.x, cp_coords.y, cp_coords.z)

                if local_distance > remote_distance then
                  position = position + 1
                end
              end
            end
          end

          race_data.my_position = position

          Citizen.Wait(250)
        end
      end)

      local start_time_race = GetNetworkTimeAccurate()
      local start_time_lap = start_time_race

      local cached_lap = current_lap

      while race_threads_running do
        local game_timer = GetNetworkTimeAccurate()

        if current_lap ~= cached_lap then
          start_time_lap = GetNetworkTimeAccurate()
          cached_lap = current_lap
        end

        local current_time_lap = game_timer - start_time_lap
        local current_time_race = game_timer - start_time_race
        race_data.current_time_race = current_time_race

        local position_info = '-/-'

        if not race_data.spectator then
          position_info = (race_data.my_position or '-') .. '/' .. race_data.participant_count
        end

        exports.blrp_hud:SetRacingData({
          time_lap = current_time_lap,
          time_overall = current_time_race,
          checkpoint_info = current_checkpoint_idx .. '/' .. race_data.checkpoint_count,
          lap_info = current_lap .. '/' .. race_data.lap_count,
          position_info = position_info,
        })

        if current_checkpoint and #(GetEntityCoords(PlayerPedId()) - current_checkpoint.coords) <= (current_checkpoint.radius + (0.2 * current_checkpoint.radius)) then
          -- Event: player passed a checkpoint and is moving on to the next one
          -- tRacing.nextCheckpoint() returns false if no next checkpoint, aka the race is over
          if not tRacing.nextCheckpoint() then
            race_threads_running = false

            -- Event: race ended
            PlaySoundFrontend(-1, 'ScreenFlash', 'WastedSounds')

            if tRacing.isVehicleDriver() then
              TriggerServerEvent('blrp_racing:server:reportFinished', race_data.join_code, race_data.current_time_race)
            else
              -- Trigger DNF if not the driver
              TriggerServerEvent('blrp_racing:server:reportFinished', race_data.join_code, -1)
            end
          end
        end

        Citizen.Wait(0)
      end

      exports.blrp_hud:SetRacingData({ fade_out = true })

      Citizen.Wait(100)

      tRacing.leaveCurrentRace()
    end)
  end
end

tRacing.nextCheckpoint = function(from_setup)
  if current_checkpoint and current_checkpoint.blip_handle and DoesBlipExist(current_checkpoint.blip_handle) then
    RemoveBlip(current_checkpoint.blip_handle)
  end

  if checkpoint_handle then
    DeleteCheckpoint(checkpoint_handle)
  end

  current_checkpoint_idx = current_checkpoint_idx + 1
  current_checkpoint = race_data.track_computed[current_checkpoint_idx]
  next_checkpoint = race_data.track_computed[current_checkpoint_idx + 1]

  if not race_data.spectator then
    TriggerServerEvent('blrp_racing:server:reportNewCheckpoint', race_data.join_code, current_checkpoint_idx)
  end

  -- Player completed the race
  if current_checkpoint_idx > race_data.checkpoint_count then
    return false
  end

  if not from_setup then
    PlaySoundFrontend(-1, 'RACE_PLACED', 'HUD_AWARDS')

    tRacing.setupBlips()
  end

  current_lap = math.ceil(current_checkpoint_idx / race_data.checkpoints_per_lap)

  local checkpoint_type = 1

  if current_checkpoint_idx == race_data.checkpoint_count then
    checkpoint_type = 4
  end

  if not next_checkpoint then
    next_checkpoint = { coords = vector3(0, 0, 0) }
  end

  local radius = (current_checkpoint.radius or 7.0) + 0.0

  local r = racing_config.marker_r
  local g = racing_config.marker_g
  local b = racing_config.marker_b
  local a = racing_config.marker_a

  if current_checkpoint.speed then
    r = 255
    g = 0
    b = 255
  end

  checkpoint_handle = CreateCheckpoint(
    checkpoint_type,
    current_checkpoint.coords.x, current_checkpoint.coords.y, current_checkpoint.coords.z - 1.0,
    next_checkpoint.coords.x, next_checkpoint.coords.y, next_checkpoint.coords.z,
    radius,
    r, g, b, a, 0
  )

  SetCheckpointIconScale(checkpoint_handle, racing_config.marker_icon_scale)
  SetCheckpointIconHeight(checkpoint_handle, racing_config.marker_icon_height)
  SetCheckpointCylinderHeight(checkpoint_handle, racing_config.marker_height, racing_config.marker_height, radius)

  return true
end

tRacing.setupBlips = function(all_blips)
  for _, handle in pairs(blip_handles) do
    if DoesBlipExist(handle) then
      RemoveBlip(handle)
    end
  end

  if not race_data or not race_data.track_computed then
    return
  end

  local lower_limit = 1
  local upper_limit = #race_data.track_computed

  if not all_blips then
    lower_limit = current_checkpoint_idx
    upper_limit = math.min(race_data.checkpoint_count, current_checkpoint_idx + 10) -- Walk 10 checkpoints ahead during race
  end

  for checkpoint_index = upper_limit, lower_limit, -1 do
    local checkpoint = race_data.track_computed[checkpoint_index]

    local handle = AddBlipForCoord(checkpoint.coords.x, checkpoint.coords.y, checkpoint.coords.z)

    table.insert(blip_handles, handle)
    race_data.track_computed[checkpoint_index].blip_handle = handle

    local blip_color = racing_config.blip_color

    if checkpoint.speed then
      blip_color = 27
    end

    SetBlipColour(handle, blip_color)
    SetBlipAsShortRange(handle, true)
    ShowNumberOnBlip(handle, checkpoint_index)

    if not all_blips and checkpoint_index == lower_limit then
      ClearAllBlipRoutes()
      SetWaypointOff()
      SetBlipRoute(handle, true)
      SetBlipRouteColour(handle, racing_config.blip_color)
    end
  end
end
