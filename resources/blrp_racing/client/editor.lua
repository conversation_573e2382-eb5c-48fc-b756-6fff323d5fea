local checkpoint_radius = 10.0
local checkpoint_speed = nil
local editing = false

local blip_handles = {}
local checkpoint_handles = {}

local nearest_checkpoint = 1

local in_range_this_frame = false

exports('IsEditing', function()
  return editing
end)

tRacing.setEditing = function(_editing)
  if _editing then
    Citizen.Wait(250)
  end

  editing = _editing

  if editing then
    nearest_checkpoint = #race_data.track_working
  else
    for _, handle in pairs(checkpoint_handles) do
      DeleteCheckpoint(handle)
    end

    for _, handle in pairs(blip_handles) do
      if DoesBlipExist(handle) then
        RemoveBlip(handle)
      end
    end
  end
end

tRacing.setupCheckpoints = function()
  for _, handle in pairs(checkpoint_handles) do
    DeleteCheckpoint(handle)
  end

  local player_coords = GetEntityCoords(PlayerPedId())

  for checkpoint_index, checkpoint in pairs(race_data.track_working) do
    local next_checkpoint = race_data.track_working[checkpoint_index + 1] or { coords = vector3(0, 0, 0) }

    if #(player_coords - checkpoint.coords) < 150.0 then
      local radius = (checkpoint.radius or 7.0) + 0.0

      local checkpoint_type = 1

      if checkpoint_index == #race_data.track_working then
        checkpoint_type = 4
      end

      local r = racing_config.marker_r
      local g = racing_config.marker_g
      local b = racing_config.marker_b
      local a = racing_config.marker_a

      if checkpoint.speed then
        r = 255
        g = 0
        b = 255
      end

      local checkpoint_handle = CreateCheckpoint(
        checkpoint_type,
        checkpoint.coords.x, checkpoint.coords.y, checkpoint.coords.z - 1.0,
        next_checkpoint.coords.x, next_checkpoint.coords.y, next_checkpoint.coords.z,
        radius,
        r, g, b, a, checkpoint_index
      )

      SetCheckpointIconScale(checkpoint_handle, racing_config.marker_icon_scale)
      SetCheckpointIconHeight(checkpoint_handle, racing_config.marker_icon_height)
      SetCheckpointCylinderHeight(checkpoint_handle, racing_config.marker_height, racing_config.marker_height, radius)

      checkpoint_handles[checkpoint_index] = checkpoint_handle
    end
  end
end

tRacing.setupBlipsEditor = function()
  for _, handle in pairs(blip_handles) do
    if DoesBlipExist(handle) then
      RemoveBlip(handle)
    end
  end

  if not race_data or not race_data.track_working then
    return
  end

  for checkpoint_index = #race_data.track_working, 1, -1 do
    local checkpoint = race_data.track_working[checkpoint_index]

    local handle = AddBlipForCoord(checkpoint.coords.x, checkpoint.coords.y, checkpoint.coords.z)

    blip_handles[checkpoint_index] = handle

    local blip_color = racing_config.blip_color

    if checkpoint.speed then
      blip_color = 27
    end

    SetBlipColour(handle, blip_color)
    SetBlipAsShortRange(handle, true)
    ShowNumberOnBlip(handle, checkpoint_index)
  end
end

function setupScaleform()
  local scaleform = RequestScaleformMovie('instructional_buttons')

  while not HasScaleformMovieLoaded(scaleform) do
    Citizen.Wait(0)
  end

  local param_counter = 1

  local function PushScaleformDataSlot(label, button)
    PushScaleformMovieFunction(scaleform, 'SET_DATA_SLOT')
    PushScaleformMovieFunctionParameterInt(param_counter)

    if button then
      ScaleformMovieMethodAddParamPlayerNameString(GetControlInstructionalButton(2, button, true))
    end

    BeginTextCommandScaleformString('STRING')
    AddTextComponentScaleform(label)
    EndTextCommandScaleformString()
    PopScaleformMovieFunctionVoid()

    param_counter = param_counter + 1
  end

  PushScaleformMovieFunction(scaleform, 'CLEAR_ALL')
  PopScaleformMovieFunctionVoid()

  PushScaleformMovieFunction(scaleform, 'SET_CLEAR_SPACE')
  PushScaleformMovieFunctionParameterInt(200)
  PopScaleformMovieFunctionVoid()

  PushScaleformDataSlot('Confirm', 191)

  if in_range_this_frame then
    PushScaleformDataSlot('Delete', 178)
  end

  PushScaleformDataSlot('Radius: ' .. checkpoint_radius, 80)
  PushScaleformDataSlot('Speed: ' .. (checkpoint_speed or 'N/A'), 175)
  PushScaleformDataSlot('Quit', 289)
  PushScaleformDataSlot('Save', 288)

  if nearest_checkpoint ~= 0 then
    PushScaleformDataSlot('Switch to insert at beginning', 29)
    PushScaleformDataSlot('Insert after checkpoint # ' .. nearest_checkpoint)
  else
    PushScaleformDataSlot('Insert at beginning')
  end

  PushScaleformDataSlot('Race Editor')

  PushScaleformMovieFunction(scaleform, 'DRAW_INSTRUCTIONAL_BUTTONS')
  PopScaleformMovieFunctionVoid()

  PushScaleformMovieFunction(scaleform, 'SET_BACKGROUND_COLOUR')
  PushScaleformMovieFunctionParameterInt(0)
  PushScaleformMovieFunctionParameterInt(0)
  PushScaleformMovieFunctionParameterInt(0)
  PushScaleformMovieFunctionParameterInt(80)
  PopScaleformMovieFunctionVoid()

  return scaleform
end

function getGenericTextInput(type)
  if not type then
    return ''
  end

  AddTextEntry('FMMC_MPM_NA', 'Enter ' .. tostring(type))
  DisplayOnscreenKeyboard(1, 'FMMC_MPM_NA', 'Enter ' .. tostring(type) .. ' message', '', '', '', '', 30)

  while (UpdateOnscreenKeyboard() == 0) do
    DisableAllControlActions(0)
    Wait(0)
  end

  return GetOnscreenKeyboardResult() or ''
end

Citizen.CreateThread(function()
  local player_coords_cached = GetEntityCoords(PlayerPedId())

  while true do
    Citizen.Wait(1000)

    if editing then
      local player_coords = GetEntityCoords(PlayerPedId())

      if #(player_coords_cached - player_coords) > 25.0 then
        tRacing.setupCheckpoints()
        player_coords_cached = player_coords
      end
    end
  end
end)

Citizen.CreateThread(function()
  while true do
    Citizen.Wait(0)

    if editing and IsPedInAnyVehicle(PlayerPedId()) then
      in_range_this_frame = false

      DisableControlAction(0, 29)
      DisableControlAction(0, 80)
      DisableControlAction(0, 177)
      DisableControlAction(0, 178)
      DisableControlAction(0, 191)

      local player_coords = GetEntityCoords(PlayerPedId())

      for checkpoint_index, checkpoint in pairs(race_data.track_working) do
        local radius = (checkpoint.radius or 7.0) + 0.0

        local nearest_checkpoint_distance = 100000.0

        local checkpoint_distance = #(player_coords - checkpoint.coords)

        if checkpoint_distance <= radius and checkpoint_distance < nearest_checkpoint_distance then
          nearest_checkpoint_distance = checkpoint_distance
          nearest_checkpoint = checkpoint_index
          in_range_this_frame = true
        end
      end

      local form = setupScaleform()

      DrawScaleformMovieFullscreen(form, 255, 255, 255, 255)

      local vehicle = GetVehiclePedIsIn(PlayerPedId())

      if vehicle and vehicle > 0 then
        local location = (GetEntityCoords(vehicle) + (GetEntityForwardVector(vehicle) * 10.0))

        local success, groundZ = GetGroundZFor_3dCoord(location.x, location.y, location.z)

        if success then
          location = vector3(location.x, location.y, groundZ + 0.075)
        end

        local r = 255
        local g = 255
        local b = 0
        local a = 127

        if checkpoint_speed then
          r = 255
          g = 0
          b = 255
        end

        DrawMarker(
          25, -- type
          location.x, location.y, location.z, -- pos x y z
          0.0, 0.0, 0.0, -- dir x y z
          0.0, 0.0, 0.0, -- rot x y z
          checkpoint_radius, checkpoint_radius, 1.0, -- scl x y z
          r, g, b, a,
          0, -- bobUpAndDown
          true, -- faceCamera
          false, 2, false, nil, nil, false
        )

        -- Helps ease the resulting circle off the ground
        location = location + vector3(0, 0, 1.0)

        if IsDisabledControlJustReleased(0, 29) then
          nearest_checkpoint = 0
        elseif IsDisabledControlJustReleased(0, 191) then
          local _track_working = {}
          local counter = 1

          for _checkpoint_index, _checkpoint in pairs(race_data.track_working) do
            -- Deal with new point before the current first point OR
            -- Deal with new point if it's before the current end of the track
            if (_checkpoint_index == 1 and nearest_checkpoint == 0) or (_checkpoint_index == (nearest_checkpoint + 1)) then
              _track_working[counter] = {
                coords = location,
                radius = checkpoint_radius,
                speed = checkpoint_speed,
              }
              counter = counter + 1
            end

            _track_working[counter] = _checkpoint
            counter = counter + 1

            -- Deal with new point if it's after the current end of the track
            if (_checkpoint_index == nearest_checkpoint and _checkpoint_index == #race_data.track_working) then
              _track_working[counter] = {
                coords = location,
                radius = checkpoint_radius,
                speed = checkpoint_speed,
              }
              counter = counter + 1
            end
          end

          if #race_data.track_working == 0 then
            _track_working[counter] = {
              coords = location,
              radius = checkpoint_radius,
              speed = checkpoint_speed,
            }
          end

          race_data.track_working = _track_working

          tRacing.setupCheckpoints()
          tRacing.setupBlipsEditor()

          exports.blrp_core:me().notify('Checkpoint inserted')
        elseif IsDisabledControlJustReleased(0, 80) then
          local desired_radius = tonumber(getGenericTextInput('Radius (5.0 - 25.0)'))

          if desired_radius and desired_radius > 0 then
            desired_radius = desired_radius + 0.0

            if desired_radius < 5.0 then
              exports.blrp_core:me().notify('Minimum radius is 5.0')
            elseif desired_radius > 25.0 then
              exports.blrp_core:me().notify('Maximum radius is 25.0')
            else
              checkpoint_radius = desired_radius
            end
          end
        elseif IsDisabledControlJustReleased(0, 175) then
          local desired_speed = tonumber(getGenericTextInput('Speed (1.0 - 200.0)'))

          if desired_speed and desired_speed > 0 then
            desired_speed = math.floor(desired_speed) + 0.0

            if desired_speed < 1 then
              exports.blrp_core:me().notify('Minimum speed is 1')
            elseif desired_speed > 200 then
              exports.blrp_core:me().notify('Maximum speed is 200')
            else
              checkpoint_speed = desired_speed
            end
          else
            checkpoint_speed = nil
          end
        elseif IsDisabledControlJustReleased(0, 178) and in_range_this_frame then
          local _track_working = {}
          local counter = 1

          for _checkpoint_index, _checkpoint in pairs(race_data.track_working) do
            if _checkpoint_index ~= nearest_checkpoint then
              _track_working[counter] = _checkpoint
              counter = counter + 1
            end
          end

          race_data.track_working = _track_working

          nearest_checkpoint = nearest_checkpoint - 1

          if nearest_checkpoint < 0 then
            nearest_checkpoint = #race_data.track_working
          end

          tRacing.setupCheckpoints()
          tRacing.setupBlipsEditor()

          exports.blrp_core:me().notify('Checkpoint ' .. nearest_checkpoint .. ' deleted')
        elseif IsControlJustReleased(0, 288) then
          TriggerServerEvent('blrp_racing:server:commitTrackWorking', race_data.id, race_data.track_working)

          tRacing.setEditing(false)
          tRacing.leaveCurrentRace()
        elseif IsDisabledControlJustReleased(0, 289) then
          tRacing.setEditing(false)
          tRacing.leaveCurrentRace()
        end
      end
    end
  end
end)
