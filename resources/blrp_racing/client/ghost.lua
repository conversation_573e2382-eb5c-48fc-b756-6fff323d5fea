local ghosted = false

function GetVehicleDimensionDelta(vehicle)
  local model = GetEntityModel(vehicle)
  local d1, d2 = GetModelDimensions(model)

  return #(d2 - d1)
end

function isRacerInAnyVehicleSeat(vehicle)
  local seat_count = GetVehicleModelNumberOfSeats(GetEntityModel(vehicle))

  for seat = -1, seat_count do
    local seat_ped = GetPedInVehicleSeat(vehicle, seat)

    if seat_ped and seat_ped > 0 then
      local ped_source = GetPlayerServerId(NetworkGetPlayerIndexFromPed(seat_ped))
      local race_id = Player(ped_source).state.race_id

      if race_id then
        return true
      end
    end
  end
end

function nonRacerNearby()
  if not race_data then
    return true
  end

  local local_ped = PlayerPedId()
  local local_id = PlayerId()
  local local_vehicle = GetVehiclePedIsIn(local_ped, false)

  if not local_vehicle or local_vehicle <= 0 then
    return true
  end

  local players = GetActivePlayers()

  local local_vehicle_size = GetVehicleDimensionDelta(local_vehicle) / 2

  for i = 1, #players do
    local remote_id = players[i]

    if local_id ~= remote_id then
      local remote_source = GetPlayerServerId(remote_id)

      local race_id = Player(remote_source).state.race_id

      if not race_id then
        local remote_ped = GetPlayerPed(remote_id)

        if IsEntityVisible(remote_ped) then
          local remote_coords = GetEntityCoords(remote_ped)

          local dist = #(remote_coords - GetEntityCoords(local_ped))

          if dist < 15.0 then
            local remote_vehicle = GetVehiclePedIsIn(remote_ped, false)

            if remote_vehicle and remote_vehicle > 0 then
              if not isRacerInAnyVehicleSeat(remote_vehicle) and (dist - GetVehicleDimensionDelta(remote_vehicle)) < local_vehicle_size + 3.0 then
                return true
              end
            elseif dist < local_vehicle_size + 3.0 and GetEntityAttachedTo(remote_ped) == 0 then
              return true
            end
          end
        end
      end
    end
  end

  return false
end

Citizen.CreateThread(function()
  SetLocalPlayerAsGhost(false)

  while true do
    SetGhostedEntityAlpha(254)

    local in_phasing_race = race_data and race_data.race_started and race_data.phasing
    local wait_time = in_phasing_race and 0 or 1000

    if in_phasing_race then
      local non_racer_nearby = nonRacerNearby()

      if not non_racer_nearby and not ghosted then -- Nobody not in the race is nearby and we are not ghosted - put into ghost
        ghosted = true
        SetLocalPlayerAsGhost(ghosted)

        print('ghost enabled', 'in vehicle and no non-racers found nearby')
      elseif non_racer_nearby and ghosted then -- Someone not in race is nearby and we are ghosted - take out of ghost
        ghosted = false
        SetLocalPlayerAsGhost(ghosted)

        print('ghost disabled', 'non-racer found nearby or left vehicle')
      end
    elseif ghosted then -- If ghosted and not in race, stop being ghosted
      ghosted = false
      SetLocalPlayerAsGhost(ghosted)

      print('ghost disabled', 'not in phasing race')
    end

    Citizen.Wait(wait_time)
  end
end)
