AddEventHandler('chatCommand', function(player, command, args)
  if command ~= 'raceimport' then
    return
  end

  CancelEvent()

  local character = exports.blrp_core:character(player)

  local legacy_races = MySQL.Sync.fetchAll("SELECT * FROM race_tracks WHERE steam_id = (SELECT REPLACE(identifier, 'steam:', '') FROM vrp_user_ids WHERE user_id = @user_id AND identifier LIKE '%steam%')", {
    user_id = character.get('identifier')
  })

  if #legacy_races <= 0 then
    character.notify('You do not have any legacy races to import')
    return
  end

  local menu = BMenu:new(true, {
    resource_title = 'Racing',
    section_title = 'Import Legacy Race',
    title_bg_image = '',
    position = { top_offset = 480, left_offset = 10 },
    max_items_shown_at_once = 13,
    prevent_sound = true,
    width = 400,
  })

  for _, legacy_race in pairs(legacy_races) do
    menu:addSelection(false, legacy_race.name, function(_, _, callback)
      if not character.request('Are you sure you want to import race "' .. legacy_race.name .. '"? It will be deleted from the legacy table on import.', 30) then
        callback('')
        return
      end

      character.notify('Importing race...')

      local waypoints = json.decode(legacy_race.waypoints)

      for waypoint_key, waypoint_value in ipairs(waypoints) do
        waypoints[waypoint_key].radius = 20.0
      end

      local rows_affected = MySQL.Sync.execute('INSERT INTO core_races (character_id, track_name, share_code, track) VALUES (@character_id, @track_name, @share_code, @track)', {
        character_id = character.get('id'),
        track_name = '>>> Legacy Import: ' .. legacy_race.name,
        share_code = generateShareCode(),
        track = json.encode(waypoints),
      })

      if rows_affected <= 0 then
        character.notify('Error importing race')
        return
        callback('')
      end

      --[[
      MySQL.Sync.execute('DELETE FROM race_tracks WHERE id = @id LIMIT 1', {
        id = legacy_race.id
      })
      ]]

      character.notify('Race imported successfully')
      callback('close')
    end)
  end

  menu:show(character.source)
end)
