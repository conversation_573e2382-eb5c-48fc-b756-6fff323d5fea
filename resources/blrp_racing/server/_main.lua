tRacing = T.getInstance('blrp_racing', 'racing')

local active_races = {}

local function randomString(iterations, upper)
  local keyspace = 'AEFHKLMNPRTUVWXYZ1346789'

  local length = string.len(keyspace)

  local output = ''

  for i = 1, iterations do
    local position = math.random(1, length)
    output = output .. string.sub(keyspace, position, position)
  end

  if upper then
    output = string.upper(output)
  end

  return output
end

function generateShareCode()
  local share_code = randomString(4, true)

  local rows = MySQL.Sync.fetchAll('SELECT id FROM core_races WHERE share_code = @share_code', {
    share_code = share_code
  })

  if #rows > 0 then
    return generateShareCode()
  end

  return share_code
end

function generateJoinCode()
  local join_code = randomString(4, true)

  if active_races[join_code] then
    return generateJoinCode()
  end

  return join_code
end

RegisterNetEvent('blrp_racing:server:openMenu', function()
  openRacingMenu(source)
end)

AddEventHandler('chatCommand', function(player, command, args)
  if command ~= 'racemenu' then
    return
  end

  CancelEvent()

  openRacingMenu(player)
end)

function joinRaceInternal(character, race_data, anonymous)
  if #(character.getCoordinates() - race_data.queued_at_coords) > 100.0 then
    character.notify('No race found nearby with that join code')
    return
  end

  race_data.time_until_start = (race_data.start_time - GetGameTimer())

  if race_data.time_until_start < 5000 then
    character.notify('It is too late to join this race now')
    return
  end

  tRacing.setCurrentRace(character.source, { race_data })

  if tRacing.isVehicleDriver(character.source) then
    active_races[race_data.join_code].participants[character.source] = 1

    local name = character.get('firstname') .. ' ' .. character.get('lastname')

    if anonymous then
      name = 'Anonymous'
    end

    active_races[race_data.join_code].participant_places[character.source] = {
      place = 0,
      time = 0,
      source = character.source,
      character_id = tonumber(character.get('id')),
      name = name,
    }
  end
end

function parseTimeString(millis)
  local minutes = math.floor(millis / 1000 / 60)

  millis = millis - (minutes * 60 * 1000)

  local seconds = math.floor(millis / 1000)

  millis = math.floor((millis - (seconds * 1000)) / 10)

  return string.format('%02d', minutes) .. ':' .. string.format('%02d', seconds) .. ':' .. string.format('%02d', millis)
end

function openGlobalLeaderboardMenu(player, race_data)
  local character = exports.blrp_core:character(player)

  local leaders = MySQL.Sync.fetchAll('SELECT t1.* FROM core_race_leaderboard t1 LEFT JOIN core_race_leaderboard t2 ON t1.character_id = t2.character_id AND t1.time > t2.time WHERE t2.time IS NULL AND t1.race_id = @race_id ORDER BY CAST(t1.time AS UNSIGNED) ASC', {
    race_id = race_data.id
  })

  local menu = BMenu:new(true, {
    resource_title = 'Racing',
    section_title = race_data.track_name .. ' [' .. race_data.share_code .. '] - Global Leaderboard',
    title_bg_image = '',
    position = { top_offset = 480, left_offset = 10 },
    max_items_shown_at_once = 13,
    prevent_sound = true,
    width = 400,
  })

  for _, leader in pairs(leaders) do
    menu:addSelection(false, leader.character_name .. ' - ' .. parseTimeString(leader.time) .. ' - ' .. leader.date)
  end

  menu:show(character.source)
end

function openLeaderboardMenu(character, join_code)
  local race_data = active_races[join_code]

  if not join_code then
    character.notify('Error accessing leaderboard')
    return
  end

  local menu = BMenu:new(true, {
    resource_title = 'Racing',
    section_title = race_data.track_name .. ' ' .. race_data.start_time_human,
    title_bg_image = '',
    position = { top_offset = 480, left_offset = 10 },
    max_items_shown_at_once = 13,
    prevent_sound = true,
    width = 400,
  })

  local participants_parsed = {}

  for _, participant_data in pairs(race_data.participant_places) do
    local data = {
      character_id = participant_data.character_id,
      time_raw = participant_data.time,
      time_parsed = parseTimeString(participant_data.time),
      name = participant_data.name,
      dnf = participant_data.dnf,
      new_record = participant_data.new_record
    }

    if data.dnf then
      data.time_raw = 9999999999
    end

    if not data.time_raw or data.time_raw <= 0 then
      data.awaiting_finish = true
      data.time_raw = 9999999998
    end

    table.insert(participants_parsed, data)
  end

  --[[ Data faker (for testing)
  for i = 1, 4 do
    local time = math.random(10000, 30000)

    local dnf = (math.random(1, 4) == 1)

    if dnf then
      time = 9999999999
    end

    table.insert(participants_parsed, {
      time_raw = time,
      time_parsed = parseTimeString(time),
      name = 'Test ' .. i,
      dnf = dnf
    })
  end
  ]]

  table.sort(participants_parsed, function(a, b)
    return a.time_raw < b.time_raw
  end)

  if race_data.leader_name then
    menu:addSelection(false, 'Global Leader: ' .. race_data.leader_name .. ' - ' .. parseTimeString(race_data.leader_time) .. ' - ' .. race_data.leader_date)
  end

  local place = 1

  for _, participant_data in pairs(participants_parsed) do
    local _place = place
    local time_string = participant_data.time_parsed

    if participant_data.dnf then
      _place = 'DNF'
      time_string = 'N/A'
    elseif participant_data.awaiting_finish then
      _place = 'In Race'
      time_string = 'N/A'
    else
      place = place + 1
    end

    local name = participant_data.name

    if name == 'Anonymous' and tonumber(participant_data.character_id) == tonumber(character.get('id')) then
      name = 'Anonymous (you)'
    end

    if participant_data.new_record then
      name = name .. ' (New Record)'
    end

    menu:addSelection(false, _place .. ' - ' .. name .. ' ' .. time_string)
  end

  menu:show(character.source)
end

function openStartRaceMenu(player, race_id)
  local character = exports.blrp_core:character(player)

  local race_data = MySQL.Sync.fetchAll('SELECT * FROM core_races WHERE id = @id', {
    id = race_id
  })

  if #race_data <= 0 then
    return
  end

  race_data = race_data[1]

  local selected_laps = 1
  local selected_time = 60
  local selected_phasing = false
  local selected_proxjoin = false

  local menu = BMenu:new(true, {
    resource_title = 'Racing',
    section_title = race_data.track_name,
    title_bg_image = '',
    position = { top_offset = 480, left_offset = 10 },
    max_items_shown_at_once = 13,
    prevent_sound = true,
    width = 400,
  })

  menu:addTextInput(false, 'Laps', selected_laps, function(player, value, callback)
    value = tonumber(value)

    if not value or value < 1 or value > 50 then
      character.notify('Invalid value')
      callback(selected_laps)
      return
    end

    selected_laps = value

    callback(value)
  end)

  menu:addTextInput(false, 'Queue Time', selected_time, function(player, value, callback)
    value = tonumber(value)

    if not value or value < 20 or value > 180 then
      character.notify('Invalid value')
      callback(selected_time)
      return
    end

    selected_time = value

    callback(value)
  end)

  menu:addCheckbox(false, 'Enable Phasing', selected_phasing, function(player, value, callback)
    selected_phasing = value

    callback(value)
  end)

  menu:addCheckbox(false, 'Allow Proximity Joining', selected_proxjoin, function(player, value, callback)
    selected_proxjoin = value

    callback(value)
  end)

  menu:addSelection(submenu_name, '<hr />')

  local function startRaceInternal()
    race_data.track = json.decode(race_data.track)

    if not race_data.track or #race_data.track < 2 then
      character.notify('Unable to start race due to insufficient number of track points')
      return false
    end

    local character_coords = character.getCoordinates()

    if #(character_coords - vector3(race_data.track[1].coords.x, race_data.track[1].coords.y, race_data.track[1].coords.z)) > 1000.0 then
      character.notify('You are too far from the first checkpoint to start this race')
      return
    end

    for checkpoint_index, checkpoint in pairs(race_data.track) do
      checkpoint.coords = vector3(checkpoint.coords.x, checkpoint.coords.y, checkpoint.coords.z)
    end

    local track_computed = {}
    local track_counter = 1

    for lap = 1, selected_laps do
      for _, checkpoint in pairs(race_data.track) do
        track_computed[track_counter] = checkpoint
        track_counter = track_counter + 1
      end
    end

    race_data.track_computed = track_computed
    race_data.track = nil
    race_data.lap_count = selected_laps
    race_data.checkpoint_count = (track_counter - 1)
    race_data.checkpoints_per_lap = (race_data.checkpoint_count / selected_laps)
    race_data.join_code = generateJoinCode()
    race_data.start_time = GetGameTimer() + (selected_time * 1000)
    race_data.phasing = selected_phasing
    race_data.prox_join = selected_proxjoin
    race_data.participants = {}
    race_data.participant_places = {}
    race_data.place_iterator = 1
    race_data.host_character_id = tonumber(character.get('id'))
    race_data.start_time_human = os.date('%Y-%m-%d %H:%M:%S')
    race_data.queued_at_coords = character_coords

    active_races[race_data.join_code] = race_data

    character.notifyNew('Race Queued. Join code: ' .. race_data.join_code, (selected_time - 5) * 1000)

    Citizen.CreateThread(function()
      while active_races[race_data.join_code] and GetGameTimer() < (race_data.start_time - 4000) do
        Citizen.Wait(0)
      end

      for participant_source, _ in pairs(active_races[race_data.join_code].participants) do
        TriggerClientEvent('blrp_racing:client:setPingCorrection', participant_source, (GetPlayerPing(participant_source) / 2))
      end

      while active_races[race_data.join_code] and GetGameTimer() < (race_data.start_time - 2000) do
        Citizen.Wait(0)
      end

      if active_races[race_data.join_code] then
        TriggerClientEvent('blrp_racing:client:syncParticipants', -1, race_data.join_code, active_races[race_data.join_code].participants)

        local participant_count = 0

        for _, __ in pairs(active_races[race_data.join_code].participants) do
          participant_count = participant_count + 1
        end

        active_races[race_data.join_code].participant_count = participant_count

        if participant_count == 0 then
          active_races[race_data.join_code] = nil
        end
      end
    end)

    return true
  end

  menu:addSelection(false, 'Send to queue and join', function(player, value, callback)
    if startRaceInternal() then
      Citizen.Wait(250)

      joinRaceInternal(character, race_data)
    end

    callback('close')
  end)

  menu:addSelection(false, 'Send to queue', function(player, value, callback)
    startRaceInternal()

    callback('close')
  end)

  menu:show(character.source)
end

function openRacingMenu(player)
  local character = exports.blrp_core:character(player)

  local races = MySQL.Sync.fetchAll('SELECT * FROM core_races WHERE character_id = @character_id ORDER BY track_name ASC', {
    character_id = character.get('id')
  })

  local menu = BMenu:new(true, {
    resource_title = 'Racing',
    section_title = 'Main Menu',
    title_bg_image = '',
    position = { top_offset = 480, left_offset = 10 },
    max_items_shown_at_once = 13,
    prevent_sound = true,
    width = 400,
  })

  local current_race_code = tRacing.getCurrentRace(character.source)

  if current_race_code then
    menu:addSelection(false, 'Leave current race (' .. current_race_code .. ')', function(player, value, callback)
      tRacing.leaveCurrentRace(character.source)

      local race_data = active_races[current_race_code]

      if race_data then
        if race_data.start_time < GetGameTimer() then
          character.notify('You have abandoned the race and will be marked as DNF')

          TriggerEvent('blrp_racing:server:reportFinished', current_race_code, -1, character.source)
        else
          character.notify('You have left the race')

          active_races[current_race_code].participants[character.source] = nil
          active_races[current_race_code].participant_places[character.source] = nil
        end
      end

      Citizen.Wait(100)

      callback('')

      openRacingMenu(character.source)
    end)
  else
    menu:addSelection(false, 'Join nearest race', function(player, value, callback)
      local player_coords = character.getCoordinates()

      callback('close')

      for k, active_race in pairs(active_races) do
        if active_race.prox_join and active_race.start_time > (GetGameTimer() + 5000) and #(player_coords - active_race.queued_at_coords) < 100.0 then
          joinRaceInternal(character, active_races[k])
          return
        end
      end

      character.notify('No applicable race found nearby')
    end)

    menu:addTextInput(false, 'Join Race by join code', '', function(player, value, callback)
      if not value or value == '' then
        character.notify('Invalid name specified')
        callback('')
        return
      end

      value = string.upper(value)

      local race_data = active_races[value]

      if not race_data then
        character.notify('No race found with that join code')
        callback('')
        return
      end

      joinRaceInternal(character, race_data)

      callback('close')
    end)

    menu:addTextInput(false, 'Join Race by join code (Anonymous)', '', function(player, value, callback)
      if not value or value == '' then
        character.notify('Invalid name specified')
        callback('')
        return
      end

      value = string.upper(value)

      local race_data = active_races[value]

      if not race_data then
        character.notify('No race found with that join code')
        callback('')
        return
      end

      joinRaceInternal(character, race_data, true)

      callback('close')
    end)
  end

  ------------------------------------------------------------------------------
  -- Recent races

  local recent_races = {}

  local my_char_id = tonumber(character.get('id'))
  local is_staff = character.hasGroup('staff')

  for join_code, race_data in pairs(active_races) do
    if race_data.start_time < GetGameTimer() then
      local should_add = false

      if
        is_staff or -- Is a staff member
        race_data.host_character_id == my_char_id or -- Is the person who started the race
        race_data.character_id == my_char_id -- Is the person who owns the race (someone else may have started, but these people should still be able to see any time their race is done)
      then
        should_add = true
      else
        for _, participant_data in pairs(race_data.participant_places) do
          if participant_data.character_id == my_char_id then
            should_add = true
            break
          end
        end
      end

      if should_add then
        table.insert(recent_races, {
          start_time = race_data.start_time,
          name = race_data.track_name .. ' [' .. join_code .. '] - ' .. race_data.start_time_human,
          join_code = race_data.join_code
        })
      end
    end
  end

  table.sort(recent_races, function(a, b)
    return a.start_time > b.start_time
  end)

  if not character.hasGroup('LEO') or character.hasGroup('FIB') then
    if #recent_races > 0 then
      local recent_submenu = 'Recent Races'

      menu:addSubMenu(false, recent_submenu)

      for _, race_data in pairs(recent_races) do
        menu:addSelection(recent_submenu, race_data.name, function(player, value, callback)
          callback('')

          openLeaderboardMenu(character, race_data.join_code)
        end)
      end
    else
      menu:addSelection(false, 'Recent Races (none)')
    end
  end

  ------------------------------------------------------------------------------

  menu:addSelection(false, '<hr />')

  menu:addTextInput(false, 'Create Race', '', function(player, value, callback)
    if not value or value == '' then
      character.notify('Invalid name specified')
      callback('')
      return
    end

    local rows = MySQL.Sync.fetchAll('SELECT * FROM core_races WHERE character_id = @character_id AND track_name = @track_name', {
      character_id = character.get('id'),
      track_name = value,
    })

    if #rows > 0 then
      character.notify('You already have a race by this name')
      callback('')
      return
    end

    local share_code = generateShareCode()

    local insert_id = MySQL.Sync.insert('INSERT INTO core_races (character_id, track_name, share_code) VALUES (@character_id, @track_name, @share_code)', {
      character_id = character.get('id'),
      track_name = value,
      share_code = share_code,
    })

    local race_data = {
      id = insert_id,
      character_id = character.get('id'),
      track_name = value,
      share_code = share_code,
      track_computed = {},
      track_working = {},
      lap_count = 1,
      checkpoint_count = 0,
      checkpoints_per_lap = 0,
    }

    tRacing.setCurrentRace(character.source, { race_data, true })

    callback('close')
  end)

  menu:addTextInput(false, 'Create Race from share code', '', function(player, value, callback)
    if not value or value == '' then
      character.notify('Invalid share code specified')
      callback('')
      return
    end

    local rows = MySQL.Sync.fetchAll('SELECT * FROM core_races WHERE share_code = @share_code', {
      character_id = character.get('id'),
      share_code = value
    })

    if #rows <= 0 then
      character.notify('Invalid share code specified')
      callback('')
      return
    end

    local old_race_data = rows[1]

    local new_share_code = generateShareCode()

    local insert_id = MySQL.Sync.insert('INSERT INTO core_races (character_id, track_name, share_code, track) VALUES (@character_id, @track_name, @share_code, @track)', {
      character_id = character.get('id'),
      track_name = old_race_data.track_name .. ' * (' .. os.date('%H:%M:%S') .. ')',
      share_code = new_share_code,
      track = old_race_data.track
    })

    callback('')

    openRacingMenu(character.source)
  end)

  menu:addTextInput(false, 'Start Race from share code', '', function(player, value, callback)
    if not value or value == '' then
      character.notify('Invalid share code specified')
      callback('')
      return
    end

    local rows = MySQL.Sync.fetchAll('SELECT * FROM core_races WHERE share_code = @share_code', {
      share_code = value
    })

    if #rows <= 0 then
      character.notify('Invalid share code specified')
      callback('')
      return
    end

    openStartRaceMenu(character.source, rows[1].id)
  end)

  menu:addSelection(submenu_name, '<hr />')

  if #races > 0 then
    for _, race_data in pairs(races) do
      local submenu_name = race_data.track_name

      menu:addSubMenu(false, submenu_name)

      menu:addSelection(submenu_name, 'Share Code: ' .. race_data.share_code)

      menu:addSelection(submenu_name, 'Start Race', function(player, value, callback)
        callback('')

        openStartRaceMenu(player, race_data.id)
      end)

      menu:addSelection(submenu_name, 'Edit Race', function(player, value, callback)
        if not tRacing.isVehicleDriver(character.source) then
          character.notify('You may only enter edit race mode from within a vehicle')
          callback('')
          return
        end

        race_data.track_computed = json.decode(race_data.track)

        if not race_data.track_computed then
          race_data.track_computed = {}
        end

        for _, checkpoint in pairs(race_data.track_computed) do
          checkpoint.coords = vector3(checkpoint.coords.x, checkpoint.coords.y, checkpoint.coords.z)
        end

        race_data.track_working = race_data.track_computed
        race_data.track = nil
        race_data.lap_count = 1
        race_data.checkpoint_count = #race_data.track_computed
        race_data.checkpoints_per_lap = #race_data.track_computed

        tRacing.setCurrentRace(character.source, { race_data, true })

        callback('close')
      end)

      if race_data.leader_name then
        menu:addSelection(submenu_name, '<hr />')
        menu:addSelection(submenu_name, 'Global Leader: ' .. race_data.leader_name .. ' - ' .. parseTimeString(race_data.leader_time) .. ' - ' .. race_data.leader_date)

        menu:addSelection(submenu_name, 'View Global Leaderboard', function(_, _, callback)
          openGlobalLeaderboardMenu(character.source, race_data)
        end)
      end

      menu:addSelection(submenu_name, '<hr />')

      menu:addTextInput(submenu_name, 'Rename', '', function(player, value, callback)
        if not value or value == '' then
          character.notify('Invalid name specified')
          callback('')
          return
        end

        local rows = MySQL.Sync.fetchAll('SELECT * FROM core_races WHERE character_id = @character_id AND track_name = @track_name', {
          character_id = character.get('id'),
          track_name = value,
        })

        if #rows > 0 then
          character.notify('You already have a race by this name')
          callback('')
          return
        end

        local rows_affected = MySQL.Sync.execute('UPDATE core_races SET track_name = @track_name WHERE id = @id LIMIT 1', {
          track_name = value,
          id = race_data.id
        })

        callback('')

        if rows_affected > 0 then
          character.notify('Race renamed successfully')
        else
          character.notify('Error renaming race')
        end

        openRacingMenu(character.source)
      end)

      menu:addSelection(submenu_name, 'Delete', function(_, _, callback)
        if not character.request('Are you sure you want to delete race ' .. race_data.track_name .. '?') then
          callback('')
          return
        end

        MySQL.Sync.execute('DELETE FROM core_races WHERE id = @id LIMIT 1', {
          id = race_data.id
        })

        callback('')

        character.notify('Race deleted')

        openRacingMenu(character.source)
      end)
    end
  else
    menu:addSelection(false, 'You do not own any races')
  end

  menu:show(character.source)
end

RegisterNetEvent('blrp_racing:server:reportNewCheckpoint', function(race_join_code, checkpoint_index)
  TriggerClientEvent('blrp_racing:client:syncParticipantCheckpoint', -1, race_join_code, source, checkpoint_index)
end)

RegisterNetEvent('blrp_racing:server:reportFinished', function(race_join_code, reported_time, player)
  if not player then
    player = source
  end

  local race_data = active_races[race_join_code]

  if not race_data then
    return
  end

  if not race_data.participants[player] then
    return
  end

  local place_data = active_races[race_join_code].participant_places[player]

  if not place_data then
    return
  end

  local place = race_data.place_iterator

  active_races[race_join_code].place_iterator = active_races[race_join_code].place_iterator + 1

  place_data.place = place
  place_data.time = reported_time

  if reported_time == -1 then
    place_data.dnf = true
  end

  if place_data.name ~= 'Anonymous' and reported_time > 0 then
    local character = exports.blrp_core:character(player)

    local query_bindings = {
      race_id = race_data.id,
      character_id = character.get('id'),
      name = character.get('firstname') .. ' ' .. character.get('lastname'),
      time = reported_time,
    }

    MySQL.Async.execute('INSERT INTO core_race_leaderboard (race_id, character_id, character_name, time, date) VALUES (@race_id, @character_id, @name, @time, CURDATE())', query_bindings)

    MySQL.Async.fetchAll('SELECT id, leader_time FROM core_races WHERE id = @id', {
      id = race_data.id
    }, function(leader_rows)
      if #leader_rows <= 0 then
        return
      end

      if not leader_rows[1].leader_time or tonumber(reported_time) <= tonumber(leader_rows[1].leader_time) then
        MySQL.Async.execute('UPDATE core_races SET leader_character_id = @character_id, leader_name = @name, leader_time = @time, leader_date = CURDATE() WHERE id = @race_id', query_bindings)

        character.notifyNew('You set a new record time for this race!', 15000)

        place_data.new_record = true
      end
    end)
  end
end)

AddEventHandler('playerDropped', function()
  local player = source

  for join_code, race_data in pairs(active_races) do
    if race_data.participants[player] then
      if active_races[join_code].participant_places[player].time == 0 then
        if race_data.start_time < GetGameTimer() then
          TriggerEvent('blrp_racing:server:reportFinished', join_code, -1, player)
        else
          active_races[join_code].participants[player] = nil
          active_races[join_code].participant_places[player] = nil
        end
      end
    end
  end
end)

RegisterNetEvent('blrp_racing:server:commitTrackWorking', function(race_id, track_working)
  local character = exports.blrp_core:character(source)

  local race_data = MySQL.Sync.fetchAll('SELECT id FROM core_races WHERE id = @id', {
    id = race_id
  })

  if #race_data <= 0 then
    character.notify('Invalid race ID on commit', race_id)
    return
  end

  if not character.request('Are you sure you want to commit these changes? They cannot be undone and an admin cannot help you undo them.', 30) then
    return
  end

  local rows_affected = MySQL.Sync.execute('UPDATE core_races SET track = @track WHERE id = @id LIMIT 1', {
    id = race_id,
    track = json.encode(track_working),
  })

  if rows_affected <= 0 then
    character.notify('Error committing changes')
    return
  end

  character.notify('Track changes committed successfully')
end)
