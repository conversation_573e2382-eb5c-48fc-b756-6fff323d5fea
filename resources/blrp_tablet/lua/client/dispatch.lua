local dispatchEnabled = false
local characters = { }
local talking = true

--pAlerts = P.getInstance('blrp_core', 'alerts')
--
--RegisterNUICallback('dispatchInit', function(data, cb)
--    cb({
--        characters = characters,
--        radioChannels = exports.blrp_radio:GetNamedChannels()
--    })
--end)
--
--RegisterNUICallback('setDispatchEnabled', function(data, cb)
--    dispatchEnabled = data.value
--    pAlerts.setCharacterDispatch()
--end)
--
--RegisterNUICallback('moveCharacterToRadioChannel', function(data, cb)
--    cb('ok')
--
--    pTablet.moveCharacterToRadioChannel({
--        data.source, data.channel
--    })
--end)
--
--RegisterNUICallback('setAlertChannel', function(data, cb)
--    TriggerServerEvent('core:server:alerts:setAlertChannel', data.local_id, data.channel)
--end)
--
--RegisterNUICallback('startTalking', function(data, cb)
--    -- Do talk logic.. somehow.
--    cb('ok')
--end)
--
--RegisterNUICallback('addRespondingFromTablet', function(data, cb)
--    pAlerts.addPlayerRespondingFromTablet({ data.source, data.local_id })
--end)
--
--RegisterNUICallback('removeRespondingFromTablet', function(data, cb)
--    pAlerts.removePlayerRespondingFromTablet({ data.source, data.local_id })
--end)
--
--RegisterNetEvent('core:client:setDispatchList', function(data)
--    characters = data
--end)
--
--Citizen.CreateThread(function()
--    while true do
--        Citizen.Wait(1)
--
--        if dispatchEnabled then
--            for _, character in pairs(characters) do
--                local p = character.synced_position
--
--                local current_street_a, current_street_b = GetStreetNameAtCoord(
--                        p.x, p.y, p.z, Citizen.ResultAsInteger(), Citizen.ResultAsInteger())
--
--                characters[_].location = {
--                    location_a = GetStreetNameFromHashKey(current_street_a),
--                    location_b = GetStreetNameFromHashKey(current_street_b),
--                    zone = GetLabelText(GetNameOfZone(p.x, p.y, p.z)),
--                    real_zone = exports.blrp_core:GetInteriorNameAtPed()
--                }
--
--                for __, characterB in pairs(characters) do
--                    if not characters[__].distances then
--                        characters[__].distances = { }
--                    end
--
--                    local distance = #(character.synced_position - characterB.synced_position)
--                    characters[__].distances[characterB.source] = distance
--                end
--            end
--
--
--            tTablet.dispatchVueState('dispatch/setState', {
--                characters = characters,
--            })
--
--            Citizen.Wait(1000)
--        end
--    end
--end)
