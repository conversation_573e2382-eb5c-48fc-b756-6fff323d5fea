local hasInputFocused = false

RegisterNUICallback('inquire-contacts', function(data, cb)
    cb('ok')

    sendContacts()
end)

RegisterNUICallback('inquire-text-threads', function(data, cb)
    cb('ok')

    sendTextThreads()
end)

RegisterNUICallback('inquire-text-thread-messages', function(data, cb)
    cb('ok')

    pPhone.setMyReadPhoneNumber({ data.phone }) --

    sendTextThreadTextMessages(data.phone)
end)

RegisterNUICallback('inquire-call-history', function(data, cb)
    cb('ok')

    sendCallHistory()
end)

RegisterNUICallback('inquire-services', function(data, cb)
    cb('ok')

    sendServices()
end)

--------------
-- Data fillers
--------------
function sendContacts()
    local results = pPhone.fetchMyContacts({ })

    tTablet.dispatchVueState('text/setContacts', {
        contacts = results
    })
end

function sendTextThreads()
    local results = pPhone.fetchMyTextThreads({ })

    tTablet.dispatchVueState('text/setThreads', {
        threads = results
    })
end

function sendTextThreadTextMessages(phone)
    local results = pPhone.fetchMyTextMessages({ phone })

    tTablet.dispatchVueState('text/setThreadMessages', {
        phone = phone,
        messages = results
    })
end

function sendCallHistory()
    local results = pPhone.fetchMyCallHistory({  })

    tTablet.dispatchVueState('text/setCallHistory', {
        history = results
    })
end

function sendServices()
    tTablet.dispatchVueState('text/setServices', {
        services = pPhone.compileServices({ })
    })
end


RegisterNUICallback('sendMessageNoNumber', function(data, cb)
    cb('ok')

    if #data.message > 2 then
        local coords = GetEntityCoords(PlayerPedId())

        local extra = {
            position = {
                current_coords = { x = coords.x, y = coords.y },
                current_zone = GetLabelText(GetNameOfZone(coords.x, coords.y, coords.z)),
                real_area = exports.blrp_core:GetInteriorNameAtPed(),
            },
            bank_details = {
              bank_account = exports.blrp_core:me().get('personalbanknumber'),
            }
        }

        pPhone.sendMessageNoNumberFromClient({ extra })
    end
end)

RegisterNUICallback('sendMessage', function(data, cb)
    if #data.message > 2 then
        local coords = GetEntityCoords(PlayerPedId())

        local extra = {
            position = {
                current_coords = { x = coords.x, y = coords.y },
                current_zone = GetLabelText(GetNameOfZone(coords.x, coords.y, coords.z)),
                real_area = exports.blrp_core:GetInteriorNameAtPed()
            },
            bank_details = {
              bank_account = exports.blrp_core:me().get('personalbanknumber'),
            }
        }

        -- @toPhoneNumber
        -- @message
        -- @fromPhoneNumber
        -- @extra
            -- @position
        pPhone.sendTextMessage({ data.phone, data.message, exports.blrp_core:me().get('phone'), extra })
    end

    cb('ok')
end)

RegisterNUICallback('addContactExistingThread', function(data, cb)
    cb('ok')

    pPhone.addContactExistingThreadFromClient({ data.phone })
end)

RegisterNUICallback('removeTextThreadMessages', function(data, cb)
    cb('ok')

    pPhone.removeMyTextThread({ data.phone })
end)

RegisterNUICallback('markTextThreadRead', function(data, cb)
    cb('ok')

    pPhone.setMyReadPhoneNumber({ data.phone }) --
end)

RegisterNUICallback('createContact', function(data, cb)
    pPhone.addMyContact({ data.phone, data.display })

    sendContacts()
    cb('ok')
end)

RegisterNUICallback('shareNumber', function(data, cb)
    pPhone.shareMyContact({ })

    cb('ok')
end)

RegisterNUICallback('updateContact', function(data, cb)
    pPhone.updateContact({ data.id, data })

    sendContacts()

    cb('ok')
end)

RegisterNUICallback('removeContact', function(data, cb)
    pPhone.removeMyContact({ data.id })

    --sendContacts()
    cb('ok')
end)

RegisterNUICallback('removeAllContacts', function(data, cb)
    pPhone.clearMyContacts({ })

    cb('ok')
end)

RegisterNUICallback('removeMessages', function(data, cb)
    pPhone.removeMyTextThread({ data.targetPhone })

    sendTextThreads()
    ok('ok')
end)


RegisterNUICallback('setInputFocused', function(data, cb)
    cb('ok')
    hasInputFocused = data.value
    TriggerEvent('core:client:setInputFocused', data.value)
end)

RegisterNUICallback('openBank', function(data, cb)
    TriggerEvent('blrp_phone:client:hide')
    TriggerEvent('blrp_tablet:client:hide')

    Citizen.Wait(300)

    TriggerServerEvent('blrp_banking:server:open', false, {
        access_type = 'phone'
    })

    cb('ok')
end)

RegisterNUICallback('openBadEats', function(data, cb)
    TriggerEvent('blrp_phone:client:hide')
    TriggerEvent('blrp_tablet:client:hide')
    TriggerServerEvent('core:server:badeats:openOrderMenu')


    cb('ok')
end)

RegisterNUICallback('getUnreadTextMessages', function(data, cb)
  cb({
    unread_messages = 0,
  })
end)

function getTextMessagPhonePosition()
    local data = {

    }

    local pos = GetEntityCoords(PlayerPedId())
    data.current_cords = pos
    data.current_zone = GetLabelText(GetNameOfZone(pos.x, pos.y, pos.z))
    data.real_area = exports.blrp_core:GetInteriorNameAtPed()
    return data
end
