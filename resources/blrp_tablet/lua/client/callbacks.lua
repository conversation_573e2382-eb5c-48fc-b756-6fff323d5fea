RegisterNUICallback('escape', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    cb('ok')
end)

RegisterNUICallback('show', function(data, cb)
    TriggerEvent('blrp_tablet:show')
    cb('ok')
end)

RegisterNUICallback('getPosition', function(data, cb)
    local contents = data.contents
    cb('ok')
end)

RegisterNUICallback('getPositionInformation', function(data, cb)
    return getPositionInformation()
end)

RegisterNUICallback('syncSelf', function(data, cb)
    local forceReloadSettings = data.force_reload_settings

    pTablet.updateMyself({ forceReloadSettings })
end)

---
--- Open a stored document
---
RegisterNUICallback('openDocument', function(data, cb)
    local id = data.game_document_id
    if id == nil then return cb('error') end
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('documents:displayDocument', id)
    cb('ok')
end)

RegisterNUICallback('openEvidence', function(data, cb)
    local id = data.id
    if id == nil then return cb('error') end
    TriggerEvent('blrp_tablet:hide')
    SetTimeout(200, function()
        TriggerServerEvent('blrp_inventory:openEvidence', 'pd_evidence_' .. id)
    end)
    cb('ok')
end)


RegisterNUICallback('openLocker', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    SetTimeout(200, function()
        TriggerServerEvent('blrp_inventory:openPrisonLockerFromTablet', data.character_number)
    end)
    cb('ok')
end)

---
--- Scan an ID
---
RegisterNUICallback('scanID', function(data, cb)
    TriggerServerEvent('InteractSound_SV:PlayWithinDistance', 3, 'beep-single-quick', 0.1)
    cb(last_seen_id)
end)

---
--- Get Evidence for police reports
---
RegisterNUICallback('getEvidence', function(data, cb)
    local id = data.id
    if id == nil then return cb('error') end
    SetTimeout(200, function()
        TriggerServerEvent('blrp_tablet:getEvidence', 'pd_evidence_' .. id)
    end)
    cb('ok')
end)

---
--- Create contract for citizens and other citizens
---
RegisterNUICallback('createContract', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    --local playerServerId = GetPlayerServerId(PlayerId())
    --TriggerServerEvent('blrp_tablet:createContractWithPlayer', playerServerId, data)
    TriggerEvent('vrp:client:selectTargetPlayer', 2, function(selected_ped)
        if selected_ped ~= -1 then
            local target_player = GetPlayerServerId(selected_ped)
            TriggerServerEvent('blrp_tablet:createContractWithPlayer', target_player, data)
        end
    end)
    cb('ok')
end)

RegisterNUICallback('printReport', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerEvent('printer:isNearPrinter', function(result)
        if result then
            TriggerServerEvent('core:server:printPdReport', data)
        else
            TriggerEvent('vrp:client:notify', 'Not near a printer')
        end
    end)
    cb('ok')
end)

RegisterNUICallback('printPaper', function(data, cb)
  TriggerEvent('printer:isNearPrinter', function(result)
      if data.copies and data.copies > 20 then
          return TriggerEvent('vrp:client:notify', 'You can only print up to 20 copies at once')
      end

      if result then
          TriggerEvent('core:client:printPaper', data.id, data.title, data.contents, false, data.copies)
      else
          TriggerServerEvent('core:server:printFromPrinterItem', data.id, data.title, data.contents, data.copies)
      end
  end)

  cb('ok')
end)

RegisterNUICallback('chargeMoneySelf', function(data, cb)
    TriggerServerEvent('blrp_tablet:server:chargeMoneySelf', data.uuid, data.amount)
    cb('ok')
end)

RegisterNUICallback('printPaperGive', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('blrp_paper:server:givePaperDirect', data.id, data.title, data.contents)
    cb('ok')
end)

RegisterNUICallback('pinTempPaper', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('blrp_tablet:server:pinPaperTemp', data.title, data.contents)
    cb('ok')
end)

RegisterNUICallback('publishPaper', function(data, cb)
  exports.blrp_core:me().notify('Cannot publish paper this way. Use third eye distribution in Weazel News HQ')
  cb('ok')
end)

RegisterNUICallback('showExistingPaper', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('blrp_paper:viewExistingPaper', data.id)
    cb('ok')
end)

RegisterNUICallback('printExistingPaper', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerEvent('printer:isNearPrinter', function(result)
        if result then
            TriggerEvent('core:client:printExistingPaper', data.id, data.title)
        else
            TriggerEvent('vrp:client:notify', 'Not near a printer')
        end
    end)
    cb('ok')
end)

RegisterNUICallback('setHouseLocation', function(data, cb)
    local coords = GetEntityCoords(PlayerPedId())
    SendNUIMessage({
        type = "tablet:message",
        name = 'housing:location_callback',
        packet = {
            type = data.type,
            coords = coords,
        }
    })
end)

RegisterNUICallback('requestNearPlayer', function(data, cb)
    TriggerEvent('vrp:client:selectTargetPlayer', 2, function(selected_ped)
        if selected_ped ~= -1 then
            local target_player = GetPlayerServerId(selected_ped)
            TriggerServerEvent('blrp_tablet:server:offerRequestNearPlayer', target_player, data.title)
        end
    end)

    cb('ok')
end)

RegisterNUICallback('sendFine', function(data, cb)
    --TriggerEvent('blrp_tablet:hide')
    TriggerEvent('vrp:client:selectTargetPlayer', 2, function(selected_ped)
        if selected_ped ~= -1 then
            local target_player = GetPlayerServerId(selected_ped)
            TriggerServerEvent('core:server:police:finePlayer', target_player, {
                fine_id = data.fine_id,
                character_number = data.character_number,
                fine = data.fine,
            })
        else
            TriggerServerEvent('blrp_tablet:server:failSentenceOrFine', data.fine_id)
        end
    end)

    cb('ok')
end)

RegisterNUICallback('sendPrison', function(data, cb)
    --TriggerEvent('blrp_tablet:hide')
    TriggerEvent('vrp:client:selectTargetPlayer', 2, function(selected_ped)
        if selected_ped ~= -1 then
            local target_player = GetPlayerServerId(selected_ped)
            TriggerServerEvent('core:server:prison:sendToJail', target_player, {
                fine_id = data.fine_id,
                character_number = data.character_number,
                fine = data.fine,
                time = data.time,
                res = data.res,
            })
        else
            TriggerServerEvent('blrp_tablet:server:failSentenceOrFine', data.fine_id)
        end
    end)

    cb('ok')
end)

RegisterNUICallback('houseInit', function(data, cb)
    TriggerServerEvent('blrp_tablet:registerHouse', data.id)
    cb('ok')
end)

RegisterNUICallback('waypointHouse', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('vrp:server:selfMarkGps', data.coords)
    cb('ok')
end)

RegisterNUICallback('saveBankAccount', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    local knownAccount = {
      account_number = tostring(data.bank),
    }
    --pBanking.addKnown({ knownAccount })
    TriggerServerEvent('blrp_banking:server:open', false, {
        access_type = 'phone',
        modality = 'Known',
        known_number = tostring(data.bank),
    })
    cb('ok')
end)

RegisterNUICallback('sellClosest', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('core:server:sellHouseClosest', data.id)
    cb('ok')
end)

RegisterNUICallback('makeClosestCoowner', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('core:server:makeClosestCoowner', data.id)
    cb('ok')
end)

RegisterNUICallback('setGarageHeading', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('core:server:properties:updateGarageHeading', data.id, GetEntityHeading(PlayerPedId()))
    cb('ok')
end)

RegisterNUICallback('changeInterior', function(data, cb)
    cb('ok')

    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('core:server:properties:changeInterior', data.id)
end)

RegisterNUICallback('updateProperty', function(data, cb)
    TriggerServerEvent('core:server:syncSingleHouse', data)
    cb('ok')
end)

RegisterNUICallback('call', function(data, cb)
  local is_anon = data.anon

  TriggerEvent('blrp_tablet:hide')
  -- TriggerEvent('blrp_phone:client:hide')
  Citizen.Wait(300)

  cb('ok')

  if exports.blrp_core:me().isDisabledDeadOrRestrained() then
    return
  end

  if is_anon then
    data.number = '#' .. data.number
  end

  TriggerEvent('gcphone:triggerCall', data)
end)

RegisterNUICallback('printDocument', function(data, cb)
    local id = data.game_document_id
    if id == nil then return cb('error') end
    TriggerEvent('blrp_tablet:hide')
    TriggerEvent('core:client:printDocument', id)
    cb('ok')
end)

RegisterNUICallback('requestCoords', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    exports.blrp_core:me().notify('Press enter to confirm selection')

    local coords = tInteract.selectCoords()

    if coords and coords.x then
        TriggerEvent('blrp_tablet:show')

        cb({ x = coords.x, y = coords.y, z = coords.z })
    end
end)

RegisterNUICallback('takePictureWithResult', function(data, cb)
    local picture = tPhone.takePicture({ true, 'blrp_phone:client:show' })
    cb(picture)
    Citizen.Wait(2300)
    TriggerEvent('blrp_phone:client:animation:playText')
end)

RegisterNUICallback('openCameraTake', function(data, cb)
    local picture = tPhone.takePicture({ false, 'blrp_phone:client:show' })
    cb(picture)
end)

RegisterNUICallback('resetMyself', function(data, cb)
    cb()
    pTablet.resetMyself({ })
end)

--RegisterNUICallback('getZoneRegions', function(data, cb)
--    cb(exports.blrp_zones:GetAllZones())
--end)

--RegisterNUICallback('setInputFocused', function(data, cb)
--    block_in_game_keys = data.value
--
--    if hybrid_mouse_enabled then
--        print('blocking keys', block_in_game_keys)
--
--        TriggerEvent('core:client:adjustInterfaceOptions', {
--            is_custom = not block_in_game_keys,
--            block_movement_keys = block_in_game_keys,
--            block_typing_keys = block_in_game_keys,
--            block_car_movement = block_in_game_keys,
--        })
--    end
--end)

--category_identifier: "planes"
--category_name: "planes"
--created_at: null
--id: 1
--identifier: "luxor"
--image_link: "https://cfx-nui-blrp_vehicles/html/cars/luxor.webp"
--name: "Luxor"
--permission: null
--price: "2250000"
RegisterNUICallback('staffSpawnVehicle', function(data, cb)
    TriggerEvent('blrp_tablet:hide')
    TriggerServerEvent('blrp_tablet:server:staffSpawnVehicle', data.identifier)
    cb()
end)

-- Used in case the tablet fails to hit the API. This will allow the phone to continue to be used
RegisterNUICallback('fetchUserFallback', function(data, cb)
    cb(pTablet.fetchCitizenFallback())
end)

RegisterNUICallback('playersWithinDistance', function(data, cb)
    local playersNearby = pTablet.getPlayerIdentifiersWithinDistance({})
    cb(playersNearby)
end)

RegisterNUICallback('iDidHateSpeech', function(data, cb)
    cb('ok')
    pTablet.banMyself({ data.content })
end)

RegisterNUICallback("checkPhoneOwnership", function(data, cb)
  local has_phone = pTablet.checkPhoneOwnership({})
  print('Checking phone ownership', has_phone)
  cb(has_phone)
end)



