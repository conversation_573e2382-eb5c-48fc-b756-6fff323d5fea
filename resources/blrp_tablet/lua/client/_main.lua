guiEnabled = false
authToken = nil
apiEndpoint = nil
socketsEndpoint = nil
socketsEndpoint = nil
last_seen_id = nil
signal_strength = nil
my_token = nil
my_endpoint = nil
my_endpoint_sockets = nil
hasInit = false


hybrid_mouse_enabled = false
block_in_game_keys = false

isDev = GetConvarInt('dev', -1)

-- Bind to proxy instances
pTablet = P.getInstance('blrp_tablet', 'tablet')
tInteract = T.getInstance('blrp_core', 'interact')
tPhone = T.getInstance('blrp_phone', 'phone')
pPhone = P.getInstance('blrp_phone', 'phone')
pBanking = P.getInstance('blrp_banking', 'main')

tTablet = {}
T.bindInstance('tablet', tTablet)

--if isDev == 1 then
  --apiEndpoint =  'http://127.0.0.1:8000'
  --apiEndpoint =  'http://dev-api.blrp.net'
--else
--  apiEndpoint = 'http://prod-api.blrp.net'
--end

RegisterNetEvent('core:client:registerSelectedPlayer', function()
  my_endpoint = nil
  my_endpoint_sockets = nil
  my_token = nil
  hasInit = false
  Citizen.Wait(5000)
  TriggerServerEvent('blrp_tablet:server:muteStaff', exports.blrp_core:LocalStorageGetBool('staff-muted') or false)
end)

tTablet.dispatchVueState = function(action, payload)
  SendNUIMessage({
    type = 'tablet:message',
    name = 'nui::tablet::dispatch',
    packet = {
      action = action,
      payload = payload,
    }
  })

  -- Chain message send to phone
  if action ~= 'system/open' then
    TriggerEvent('blrp_phone:client:dispatchVueState', action, payload)
  end
end

tTablet.setSignalStrength = function(value)
  signal_strength = value
  SendNUIMessage({
    type = "tablet:message",
    name = 'system:signalStrength:update',
    packet = {
      signal_strength = value,
    }
  })
end

RegisterNetEvent('blrp_tablet:getTabletState', function(cb)
  cb(guiEnabled)
end)

exports('IsTabletOpen', function()
  return guiEnabled
end)

--RegisterNetEvent('core:client:removeUI:tablet')
--AddEventHandler('core:client:removeUI:tablet', function()
--  TriggerEvent('blrp_tablet:hide')
--  tabletAnim(false)
--end)

RegisterNetEvent('menu:forceCloseMenu', function()
  TriggerEvent('blrp_tablet:hide')
  tabletAnim(false)
end)

function focusNUI (shouldDisplay)
  guiEnabled = shouldDisplay
  SetNuiFocus(shouldDisplay, shouldDisplay)
  SetNuiFocusKeepInput(shouldDisplay)
  if hybrid_mouse_enabled then
    TriggerEvent('core:client:enableInterface', 'tablet', shouldDisplay, function()
      -- Callback
    end, {
      is_custom = not block_in_game_keys,
      block_movement_keys = block_in_game_keys,
      block_typing_keys = block_in_game_keys,
      block_car_movement = block_in_game_keys,
    })
  else
    TriggerEvent('core:client:enableInterface', 'tablet', shouldDisplay)
  end
end

-- Register the token with the client side to send to the vue application`
RegisterNetEvent('blrp_tablet:register', function(token, endpoint)
  tTablet.dispatchVueState('auth/initUser', {
    token = token,
    endpoint = endpoint,
  })
end)

-- Set focus and tell vue to display
RegisterNetEvent('blrp_tablet:show')
AddEventHandler('blrp_tablet:show', function()
  if locked then
    return
  end

  locked = true

  Citizen.CreateThread(function()
    Citizen.Wait(2000)
    locked = false
  end)

  --if not ownsPhone then
  --  return exports.blrp_core:me().notify('You do not own a phone (to use the tablet) (or maybe it\'s off..')
  --end

  if not pTablet.hasTablet() then
    return
  end

  local syncSuccess = false

  if not exports.blrp_core:me().hasInterfaceOpen()
          and not exports.blrp_core:me().isDisabledDeadOrRestrained() then
    if hasInit then
      syncSuccess = true
      Citizen.CreateThread(function()
        pTablet.updateMyself({ }) -- Do not Wait for this

        tTablet.dispatchVueState('auth/initUser', {
          token = my_token,
          endpoint = my_endpoint,
          endpoint_sockets = my_endpoint_sockets,
        })
      end)
    else
      exports.blrp_core:me().notify('Please wait, syncing..')
      syncSuccess = pTablet.updateMyself({  }) -- Wait for this

      if syncSuccess then
        hasInit = true
        my_token = syncSuccess.token
        my_endpoint = syncSuccess.endpoint
        my_endpoint_sockets = syncSuccess.endpoint_sockets

        tTablet.dispatchVueState('auth/initUser', {
          token = my_token,
          endpoint = my_endpoint,
          endpoint_sockets = my_endpoint_sockets,
        })
      end
    end

    if hasInit and syncSuccess then
      tabletAnim(true)
      focusNUI(true)
      tTablet.dispatchVueState('system/open', { mode = 'tablet', position = getPositionInformation() })
      tTablet.dispatchVueState('location/setPosition', { position = getPositionInformation() })
    end
  else
    exports.blrp_core:me().notify('Unable to enable tablet, you already have an interface open')
  end
end)

RegisterNetEvent('blrp_tablet:client:searchPlate')
AddEventHandler('blrp_tablet:client:searchPlate', function(plate)
  --TriggerEvent('blrp_tablet:show')
  SendNUIMessage({
    type = 'tablet:message',
    name = 'blrp_tablet:client:searchPlate',
    packet = {
      plate = plate,
    }
  })
end)

RegisterNetEvent('blrp_tablet:setLastSeenId')
AddEventHandler('blrp_tablet:setLastSeenId', function(data)
  last_seen_id = data
end)


-- Set focus and tell vue to display
RegisterNetEvent('blrp_tablet:ghost')
AddEventHandler('blrp_tablet:ghost', function()
  SendNUIMessage({
    type = "tablet:toggle",
    enable = true,
    token = authToken,
    ghost = true,
    endpoint = apiEndpoint,
  })
end)

-- Set focus and tell vue to display
RegisterNetEvent('blrp_tablet:client:initVue')
AddEventHandler('blrp_tablet:client:initVue', function()

end)

RegisterNetEvent('blrp_tablet:sendItems')
AddEventHandler('blrp_tablet:sendItems', function(items)
  SendNUIMessage({
    type = "tablet:message",
    name = 'items:listItems',
    packet = {
      items = items,
    }
  })
end)

RegisterNetEvent('blrp_tablet:client:triggerNotification')
AddEventHandler('blrp_tablet:client:triggerNotification', function(notification)
  SendNUIMessage({
    type = "tablet:message",
    name = 'blrp_tablet:client:triggerNotification',
    packet = notification
  })
end)

RegisterNetEvent('blrp_tablet:client:nui:sendEvent')
AddEventHandler('blrp_tablet:client:nui:sendEvent', function(event)
  event.type = 'tablet:message'
  SendNUIMessage(event)
end)

-- Set focus and tell vue to hide
RegisterNetEvent('blrp_tablet:hide')
AddEventHandler('blrp_tablet:hide', function()
  focusNUI(false)
  tTablet.dispatchVueState('system/exit', { })
  tabletAnim(false)
end)

RegisterNetEvent('blrp_tablet:client:hide')
AddEventHandler('blrp_tablet:client:hide', function()
  focusNUI(false)
  tTablet.dispatchVueState('system/exit', { })
  tabletAnim(false)
end)

Citizen.CreateThread(function()
  -- This is just in case the resources restarted whilst the NUI is focused
  TriggerEvent('blrp_tablet:hide')
end)

----------------------------
-- Helper Functions
----------------------------

function getPositionInformation()
  local data = {}
  local pos = GetEntityCoords(PlayerPedId())
  data.current_cords = "["..pos.x..","..pos.y..","..pos.z.."]"
  data.current_street_a, data.current_street_b = GetStreetNameAtCoord(pos.x, pos.y, pos.z, Citizen.ResultAsInteger(), Citizen.ResultAsInteger())
  data.current_zone = GetLabelText(GetNameOfZone(pos.x, pos.y, pos.z))
  data.current_street_a = GetStreetNameFromHashKey(data.current_street_a)
  data.current_street_b = GetStreetNameFromHashKey(data.current_street_b)
  data.real_area = exports.blrp_core:GetInteriorNameAtPed()
  return data
end

function tabletAnim(boolean)
  if boolean then
    TriggerEvent('core:client:animations:queueAnimation', {
      name = 'tablet',
      group = 'tablet',
      priority = 3,
      resume = false,
      once = false,
      persist = true,
      animation = {
        dict = 'amb@code_human_in_bus_passenger_idles@female@tablet@base',
        name = 'base',
        speed = 3.0,
        speed_multiplier = -1,
        duration = -1,
        move_type = 49,
      },
      items = {
        {
          prop = 'prop_cs_tablet',
          offsets = { x = 0.17, y = 0.08, z = 0.13 },
          bonePos = { x = 0.17, y = 0.08, z = 0.13, rotX = 13.0, rotY = 378.0, rotZ = -151.0 },
          bone = 18905,
          wait = 380,
        }
      }
    })
  else
    TriggerEvent('core:client:animations:wipeProps', { -1585232418 }, 1.5, true, true)
    ClearPedSecondaryTask(PlayerPedId())
  end
end

RegisterNUICallback('refreshNUI', function()
  TriggerEvent('blrp:refreshnui')
end)

AddEventHandler('blrp:refreshnui', function()
  hasInit = false
  pTablet.resetMyself()
  tTablet.dispatchVueState('system/reloadUI', { })
  TriggerEvent('blrp:nuirefreshed')
end)
