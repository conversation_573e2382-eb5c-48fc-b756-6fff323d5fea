tEnvironment = T.getInstance('blrp_core', 'environment')

local license_translations = {
  ['driverlicense']  = 'Driver License:',
  ['firearmlicense'] = 'Firearm License:',
  ['huntinglicense'] = 'Hunting License:',
  ['pilotlicense']   = 'Pilot License:',
  ['towlicense']     = 'Tow License:',
  ['cdl']     = 'Commercial Drivers License:',
  ['fishinglicense']     = 'Fishing License:',
}

pTablet.getWeaponInformation = function(registration)
  local rows = MySQL.Sync.fetchAll('SELECT characters.id, characters.firstname, characters.lastname, characters.phone, characters.dlnumber, evidence_registrations.registration, evidence_registrations.business FROM evidence_registrations JOIN characters on evidence_registrations.character_number = characters.id WHERE evidence_registrations.registration = @registration', {
    registration = registration
  })

  return rows[1] or nil
end

pTablet.getVehicleInformation = function(registration)
  local src = source
  registration = string.upper(registration)

  local result = MySQL.single.await('SELECT vrp_user_vehicles.id, vrp_user_vehicles.user_id, vrp_user_vehicles.characterNumber, vrp_user_vehicles.business, vrp_user_vehicles.registration, vrp_user_vehicles.vehicle, vrp_user_vehicles.colour, vrp_user_vehicles.scolour, vrp_user_vehicles.colour_rgb, vrp_user_vehicles.scolour_rgb, vrp_user_vehicles.seized, vrp_user_vehicles.vinscratch, characters.firstname, characters.lastname, characters.phone, characters.dlnumber FROM vrp_user_vehicles LEFT JOIN characters ON characters.id = vrp_user_vehicles.characterNumber AND characters.identifier = vrp_user_vehicles.user_id WHERE vrp_user_vehicles.registration = ?', {
    registration
  })

  if result and result.user_id == 0 and result.business then
    result = {
      registration = registration,
      model = result.vehicle,
      vehicle = result.vehicle,
      color = result.colour,
      scolour = result.scolour,

      firstname = result.business,
      lastname = '',
      phone = 'N/A',
      dlnumber = 'N/A',
    }
  elseif (not result or result.user_id == 0) and string.len(registration) == 8 and string.match(registration, '%d%d%a%a%a%d%d%d') then
    -- Pass the requesting player's source ID for proximity checking
    result = getPlateQueryInfo(registration, src)
  end

  return result
end

pTablet.getDLInformation = function(term)
  local rows = MySQL.Sync.fetchAll('SELECT id, firstname, lastname, phone, dlnumber FROM characters WHERE dlnumber = @dl_number LIMIT 1', {
    dl_number = term
  })

  if #rows <= 0 then
    return false
  end

  local character_data = rows[1]

  local license_statuses = {
    ['Driver License:']  = 'ERR',
    ['Firearm License:'] = 'ERR',
    ['Hunting License:'] = 'ERR',
    ['Pilot License:']   = 'ERR',
    ['Tow License:']     = 'ERR',
    ['Commercial Drivers License:']     = 'ERR',
  }

  local license_rows = MySQL.Sync.fetchAll('SELECT * FROM vrp_user_data WHERE dkey = @dkey LIMIT 1', {
    dkey = 'vRP:licenses' .. character_data.id
  })

  if #license_rows > 0 then
    local license_row = json.decode(license_rows[1].dvalue)

    for k, v in pairs(license_row) do
      local translation = license_translations[k]

      if translation then
        if tonumber(v.suspended) == 1 then
          license_statuses[translation] = 'REVOKED'
        elseif tonumber(v.licensed) == 1 then
          license_statuses[translation] = 'ACTIVE'
        else
          license_statuses[translation] = 'NONE'
        end
      end
    end
  end

  local vehicle_rows = MySQL.Sync.fetchAll('SELECT vrp_user_vehicles.id, vrp_user_vehicles.user_id, vrp_user_vehicles.characterNumber, vrp_user_vehicles.registration, vrp_user_vehicles.vehicle, vrp_user_vehicles.colour, vrp_user_vehicles.scolour, vrp_user_vehicles.seized, characters.firstname, characters.lastname, characters.phone, characters.dlnumber FROM vrp_user_vehicles JOIN characters ON characters.id = vrp_user_vehicles.characterNumber AND characters.identifier = vrp_user_vehicles.user_id WHERE vrp_user_vehicles.vinscratch = false AND vrp_user_vehicles.characterNumber = @character_id ORDER BY vehicle', {
    character_id = character_data.id
  })

  return character_data, license_statuses, vehicle_rows
end

pTablet.getPlayerIdentifiersWithinDistance = function()
  local character = exports.blrp_core:character(source)
  local playersNearby = character.peopleAroundMe(250)

  local playerIds = { character.get('identifier') } -- add self

  for _, _source in pairs(playersNearby) do
    local aroundMeCharacter = exports.blrp_core:character(_source)

    if aroundMeCharacter ~= nil then
      table.insert(playerIds, aroundMeCharacter.get('identifier'))
    end
  end

  return playerIds
end

local cachedBoloPlates = {}

Citizen.CreateThread(function()
  Citizen.Wait(5000)
  while true do
    local result = doPostRequest('/police/bolo-plates')
    if result and type(result) == "table" then
      cachedBoloPlates = result
      print('[BOLO] Plates cache updated.')
    else
      print('[BOLO] Failed to update plates cache.')
    end

    Citizen.Wait(60000)
  end
end)

pTablet.getCurrentBolos = function()
  return cachedBoloPlates
end

pTablet.checkPhoneOwnership = function()
  local character = exports.blrp_core:character(source)

  if not character then
    return false
  end
  local has_phone = character.hasItemFromCategory('phones')
  print('Checking phone ownership', has_phone)
  return has_phone
end
