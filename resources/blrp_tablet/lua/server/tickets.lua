RegisterServerEvent('blrp_tablet:server:payTickets')
AddEventHandler('blrp_tablet:server:payTickets', function()
  local character = exports.blrp_core:character(source)

  if not character then
    return
  end

  local character_number = character.get('id')

  if not character_number then
    character.notify('Unable to retrieve character information')
    return
  end

  -- First check if there are unpaid tickets
  local tickets_data = doGetRequest('/police/unpaid-tickets/' .. character_number)

  if not tickets_data then
    character.notify('You have no unpaid tickets')
    return
  end

  local amount_due = tonumber(tickets_data.amount_due)

  -- Ask for confirmation
  if not character.request('Pay $' .. amount_due .. ' for ' .. tickets_data.tickets_due .. ' unpaid ticket(s)?') then
    return
  end

  -- Remove money from character
  if not character.tryPayment(amount_due, false, true, true) then
    character.notify('Failed to process payment')
    return
  end

  -- Mark tickets as paid via API
  local payment_result = doPostRequest('/police/mark-tickets-paid/' .. tostring(character_number))

  if payment_result then
    character.notify('Successfully paid $' .. amount_due .. ' in traffic tickets')
    character.log('TICKETS', 'Paid $' .. amount_due .. ' in traffic tickets')
  else
    -- If API call failed, notify the user of a bug
    character.notify('Failed to process ticket payment.')
  end
end)