local isDev = GetConvarInt('dev', -1)
local robbable_things = { }

SetConvarReplicated("dev", isDev)
pTablet = { }
P.bindInstance('tablet', pTablet)
tPhone = T.getInstance('blrp_phone', 'phone')
tTablet = T.getInstance('blrp_tablet', 'tablet')

local pending_reloads = { }
local cached_players = { }
local cached_tokens = { }
local staff_muted = {}

AddEventHandler('onResourceStart', function(resourceName)
  local resources = {
    ['blrp_tablet'] = true
  }

  if GlobalState.is_dev then
    resources = {
      ['blrp_tablet'] = true,
      ['blrp_core'] = true,
      ['housing'] = true,
    }
  end

  if not (resources)[resourceName] then
    return
  end

  Citizen.Wait(2000)

  fetchNewsReleases()
  pTablet.loadTabletData('housing', '/housing/all', function(data)
    TriggerEvent('core:server:syncHousing', data)
  end)
end)

-- Sync tablet to client when player joins
RegisterNetEvent('core:server:registerSelectedPlayer', function(source)
  local player = source

  Citizen.Wait(2000)

  commitCitizenAsync(player)
end)

RegisterNetEvent('blrp_tablet::server:attemptApiRecovery', function()
  local player = source

  Citizen.Wait(2000)

  commitCitizenAsync(player, false, false, function(result)
    if result.token then
      tPhone.dispatchVueState(player, { 'auth/initUser', {
        token = result.token,
        endpoint = result.endpoint,
        endpoint_sockets = result.endpoint_sockets,
      }})

      tTablet.dispatchVueState(player, { 'auth/initUser', {
        token = result.token,
        endpoint = result.endpoint,
        endpoint_sockets = result.endpoint_sockets,
      }})
    end
  end, true)
end)

pTablet.hasTablet = function()
  return exports.blrp_core:character(source).hasItemQuantity('tablet', 1)
end

pTablet.updateMyself = function(forceReloadSettings)
  local p = promise.new()

   commitCitizenAsync(source, forceReloadSettings, false, function(result)
    p:resolve(result)
  end)

  return Citizen.Await(p)
end

pTablet.resetMyself = function()
  local character = exports.blrp_core:character(source)
  local character_number = character.get('id')

  if cached_players[character_number] then
    cached_players[character_number] = nil
  end
end

function commitCitizenAsync(playerSource, reloadSettings, mode, cb, recovery)
  Citizen.CreateThread(function()
    local character = exports.blrp_core:character(playerSource)
    cb = cb or function() end
    local changed_groups = {}
    local my_groups_arr = character.getGroups()

    if my_groups_arr then
      for group_name, _ in pairs(my_groups_arr) do
        if group_name == 'LSFD' then
          changed_groups['Emergency'] = true
        end

        if not (group_name == 'staff' or group_name == 'mod' or group_name == 'admin' or group_name == 'superadmin') or not staff_muted[character.get('identifier')] then
          changed_groups[group_name] = true
        end
      end
    end

    local data = {
      source = playerSource,
      player_id = character.get('identifier'),
      vrp_id = character.get('id'),
      name = character.get('firstname') .. ' ' .. character.get('lastname'),
      phone = character.get('phone'),
      dlnumber = character.get('dlnumber'),
      character_number = character.get('id'),
      groups = changed_groups,
      permissions = character.getPermissions(),
    }

    local encodedData = json.encode(data)
    local charId = data.character_number

    if cached_players[charId] and cached_players[charId] == encodedData then
      if cached_tokens[charId] then
        if reloadSettings then
          local response = doPostRequest('/secure/authorize-player', data)
          if response then
            TriggerEvent('core:server:character:registerSettings', playerSource, response.settings)
          end
        end
        cb({ token = cached_tokens[charId], endpoint = apiEndpoint, endpoint_sockets = socketsEndpoint })
        return
      end
    end

    local response = doPostRequest('/secure/authorize-player', data)
    if not response then
      cached_tokens[charId] = nil
      cached_players[charId] = nil
      if not recovery then
        character.notify('Failed to register tablet/phone instance. Please wait a while and try again..')
        cb({ token = 'fetch:failed', endpoint = apiEndpoint })
      end
      return
    end

    TriggerEvent('core:server:character:registerSettings', playerSource, response.settings)

    if response.token then
      cached_tokens[charId] = response.token
      cached_players[charId] = encodedData
      cb({ token = response.token, endpoint = apiEndpoint, endpoint_sockets = socketsEndpoint })
      return
    end

    cb({ token = 'fetch:noConditionReached', endpoint = apiEndpoint, endpoint_sockets = socketsEndpoint })
  end)
end


-- This gets called by the tablet UI if the actual API fails. Allow use of phone or non API features while
-- it is down or not responding
pTablet.fetchCitizenFallback = function()
    local character = exports.blrp_core:character(source)

    return {
        id = 999,
        player_id = character.get('identifier'),
        vrp_id = character.get('id'),
        name = character.get('firstname') .. ' ' .. character.get('lastname'),
        phone = character.get('phone'),
        license = character.get('dlnumber'),
        character_number = character.get('id'),
        object_groups = character.getGroups(),
    }
end

RegisterNetEvent('blrp_tablet:server:muteStaff')
AddEventHandler('blrp_tablet:server:muteStaff', function(value)
  local character = exports.blrp_core:character(source)
  if character then
    staff_muted[character.get('identifier')] = value
    print('Updated Staff Muted status for '..character.get('fullname'))
    pTablet.updateMyself()
  end
end)

RegisterServerEvent('blrp_tablet:registerPapers')
AddEventHandler('blrp_tablet:registerPapers', function()
  fetchNewsReleases()
end)

RegisterServerEvent('blrp_tablet:registerHouse')
AddEventHandler('blrp_tablet:registerHouse', function(id)
  fetchHouse(id)
end)

RegisterServerEvent('tablet:server:fetchNewsReleases')
AddEventHandler('tablet:server:fetchNewsReleases', function()
  fetchNewsReleases()
end)

function fetchNewsReleases()
  pTablet.loadTabletData('news', '/news/records', function(data)

  end)
end

function fetchHouse(id)
  local data = doGetRequest('/housing/house-details/' .. id)
  TriggerEvent('core:server:syncSingleHouse', data)
end

function updateHouse(id, values)
  Citizen.CreateThread(function()
    values.id = id -- Makes it update instead of create
    local data = doPostRequest('/housing/create', values)
    if data then
      TriggerEvent('core:server:syncSingleHouse', data)
    else
      print('[updateHouse] Failed to update house ID ' .. tostring(id))
    end
  end)
end

AddEventHandler('blrp_tablet:updateHouse', updateHouse)
exports('UpdateHouse', updateHouse)

RegisterServerEvent('blrp_tablet:server:failSentenceOrFine')
AddEventHandler('blrp_tablet:server:failSentenceOrFine', function(id)
  -- print('failing')
  doPostRequest('/secure/cancel-sentence', { id = id })
end)

RegisterServerEvent('blrp_tablet:server:confirmSentenceOrFine')
AddEventHandler('blrp_tablet:server:confirmSentenceOrFine', function(id)
  -- print('succeeding')
  doPostRequest('/secure/confirm-sentence', { id = id })
end)

RegisterServerEvent('blrp_tablet:server:chargeMoneySelf')
AddEventHandler('blrp_tablet:server:chargeMoneySelf', function(uuid, amount)
  local character = exports.blrp_core:character(source)

  character.tryTakeBankMoney(amount, function(paid)
    if not paid then
      character.notify('Not enough money to do this')
      return
    end

    local result = doPostRequest('/secure/confirm-sentence', {
      uuid = uuid,
      amount = amount
    })

    if result then
      character.notify('Payment successful')
    end
  end)
end)

RegisterServerEvent('blrp_tablet:insertDocument')
AddEventHandler('blrp_tablet:insertDocument', function(params)
  doPostRequest('/secure/submit-document', params)
end)

RegisterServerEvent('blrp_tablet:server:pinPaperTemp')
AddEventHandler('blrp_tablet:server:pinPaperTemp', function(title, content)
  local character = exports.blrp_core:character(source)
  character.notify('Added temporary display in area')
  TriggerEvent('groundPapers:server:registerTempPaper', {
    paper_id = math.random(0,*********),
    paper_title = title,
    content = content,
    owner_character_id = character.get('id'),
    owner_user_id = character.get('identifier'),
    pos = character.getCoordinates(),
    is_temp = true,
  })
end)

RegisterServerEvent('blrp_tablet:server:offerRequestNearPlayer')
AddEventHandler('blrp_tablet:server:offerRequestNearPlayer', function(target_player, title)
  local online_source = source
  local selfCharacter = exports.blrp_core:character(source)
  local targetCharacter = exports.blrp_core:character(target_player)

  selfCharacter.notify('Offering to the closest person')

  if not targetCharacter.request(title) then
    return
  end

  TriggerClientEvent('blrp_tablet:client:nui:sendEvent', online_source, {
    name = 'tools:offerRequestNearPlayer:accept',
    packet = {
      character_number = targetCharacter.get('id')
    }
  })
end)
  -- TriggerEvent('core:server:offerSignaturePlayer', target_player, from_player, data)

RegisterServerEvent('blrp_tablet:createContractWithPlayer')
AddEventHandler('blrp_tablet:createContractWithPlayer', function(target_player, data)
  local from_player = source
  TriggerEvent('core:server:offerSignaturePlayer', target_player, from_player, data)
end)

RegisterServerEvent('blrp_tablet:getEvidence')
AddEventHandler('blrp_tablet:getEvidence', function(identifier)
  local from_player = source
  local fullIdentifier = 'pd_evidence_' .. identifier
  exports.blrp_core:chest(fullIdentifier).fetchContents(function(items)
    TriggerClientEvent('blrp_tablet:sendItems', from_player, items)
    end, true)
end)

-- pd_evidence_inc_person_10025295
-- pd_evidence_pd_evidence_inc_person_10025295

function confirmReloaded(index)
  local id = pending_reloads[index].id
  pending_reloads[index] = nil
  doPostRequest('/secure/confirm-reload' .. id)
end

---
--- For dev purposes
---
local iAm = nil

RegisterServerEvent('blrp_tablet:server:setMyselfDev', function()
  if not iAm then
    local userId = exports.blrp_core:character(source).get('identifier')
    iAm =  userId
  end
end)

-- Only do this when in dev
if isDev == 1 then
  Citizen.CreateThread(function()
    while true do
      Citizen.Wait(2000)
      if iAm and GetResourceState('blrp_dev') == 'started' then
        checkNewReloads()
      end
      --if iAm then
      --  checkNewReloads()
      --end
    end
  end)
end

function checkNewReloads()
  local alreadyReloadedOne = false
  doGetRequest('/secure/pending-reloads/' .. tostring(iAm), function(data)
    --print('---- DOING AUTO RELOAD CHECK. THIS SHOULD NOT BE RUNNING IN PRODUCTION.', json.encode(data))
    for _, reloadable in pairs(data) do
      doPostRequest('/secure/confirm-reload/' .. reloadable.id)
      if not alreadyReloadedOne then
        alreadyReloadedOne = true
        if reloadable.name == 'blrp_dev' then
          TriggerClientEvent('vrp:client:notify', -1, 'Restarted ' .. reloadable.name .. ' from tablet instead of blrp_dev')
          StopResource(reloadable.name)
          Citizen.Wait(300)
          StartResource(reloadable.name)
        else
          TriggerEvent('blrp_dev:server:resetResource', reloadable.name)
        end
      end
    end
  end)
end

pTablet.banMyself = function(content)
  -- content is what they said
  local character = exports.blrp_core:character(source)
  print('automod tablet ban triggered')

  character.log('AUTOMOD', 'Tablet hate speech detection: ' .. content)
  --character.kick('Watch your language')
  character.ban(character.get('identifier') .. ' db must appeal (automod)', 0, 'Tablet hate speech detection: ' .. content, true)
end


AddEventHandler('blrp_tablet:server:setRobbableStatus', function(type, can_be_robbed)
  robbable_things[type] = can_be_robbed
end)

-- centralbank / digiden / fleeca / vangelico / bobcat
--Citizen.CreateThread(function()
--  while true do
--    Citizen.Wait(63000)
--    doPostRequest('/robbable/sync', {
--      robbable_things = robbable_things -- [digiden] = false/true
--    })
--  end
--end)

exports('staffMuted', function(identifier)
  return staff_muted[identifier]
end)
