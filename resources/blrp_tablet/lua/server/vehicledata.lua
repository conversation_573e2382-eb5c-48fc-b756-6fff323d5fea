local vehicle_plate_data = {}
local plates_stolen = {}

exports('SetPlateStolen', function(plate)
  plate = string.gsub(string.upper(plate), '^%s*(.-)%s*$', '%1')

  plates_stolen[plate] = true
end)

-- Search for a vehicle near the requesting player by plate
local function findVehicleNearPlayer(playerId, targetPlate)
  local playerPed = GetPlayerPed(playerId)
  if not playerPed or playerPed == 0 then
    return nil
  end

  local playerCoords = GetEntityCoords(playerPed)
  local vehicles = GetAllVehicles()

  for _, vehicle in ipairs(vehicles) do
    if DoesEntityExist(vehicle) then
      local vehicleCoords = GetEntityCoords(vehicle)
      local distance = #(playerCoords - vehicleCoords)

      -- Check within 100 meter radius
      if distance <= 100.0 then
        local plate = string.gsub(string.upper(GetVehicleNumberPlateText(vehicle)), '^%s*(.-)%s*$', '%1')

        if plate == targetPlate then
          local model = GetEntityModel(vehicle)
          local color_primary, color_secondary = GetVehicleColours(vehicle)

          return {
            registration = plate,
            model = model,
            vehicle = model,
            colour = color_primary,
            scolour = color_secondary,
            cached_at = GetGameTimer(),
            discovered_by_proximity = true
          }
        end
      end
    end
  end

  return nil
end

function getPlateQueryInfo(plate, requestingPlayerId)

  local plate_data = vehicle_plate_data[plate]

  -- If no cached data, try to find the vehicle near the requesting player
  if not plate_data and requestingPlayerId then
    plate_data = findVehicleNearPlayer(requestingPlayerId, plate)
    if plate_data then
      vehicle_plate_data[plate] = plate_data
    end
  end

  if not plate_data then
    return nil
  end

  if not plate_data.ro_info_obtained then
    local p = promise:new()

    PerformHttpRequest('https://randomuser.me/api/?nat=us,dk,fr,gb&inc=name&noinfo', function(errorCode, resultData, resultHeaders)
      if errorCode == 200 then
        p:resolve(tostring(resultData))
      else
        p:resolve(json.encode({
          results = {
            {
              name = {
                first = 'UNKNOWN',
                last = 'UNKNOWN'
              }
            }
          }
        }))
      end
    end)

    local ro_info = json.decode(Citizen.Await(p)).results[1]

    vehicle_plate_data[plate].firstname = ro_info.name.first
    vehicle_plate_data[plate].lastname = ro_info.name.last
    vehicle_plate_data[plate].phone = math.random(100, 999) .. '-' .. math.random(1000, 9999)
    vehicle_plate_data[plate].dlnumber = math.random(11111111, 99999999)
    vehicle_plate_data[plate].ro_info_obtained = true
  end

  plate_data.stolen = plates_stolen[plate]

  -- Check if vehicle should be marked as stolen based on time elapsed since theft
  if plate_data.stolen_timestamp and plate_data.stolen_delay and not plate_data.stolen then
    local current_time = GetGameTimer()
    local time_since_theft = current_time - plate_data.stolen_timestamp

    if time_since_theft >= plate_data.stolen_delay then
      plate_data.stolen = true
      plates_stolen[plate] = true -- Also add to the stolen plates registry
      vehicle_plate_data[plate].stolen = true -- Update the stored data too
      print('[Tablet] Vehicle ' .. plate .. ' marked as stolen after ' .. math.floor(time_since_theft / 60000) .. ' minutes')
    end
  end

  return plate_data
end

exports('HandleEntityCreating', function(entity, model)
  if not DoesEntityExist(entity) then
    return
  end

  local plate = string.gsub(string.upper(GetVehicleNumberPlateText(entity)), '^%s*(.-)%s*$', '%1')
  local color_primary, color_secondary = GetVehicleColours(entity)

  -- Check if we already have data for this plate and preserve owner info
  local existing_data = vehicle_plate_data[plate]

  vehicle_plate_data[plate] = {
    registration = plate,
    model = model,
    vehicle = model,
    colour = color_primary,
    scolour = color_secondary,
  }

  -- Preserve existing owner information if it exists
  if existing_data and existing_data.ro_info_obtained then
    vehicle_plate_data[plate].firstname = existing_data.firstname
    vehicle_plate_data[plate].lastname = existing_data.lastname
    vehicle_plate_data[plate].phone = existing_data.phone
    vehicle_plate_data[plate].dlnumber = existing_data.dlnumber
    vehicle_plate_data[plate].ro_info_obtained = existing_data.ro_info_obtained
  end

  -- Preserve existing stolen timestamp, delay, and criminal activity flags
  if existing_data and existing_data.stolen_timestamp then
    vehicle_plate_data[plate].stolen_timestamp = existing_data.stolen_timestamp
    vehicle_plate_data[plate].stolen_delay = existing_data.stolen_delay
    vehicle_plate_data[plate].criminal_activity = existing_data.criminal_activity
  end
end)

-- Handler for when vehicles are hotwired or lockpicked
local function processCriminalityTowardVehicle(network_id, vehicle_data, player)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if not DoesEntityExist(vehicle) then
    return
  end

  local plate = string.gsub(string.upper(GetVehicleNumberPlateText(vehicle)), '^%s*(.-)%s*$', '%1')
  local model = GetEntityModel(vehicle)
  local color_primary, color_secondary = GetVehicleColours(vehicle)

  -- Add hotwired/lockpicked vehicles to the registry so they can always be queried
  -- Check if we already have data for this plate and preserve owner info
  local existing_data = vehicle_plate_data[plate]

  vehicle_plate_data[plate] = {
    registration = plate,
    model = model,
    vehicle = model,
    colour = color_primary,
    scolour = color_secondary,
    cached_at = GetGameTimer(),
    criminal_activity = true,
    stolen_timestamp = GetGameTimer(), -- Track when vehicle was first stolen
    stolen_delay = math.random(30, 60) * 60 * 1000 -- Random 30-60 minutes delay in milliseconds
  }

  -- Preserve existing owner information if it exists
  if existing_data and existing_data.ro_info_obtained then
    vehicle_plate_data[plate].firstname = existing_data.firstname
    vehicle_plate_data[plate].lastname = existing_data.lastname
    vehicle_plate_data[plate].phone = existing_data.phone
    vehicle_plate_data[plate].dlnumber = existing_data.dlnumber
    vehicle_plate_data[plate].ro_info_obtained = existing_data.ro_info_obtained
  end

  -- Preserve existing stolen timestamp and delay if it exists (don't reset the timer)
  if existing_data and existing_data.stolen_timestamp then
    vehicle_plate_data[plate].stolen_timestamp = existing_data.stolen_timestamp
    vehicle_plate_data[plate].stolen_delay = existing_data.stolen_delay
  end

  print('[Tablet] Vehicle with criminal activity, added to registry: ' .. plate)
end

-- Register event handlers for vehicle criminal activity
AddEventHandler('blrp_vehicles:server:registerLockpickedVehicle', processCriminalityTowardVehicle)
AddEventHandler('blrp_vehicles:server:registerHotwiredVehicle', processCriminalityTowardVehicle)

-- Glovebox search functionality for hotwired vehicles
local glovebox_items = {
  -- Fake IDs and paperwork (higher chance) - using existing items
  {item = 'id_citizen', weight = 25, name_required = true}, -- Existing item
  --{item = 'manilla_folder', weight = 15, name_required = true}, -- Existing item
  --{item = 'manilla_folder_classified', weight = 5, name_required = true}, -- Existing item - rarer

  -- Business cards and receipts with names
  {item = 'business_card', weight = 12, name_required = true}, -- Custom item for RP
  {item = 'receipt', weight = 8, name_required = true}, -- Custom item for RP

  -- Random junk items - using existing items
  {item = 'water', weight = 10}, -- Existing item
  {item = 'cigarettes', weight = 7}, -- Existing item
  {item = 'lighter', weight = 6}, -- Existing item
  {item = 'pussycatmag', weight = 4}, -- Existing item
  {item = 'pussycatmag2', weight = 3}, -- Existing item
  {item = 'xxscondom', weight = 4}, -- Existing item
  {item = 'xxlcondom', weight = 3}, -- Existing item
  {item = 'binoculars', weight = 3}, -- Existing item

  -- Rare valuable items - using existing items
  {item = 'cash', weight = 3, amount_min = 100, amount_max = 1250}, -- Existing item

  -- Nothing found
  {item = nil, weight = 15} -- 15% chance of finding nothing
}

local function generateRandomName(vehicle_entity)
  -- Try to get the plate from the vehicle to check for existing data
  local plate = nil
  if vehicle_entity and DoesEntityExist(vehicle_entity) then
    plate = string.gsub(string.upper(GetVehicleNumberPlateText(vehicle_entity)), '^%s*(.-)%s*$', '%1')
  end

  -- Check if we already have owner info for this vehicle's plate
  if plate and vehicle_plate_data[plate] and vehicle_plate_data[plate].ro_info_obtained then
    local existing_data = vehicle_plate_data[plate]
    return existing_data.firstname .. ' ' .. existing_data.lastname, existing_data.phone, existing_data.dlnumber
  end

  -- Generate new random info if we don't have existing data
  local p = promise:new()

  PerformHttpRequest('https://randomuser.me/api/?nat=us,dk,fr,gb&inc=name&noinfo', function(errorCode, resultData, resultHeaders)
    if errorCode == 200 then
      p:resolve(tostring(resultData))
    else
      p:resolve(json.encode({
        results = {
          {
            name = {
              first = 'UNKNOWN',
              last = 'UNKNOWN'
            }
          }
        }
      }))
    end
  end)

  local ro_info = json.decode(Citizen.Await(p)).results[1]
  local full_name = ro_info.name.first .. ' ' .. ro_info.name.last
  local phone = math.random(100, 999) .. '-' .. math.random(1000, 9999)
  local dlnumber = math.random(11111111, 99999999)
  vehicle_plate_data[plate] = {}
  -- If we have a plate, store this info for consistency
  if plate and vehicle_plate_data[plate] then
    vehicle_plate_data[plate].firstname = ro_info.name.first
    vehicle_plate_data[plate].lastname = ro_info.name.last
    vehicle_plate_data[plate].phone = phone
    vehicle_plate_data[plate].dlnumber = dlnumber
    vehicle_plate_data[plate].ro_info_obtained = true
  end

  return full_name, phone, dlnumber
end

local function getRandomGloveboxItem()
  local total_weight = 0
  for _, item_data in ipairs(glovebox_items) do
    total_weight = total_weight + item_data.weight
  end

  local random_num = math.random(total_weight)
  local current_weight = 0

  for _, item_data in ipairs(glovebox_items) do
    current_weight = current_weight + item_data.weight
    if random_num <= current_weight then
      return item_data
    end
  end

  return glovebox_items[#glovebox_items] -- fallback to last item
end

exports('SearchVehicleGlovebox', function(playerId, entity)
  if not DoesEntityExist(entity) then
    return false
  end

  local character = exports.blrp_core:character(playerId)
  if not character then
    return false
  end

  local found_item = getRandomGloveboxItem()

  if not found_item.item then
    -- Nothing found
    character.notify('You search the glovebox but find nothing of interest.')
    return false
  end

  local item_name = found_item.item
  local item_metadata = {}

  -- Add name to items that require it
  if found_item.name_required then
    local fake_name, fake_phone, fake_dlnumber = generateRandomName(entity)

    item_metadata.fake_name = fake_name
    item_metadata.fake_phone = fake_phone
    item_metadata.fake_dlnumber = fake_dlnumber

    if item_name == 'id_citizen' then
      character.notify('You found a fake ID belonging to ' .. fake_name .. '!')
      character.notify('Phone: ' .. fake_phone .. ' | DL#: ' .. fake_dlnumber)
      item_name = 'id_citizen:meta:' .. character.get('id') .. '-' .. os.time()
    elseif item_name == 'manilla_folder' then
      character.notify('You found some paperwork with the name ' .. fake_name .. ' on it.')
      character.notify('Contains contact info: ' .. fake_phone)
    elseif item_name == 'manilla_folder_classified' then
      character.notify('You found classified documents belonging to ' .. fake_name .. '!')
      character.notify('This looks important... Phone: ' .. fake_phone)
    elseif item_name == 'business_card' then
      character.notify('You found a business card for ' .. fake_name .. '.')
      character.notify('Contact: ' .. fake_phone)
    elseif item_name == 'receipt' then
      character.notify('You found a receipt with the name ' .. fake_name .. ' on it.')
      character.notify('Purchased with card ending in ' .. string.sub(fake_dlnumber, -4))
    end
  end

  -- Add item to player inventory using the proper core system
  local amount = math.random(found_item.amount_min or 1, found_item.amount_max or 1)
  character.give(item_name, amount, item_metadata, true)

  -- Log the item found in glovebox search
  character.log('ACTION', 'Found item in vehicle glovebox search', {
    item_id = item_name,
    amount = amount,
    metadata = item_metadata
  })

  return true
end)

-- Export to check if a plate is registered in the system
exports('IsPlateRegistered', function(plate)
  plate = string.gsub(string.upper(plate), '^%s*(.-)%s*$', '%1')
  return vehicle_plate_data[plate] ~= nil
end)

-- Export to get cached vehicle count (for debugging)
exports('GetCachedVehicleCount', function()
  local count = 0
  for _ in pairs(vehicle_plate_data) do
    count = count + 1
  end
  return count
end)

-- Track which gloveboxes have been searched to prevent spam
local searched_gloveboxes = {}

-- New event for searching hotwired/lockpicked vehicles
RegisterNetEvent('blrp_tablet:server:searchVehicle', function(network_id)
  local character = exports.blrp_core:character(source)
  local vehicle = NetworkGetEntityFromNetworkId(network_id)

  if not character or not DoesEntityExist(vehicle) then
    return
  end

  local state = Entity(vehicle).state

  if not state.flags or not state.server_uid then
    character.notify('Nothing interesting to search here.')
    return
  end

  -- Check if vehicle was hotwired or lockpicked
  local has_criminal_flags = exports.blrp_vehicles:HasAnyVehicleFlags(state.flags, 'FLAG_HOTWIRED', 'FLAG_LOCKPICKED')

  if not has_criminal_flags then
    character.notify('This vehicle doesn\'t appear to have been tampered with.')
    return
  end

  if searched_gloveboxes[state.server_uid] then
    character.notify('This vehicle has already been thoroughly searched.')
    return
  end

  searched_gloveboxes[state.server_uid] = true

  -- Progress bar for searching
  if not character.progressPromise('Searching Vehicle', 5, {
    controlDisables = {
      disableMovement = true,
      disableCarMovement = true,
      disableMouse = false,
      disableCombat = true,
    },
    animation = {
      animDict = 'mini@repair',
      anim = 'fixing_a_ped',
      flags = 49,
    }
  }) then
    searched_gloveboxes[state.server_uid] = nil -- Reset if cancelled
    return
  end

  -- 80% chance to find something when actively searching criminal vehicle
  if math.random(100) <= 80 then
    if exports.blrp_tablet:SearchVehicleGlovebox(character.source, vehicle) then
      character.notify('You found something hidden in the vehicle!')
    end
  else
    character.notify('You search the vehicle thoroughly but find nothing of interest.')
  end
end)

local random_models = {
  `asea`,
  `hauler`,
  `blista`,
  `dilettante`,
  `issi2`,
  `rhapsody`,
  `felon`,
  `f620`,
  `sentinel`,
  `zion`,
  `blade`,
  `buccaneer`
}

exports('HandlePlateSwitch', function(plate)
  local plate = string.gsub(string.upper(plate), '^%s*(.-)%s*$', '%1')

  local model = random_models[math.random(1, #random_models)]

  -- Check if we already have data for this plate and preserve owner info
  local existing_data = vehicle_plate_data[plate]

  vehicle_plate_data[plate] = {
    registration = plate,
    model = model,
    vehicle = model,
    colour = 111,
    scolour = 111,
  }

  -- Preserve existing owner information if it exists
  if existing_data and existing_data.ro_info_obtained then
    vehicle_plate_data[plate].firstname = existing_data.firstname
    vehicle_plate_data[plate].lastname = existing_data.lastname
    vehicle_plate_data[plate].phone = existing_data.phone
    vehicle_plate_data[plate].dlnumber = existing_data.dlnumber
    vehicle_plate_data[plate].ro_info_obtained = existing_data.ro_info_obtained
  end

  -- Preserve existing stolen timestamp, delay, and criminal activity flags
  if existing_data and existing_data.stolen_timestamp then
    vehicle_plate_data[plate].stolen_timestamp = existing_data.stolen_timestamp
    vehicle_plate_data[plate].stolen_delay = existing_data.stolen_delay
    vehicle_plate_data[plate].criminal_activity = existing_data.criminal_activity
  end
end)
