pTablet.moveCharacterToRadioChannel = function(player, channel)
    local meCharacter = exports.blrp_core:character(source)
    local targetCharacter = exports.blrp_core:character(player)

    if meCharacter.hasGroup('LEO') and targetCharacter.hasGroup('LEO') then
        local message = 'Dispatch is asking you to join ch.' .. tostring(channel)

        TriggerEvent('blrp_radio:server:tryJoinChannel', player, tonumber(channel))
        --if targetCharacter.request(message, 30) then
        --    TriggerEvent('blrp_radio:server:tryJoinChannel', player, tonumber(channel))
        --end
    end
end
