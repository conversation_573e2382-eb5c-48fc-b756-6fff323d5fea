-- Obviously not to be called from client
pTablet.loadTabletData = function(name, endpoint, callback)
    Citizen.CreateThread(function()
        local token = name .. '_has_loaded'

        if not GlobalState[token] or GlobalState.is_dev then
            print('[pTablet.loadTabletData] loading', endpoint, 'data for the first time')

            GlobalState[token] = true

            local data = doGetRequest(endpoint)

            callback(data)
        else
            print('[pTablet.loadTabletData] ignored', endpoint, 'from loading because already loaded', '[', name, ']')
        end
    end)
end
