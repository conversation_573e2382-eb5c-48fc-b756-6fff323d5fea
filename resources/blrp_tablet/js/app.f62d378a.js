(function(e){function t(t){for(var i,o,r=t[0],c=t[1],u=t[2],l=0,d=[];l<r.length;l++)o=r[l],Object.prototype.hasOwnProperty.call(a,o)&&a[o]&&d.push(a[o][0]),a[o]=0;for(i in c)Object.prototype.hasOwnProperty.call(c,i)&&(e[i]=c[i]);f&&f(t);while(d.length)d.shift()();return s.push.apply(s,u||[]),n()}function n(){for(var e,t=0;t<s.length;t++){for(var n=s[t],i=!0,o=1;o<n.length;o++){var r=n[o];0!==a[r]&&(i=!1)}i&&(s.splice(t--,1),e=c(c.s=n[0]))}return e}var i={},o={app:0},a={app:0},s=[];function r(e){return c.p+"js/"+({}[e]||e)+"."+{"chunk-0a48ba6c":"7cb1b0aa","chunk-0f714df1":"190ab5b5","chunk-2d0a4b7b":"43369171","chunk-2d0b3289":"4ab38ae9","chunk-2d0b9d35":"5f8a574b","chunk-49478d14":"9ebc4bb8","chunk-511de986":"1fd9d340","chunk-51781782":"1b2e00a0","chunk-3248a8f2":"626beaf5","chunk-03163d22":"7b52ec66","chunk-3c1ce8ae":"607193bb","chunk-774befdb":"35a98b7c","chunk-1973a081":"74dae70a","chunk-63f29872":"a4ebcc97","chunk-61a710c0":"80f7f21a","chunk-f3272f24":"f016382b","chunk-f505351e":"25ba6d9e"}[e]+".js"}function c(t){if(i[t])return i[t].exports;var n=i[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,c),n.l=!0,n.exports}c.e=function(e){var t=[],n={"chunk-0a48ba6c":1,"chunk-0f714df1":1,"chunk-49478d14":1,"chunk-511de986":1,"chunk-3248a8f2":1,"chunk-03163d22":1,"chunk-3c1ce8ae":1,"chunk-774befdb":1,"chunk-1973a081":1,"chunk-63f29872":1,"chunk-61a710c0":1,"chunk-f3272f24":1,"chunk-f505351e":1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=new Promise((function(t,n){for(var i="css/"+({}[e]||e)+"."+{"chunk-0a48ba6c":"bf586744","chunk-0f714df1":"de8ddfd5","chunk-2d0a4b7b":"31d6cfe0","chunk-2d0b3289":"31d6cfe0","chunk-2d0b9d35":"31d6cfe0","chunk-49478d14":"de8ddfd5","chunk-511de986":"fa021f08","chunk-51781782":"31d6cfe0","chunk-3248a8f2":"57da4906","chunk-03163d22":"c324562b","chunk-3c1ce8ae":"351c0259","chunk-774befdb":"6b5cb5d2","chunk-1973a081":"1884a403","chunk-63f29872":"8afd3e92","chunk-61a710c0":"cf9f3a8c","chunk-f3272f24":"3674c183","chunk-f505351e":"bf586744"}[e]+".css",a=c.p+i,s=document.getElementsByTagName("link"),r=0;r<s.length;r++){var u=s[r],l=u.getAttribute("data-href")||u.getAttribute("href");if("stylesheet"===u.rel&&(l===i||l===a))return t()}var d=document.getElementsByTagName("style");for(r=0;r<d.length;r++){u=d[r],l=u.getAttribute("data-href");if(l===i||l===a)return t()}var f=document.createElement("link");f.rel="stylesheet",f.type="text/css",f.onload=t,f.onerror=function(t){var i=t&&t.target&&t.target.src||a,s=new Error("Loading CSS chunk "+e+" failed.\n("+i+")");s.code="CSS_CHUNK_LOAD_FAILED",s.request=i,delete o[e],f.parentNode.removeChild(f),n(s)},f.href=a;var h=document.getElementsByTagName("head")[0];h.appendChild(f)})).then((function(){o[e]=0})));var i=a[e];if(0!==i)if(i)t.push(i[2]);else{var s=new Promise((function(t,n){i=a[e]=[t,n]}));t.push(i[2]=s);var u,l=document.createElement("script");l.charset="utf-8",l.timeout=120,c.nc&&l.setAttribute("nonce",c.nc),l.src=r(e);var d=new Error;u=function(t){l.onerror=l.onload=null,clearTimeout(f);var n=a[e];if(0!==n){if(n){var i=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;d.message="Loading chunk "+e+" failed.\n("+i+": "+o+")",d.name="ChunkLoadError",d.type=i,d.request=o,n[1](d)}a[e]=void 0}};var f=setTimeout((function(){u({type:"timeout",target:l})}),12e4);l.onerror=l.onload=u,document.head.appendChild(l)}return Promise.all(t)},c.m=e,c.c=i,c.d=function(e,t,n){c.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},c.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.t=function(e,t){if(1&t&&(e=c(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(c.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)c.d(n,i,function(t){return e[t]}.bind(null,i));return n},c.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return c.d(t,"a",t),t},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},c.p="/",c.oe=function(e){throw console.error(e),e};var u=window["webpackJsonp"]=window["webpackJsonp"]||[],l=u.push.bind(u);u.push=t,u=u.slice();for(var d=0;d<u.length;d++)t(u[d]);var f=l;s.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"05f2":function(e,t,n){},"121d":function(e,t,n){"use strict";n.r(t),n.d(t,"state",(function(){return s})),n.d(t,"getters",(function(){return r})),n.d(t,"mutations",(function(){return c})),n.d(t,"actions",(function(){return u}));n("96cf");var i=n("1da1"),o=n("ade3"),a=(n("1e9f"),n("9fb0")),s={loading:!0},r={loading:function(e){return e.loading}},c=Object(o["a"])({},a["d"],(function(e,t){var n=t.loading;e.loading=n})),u={setLoading:function(e,t){return Object(i["a"])(regeneratorRuntime.mark((function n(){var i;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.commit,i(a["d"],{loading:t});case 2:case"end":return n.stop()}}),n)})))()}}},"20b2":function(e,t,n){},"2d6b":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("b-row",{staticClass:"p-top"},[n("b-col",{attrs:{md:"4"}},[n("div",{staticClass:"notes-sidebar"},[e.createCooldown?n("button",{staticClass:"btn btn-outline-success btn-block",staticStyle:{"margin-bottom":"10px"},attrs:{disabled:""}},[e._v(" Create on Cooldown ")]):e._e(),e.createCooldown?e._e():n("button",{staticClass:"btn btn-outline-success btn-block",staticStyle:{"margin-bottom":"10px"},on:{click:e.createNote}},[n("i",{staticClass:"far fa-plus-square"}),e._v(" New Note ")]),e._l(e.notes,(function(t){return n("div",{staticClass:"note-item",class:{active:e.selected&&e.selected.id===t.id},on:{click:function(n){e.selected=t}}},[n("span",{staticClass:"note-title"},[e.selected&&e.selected.id===t.id?n("span",{staticClass:" text-primary"},[e._v(e._s(t.title))]):n("span",[e._v(e._s(t.title))])])])}))],2)]),e.selected?n("b-col",[n("b-row",[n("b-col",[n("button",{staticClass:"btn btn-sm btn-outline-danger float-right",staticStyle:{"margin-right":"30px"},on:{click:e.deleteNote}},[n("i",{staticClass:"far fa-trash-alt"}),e._v(" Delete ")]),n("button",{directives:[{name:"promise-btn",rawName:"v-promise-btn"}],staticClass:"btn btn-sm btn-outline-success float-right",staticStyle:{"margin-right":"30px"},on:{click:e.saveNote}},[n("i",{staticClass:"fas fa-cloud-upload-alt"}),e._v(" Save ")]),e.isAdmin?n("button",{directives:[{name:"promise-btn",rawName:"v-promise-btn"}],staticClass:"btn btn-sm btn-outline-danger float-right",staticStyle:{"margin-right":"30px"},on:{click:e.publishNote}},[n("i",{staticClass:"fas fa-cloud-upload-alt"}),e._v(" Publish ")]):e._e(),e.printerCooldown?n("button",{staticClass:"btn btn-sm btn-outline-primary float-right",staticStyle:{"margin-right":"30px"},attrs:{disabled:""}},[n("i",{staticClass:"fas fa-print"}),e._v(" Printer on Cooldown ")]):e._e(),e.printerCooldown?e._e():n("button",{staticClass:"btn btn-sm btn-outline-primary float-right",staticStyle:{"margin-right":"30px"},on:{click:function(t){return e.printPaper(e.selected)}}},[n("i",{staticClass:"fas fa-print"}),e._v(" Print Paper ")])])],1),n("b-row",[n("b-col",[n("b-form-group",{attrs:{label:"Note Title"}},[n("b-input",{model:{value:e.selected.title,callback:function(t){e.$set(e.selected,"title",t)},expression:"selected.title"}})],1),n("b-form-group",{attrs:{label:""}},[n("b-textarea",{attrs:{rows:"20"},model:{value:e.selected.contents,callback:function(t){e.$set(e.selected,"contents",t)},expression:"selected.contents"}})],1)],1)],1)],1):e._e()],1)},o=[],a=(n("d3b7"),n("4795"),n("2f62")),s={name:"CloudNotes",data:function(){return{selected:null,form:{},printerCooldown:!1,createCooldown:!1,notes:[]}},mounted:function(){this.index()},methods:{index:function(){var e=this;this.axios.get("/cloud/notes?registration=".concat(this.user.registration)).then((function(t){e.notes=t.data}))},createNote:function(){var e=this;this.createCooldown=!0,setTimeout((function(){e.createCooldown=!1}),5e3),this.axios.post("/cloud/create-note",{registration:this.user.registration}).then((function(t){e.index()}))},saveNote:function(){var e=this;return this.axios.post("/cloud/notes",this.selected).then((function(t){e.index()}))},publishNote:function(){var e=this;return this.axios.post("/news/submit",{title:this.selected.title,note_id:this.selected.id}).then((function(t){e.index(),fetch("https://blrp_tablet/publishPaper",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify({id:e.selected.id,title:e.selected.title,content:e.selected.contents})}).then((function(e){return e.json()})).then((function(e){return e}))}))},printPaper:function(e){fetch("https://blrp_tablet/printPaper",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify({id:e.id,title:e.title,content:e.contents})}).then((function(e){return e.json()})).then((function(e){return e}))},printNote:function(){var e=this;this.printerCooldown=!0,setTimeout((function(){e.printerCooldown=!1}),6e3),this.axios.post("https://blrp_tablet/printNote",{contents:this.selected.contents})},deleteNote:function(){var e=this;this.axios.post("/cloud/remove-note",this.selected).then((function(t){e.selected=null,e.index()}))}},computed:Object(a["b"])({user:"auth/user",isAdmin:"auth/isAdmin"})},r=s,c=(n("7ad5"),n("2877")),u=Object(c["a"])(r,i,o,!1,null,null,null);t["a"]=u.exports},4207:function(e,t,n){"use strict";var i=n("05f2"),o=n.n(i);o.a},"56d7":function(e,t,n){"use strict";n.r(t);n("b0c0"),n("d3b7"),n("e260"),n("e6cf"),n("cca6"),n("a79d");var i=n("2b0e"),o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"scroll"},[e.user?n("b-container",{directives:[{name:"drag",rawName:"v-drag",value:{handle:"#signal-bar"},expression:"{ handle: '#signal-bar'}"},{name:"show",rawName:"v-show",value:e.isShowing&&e.user,expression:"isShowing && user"}],staticClass:"tablet",style:"background-image: url('"+e.user.settings.background_url+"');",attrs:{id:"tablet"}},[n("tablet-signal-bar"),e.user?n("div",{staticClass:"application-logged-in"},[n("div",{directives:[{name:"show",rawName:"v-show",value:e.loading,expression:"loading"}]},[n("div",{staticClass:"application-logged-out"},[n("center",[e._v("t "),n("img",{attrs:{src:"https://badlandsrp.com/uploads/monthly_2020_02/hello.png.bd09a2b839d07f92ad76fdd9762c4395.png",height:"128"}}),n("br"),e._v(" Launching Application... ")])],1)]),n("div",{directives:[{name:"show",rawName:"v-show",value:!e.loading,expression:"!loading"}]},[n("TabletQuickMenu",{attrs:{tool:e.tool},on:{change:function(t){return e.toggleMenuItem(t)}}}),n("ApplicationScreen",{directives:[{name:"show",rawName:"v-show",value:"menu"===e.tool,expression:"tool === 'menu'"}],on:{"tool-switch":function(t){return e.toggleMenuItem(t)},switch:function(t){return e.menuSwitch(t)}}}),n("div",{staticClass:"view-container"},[e.tool?e._e():n("router-view"),"notes"===e.tool?n("CloudNotes"):e._e()],1)],1)]):e._e()],1):e._e(),!e.user&&e.isShowing?n("b-container",{staticClass:"tablet logged-out",attrs:{id:"tablet-logged-out"}},[n("div",{staticClass:"application-logged-out"},[n("r-box",[n("center",[n("img",{attrs:{src:"https://badlandsrp.com/uploads/monthly_2020_02/hello.png.bd09a2b839d07f92ad76fdd9762c4395.png",height:"128"}})]),n("center",[n("span",{staticClass:"text text-muted"},[e._v("Connecting to PickleNet")])]),e.debug?n("span",{staticClass:"text-secondary"},[e._v(" Having troubles connecting - having headaches? "),n("br"),n("br"),e._v(" Dev mode is on, enter a custom token below to log in "),n("b-input",{model:{value:e.testToken,callback:function(t){e.testToken=t},expression:"testToken"}}),n("br"),n("b-button",{attrs:{variant:"primary"},on:{click:function(t){return e.registerSecureCitizen(e.testToken)}}},[e._v("Set Token")])],1):e._e()],1)],1)]):e._e()],1)},a=[],s=(n("caad"),n("2532"),n("782e"),n("2f62")),r=n("4583"),c=n("9e0a"),u=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("b-container",{staticClass:"notification-area"},[e.notification?n("div",{key:e.notifications.hash},[n("div",{staticClass:"notification test"},[n("b-alert",{attrs:{show:e.dismissCountDown,dismissible:"",variant:"danger"},on:{dismissed:e.countDownFinished,"dismiss-count-down":e.countDownChanged}},[n("span",[e._v(" "+e._s(e.notification.message)+" ")])]),e.dismissCountDown?n("b-progress",{attrs:{variant:"primary",max:e.notification.seconds,value:e.dismissCountDown,height:"4px"}}):e._e()],1)]):e._e()])},l=[],d=(n("c0b6"),{name:"Notifications",data:function(){return{dismissCountDown:0,notification:null,notifications:[]}},created:function(){var e=this,t=pusher.subscribe("notification");t.bind("notification",(function(t){"personal"===t.type&&t.citizen_id===e.user.id&&e.showAlert(t),"faction"===t.type&&"police"===t.faction_name&&e.user.police&&e.showAlert(t)}))},methods:{countDownFinished:function(){this.dismissCountDown=0,this.notifications.length>0&&(this.notification=this.notifications.pop())},countDownChanged:function(e){this.dismissCountDown=e},showAlert:function(e){this.dismissCountDown=e.seconds,this.notification?this.notifications.push(e):this.notification=e}},computed:Object(s["b"])({user:"auth/user"})}),f=d,h=(n("9cd4"),n("2877")),p=Object(h["a"])(f,u,l,!1,null,null,null),m=(p.exports,n("2d6b")),b=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"signal-bar",attrs:{id:"signal-bar"}},[n("div",{staticClass:"icon-area"},[n("i",{staticClass:"fas fa-battery-three-quarters",staticStyle:{color:"lawngreen"}}),n("i",{staticClass:"fas fa-satellite-dish",staticStyle:{color:"lightblue","padding-left":"15px"}}),n("i",{staticClass:"fas fa-envelope-open-text",staticStyle:{color:"gray","padding-left":"15px"}}),n("i",{staticClass:"fab fa-snapchat-ghost",staticStyle:{color:"yellow","padding-left":"15px"}}),n("i",{staticClass:"fab fa-facebook-messenger",staticStyle:{color:"deepskyblue","padding-left":"15px"}}),n("span",{staticStyle:{color:"white","padding-left":"780px"}},[e._v("BAD LTE")]),n("i",{class:"fas fa-signal-"+e.signals,staticStyle:{color:"deepskyblue","padding-left":"15px","margin-right":"15px"}}),n("i",{directives:[{name:"show",rawName:"v-show",value:e.spinning,expression:"spinning"}],staticClass:"fas fa-spinner fa-spin",staticStyle:{color:"white"}}),n("i",{directives:[{name:"show",rawName:"v-show",value:e.loading,expression:"loading"}],staticClass:"fas fa-spinner fa-spin",staticStyle:{color:"yellow"}})])])},g=[],v=(n("4795"),{name:"TabletSignalBar",props:[],data:function(){return{spinning:!1,signals:4}},mounted:function(){var e=this;window.$events.$on("axios::request",(function(t){e.spinning=!0,setTimeout((function(){e.spinning=!1}),Math.floor(1e3*Math.random()+200))}))},destroyed:function(){window.$events.$off("axios::request")},methods:{},computed:Object(s["b"])({user:"auth/user",loading:"loading/loading"})}),w=v,k=(n("8247"),Object(h["a"])(w,b,g,!1,null,null,null)),C=k.exports,_=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("b-row",[n("b-col",[n("div",{staticClass:"menu-show-toggle",attrs:{id:"nav"}},[e.user.settings.avatar_url?n("img",{staticClass:"float-right",attrs:{height:"48",alt:"avatar",src:e.user.settings.avatar_url}}):e._e(),n("div",{staticClass:"float-right signed-in-as"},[n("div",{staticStyle:{"font-size":"18px"}},[e._v(e._s(e.user.name))]),"police"===e.user.active_job?n("div",{staticClass:"text-muted"},[e._v(" Los Santos Police ")]):e._e()]),"menu"!==e.tool?n("a",{staticClass:"float-left menu-item-menu"},[n("i",{staticClass:"fas fa-bars fa-2x",on:{click:function(t){return e.$emit("change","menu")}}})]):"menu"===e.tool?n("a",{staticClass:"float-left"},[n("i",{staticClass:"fas fa-bars fa-2x text-primary",on:{click:function(t){return e.$emit("change","menu")}}})]):e._e(),"chat"!==e.tool?n("a",{staticClass:"float-left menu-item-chat",class:{pendingNotification:e.pendingNotification}},[n("i",{staticClass:"fal fa-comment-alt-lines fa-2x",on:{click:function(t){return e.change("chat")}}})]):"chat"===e.tool?n("a",{staticClass:"float-left"},[n("i",{staticClass:"fal fa-comment-alt-lines fa-2x text-primary",on:{click:function(t){return e.change("chat")}}})]):e._e(),"notes"!==e.tool?n("a",{staticClass:"float-left menu-item-notes"},[n("i",{staticClass:"fal fa-clipboard fa-2x",on:{click:function(t){return e.$emit("change","notes")}}})]):"notes"===e.tool?n("a",{staticClass:"float-left"},[n("i",{staticClass:"fal fa-clipboard fa-2x text-primary",on:{click:function(t){return e.$emit("change","notes")}}})]):e._e(),e.tool&&"menu"!==e.tool?n("a",{staticClass:"float-left menu-item-notes"},[n("i",{staticClass:"far fa-times fa-2x text-danger",on:{click:function(t){return e.$emit("change","exit")}}})]):e._e()])])],1)},y=[],x={name:"TabletQuickMenu",props:["tool"],data:function(){return{pendingNotification:!1}},mounted:function(){var e=this;window.$events.$on("chat::pending::notification",(function(t){e.pendingNotification=!0}))},destroyed:function(){window.$events.$off("chat::pending::notification")},methods:{change:function(e){this.$emit("change","chat"),"chat"===e&&(this.pendingNotification=!1)}},computed:Object(s["b"])({user:"auth/user"})},S=x,j=(n("60d8"),Object(h["a"])(S,_,y,!1,null,null,null)),T=j.exports,$=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"application-screen"},[n("b-row",[n("b-col",[n("b-row",{staticClass:"menu-block-area"},e._l(e.applications,(function(t){return t.is_visible?n("b-col",{staticClass:"menu-block-item",attrs:{md:"5"},on:{click:function(n){return e.applicationClick(t)}}},[n("b-row",[n("b-col",{attrs:{md:"3"}},[n("center",[n("img",{attrs:{src:t.picture,alt:"ahg",height:"48"}})])],1),n("b-col",[n("span",[e._v(" "+e._s(t.long)+" ")]),n("br"),n("span",{staticClass:"text-secondary",staticStyle:{"font-size":"12px"}},[e._v(" "+e._s(t.description)+" ")])])],1)],1):e._e()})),1)],1)],1)],1)},O=[],P={name:"ApplicationScreen",props:[],data:function(){return{applications:[{name:"LSPD",long:"San Andreas Police CAD",route:"police",icon:null,picture:"img/police.png",description:"Police records environment for the State of San Andreas",requires:"isPoliceFaction"},{name:"DOJ",long:"Department of Justice Records",route:"cases",icon:null,picture:"img/court.png",description:"The Official Department of Justice",requires:"isJusticeFaction"},{name:"EMS",long:"Los Santos Fire Records",route:"ems",icon:null,picture:"img/ems.png",description:"The Official Los Santos Fire Department",requires:"isEmsFaction"},{name:"Information",long:"Information & Help",route:"information",icon:null,picture:"img/info.png",description:"Information & Support for the badlands community"},{name:"",long:"Bad® Notes",route:"notes",icon:null,picture:"img/notes.png",description:"Create & Manage notes stored on the bad cloud",type:"tool"},{name:"Games",long:"Game Center",route:"games",icon:null,picture:"img/games.png",description:"Bored? Play some games"},{name:"YellowPages",long:"Yellow Pages",route:"yellow-pages",icon:null,picture:"img/yellow-pages.png",description:"Advertise your business"},{name:"Housing",long:"Los Santos Housing",route:"housing",icon:null,picture:"img/house.png",description:"View and rent houses"},{name:"Documents",long:"My Documents",route:"documents",icon:null,picture:"img/documents.png",description:"View signed or given documents"},{name:"News",long:"News",route:"news",icon:null,picture:"img/news.png",description:"Published News / Articles"},{name:"Settings",long:"Tablet Settings",route:"settings",icon:null,picture:"img/settings.png",description:"Tablet settings and usage support / help"},{name:"Exit",long:"Lock Tablet",route:"lock",icon:null,picture:"img/lock.png",description:"Exit the tablet"}]}},mounted:function(){this.init()},methods:{init:function(){var e=this;this.showApplications(),window.$events.$on("user::loaded",(function(){e.showApplications()}))},showApplications:function(){for(var e in this.applications){var t=this.applications[e];i["default"].set(this.applications[e],"is_visible",!0),t.requires&&(this[t.requires]||i["default"].set(this.applications[e],"is_visible",!1))}},applicationClick:function(e){if("tool"===e.type)return this.$emit("tool-switch",e.route);this.$emit("switch",e.route)}},computed:Object(s["b"])({user:"auth/user",isPoliceFaction:"auth/isPoliceFaction",isJusticeFaction:"auth/isJusticeFaction",isEmsFaction:"auth/isEmsFaction",isAdmin:"auth/isJusticeFaction"})},N=P,E=Object(h["a"])(N,$,O,!1,null,null,null),A=E.exports,D=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div")},L=[],R={name:"Live",data:function(){return{civChannel:null}},created:function(){this.onlinePlayers(),this.citizenUpdated()},destroyed:function(){presence.unbind("pusher:subscription_succeeded"),presence.unbind("pusher:member_added"),presence.unbind("pusher:member_removed"),presence.unsubscribe(),this.civChannel.unbind("citizen-updated"),this.civChannel.unsubscribe()},methods:{onlinePlayers:function(){var e=this;window.presence=pusher.subscribe("presence-online"),presence.bind("pusher:subscription_succeeded",(function(t){t.each((function(t){e.$store.dispatch("online/addPlayer",t.info)}))})),presence.bind("pusher:member_added",(function(t){e.$store.dispatch("online/addPlayer",t.info)})),presence.bind("pusher:member_removed",(function(t){e.$store.dispatch("online/removePlayer",t.info)}))},citizenUpdated:function(){var e=this;this.civChannel=pusher.subscribe("citizen.".concat(this.user.token)),this.civChannel.bind("citizen-updated",(function(t){e.$store.dispatch("auth/setUser",t.citizen).then((function(){e.loaded=!0}))}))}},watch:{user:function(){}},computed:Object(s["b"])({user:"auth/user",online:"online/all"})},F=R,U=Object(h["a"])(F,D,L,!1,null,null,null),z=U.exports,M={components:{Live:z,ApplicationScreen:A,TabletQuickMenu:T,TabletSignalBar:C,CloudNotes:m["a"],RBox:c["a"],FulfillingBouncingCircleSpinner:r["a"]},data:function(){return{loaded:!1,tool:null,chatShowing:!1,isShowing:!1,activeToken:null,debug:!1}},created:function(){var e=this;this.tool="menu",this.createListeners(),document.onkeydown=function(t){if(!e.isShowing)return!1;t=t||window.event,27===t.keyCode&&e.exitTablet()},console.log(navigator.userAgent),navigator.userAgent.includes("Chrome/101.0.4951.54")&&(this.registerSecureCitizen("DJzSkV7iB8KY4cGCMKe3QtRjkrmJu9q5"),this.debug=!0,this.isShowing=!0,document.body.classList.add("dev-mode"))},methods:{registerSecureCitizen:function(e){var t=this;if(!e)return!1;this.loaded&&this.activeToken===e?this.axios.get("/secure/me").then((function(e){t.$store.dispatch("auth/setUser",e.data).then((function(){window.$events.$emit("user::loaded")}))})):this.$store.dispatch("auth/setToken",e).then((function(){Intl.DateTimeFormat().resolvedOptions().timeZone;t.axios.get("/secure/me").then((function(e){t.$store.dispatch("auth/setUser",e.data).then((function(){window.$events.$emit("user::loaded"),t.loaded=!0}))}))}))},createListeners:function(){var e=this;window.$events.$on("nui::tablet::show",(function(t){e.openTablet(t)})),window.$events.$on("nui::tablet::ghost-show",(function(t){e.openTablet(t,!0)})),window.$events.$on("nui::tablet::hide",(function(){e.exitTablet()})),window.$events.$on("friends::chat::open",(function(){e.toggleMenuItem("chat")}))},exitTablet:function(){!1!==this.isShowing&&(this.isShowing=!1,this.axios.post("https://blrp_tablet/escape"),this.playSound("/audio/iphone-unlock.mp3"))},openTablet:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];n?this.isShowing=!1:(this.playSound("/audio/iphone-unlock.mp3"),this.isShowing=!0),e&&this.activeToken!==e?(this.activeToken=e,this.registerSecureCitizen(e)):this.activeToken&&this.axios.get("/secure/me").then((function(e){t.$store.dispatch("auth/setUser",e.data).then((function(){window.$events.$emit("user::loaded")}))}))},menuSwitch:function(e){if(this.playSound("/audio/fast-beep.mp3"),"lock"===e)return this.exitTablet();this.$route.name!==e&&this.$router.push({name:e}),this.tool=null},playSound:function(e){var t=new Audio(e);t.currentTime=.05,t.volume=.2,t.play()},toggleMenuItem:function(e){return"exit"===e||this.tool===e?this.tool=null:void(this.tool=e)}},computed:Object(s["b"])({user:"auth/user",loading:"loading/loading"})},J=M,q=(n("5c0b"),Object(h["a"])(J,o,a,!1,null,null,null)),I=q.exports,B=n("8c4f");i["default"].use(B["a"]);var V=[{path:"/",name:"home",component:function(){return n.e("chunk-49478d14").then(n.bind(null,"bb51"))}},{path:"/cases",name:"cases",component:function(){return Promise.all([n.e("chunk-51781782"),n.e("chunk-3248a8f2"),n.e("chunk-1973a081")]).then(n.bind(null,"25d9"))}},{path:"/police",name:"police",component:function(){return Promise.all([n.e("chunk-51781782"),n.e("chunk-3248a8f2"),n.e("chunk-03163d22"),n.e("chunk-3c1ce8ae")]).then(n.bind(null,"ea53"))}},{path:"/ems",name:"ems",component:function(){return Promise.all([n.e("chunk-51781782"),n.e("chunk-3248a8f2"),n.e("chunk-03163d22"),n.e("chunk-774befdb")]).then(n.bind(null,"630e"))}},{path:"/settings",name:"settings",component:function(){return n.e("chunk-2d0b3289").then(n.bind(null,"26d3"))}},{path:"/notes",name:"notes",component:function(){return n.e("chunk-2d0a4b7b").then(n.bind(null,"0841"))}},{path:"/information",name:"information",component:function(){return n.e("chunk-0f714df1").then(n.bind(null,"5798"))}},{path:"/friends",name:"friends",component:function(){return n.e("chunk-f3272f24").then(n.bind(null,"6c40"))}},{path:"/yellow-pages",name:"yellow-pages",component:function(){return n.e("chunk-61a710c0").then(n.bind(null,"ffeb"))}},{path:"/admin",name:"admin",component:function(){return n.e("chunk-2d0b9d35").then(n.bind(null,"3530"))}},{path:"/games",name:"games",component:function(){return n.e("chunk-511de986").then(n.bind(null,"a2e9"))}},{path:"/documents",name:"documents",component:function(){return n.e("chunk-0a48ba6c").then(n.bind(null,"b368"))}},{path:"/news",name:"news",component:function(){return n.e("chunk-f505351e").then(n.bind(null,"a2f9"))}},{path:"/housing",name:"housing",component:function(){return Promise.all([n.e("chunk-51781782"),n.e("chunk-63f29872")]).then(n.bind(null,"0643"))}}],H=new B["a"]({routes:V}),G=H,K=(n("d81d"),n("13d5"),n("ac1f"),n("5319"),n("ddb0"),n("ade3")),Q=n("5530"),Y=n("3835");i["default"].use(s["a"]);var Z=n("6c17"),W=Z.keys().map((function(e){return[e.replace(/(^.\/)|(\.js$)/g,""),Z(e)]})).reduce((function(e,t){var n=Object(Y["a"])(t,2),i=n[0],o=n[1];return void 0===o.namespaced&&(o.namespaced=!0),Object(Q["a"])({},e,Object(K["a"])({},i,o))}),{}),X=new s["a"].Store({modules:W}),ee=(n("1e9f"),n("498a")),te=n("8c60"),ne=n("cca8"),ie=n("b1fc"),oe=n("7049"),ae=n("1073"),se=n("700c"),re=n("cbd0"),ce=n("521d"),ue=n("a166"),le=n("f7ca"),de=n("44d4"),fe=n("3d31"),he=n("40aa"),pe=(n("f9e3"),n("2dd8"),n("73ec"),new i["default"]),me=(n("99af"),n("bc3a")),be=n.n(me);window.apiEndpoint||(console.log("endpoint, not set. Setting to dev"),window.apiEndpoint="http://dev-api.blrp.net"),be.a.interceptors.request.use((function(e){if("post"===e.method&&e.url.includes("chats/reply")){var t=new Audio("/audio/iphone-text-sent.mp3");t.volume=.2,t.play()}return e.url.includes("blrp-rizz")||e.url.includes("blrp-rizz")||e.url.includes("https")||e.url.includes("blrp_tablet")||(window.apiEndpoint?e.url="".concat(window.apiEndpoint).concat(e.url):e.url="http://dev-api.blrp.net".concat(e.url)),e.params={token:window.localStorage.getItem("blrp_token")},e.url.includes("me")&&(e.params.timezone=Intl.DateTimeFormat().resolvedOptions().timeZone),window.$events.$emit("axios::request",e),e}),(function(e){return Promise.reject(e)})),be.a.interceptors.response.use((function(e){return e.data.error,e}),(function(e){return Promise.reject(e)}));var ge=be.a,ve=n("a7fe"),we=n.n(ve),ke=n("3c29"),Ce=(n("9c9f"),n("123d")),_e=n.n(Ce);i["default"].use(ee["a"]),i["default"].use(te["a"]),i["default"].use(ne["a"]),i["default"].use(ie["a"]),i["default"].use(oe["a"]),i["default"].use(ae["a"]),i["default"].use(se["a"]),i["default"].use(re["a"]),i["default"].use(ce["a"]),i["default"].use(ue["a"]),i["default"].use(le["a"]),i["default"].use(de["a"]),i["default"].use(fe["a"]),i["default"].use(he["a"]),window.$events=pe,i["default"].use(we.a,ge);var ye=n("c48e");i["default"].use(ye),i["default"].config.productionTip=!1,window.addEventListener("message",(function(e){if(!e.data)return!1;"tablet:toggle"===e.data.type&&(e.data.endpoint&&(window.apiEndpoint=e.data.endpoint),!0===e.data.enable?!0===e.data.ghost?window.$events.$emit("nui::tablet::ghost-show",e.data.token):window.$events.$emit("nui::tablet::show",e.data.token):!1===e.data.enable&&window.$events.$emit("nui::tablet::hide")),"tablet:message"===e.data.type&&window.$events.$emit(e.data.name,e.data.packet)})),i["default"].use(ke["a"]),i["default"].use(_e.a),window.api=function(e,t){var n=JSON.stringify({event_name:e,packet:t});fetch("https://blrp_tablet/handleCallback",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:n}).then((function(e){console.log(e)}))},G.beforeEach((function(e,t,n){X.dispatch("loading/setLoading",!0),n()})),G.afterEach((function(e,t){X.dispatch("loading/setLoading",!1)})),new i["default"]({router:G,store:X,render:function(e){return e(I)}}).$mount("#app")},"5c0b":function(e,t,n){"use strict";var i=n("9c0c"),o=n.n(i);o.a},"60d8":function(e,t,n){"use strict";var i=n("20b2"),o=n.n(i);o.a},"6c17":function(e,t,n){var i={"./auth.js":"c7d4","./loading.js":"121d","./online.js":"d0e4"};function o(e){var t=a(e);return n(t)}function a(e){if(!n.o(i,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return i[e]}o.keys=function(){return Object.keys(i)},o.resolve=a,e.exports=o,o.id="6c17"},"73ec":function(e,t,n){},"7ad5":function(e,t,n){"use strict";var i=n("7f5e"),o=n.n(i);o.a},"7f5e":function(e,t,n){},8247:function(e,t,n){"use strict";var i=n("a855"),o=n.n(i);o.a},"9c0c":function(e,t,n){},"9cd4":function(e,t,n){"use strict";var i=n("a420"),o=n.n(i);o.a},"9e0a":function(e,t,n){"use strict";var i=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"r-box"},[e._t("default")],2)},o=[],a={name:"r-box"},s=a,r=(n("4207"),n("2877")),c=Object(r["a"])(s,i,o,!1,null,null,null);t["a"]=c.exports},"9fb0":function(e,t,n){"use strict";n.d(t,"f",(function(){return i})),n.d(t,"e",(function(){return o})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return s})),n.d(t,"b",(function(){return r})),n.d(t,"d",(function(){return c}));var i="UPDATE_USER",o="SET_TOKEN",a="SET_CURRENT_USERS",s="ADD_CURRENT_USERS",r="REMOVE_CURRENT_USERS",c="SET_LOADING"},a420:function(e,t,n){},a855:function(e,t,n){},c7d4:function(e,t,n){"use strict";n.r(t),n.d(t,"state",(function(){return r})),n.d(t,"getters",(function(){return c})),n.d(t,"mutations",(function(){return u})),n.d(t,"actions",(function(){return l}));n("96cf");var i,o=n("1da1"),a=n("ade3"),s=(n("1e9f"),n("9fb0")),r={user:null,online:null},c={user:function(e){return e.user},online:function(e){return e.online},isPoliceFaction:function(e){if(e.user){if(1===e.user.police)return!0;if(e.user.dojLevel>1)return!0;if(e.user.emergencyLevel>=4)return!0}return!1},isJusticeFaction:function(e){return!(!e.user||1!==e.user.doj)},isTrustedJusticeFaction:function(e){return!!(e.user&&1===e.user.doj&&e.user.dojLevel>=2)},isEmsFaction:function(e){return!(!e.user||1!==e.user.emergency)},isChiefParamedic:function(e){return!(!e.user||4!==e.user.emergencyLevel)},isAbleViewTicketingTool:function(e){return!!(e.user&&e.user.policeLevel>=2)},isPoliceLt:function(e){return!!(e.user&&e.user.policeLevel>=5)},isAbleDeleteCatRecord:function(e){return!!(e.user&&e.user.dojLevel>=4)},canViewPardonedRecords:function(e){return!!(e.user&&e.user.dojLevel>=3)},canViewDOJCases:function(e){return!!(e.user&&e.user.dojLevel>1)},isAdmin:function(e){return!(!e.user||1!==e.user.admin)},isRealtor:function(e){return!(!e.user||!e.user.active_job||"Realtor"!==e.user.active_job)}},u=(i={},Object(a["a"])(i,s["f"],(function(e,t){var n=t.user;e.user=n})),Object(a["a"])(i,s["e"],(function(e,t){var n=t.token;window.localStorage.setItem("blrp_token",n),e.token=n})),Object(a["a"])(i,s["c"],(function(e,t){var n=t.users;e.online=n})),i),l={setUser:function(e,t){return Object(o["a"])(regeneratorRuntime.mark((function n(){var i;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.commit,i(s["f"],{user:t});case 2:case"end":return n.stop()}}),n)})))()},setToken:function(e,t){return Object(o["a"])(regeneratorRuntime.mark((function n(){var i;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.commit,i(s["e"],{token:t});case 2:case"end":return n.stop()}}),n)})))()},setCurrentUsers:function(e,t){return Object(o["a"])(regeneratorRuntime.mark((function n(){var i;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.commit,i(s["c"],{users:t});case 2:case"end":return n.stop()}}),n)})))()}}},d0e4:function(e,t,n){"use strict";n.r(t),n.d(t,"state",(function(){return r})),n.d(t,"getters",(function(){return c})),n.d(t,"mutations",(function(){return u})),n.d(t,"actions",(function(){return l}));n("4de4"),n("c740"),n("a434"),n("96cf");var i,o=n("1da1"),a=n("ade3"),s=n("9fb0"),r={all:[]},c={all:function(e){return e.all},police:function(e){return e.all.filter((function(e){return 1==e.police}))}},u=(i={},Object(a["a"])(i,s["c"],(function(e,t){var n=t.users;e.all=n})),Object(a["a"])(i,s["a"],(function(e,t){var n=t.user;e.all.push(n)})),Object(a["a"])(i,s["b"],(function(e,t){var n=t.id,i=e.all.findIndex((function(e){return e.id===n}));e.state.all.splice(i,1)})),i),l={setPlayers:function(e,t){return Object(o["a"])(regeneratorRuntime.mark((function n(){var i;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.commit,i(s["c"],{users:t});case 2:case"end":return n.stop()}}),n)})))()},addPlayer:function(e,t){return Object(o["a"])(regeneratorRuntime.mark((function n(){var i;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.commit,i(s["a"],{user:t});case 2:case"end":return n.stop()}}),n)})))()},removePlayer:function(e,t){return Object(o["a"])(regeneratorRuntime.mark((function n(){var i;return regeneratorRuntime.wrap((function(n){while(1)switch(n.prev=n.next){case 0:i=e.commit,i(s["b"],{id:t});case 2:case"end":return n.stop()}}),n)})))()}}}});