(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3c1ce8ae"],{3176:function(e,t,a){"use strict";var r=a("b79b"),n=a.n(r);n.a},"3d9e":function(e,t,a){"use strict";var r=a("5558"),n=a.n(r);n.a},5558:function(e,t,a){},"66a9":function(e,t,a){"use strict";var r=a("fd51"),n=a.n(r);n.a},b47f:function(e,t,a){},b79b:function(e,t,a){},ea53:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("b-tabs",{attrs:{"no-fade":"","no-key-nav":""},model:{value:e.tabIndex,callback:function(t){e.tabIndex=t},expression:"tabIndex"}},[a("b-tab",{attrs:{title:"Dashboard",lazy:""}},[a("PoliceDashboard")],1),a("b-tab",{attrs:{title:"Records"}},[a("PoliceRecords",{ref:"records"})],1),a("b-tab",{attrs:{title:"Submit Record"}},[a("SubmitRecord")],1),a("b-tab",{attrs:{title:"Database Query"}},[a("DatabaseQuery")],1),a("b-tab",{attrs:{title:"Ticketing Aid",lazy:""}},[a("TicketingAid",{staticStyle:{"padding-top":"15px"}})],1),a("b-tab",{attrs:{title:"Evidence Locker"}},[a("EvidenceLocker",{ref:"evidence-records"})],1),a("b-tab",{attrs:{title:"Documents",lazy:""}},[a("Documents",{attrs:{faction_type:"police",personal_id:!1}})],1),a("b-tab",{attrs:{title:"External Links",lazy:""}},[a("PoliceGuides")],1)],1)},n=[],s=a("f9c4"),c=a("f0f3"),i=a("55ce"),o=a("4942"),l=a("aa7b"),d=a("e27b"),u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"30px"}},[a("h6",[e._v("Clocked In Police (Under testing)")]),e._l(e.police,(function(t){return a("b-row",{staticClass:"case-row"},[a("b-col",{attrs:{md:"1"}},[a("i",{staticClass:"fas fa-circle text-success"})]),a("b-col",{attrs:{md:"2"}},[e._v(" "+e._s(t.name)+" ")]),a("b-col",[a("span",[e._v(e._s(t.active_rank))])])],1)})),a("br"),a("br"),a("div",[a("span",{staticClass:"text-muted"},[e._v(" There are currently "),a("span",{staticClass:"text-primary"},[e._v(e._s(e.police.length))]),e._v(" officer(s) clocked in with "),a("span",{staticClass:"text-primary"},[e._v(e._s(e.all.length)+",000")]),e._v(" citizens ")])])],2)},f=[],h=a("2f62"),b={name:"PoliceLive",props:[],methods:{},computed:Object(h["b"])({all:"online/all",police:"online/police"})},p=b,m=a("2877"),v=Object(m["a"])(p,u,f,!1,null,null,null),_=v.exports,y=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("b-row",{staticClass:"p-top"},[a("b-col",{attrs:{md:"4"}},[a("b-form",{on:{submit:function(t){return t.preventDefault(),e.index("all")}}},[a("b-input",{staticStyle:{width:"330px"},attrs:{placeholder:"Search for title or author"},model:{value:e.searchTerm,callback:function(t){e.searchTerm=t},expression:"searchTerm"}})],1)],1),a("b-col",[a("button",{directives:[{name:"promise-btn",rawName:"v-promise-btn"}],staticClass:"btn btn-outline-primary btn-sm",on:{click:function(t){return e.index("all")}}},[e._v("Search")])])],1),a("div",[e._l(e.records,(function(t){return a("div",[a("PoliceRecordItem",{attrs:{r:t},on:{index:e.index}})],1)})),a("p",{staticClass:"text-muted text-center p-top"},[e._v(" Results are limited to 200 records ")])],2)],1)},D=[],w=a("a8b4"),T=a("d1f5"),C={name:"EvidenceLocker",components:{PoliceRecordsLoop:T["a"],PoliceRecordItem:w["a"]},data:function(){return{recordsTab:null,searchTerm:null,records:[]}},mounted:function(){var e=this;this.index(),window.$events.$on("police:record:submitted",(function(t){e.index()})),window.$events.$on("police:record:search",(function(t){e.searchTerm=t,e.index()}))},destroyed:function(){window.$events.$off("police:record:submitted"),window.$events.$off("police:record:search")},methods:{indexAll:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.searchTerm=null,this.index()},index:function(){var e=this;arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this.axios.get("/police/evidence-records?searchTerm=".concat(this.searchTerm)).then((function(t){e.records=t.data}))}},computed:Object(h["b"])({user:"auth/user"})},x=C,$=Object(m["a"])(x,y,D,!1,null,null,null),g=$.exports,S=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("b-row",{staticClass:"p-top"},[a("b-col",{attrs:{md:"4"}},[a("b-form",{on:{submit:function(t){return t.preventDefault(),e.index("all")}}},[a("b-input",{staticStyle:{width:"330px"},attrs:{placeholder:"Search Type, Name, ID or Signer"},model:{value:e.searchTerm,callback:function(t){e.searchTerm=t},expression:"searchTerm"}})],1)],1),a("b-col",[a("button",{directives:[{name:"promise-btn",rawName:"v-promise-btn"}],staticClass:"btn btn-outline-primary btn-sm",on:{click:function(t){return e.index("all")}}},[e._v("Search")]),a("div",{staticClass:"float-right"})])],1),a("div",{staticClass:"p-top"}),e._l(e.documents,(function(t){return a("b-row",{staticClass:"case-row"},[a("b-col",{attrs:{md:"1"}},[a("b-badge",[e._v(e._s(t.type))])],1),a("b-col",{attrs:{md:"3"}},[e._v(" "+e._s(t.name)+" ")]),a("b-col",{attrs:{md:"2"}},[e._v(" "+e._s(t.submitted_by)+" ")]),a("b-col",{attrs:{md:"2"}},[e._v(" "+e._s(t.requested_by)+" ")]),a("b-col",[e._v(" "+e._s(t.created_at)+" ")]),a("b-col",{attrs:{md:"2"}},[a("span",{staticClass:"text-primary font-weight-bold",on:{click:function(a){return e.openDocument(t.game_document_id)}}},[e._v(" View ")]),a("span",{staticClass:"text-warning font-weight-bold",staticStyle:{"margin-left":"12px"},on:{click:function(a){return e.printDocument(t.game_document_id)}}},[e._v(" Print ")])])],1)}))],2)},k=[],L=(a("d3b7"),{name:"PoliceDocuments",data:function(){return{searchTerm:null,searchType:null,documents:[]}},props:[],mounted:function(){this.index()},methods:{index:function(){var e=this,t="/documents/records?type=police&searchTerm=".concat(this.searchTerm);return this.axios.get(t).then((function(t){console.log("result is",t.data),e.documents=t.data}))},openDocument:function(e){fetch("https://blrp_tablet/openDocument",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify({id:e})}).then((function(e){return e.json()})).then((function(e){return e}))},printDocument:function(e){fetch("https://blrp_tablet/printDocument",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify({id:e})}).then((function(e){return e.json()})).then((function(e){return e}))}},computed:Object(h["b"])({})}),O=L,R=Object(m["a"])(O,S,k,!1,null,null,null),F=R.exports,P=a("da2b"),j=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("b-row",{staticClass:"p-top"},[a("b-col",{attrs:{md:"3"}},[a("b-form",{on:{submit:function(t){return t.preventDefault(),e.runSearch(t)}}},[a("b-input",{staticStyle:{width:"250px"},attrs:{id:"searchTerm",placeholder:"Search by license plate"},on:{keyup:e.validateInputs},model:{value:e.searchTerm,callback:function(t){e.searchTerm=t},expression:"searchTerm"}})],1)],1),a("b-col",{attrs:{md:"3"}},[a("b-form",{on:{submit:function(t){return t.preventDefault(),e.runSearch(t)}}},[a("b-input",{staticStyle:{width:"250px"},attrs:{id:"searchTermDL",placeholder:"Search by DL #"},on:{keyup:e.validateInputs},model:{value:e.searchTermDL,callback:function(t){e.searchTermDL=t},expression:"searchTermDL"}})],1)],1),a("b-col",{attrs:{md:"3"}},[a("b-form",{on:{submit:function(t){return t.preventDefault(),e.runSearch(t)}}},[a("b-input",{staticStyle:{width:"250px"},attrs:{id:"searchTermFirearm",placeholder:"Search firearm registration"},on:{keyup:e.validateInputs},model:{value:e.searchTermFirearm,callback:function(t){e.searchTermFirearm=t},expression:"searchTermFirearm"}})],1)],1),a("b-col",[a("button",{directives:[{name:"promise-btn",rawName:"v-promise-btn"}],staticClass:"btn btn-outline-primary btn-sm mr-2",on:{click:e.runSearch}},[e._v("Search")]),a("button",{directives:[{name:"promise-btn",rawName:"v-promise-btn"}],staticClass:"btn btn-outline-dark btn-sm",on:{click:e.clearQuery}},[e._v("Clear")])])],1),a("b-row",{ref:"queryResult"})],1)},E=[],N=(a("4160"),a("fb6a"),a("0d03"),a("25f0"),a("2b0e")),I=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("br"),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("License:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.vehicleData.registration))])]),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Model:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.vehicleData.vehicle.toUpperCase()))])]),1!=e.vehicleData.vinscratch?a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Owner name:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.vehicleData.firstname)+" "+e._s(e.vehicleData.lastname))])]):e._e(),1!=e.vehicleData.vinscratch?a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Owner phone:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.vehicleData.phone))])]):e._e(),1!=e.vehicleData.vinscratch?a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Owner DL:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.vehicleData.dlnumber))])]):e._e(),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Primary color:")]),a("span",{staticClass:"vehicle-return-data",style:e.primaryStyle})]),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Secondary color:")]),a("span",{staticClass:"vehicle-return-data",style:e.secondaryStyle})]),1==e.vehicleData.stolen?a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Vehicle status:")]),a("span",{staticClass:"vehicle-return-data",staticStyle:{"font-weight":"bold",color:"yellow"}},[e._v("PREVIOUSLY REPORTED STOLEN")])]):e._e(),1==e.vehicleData.vinscratch?a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Vehicle status:")]),a("span",{staticClass:"vehicle-return-data",staticStyle:{"font-weight":"bold",color:"red"}},[e._v("STOLEN VEHICLE")])]):e._e()],1)},q=[],J={name:"DatabaseResultPlate",components:{},props:["vehicleData"],data:function(){return{primaryStyle:{display:"inline-block",width:"100px !important",height:"19px !important",border:"1px solid #FFF",backgroundColor:this.vehicleData.colour_code},secondaryStyle:{display:"inline-block",width:"100px !important",height:"19px !important",border:"1px solid #FFF",backgroundColor:this.vehicleData.scolour_code}}},mounted:function(){},destroyed:function(){},methods:{},computed:Object(h["b"])({user:"auth/user"})},M=J,U=(a("66a9"),Object(m["a"])(M,I,q,!1,null,"bf08da3a",null)),H=U.exports,V=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("br"),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("DL Number:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.characterData.dlnumber))])]),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Name:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.characterData.firstname)+" "+e._s(e.characterData.lastname))])]),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Phone:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.characterData.phone))])]),a("br"),e._l(e.licensesData,(function(t,r){return a("b-col",{attrs:{"key-":r}},[a("span",{staticClass:"vehicle-return-field"},[e._v(e._s(r))]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(t))])])})),a("br"),e._l(e.addressesData,(function(t,r){return a("b-col",{attrs:{"key-":r}},[a("span",{staticClass:"vehicle-return-field"},[e._v("Address:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(t))])])})),a("br"),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Registration")]),a("span",{staticClass:"vehicle-return-data"},[e._v("Model")])]),e._l(e.vehiclesData,(function(t,r){return a("b-col",{attrs:{"key-":r}},[a("span",{staticClass:"vehicle-return-field"},[e._v(e._s(t.registration))]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(t.vehicle.toUpperCase()))])])}))],2)},A=[],Q={name:"DatabaseResultDL",components:{},props:["vehiclesData","addressesData","licensesData","characterData"],data:function(){return{}},mounted:function(){},destroyed:function(){},methods:{},computed:Object(h["b"])({user:"auth/user"})},z=Q,G=(a("f7fd"),Object(m["a"])(z,V,A,!1,null,"4d42cbfd",null)),B=G.exports,W=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("br"),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Registration:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.weaponData.registration))])]),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("DL Number:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.weaponData.dlnumber))])]),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Name:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.weaponData.firstname)+" "+e._s(e.weaponData.lastname))])]),a("b-col",[a("span",{staticClass:"vehicle-return-field"},[e._v("Phone:")]),a("span",{staticClass:"vehicle-return-data"},[e._v(e._s(e.weaponData.phone))])])],1)},Y=[],K={name:"DatabaseResultFirearm",components:{},props:["weaponData"],data:function(){return{}},mounted:function(){},destroyed:function(){},methods:{},computed:Object(h["b"])({user:"auth/user"})},X=K,Z=(a("3176"),Object(m["a"])(X,W,Y,!1,null,"94bb0bce",null)),ee=Z.exports,te={name:"DatabaseQuery",components:{DatabaseResultPlate:H,DatabaseResultDL:B,DatabaseResultFirearm:ee},data:function(){return{searchTerm:null,searchTermDL:null,searchTermFirearm:null,colorTranslations:{0:"#0d1116",1:"#1c1d21",2:"#32383d",3:"#454b4f",4:"#999da0",5:"#c2c4c6",6:"#979a97",7:"#637380",8:"#63625c",9:"#3c3f47",10:"#444e54",11:"#1d2129",12:"#13181f",13:"#26282a",14:"#515554",15:"#151921",16:"#1e2429",17:"#333a3c",18:"#8c9095",19:"#39434d",20:"#506272",21:"#1e232f",22:"#363a3f",23:"#a0a199",24:"#d3d3d3",25:"#b7bfca",26:"#778794",27:"#c00e1a",28:"#da1918",29:"#b6111b",30:"#a51e23",31:"#7b1a22",32:"#8e1b1f",33:"#6f1818",34:"#49111d",35:"#b60f25",36:"#d44a17",37:"#c2944f",38:"#f78616",39:"#cf1f21",40:"#732021",41:"#f27d20",42:"#ffc91f",43:"#9c1016",44:"#de0f18",45:"#8f1e17",46:"#a94744",47:"#b16c51",48:"#371c25",49:"#132428",50:"#122e2b",51:"#12383c",52:"#31423f",53:"#155c2d",54:"#1b6770",55:"#66b81f",56:"#22383e",57:"#1d5a3f",58:"#2d423f",59:"#45594b",60:"#65867f",61:"#222e46",62:"#233155",63:"#304c7e",64:"#47578f",65:"#637ba7",66:"#394762",67:"#d6e7f1",68:"#76afbe",69:"#345e72",70:"#0b9cf1",71:"#2f2d52",72:"#282c4d",73:"#2354a1",74:"#6ea3c6",75:"#112552",76:"#1b203e",77:"#275190",78:"#608592",79:"#2446a8",80:"#4271e1",81:"#3b39e0",82:"#1f2852",83:"#253aa7",84:"#1c3551",85:"#4c5f81",86:"#58688e",87:"#74b5d8",88:"#ffcf20",89:"#fbe212",90:"#916532",91:"#e0e13d",92:"#98d223",93:"#9b8c78",94:"#503218",95:"#473f2b",96:"#221b19",97:"#653f23",98:"#775c3e",99:"#ac9975",100:"#6c6b4b",101:"#402e2b",102:"#a4965f",103:"#46231a",104:"#752b19",105:"#bfae7b",106:"#dfd5b2",107:"#f7edd5",108:"#3a2a1b",109:"#785f33",110:"#b5a079",111:"#fffff6",112:"#eaeaea",113:"#b0ab94",114:"#453831",115:"#2a282b",116:"#726c57",117:"#6a747c",118:"#354158",119:"#9ba0a8",120:"#5870a1",121:"#eae6de",122:"#dfddd0",123:"#f2ad2e",124:"#f9a458",125:"#83c566",126:"#f1cc40",127:"#4cc3da",128:"#4e6443",129:"#bcac8f",130:"#f8b658",131:"#fcf9f1",132:"#fffffb",133:"#81844c",134:"#ffffff",135:"#f21f99",136:"#fdd6cd",137:"#df5891",138:"#f6ae20",139:"#b0ee6e",140:"#08e9fa",141:"#0a0c17",142:"#0c0d18",143:"#0e0d14",144:"#9f9e8a",145:"#621276",146:"#0b1421",147:"#11141a",148:"#6b1f7b",149:"#1e1d22",150:"#bc1917",151:"#2d362a",152:"#696748",153:"#7a6c55",154:"#c3b492",155:"#5a6352",156:"#81827f",157:"#afd6e4",158:"#7a6440",159:"#7f6a48",160:"#ffd859"}}},mounted:function(){var e=this;window.$events.$on("vehicle:data_callback",(function(t){if(null!==t.vehicle&&null!=t.vehicle){if(t=t.vehicle,t.colour_code=e.colorTranslations[t.colour],t.scolour_code=e.colorTranslations[t.scolour],(1e5===t.colour||1e5===t.scolour)&&void 0!==t.colour_rgb&&void 0!==t.scolour_rgb){var a=JSON.parse(t.colour_rgb),r=JSON.parse(t.scolour_rgb);t.colour_code="#"+(16777216+(a[0]<<16)|a[1]<<8|a[2]<<0).toString(16).slice(1),t.scolour_code="#"+(16777216+(r[0]<<16)|r[1]<<8|r[2]<<0).toString(16).slice(1)}void 0===t.colour_code&&(t.colour_code="#FFF"),void 0===t.scolour_code&&(t.scolour_code="#FFF");var n=N["default"].extend(H),s=new n({propsData:{vehicleData:t}});s.$mount(),e.$refs.queryResult.innerHTML="",e.$refs.queryResult.appendChild(s.$el)}else e.$refs.queryResult.innerHTML="No results found"})),window.$events.$on("vehicle:data_callback_dl",(function(t){!1===t.character?e.$refs.queryResult.innerHTML="No results found":e.axios.get("/police/characterAddresses/".concat(t.character.id)).then((function(a){var r=N["default"].extend(B),n=new r({propsData:{characterData:t.character,licensesData:t.licenses,vehiclesData:t.vehicles,addressesData:a.data}});n.$mount(),e.$refs.queryResult.innerHTML="",e.$refs.queryResult.appendChild(n.$el)}))})),window.$events.$on("vehicle:data_callback_weapon",(function(t){if(t.weapon_data){var a=N["default"].extend(ee),r=new a({propsData:{weaponData:t.weapon_data}});r.$mount(),e.$refs.queryResult.innerHTML="",e.$refs.queryResult.appendChild(r.$el)}else e.$refs.queryResult.innerHTML="No results found"}))},destroyed:function(){window.$events.$off("vehicle:data_callback"),window.$events.$off("vehicle:data_callback_dl"),window.$events.$off("vehicle:data_callback_weapon")},methods:{runSearch:function(){this.searchTerm&&""!==this.searchTerm&&fetch("https://blrp_tablet/getVehicleData",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify({term:this.searchTerm})}).then((function(e){return e.json()})).then((function(e){})),this.searchTermDL&&""!==this.searchTermDL&&fetch("https://blrp_tablet/getVehicleDataByDL",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify({term:this.searchTermDL})}).then((function(e){return e.json()})).then((function(e){})),this.searchTermFirearm&&""!==this.searchTermFirearm&&fetch("https://blrp_tablet/getWeaponInformation",{method:"POST",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify({term:this.searchTermFirearm})}).then((function(e){return e.json()})).then((function(e){}))},clearQuery:function(){this.$refs.queryResult.innerHTML="",this.searchTerm="",this.searchTermDL="",this.searchTermFirearm=""},validateInputs:function(e){var t=this;["searchTerm","searchTermDL","searchTermFirearm"].forEach((function(a){e.target.id!=a&&(t[a]="")}))}},computed:Object(h["b"])({user:"auth/user"})},ae=te,re=Object(m["a"])(ae,j,E,!1,null,null,null),ne=re.exports,se={components:{DatabaseQuery:ne,Documents:P["a"],PoliceDocuments:F,EvidenceLocker:g,PoliceLive:_,PoliceDashboard:d["a"],PoliceProcedures:l["a"],PoliceGuides:o["a"],TicketingAid:i["a"],PoliceRecords:c["a"],SubmitRecord:s["a"]},data:function(){return{tabIndex:0}},mounted:function(){var e=this;window.$events.$on("police:record:submitted",(function(t){e.tabIndex=1})),window.$events.$on("police::record::edit",(function(t){e.tabIndex=2})),window.$events.$on("police:record:search",(function(t){e.tabIndex=1}))},destroyed:function(){window.$events.$off("police:record:submitted"),window.$events.$off("police::record::edit"),window.$events.$off("police:record:search")}},ce=se,ie=(a("3d9e"),Object(m["a"])(ce,r,n,!1,null,null,null));t["default"]=ie.exports},f7fd:function(e,t,a){"use strict";var r=a("b47f"),n=a.n(r);n.a},fd51:function(e,t,a){}}]);