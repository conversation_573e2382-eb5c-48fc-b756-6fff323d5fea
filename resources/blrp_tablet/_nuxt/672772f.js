(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[182],{

/***/ 2170:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call/incoming.vue?vue&type=template&id=9e2290a0
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_vm._m(0), _vm._v(" "), _c('div', {
    staticClass: "d-flex justify-content-center"
  }, [_c('h3', [_vm._v(_vm._s(_vm.activeCall ? _vm.activeCall.phone : 'No Phone Number'))])]), _vm._v(" "), _c('div', {
    staticClass: "d-flex mt-5 group-buttons"
  }, [_c('div', {
    staticClass: "accept mr-5 font-weight-bold",
    on: {
      "click": function click($event) {
        return _vm.$store.dispatch('text/cancelCall');
      }
    }
  }, [_vm._v("\n      Answer\n      "), _c('kbd', [_vm._v("ENTER")])]), _vm._v(" "), _c('div', {
    staticClass: "deny text-right font-weight-bold",
    on: {
      "click": function click($event) {
        return _vm.$store.dispatch('text/acceptCall');
      }
    }
  }, [_c('kbd', [_vm._v("BACKSPACE")]), _vm._v("\n      Deny\n    ")])])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "d-flex justify-content-center"
  }, [_c('h2', {
    staticClass: "mt-5"
  }, [_vm._v("Incoming Call")])]);
}];

// CONCATENATED MODULE: ./pages/phone/call/incoming.vue?vue&type=template&id=9e2290a0

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call/incoming.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var incomingvue_type_script_lang_js = ({
  name: 'incoming',
  components: {},
  props: [],
  data: function data() {
    return {};
  },
  created: function created() {
    window.onkeydown = function (e) {
      var code = e.keyCode ? e.keyCode : e.which;
      if (code === 13) {
        // enter
        this.$store.dispatch('text/acceptCall');
      }
      if (code === 8) {
        // backspace
        this.$store.dispatch('text/cancelCall');
      }
    };
  },
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    call: 'text/incoming'
  }))
});
// CONCATENATED MODULE: ./pages/phone/call/incoming.vue?vue&type=script&lang=js
 /* harmony default export */ var call_incomingvue_type_script_lang_js = (incomingvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/phone/call/incoming.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  call_incomingvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var incoming = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);