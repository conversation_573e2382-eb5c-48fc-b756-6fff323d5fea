(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[179],{

/***/ 1727:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1919);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("91938f1c", content, true, {"sourceMap":false});

/***/ }),

/***/ 1918:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_call_vue_vue_type_style_index_0_id_92124e82_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1727);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_call_vue_vue_type_style_index_0_id_92124e82_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_call_vue_vue_type_style_index_0_id_92124e82_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1919:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".buttons{display:flex;flex-wrap:wrap;font-weight:400;justify-content:flex-start;margin:0 auto;width:310px}.button{border:#fff;border-radius:50px;color:#fff;cursor:pointer;display:inline-block;font-size:26px;font-weight:700;height:80px;line-height:80px;margin:10px;text-align:center;transition:all .3s;width:80px}.button:hover{background-color:var(--person-color);color:#fff}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2168:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VSpacer.js
var VSpacer = __webpack_require__(1523);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call.vue?vue&type=template&id=92124e82


var callvue_type_template_id_92124e82_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('div', {
    staticClass: "ml-5 d-flex justify-content-between text-center",
    staticStyle: {
      "margin-top": "36px"
    }
  }, [_c(VSpacer["a" /* default */]), _vm._v(" "), _vm.phone && _vm.phone.length > 0 ? _c('h1', {}, [_vm._v("\n      " + _vm._s(_vm.phone) + " "), _c('i', {
    staticClass: "fa-solid fa-arrow-left ml-3 hover-icon",
    on: {
      "click": _vm.backspace
    }
  })]) : _c('h1', {
    staticClass: "text-muted"
  }, [_vm._v("\n      ###-####\n    ")]), _vm._v(" "), _c(VSpacer["a" /* default */])], 1), _vm._v(" "), _c('div', {
    staticClass: "buttons"
  }, [_c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['1'],
      expression: "['1']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('1');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('1');
      }
    }
  }, [_vm._v("1 "), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  })]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['2'],
      expression: "['2']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('2');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('2');
      }
    }
  }, [_vm._v("2"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("ABC")])]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['3'],
      expression: "['3']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('3');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('3');
      }
    }
  }, [_vm._v("3"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("DEF")])]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['4'],
      expression: "['4']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('4');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('4');
      }
    }
  }, [_vm._v("4"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("GHI")])]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['5'],
      expression: "['5']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('5');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('5');
      }
    }
  }, [_vm._v("5"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("JKL")])]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['6'],
      expression: "['6']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('6');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('6');
      }
    }
  }, [_vm._v("6"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("MNO")])]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['7'],
      expression: "['7']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('7');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('7');
      }
    }
  }, [_vm._v("7"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("PQRS")])]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['8'],
      expression: "['8']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('8');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('8');
      }
    }
  }, [_vm._v("8"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("TUV")])]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['9'],
      expression: "['9']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('9');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('9');
      }
    }
  }, [_vm._v("9"), _c('span', {
    staticClass: "ml-2",
    staticStyle: {
      "font-size": "13px",
      "display": "inline-block"
    }
  }, [_vm._v("WXYZ")])]), _vm._v(" "), _c('div', {
    staticClass: "button text-white"
  }, [_vm._v("#")]), _vm._v(" "), _c('div', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['0'],
      expression: "['0']",
      modifiers: {
        "once": true
      }
    }],
    staticClass: "button",
    on: {
      "click": function click($event) {
        return _vm.input('0');
      },
      "shortkey": function shortkey($event) {
        return _vm.input('0');
      }
    }
  }, [_vm._v("0")]), _vm._v(" "), _c('div', {
    staticClass: "button text-white",
    class: {
      '': _vm.activeCall
    },
    staticStyle: {
      "background-color": "#00ff52"
    },
    on: {
      "click": _vm.call
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-phone"
  })]), _vm._v(" "), _c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['shift', '#'],
      expression: "['shift', '#']",
      modifiers: {
        "once": true
      }
    }],
    on: {
      "shortkey": function shortkey($event) {
        return _vm.input('#');
      }
    }
  })]), _vm._v(" "), _c('div', {
    staticClass: "ml-5 mt-6 d-flex justify-content-between text-center",
    staticStyle: {
      "margin-top": "50px"
    }
  }, [_c(VSpacer["a" /* default */]), _vm._v(" "), _c('h4', [_c('NuxtLink', {
    attrs: {
      "to": "/phone/services"
    }
  }, [_c('span', {
    staticStyle: {
      "color": "red"
    }
  }, [_vm._v("\n          🚨 Tap for Emergencies\n        ")])])], 1), _vm._v(" "), _c(VSpacer["a" /* default */])], 1)]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/phone/call.vue?vue&type=template&id=92124e82

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/call.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var callvue_type_script_lang_js = ({
  scrollToTop: true,
  components: {},
  props: [],
  data: function data() {
    return {
      phone: ''
    };
  },
  created: function created() {},
  methods: {
    call: function call() {
      if (!this.activeCall && this.phone && this.phone.length > 0) {
        this.$store.dispatch('text/startCall', {
          phone: this.phone
        });
        this.phone = '';
      }
    },
    input: function input(value) {
      if (this.phone.length > 12) {
        return this.$store.dispatch('audio/play', "click-error");
      }
      this.$store.dispatch('audio/play', "dial-".concat(value));
      if (this.phone.length === 3) this.phone += '-';
      this.phone += value;
    },
    backspace: function backspace() {
      this.$store.dispatch('audio/play', 's-light-beep');
      this.phone = this.phone.substring(0, this.phone.length - 1);
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    activeCall: 'text/activeCall'
  }))
});
// CONCATENATED MODULE: ./pages/phone/call.vue?vue&type=script&lang=js
 /* harmony default export */ var phone_callvue_type_script_lang_js = (callvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/phone/call.vue?vue&type=style&index=0&id=92124e82&prod&lang=scss
var callvue_type_style_index_0_id_92124e82_prod_lang_scss = __webpack_require__(1918);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/phone/call.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  phone_callvue_type_script_lang_js,
  callvue_type_template_id_92124e82_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var call = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);