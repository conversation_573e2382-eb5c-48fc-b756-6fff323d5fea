(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[5],{

/***/ 1566:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(0);
/* harmony import */ var _babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(4);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(89);
/* harmony import */ var core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_find_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var regenerator_runtime_runtime_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(58);
/* harmony import */ var regenerator_runtime_runtime_js__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(regenerator_runtime_runtime_js__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _utils_store_accessor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(27);
/* harmony import */ var vuex__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(13);


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }











/* harmony default export */ __webpack_exports__["a"] = ({
  methods: {
    sendNewMessage: function sendNewMessage() {
      var _this = this;
      _utils_store_accessor__WEBPACK_IMPORTED_MODULE_11__[/* CrudyFormStore */ "b"].setSchema({
        finish: function () {
          var _finish = Object(_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
            return regeneratorRuntime.wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return _this.$axios.$post('https://blrp_tablet/inquire-contacts');
                case 2:
                  _context.next = 4;
                  return _this.$router.push({
                    path: "/phone/thread/".concat(phone)
                  });
                case 4:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          }));
          function finish() {
            return _finish.apply(this, arguments);
          }
          return finish;
        }(),
        action: {
          title: 'Send SMS to Phone #',
          endpoint: 'https://blrp_tablet/sendMessage',
          fields: {
            phone: {
              name: 'phone',
              label: 'Contact Phone #',
              help: 'Format should bee ###-####. To send a message to an existing contact, use the contacts page.',
              type: 'CrudyFormFieldText',
              validators: {
                required: true,
                min: 1,
                max: 8
              },
              editable: []
            },
            message: {
              name: 'message',
              label: 'What would you like to say?',
              help: 'This will be the start of a conversation.',
              type: 'CrudyFormFieldMarkdown',
              validators: {
                required: true,
                min: 4,
                max: 255
              },
              editable: []
            }
          }
        }
      });
    },
    sendNewMessageToPhone: function sendNewMessageToPhone(phone) {
      var _this2 = this;
      _utils_store_accessor__WEBPACK_IMPORTED_MODULE_11__[/* CrudyFormStore */ "b"].setSchema({
        finish: function () {
          var _finish2 = Object(_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
            return regeneratorRuntime.wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.next = 2;
                  return _this2.$axios.$post('https://blrp_tablet/inquire-contacts');
                case 2:
                  _context2.next = 4;
                  return _this2.$router.push({
                    path: "/phone/thread/".concat(phone)
                  });
                case 4:
                case "end":
                  return _context2.stop();
              }
            }, _callee2);
          }));
          function finish() {
            return _finish2.apply(this, arguments);
          }
          return finish;
        }(),
        action: {
          title: 'Send SMS to Phone #',
          endpoint: 'https://blrp_tablet/sendMessage',
          fields: {
            phone: {
              name: 'phone',
              label: 'Contact Phone #',
              help: 'Only shown for display purposes. Not editable.',
              type: 'CrudyFormFieldText',
              default: phone,
              hidden: true,
              validators: {
                required: true,
                min: 1,
                max: 8
              },
              editable: [],
              disabled: true
            },
            message: {
              name: 'message',
              label: 'What would you like to say?',
              help: 'This will be the start of a conversation.',
              type: 'CrudyFormFieldMarkdown',
              validators: {
                required: true,
                min: 4,
                max: 255
              },
              editable: []
            }
          }
        }
      });
    },
    doContactCreateOrUpdate: function doContactCreateOrUpdate(_ref) {
      var _this3 = this;
      var contactPacket = _ref.contactPacket,
        phoneNumber = _ref.phoneNumber;
      this.showing = true;
      if (contactPacket) {
        var _contactPacket$color;
        return _utils_store_accessor__WEBPACK_IMPORTED_MODULE_11__[/* CrudyFormStore */ "b"].setSchema({
          action: {
            title: 'Update Contact',
            endpoint: 'https://blrp_tablet/updateContact',
            fields: {
              id: {
                name: 'id',
                default: contactPacket.id,
                editable: []
              },
              display: {
                name: '🏷 display',
                label: 'Contact Name',
                help: 'The contact name, is your personal display name for the contact.',
                default: contactPacket.display,
                type: 'CrudyFormFieldText',
                validators: {
                  required: true,
                  min: 1,
                  max: 25
                },
                editable: ['create']
              },
              phone: {
                name: 'phone',
                label: '📱 Contact Phone #',
                help: 'Only shown for display purposes. Not editable.',
                hidden: true,
                default: contactPacket.number,
                type: 'CrudyFormFieldText',
                validators: {
                  required: true,
                  min: 4,
                  max: 12
                },
                editable: ['create'],
                disabled: true
              },
              color: {
                name: 'color',
                label: '🎨 Contact Color',
                help: 'Color for this contact. This is only visible to you.',
                default: (_contactPacket$color = contactPacket.color) !== null && _contactPacket$color !== void 0 ? _contactPacket$color : '#FFFFFF',
                type: 'CrudyFormFieldColor',
                editable: ['create']
              },
              avatar_url: {
                name: 'avatar_url',
                label: '🔗 Contact Avatar (URL)',
                help: 'Direct link to image for avatar. This should end in .jpg/.png/.gif/etc. This is only visible to you.',
                default: contactPacket.avatar_url,
                type: 'CrudyFormFieldText',
                editable: ['create']
              },
              silence_texts: {
                name: 'silence_texts',
                label: '🔕 Silence Incoming Texts',
                help: 'Disable phone sounds when receiving text messages for this specific contact.',
                default: contactPacket.silence_texts,
                type: 'CrudyFormFieldBoolean',
                editable: ['create']
              },
              silence_calls: {
                name: '🔕 silence_calls',
                label: 'Silence Incoming Calls',
                help: 'Disable phone ringtone when receiving calls messages for this specific contact.',
                default: contactPacket.silence_calls,
                type: 'CrudyFormFieldBoolean',
                editable: ['create']
              },
              favorite: {
                name: 'favorite',
                label: '⭐ Set Favorite',
                help: 'Favorite\'d Contacts have a ⭐ that appears next to them.',
                default: contactPacket.favorite,
                type: 'CrudyFormFieldBoolean',
                editable: ['create']
              }
            }
          }
        });
      }
      _utils_store_accessor__WEBPACK_IMPORTED_MODULE_11__[/* CrudyFormStore */ "b"].setSchema({
        finish: function () {
          var _finish3 = Object(_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
            return regeneratorRuntime.wrap(function _callee3$(_context3) {
              while (1) switch (_context3.prev = _context3.next) {
                case 0:
                  _context3.next = 2;
                  return _this3.$axios.$post('https://blrp_tablet/inquire-contacts');
                case 2:
                case "end":
                  return _context3.stop();
              }
            }, _callee3);
          }));
          function finish() {
            return _finish3.apply(this, arguments);
          }
          return finish;
        }(),
        validate: function () {
          var _validate = Object(_babel_runtime_helpers_esm_asyncToGenerator__WEBPACK_IMPORTED_MODULE_1__[/* default */ "a"])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4(item) {
            return regeneratorRuntime.wrap(function _callee4$(_context4) {
              while (1) switch (_context4.prev = _context4.next) {
                case 0:
                  if (!_this3.contacts.find(function (c) {
                    return c.phone === item.phone;
                  })) {
                    _context4.next = 2;
                    break;
                  }
                  return _context4.abrupt("return", 'You already have a contact with this phone number.');
                case 2:
                  if (!_this3.contacts.find(function (c) {
                    return c.display === item.display;
                  })) {
                    _context4.next = 4;
                    break;
                  }
                  return _context4.abrupt("return", 'You already have a contact with this display name.');
                case 4:
                  if (!(_this3.contacts.length > 500)) {
                    _context4.next = 6;
                    break;
                  }
                  return _context4.abrupt("return", 'Not enough contact slots available. 500/500 used.');
                case 6:
                case "end":
                  return _context4.stop();
              }
            }, _callee4);
          }));
          function validate(_x) {
            return _validate.apply(this, arguments);
          }
          return validate;
        }(),
        action: {
          title: 'Add Contact',
          endpoint: 'https://blrp_tablet/createContact',
          fields: {
            display: {
              name: 'display',
              label: 'Contact Name',
              help: 'The contact name, is your personal display name for the contact.',
              type: 'CrudyFormFieldText',
              validators: {
                required: true,
                min: 1,
                max: 25
              },
              editable: ['create']
            },
            phone: {
              name: 'phone',
              label: 'Contact Phone #',
              mask: '###-####',
              default: phoneNumber,
              help: 'Format should be ###-####',
              type: 'CrudyFormFieldText',
              validators: {
                required: true,
                min: 4,
                max: 12
              },
              editable: ['create']
            }
          }
        }
      });
    },
    removeContact: function removeContact(id) {
      var _this4 = this;
      this.$axios.$post('https://blrp_tablet/removeContact', {
        id: id
      }).then(function () {
        _this4.showing = false;
        _this4.$axios.$post('https://blrp_tablet/inquire-contacts');
      });
    },
    removeMessages: function removeMessages(targetPhone) {
      var _this5 = this;
      this.$axios.$post('https://blrp_tablet/removeMessages', {
        targetPhone: targetPhone
      }).then(function () {
        _this5.showing = false;
        _this5.$axios.$post('https://blrp_tablet/inquire-contacts');
      });
    }
  },
  computed: _objectSpread({}, Object(vuex__WEBPACK_IMPORTED_MODULE_12__[/* mapGetters */ "b"])({
    contacts: 'text/contacts'
  }))
});

/***/ }),

/***/ 1590:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 2 modules
var components_VList = __webpack_require__(50);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/VMenu.js
var VMenu = __webpack_require__(461);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-contact-actions.vue?vue&type=template&id=1278a80b









var text_contact_actionsvue_type_template_id_1278a80b_render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _c('span', [_c(VMenu["a" /* default */], {
    ref: "contactMenu",
    attrs: {
      "offset-y": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref) {
        var attrs = _ref.attrs;
        return [_c(VBtn["a" /* default */], _vm._b({
          attrs: {
            "text": "",
            "color": "primary",
            "dark": "",
            "fab": "",
            "icon": "",
            "small": ""
          },
          on: {
            "click": function click($event) {
              $event.stopPropagation();
              return _vm.toggleMenu.apply(null, arguments);
            }
          }
        }, 'v-btn', attrs, false), [_c(VIcon["a" /* default */], {
          attrs: {
            "small": ""
          }
        }, [_vm._v("\n          fa-solid fa-ellipsis-vertical\n        ")])], 1)];
      }
    }]),
    model: {
      value: _vm.menuOpen,
      callback: function callback($$v) {
        _vm.menuOpen = $$v;
      },
      expression: "menuOpen"
    }
  }, [_vm._v(" "), _c(VList["a" /* default */], [_c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.$store.dispatch('text/startCall', {
          phone: _vm.phone
        });
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "success",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-phone-arrow-up-right\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Call Phone\n          ")])])], 1), _vm._v(" "), _c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.$store.dispatch('text/startCall', {
          phone: _vm.phone,
          anon: true
        });
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "grey",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-user-secret\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Call Phone (Anonymous)\n          ")])])], 1), _vm._v(" "), !_vm.contact && _vm.phone ? _c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.doContactCreateOrUpdate({
          phoneNumber: _vm.phone
        });
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "success",
      "small": ""
    }
  }, [_vm._v("\n              fa-plus\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Add as New Contact\n          ")])])], 1) : _vm._e(), _vm._v(" "), _vm.contact ? _c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.doContactCreateOrUpdate({
          contactPacket: _vm.contact
        });
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "brown",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-gears\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Update Contact\n          ")])])], 1) : _vm._e(), _vm._v(" "), _vm.hasThread ? _c(VListItem["a" /* default */], {
    on: {
      "click": _vm.viewThread
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "primary",
      "small": ""
    }
  }, [_vm._v("\n              fa-regular fa-messages\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            View Messages\n          ")])])], 1) : _vm._e(), _vm._v(" "), !_vm.hasThread ? _c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.sendNewMessageToPhone(_vm.phone);
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "primary",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-message-medical\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Send Message to " + _vm._s(_vm.phone) + "\n          ")])])], 1) : _vm._e(), _vm._v(" "), _c(VListItem["a" /* default */], {
    on: {
      "click": _vm.sendLocation
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "yellow",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-location-arrow\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Send Current GPS Position\n          ")])])], 1), _vm._v(" "), _c(VListItem["a" /* default */], {
    on: {
      "click": _vm.sendBankDetails
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "primary",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-building-columns\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Send Bank Details\n          ")])])], 1), _vm._v(" "), _vm.contact && _vm.$route.path.includes('contacts') ? _c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.removeContact(_vm.contact.id);
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "red",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-trash\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Remove Contact\n          ")])])], 1) : _vm._e(), _vm._v(" "), _vm.$route.path.includes('sms') ? _c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.removeMessages(_vm.phone);
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "red",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-trash\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Remove Message Thread\n          ")])])], 1) : _vm._e()], 1)], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-contact-actions.vue?vue&type=template&id=1278a80b

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// EXTERNAL MODULE: ./components/Common/app-btn.vue + 4 modules
var app_btn = __webpack_require__(495);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-contact-actions.vue?vue&type=script&lang=ts








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/* harmony default export */ var text_contact_actionsvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'text-contact-actions',
  mixins: [ContactFormMixin["a" /* default */]],
  components: {
    AppBtn: app_btn["a" /* default */]
  },
  props: {
    phone: {},
    contact: {}
  },
  data: function data() {
    return {
      menuOpen: false
    };
  },
  methods: {
    toggleMenu: function toggleMenu() {
      // Event trigger method to toggle the menu
      this.menuOpen = !this.menuOpen;
    },
    openMenu: function openMenu() {
      // Public method to open menu via event trigger
      this.menuOpen = true;
    },
    closeMenu: function closeMenu() {
      // Public method to close menu via event trigger
      this.menuOpen = false;
    },
    viewThread: function viewThread() {
      this.$router.push({
        path: "/phone/thread/".concat(this.phone)
      });
    },
    sendLocation: function sendLocation() {
      var _this = this;
      this.$axios.$post('https://blrp_tablet/sendMessage', {
        phone: this.phone,
        message: '%pos%'
      }).then(function () {
        setTimeout(function () {
          _this.$forceUpdate();
        }, 300);
      });
    },
    sendBankDetails: function sendBankDetails() {
      var _this2 = this;
      this.$axios.$post('https://blrp_tablet/sendMessage', {
        phone: this.phone,
        message: '%bank%'
      }).then(function () {
        setTimeout(function () {
          _this2.$forceUpdate();
        }, 300);
      });
    }
  },
  computed: _objectSpread({
    hasThread: function hasThread() {
      return this.messageThread(this.phone);
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    messageThread: 'text/messageThread'
  }))
}));
// CONCATENATED MODULE: ./components/Pages/Text/text-contact-actions.vue?vue&type=script&lang=ts
 /* harmony default export */ var Text_text_contact_actionsvue_type_script_lang_ts = (text_contact_actionsvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-contact-actions.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_contact_actionsvue_type_script_lang_ts,
  text_contact_actionsvue_type_template_id_1278a80b_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var text_contact_actions = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1636:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-avatar.vue?vue&type=template&id=51802224
var render = function render() {
  var _vm$currentContact$av;
  var _vm = this,
    _c = _vm._self._c;
  return _c('span', [_vm.currentContact ? _c('app-avatar', {
    attrs: {
      "size": "44",
      "src": (_vm$currentContact$av = _vm.currentContact.avatar_url) !== null && _vm$currentContact$av !== void 0 ? _vm$currentContact$av : "https://api.dicebear.com/9.x/bottts-neutral/svg?seed=".concat(_vm.strippedPhone)
    }
  }) : _c('app-avatar', {
    attrs: {
      "size": "44",
      "src": "https://api.dicebear.com/9.x/bottts-neutral/svg?seed=".concat(_vm.strippedPhone)
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-avatar.vue?vue&type=template&id=51802224

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-avatar.vue + 4 modules
var app_avatar = __webpack_require__(189);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-avatar.vue?vue&type=script&lang=js










function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var text_avatarvue_type_script_lang_js = ({
  name: 'text-avatar',
  components: {
    AppAvatar: app_avatar["a" /* default */]
  },
  props: ['phone'],
  data: function data() {
    return {};
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({
    currentContact: function currentContact() {
      return this.keyedContacts[this.phone];
    },
    strippedPhone: function strippedPhone() {
      return this.phone.replace('#', '');
    },
    strippedDisplay: function strippedDisplay() {
      var _this$currentContact;
      return (_this$currentContact = this.currentContact) === null || _this$currentContact === void 0 ? void 0 : _this$currentContact.display.replace('#', '').replace('?', '');
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    keyedContacts: 'text/keyedContacts'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Text/text-avatar.vue?vue&type=script&lang=js
 /* harmony default export */ var Text_text_avatarvue_type_script_lang_js = (text_avatarvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-avatar.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_avatarvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var text_avatar = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1670:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.starts-with.js
var es_string_starts_with = __webpack_require__(144);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-thread.vue?vue&type=template&id=0c2d55b0&scoped=true



var text_threadvue_type_template_id_0c2d55b0_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "thread-card",
    class: {
      'thread-unread': _vm.lastMessage && !_vm.lastMessage.isRead
    }
  }, [_c('div', {
    staticClass: "thread-content"
  }, [_c('div', {
    staticClass: "thread-avatar"
  }, [_c('text-avatar', {
    attrs: {
      "phone": _vm.thread.phone
    }
  }), _vm._v(" "), _vm.lastMessage && !_vm.lastMessage.isRead ? _c('div', {
    staticClass: "unread-indicator"
  }) : _vm._e()], 1), _vm._v(" "), _c('div', {
    staticClass: "thread-info"
  }, [_c('div', {
    staticClass: "thread-header"
  }, [_c('div', {
    staticClass: "contact-info"
  }, [_c('div', {
    staticClass: "contact-name"
  }, [_vm.currentContact ? _c('span', {
    style: "color: ".concat(_vm.currentContact.color || '#ffffff')
  }, [_vm._v("\n              " + _vm._s(_vm.currentContact.display) + "\n            ")]) : _c('span', {
    staticClass: "phone-number"
  }, [_vm._v("\n              " + _vm._s(_vm.thread.phone) + "\n            ")])]), _vm._v(" "), _vm.lastMessage && !_vm.hideMessagePreview ? _c('div', {
    staticClass: "message-preview mt-2",
    class: {
      'font-weight-bold': _vm.lastMessage && !_vm.lastMessage.isRead
    }
  }, [_vm.lastMessage.message && !_vm.lastMessage.message.startsWith('{') ? _c('span', {
    staticClass: "message-text"
  }, [_c('app-markdown-view', {
    attrs: {
      "source": _vm.lastMessage.message
    }
  })], 1) : _c('span', {
    staticClass: "message-embed"
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-1",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-paperclip")]), _vm._v("\n              Attachment\n            ")], 1)]) : _vm._e()]), _vm._v(" "), _c('div', {
    staticClass: "thread-meta"
  }, [_vm.currentContact && _vm.currentContact.favorite ? _c(VIcon["a" /* default */], {
    staticClass: "meta-icon",
    attrs: {
      "small": "",
      "color": "#FFD700"
    }
  }, [_vm._v("\n            fa-solid fa-star\n          ")]) : _vm.currentContact ? _c(VIcon["a" /* default */], {
    staticClass: "meta-icon",
    attrs: {
      "small": "",
      "color": "#8e8e93"
    }
  }, [_vm._v("\n            fa-solid fa-user\n          ")]) : _vm._e(), _vm._v(" "), _vm.lastMessage ? _c('span', {
    staticClass: "message-time"
  }, [_c('app-timestamp', {
    attrs: {
      "stamp": _vm.lastMessage.time,
      "format": "short"
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.lastMessage && !_vm.lastMessage.isRead ? _c('div', {
    staticClass: "unread-count"
  }, [_c('span', {
    staticClass: "unread-badge"
  }, [_vm._v("1")])]) : _vm._e(), _vm._v(" "), _c('text-contact-actions', {
    attrs: {
      "phone": _vm.thread.phone,
      "contact": _vm.currentContact
    }
  })], 1)])])])]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-thread.vue?vue&type=template&id=0c2d55b0&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Common/app-avatar.vue + 4 modules
var app_avatar = __webpack_require__(189);

// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Pages/Text/text-contact-actions.vue + 4 modules
var text_contact_actions = __webpack_require__(1590);

// EXTERNAL MODULE: ./components/Common/app-btn.vue + 4 modules
var app_btn = __webpack_require__(495);

// EXTERNAL MODULE: ./components/Pages/Text/text-avatar.vue + 4 modules
var text_avatar = __webpack_require__(1636);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-thread.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








/* harmony default export */ var text_threadvue_type_script_lang_js = ({
  name: "text-thread",
  components: {
    TextAvatar: text_avatar["a" /* default */],
    AppBtn: app_btn["a" /* default */],
    TextContactActions: text_contact_actions["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppAvatar: app_avatar["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */]
  },
  props: {
    thread: {
      type: Object,
      required: true
    },
    expanded: {
      type: Boolean,
      default: false
    },
    hideMessagePreview: {
      type: Boolean,
      default: false
    }
  },
  mixins: [ContactFormMixin["a" /* default */]],
  methods: {
    view: function view(phone) {
      this.$router.push("/phone/thread/".concat(phone));
    }
  },
  computed: _objectSpread({
    currentContact: function currentContact() {
      return this.keyedContacts[this.thread.phone];
    },
    lastMessage: function lastMessage() {
      return this.thread.last;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    contacts: 'text/contacts',
    keyedContacts: 'text/keyedContacts'
    // messageThread: 'text/messageThread',
  }))
});
// CONCATENATED MODULE: ./components/Pages/Text/text-thread.vue?vue&type=script&lang=js
 /* harmony default export */ var Text_text_threadvue_type_script_lang_js = (text_threadvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Text/text-thread.vue?vue&type=style&index=0&id=0c2d55b0&prod&lang=scss&scoped=true
var text_threadvue_type_style_index_0_id_0c2d55b0_prod_lang_scss_scoped_true = __webpack_require__(1928);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-thread.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_threadvue_type_script_lang_js,
  text_threadvue_type_template_id_0c2d55b0_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "0c2d55b0",
  null
  
)

/* harmony default export */ var text_thread = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1732:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1929);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("41faa0c5", content, true, {"sourceMap":false});

/***/ }),

/***/ 1928:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_thread_vue_vue_type_style_index_0_id_0c2d55b0_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1732);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_thread_vue_vue_type_style_index_0_id_0c2d55b0_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_thread_vue_vue_type_style_index_0_id_0c2d55b0_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1929:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".thread-card[data-v-0c2d55b0]{background:linear-gradient(135deg,#1c1c1e,#2c2c2e);border:1px solid hsla(0,0%,100%,.1);border-radius:8px;overflow:hidden;padding:10px 12px;position:relative;transition:all .2s ease}.thread-card[data-v-0c2d55b0]:hover{background:linear-gradient(135deg,#2c2c2e,#3c3c3e);border-color:hsla(0,0%,100%,.2)}.thread-card.thread-unread[data-v-0c2d55b0]{background:linear-gradient(135deg,#1a1a2e,#2a2a3e);border-left:3px solid #007aff}.thread-content[data-v-0c2d55b0]{align-items:flex-start;display:flex;gap:8px}.thread-avatar[data-v-0c2d55b0]{flex-shrink:0;position:relative}.thread-avatar .unread-indicator[data-v-0c2d55b0]{background:#007aff;border:2px solid #1c1c1e;border-radius:50%;box-shadow:0 0 0 1px rgba(0,122,255,.3);height:10px;position:absolute;right:-1px;top:-1px;width:10px}.thread-info[data-v-0c2d55b0]{flex:1;min-width:0}.thread-header[data-v-0c2d55b0]{align-items:flex-start;display:flex;justify-content:space-between}.contact-info[data-v-0c2d55b0]{flex:1;min-width:0}.contact-name[data-v-0c2d55b0]{color:#fff;font-size:14px;font-weight:600;line-height:1.2;margin-bottom:1px}.contact-name .phone-number[data-v-0c2d55b0]{font-weight:500}.thread-meta[data-v-0c2d55b0]{align-items:center;display:flex;flex-shrink:0;gap:4px}.thread-meta .meta-icon[data-v-0c2d55b0]{opacity:.8}.thread-meta .message-time[data-v-0c2d55b0]{color:#8e8e93;font-size:11px;white-space:nowrap}.message-preview[data-v-0c2d55b0]{margin-top:1px}.message-preview .message-sender[data-v-0c2d55b0]{color:#8e8e93;font-size:12px;font-weight:500}.message-preview .message-text[data-v-0c2d55b0]{color:#8e8e93;display:-webkit-box;font-size:12px;line-height:1.3;-webkit-line-clamp:1;-webkit-box-orient:vertical;overflow:hidden}.message-preview .message-text[data-v-0c2d55b0] .markdown-content p{display:inline;margin:0}.message-preview .message-text[data-v-0c2d55b0] .markdown-content *{color:inherit!important;font-size:inherit!important}.message-preview .message-embed[data-v-0c2d55b0]{align-items:center;color:#8e8e93;display:flex;font-size:12px;font-style:italic}.unread-count[data-v-0c2d55b0]{flex-shrink:0}.unread-count .unread-badge[data-v-0c2d55b0]{background:#007aff;border-radius:8px;box-shadow:0 2px 4px rgba(0,122,255,.3);color:#fff;display:inline-block;font-size:10px;font-weight:600;min-width:16px;padding:1px 5px;text-align:center}@media(max-width:768px){.thread-card[data-v-0c2d55b0]{padding:12px}.contact-name[data-v-0c2d55b0]{font-size:15px}.message-preview .message-text[data-v-0c2d55b0]{font-size:13px}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

}]);