(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[194],{

/***/ 1737:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1939);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("0c888a8e", content, true, {"sourceMap":false});

/***/ }),

/***/ 1938:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5726321f_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1737);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5726321f_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_5726321f_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1939:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".settings-container[data-v-5726321f]{background:linear-gradient(135deg,#0a0a0a,#1a1a1a);color:#fff;min-height:100vh}.settings-header[data-v-5726321f]{align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:rgba(0,0,0,.8);border-bottom:1px solid #333;display:flex;min-height:60px;padding:16px 20px}.back-btn[data-v-5726321f]{margin-right:16px}.settings-title[data-v-5726321f]{color:#fff;font-size:20px;font-weight:700;margin:0;text-shadow:0 1px 2px rgba(0,0,0,.5)}.categories-list .settings-title[data-v-5726321f]{font-size:24px;font-weight:800;text-align:center;width:100%}.categories-grid[data-v-5726321f]{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));padding:20px;grid-gap:16px;gap:16px}.category-card[data-v-5726321f]{background:linear-gradient(145deg,#1e1e1e,#2a2a2a);border:1px solid #333;border-radius:16px;box-shadow:0 4px 12px rgba(0,0,0,.4);cursor:pointer;overflow:hidden;position:relative;transition:all .3s ease}.category-card[data-v-5726321f]:hover{border-color:#555;box-shadow:0 8px 24px rgba(0,0,0,.6);transform:translateY(-2px)}.category-card[data-v-5726321f]:active{transform:translateY(0)}.category-card-inner[data-v-5726321f]{align-items:center;display:flex;gap:16px;min-height:80px;padding:20px}.category-icon-section[data-v-5726321f]{flex-shrink:0}.category-icon-bg[data-v-5726321f]{align-items:center;background:hsla(0,0%,100%,.05);border:2px solid;border-radius:12px;display:flex;height:48px;justify-content:center;width:48px}.category-text-section[data-v-5726321f]{flex:1;min-width:0}.category-name[data-v-5726321f]{color:#fff;font-size:18px;font-weight:600;line-height:1.3;margin:0 0 4px}.category-count[data-v-5726321f]{color:#999;font-size:14px;line-height:1.2;margin:0}.category-arrow[data-v-5726321f]{flex-shrink:0;opacity:.6;transition:opacity .2s ease}.category-card:hover .category-arrow[data-v-5726321f]{opacity:1}.settings-detail-content[data-v-5726321f]{padding:20px}.setting-row[data-v-5726321f]{background:linear-gradient(145deg,#1e1e1e,#2a2a2a);border:1px solid #333;border-radius:12px;box-shadow:0 2px 8px rgba(0,0,0,.3);margin-bottom:12px;transition:all .2s ease}.setting-row[data-v-5726321f]:hover{border-color:#555;box-shadow:0 4px 12px rgba(0,0,0,.4)}.setting-row-inner[data-v-5726321f]{align-items:flex-start;display:flex;gap:16px;padding:16px 20px}.setting-icon-area[data-v-5726321f]{flex-shrink:0;margin-top:2px}.setting-icon-bg[data-v-5726321f]{align-items:center;background:hsla(0,0%,100%,.05);border:2px solid;border-radius:8px;display:flex;height:36px;justify-content:center;width:36px}.setting-details[data-v-5726321f]{flex:1;min-width:0}.setting-label[data-v-5726321f]{align-items:center;color:#fff;display:flex;flex-wrap:wrap;font-size:17px;font-weight:600;gap:8px;line-height:1.3;margin-bottom:6px}.inline-save-indicator[data-v-5726321f]{align-items:center;animation:fadeIn-5726321f .3s ease;background:hsla(0,0%,100%,.1);border-radius:12px;display:flex;font-size:12px;font-weight:500;margin-left:auto;padding:2px 8px}.save-indicator-text[data-v-5726321f]{color:#fff;font-size:11px;letter-spacing:.5px;text-transform:uppercase}@keyframes fadeIn-5726321f{0%{opacity:0;transform:translateY(-5px)}to{opacity:1;transform:translateY(0)}}.admin-tag[data-v-5726321f]{background:#ff453a;border-radius:4px;color:#fff;font-size:10px;font-weight:700;letter-spacing:.5px;padding:2px 6px;text-transform:uppercase}.setting-description[data-v-5726321f]{color:#999;font-size:14px;line-height:1.4;margin-bottom:12px}.setting-control[data-v-5726321f]{margin-top:8px}@media (max-width:600px){.settings-header[data-v-5726321f]{min-height:56px;padding:12px 16px}.settings-title[data-v-5726321f]{font-size:18px}.categories-list .settings-title[data-v-5726321f]{font-size:22px}.categories-grid[data-v-5726321f]{gap:12px;grid-template-columns:1fr;padding:16px}.category-card-inner[data-v-5726321f]{min-height:70px;padding:16px}.category-icon-bg[data-v-5726321f]{height:40px;width:40px}.category-name[data-v-5726321f]{font-size:16px}.category-count[data-v-5726321f]{font-size:13px}.settings-list-container[data-v-5726321f]{padding:16px}.setting-row-inner[data-v-5726321f]{gap:12px;padding:14px 16px}.setting-icon-bg[data-v-5726321f]{height:32px;width:32px}.setting-label[data-v-5726321f]{font-size:16px}.setting-description[data-v-5726321f]{font-size:13px}.inline-save-indicator[data-v-5726321f]{font-size:11px;padding:1px 6px}.save-indicator-text[data-v-5726321f]{font-size:10px}.save-section[data-v-5726321f]{padding:16px}.save-button[data-v-5726321f]{font-size:15px;height:48px}}@media (min-width:601px) and (max-width:1024px){.categories-grid[data-v-5726321f]{grid-template-columns:repeat(auto-fit,minmax(320px,1fr))}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2181:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/settings/index.vue?vue&type=template&id=5726321f&scoped=true








var settingsvue_type_template_id_5726321f_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "settings-container"
  }, [_vm.selectedCategory ? _c('div', {
    staticClass: "category-detail"
  }, [_c('div', {
    staticClass: "settings-header"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "back-btn",
    attrs: {
      "icon": ""
    },
    on: {
      "click": function click($event) {
        _vm.selectedCategory = null;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "white"
    }
  }, [_vm._v("fa-solid fa-chevron-left")])], 1), _vm._v(" "), _c('h2', {
    staticClass: "settings-title"
  }, [_vm._v(_vm._s(_vm.selectedCategory.name))])], 1), _vm._v(" "), _c('div', {
    staticClass: "settings-detail-content"
  }, _vm._l(_vm.settings.filter(function (s) {
    return s.category === _vm.selectedCategory.name;
  }), function (setting) {
    return _c('div', {
      key: setting.id,
      staticClass: "setting-row"
    }, [_c('div', {
      staticClass: "setting-row-inner"
    }, [_c('div', {
      staticClass: "setting-icon-area"
    }, [_c('div', {
      staticClass: "setting-icon-bg",
      style: {
        backgroundColor: setting.color + '20',
        borderColor: setting.color
      }
    }, [_c(VIcon["a" /* default */], {
      attrs: {
        "color": setting.color,
        "size": "18"
      }
    }, [_vm._v("\n                " + _vm._s(setting.icon) + "\n              ")])], 1)]), _vm._v(" "), _c('div', {
      staticClass: "setting-details"
    }, [_c('div', {
      staticClass: "setting-label"
    }, [_vm._v("\n              " + _vm._s(setting.label) + "\n              "), setting.staff_only ? _c('span', {
      staticClass: "admin-tag"
    }, [_vm._v("\n                ADMIN\n              ")]) : _vm._e(), _vm._v(" "), _vm.getSettingStatus(setting.identifier) !== 'saved' ? _c('div', {
      staticClass: "inline-save-indicator"
    }, [_c(VIcon["a" /* default */], {
      staticClass: "mr-1",
      attrs: {
        "color": _vm.getSettingStatusColor(setting.identifier),
        "size": "14"
      }
    }, [_vm._v("\n                  " + _vm._s(_vm.getSettingStatusIcon(setting.identifier)) + "\n                ")]), _vm._v(" "), _c('span', {
      staticClass: "save-indicator-text"
    }, [_vm._v(_vm._s(_vm.getSettingStatusText(setting.identifier)))])], 1) : _vm._e()]), _vm._v(" "), setting.description ? _c('div', {
      staticClass: "setting-description"
    }, [_c('app-markdown-view', {
      attrs: {
        "source": setting.description
      }
    })], 1) : _vm._e(), _vm._v(" "), _c('div', {
      staticClass: "setting-control"
    }, [_c('crudy-form-field', {
      attrs: {
        "field": setting,
        "type": setting.field_type
      }
    })], 1)])])]);
  }), 0)]) : _c('div', {
    staticClass: "categories-list"
  }, [_vm._m(0), _vm._v(" "), _c('div', {
    staticClass: "categories-grid"
  }, _vm._l(_vm.categories, function (category) {
    return _c('div', {
      key: category.name,
      staticClass: "category-card",
      on: {
        "click": function click($event) {
          return _vm.selectCategory(category);
        }
      }
    }, [_c('div', {
      staticClass: "category-card-inner"
    }, [_c('div', {
      staticClass: "category-icon-section"
    }, [_c('div', {
      staticClass: "category-icon-bg",
      style: {
        backgroundColor: category.color + '20',
        borderColor: category.color
      }
    }, [_c(VIcon["a" /* default */], {
      attrs: {
        "color": category.color,
        "size": "24"
      }
    }, [_vm._v("\n                " + _vm._s(category.icon) + "\n              ")])], 1)]), _vm._v(" "), _c('div', {
      staticClass: "category-text-section"
    }, [_c('h3', {
      staticClass: "category-name"
    }, [_vm._v(_vm._s(category.name))]), _vm._v(" "), _c('p', {
      staticClass: "category-count"
    }, [_vm._v(_vm._s(category.settingsCount) + " setting" + _vm._s(category.settingsCount !== 1 ? 's' : ''))])]), _vm._v(" "), _c('div', {
      staticClass: "category-arrow"
    }, [_c(VIcon["a" /* default */], {
      attrs: {
        "color": "#666",
        "size": "14"
      }
    }, [_vm._v("fa-solid fa-chevron-right")])], 1)])]);
  }), 0)])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "settings-header"
  }, [_c('h1', {
    staticClass: "settings-title"
  }, [_vm._v("Settings")])]);
}];

// CONCATENATED MODULE: ./pages/settings/index.vue?vue&type=template&id=5726321f&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.splice.js
var es_array_splice = __webpack_require__(133);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.map.js
var es_map = __webpack_require__(391);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.values.js
var es_object_values = __webpack_require__(316);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.delete-all.js
var esnext_map_delete_all = __webpack_require__(392);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.every.js
var esnext_map_every = __webpack_require__(393);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.filter.js
var esnext_map_filter = __webpack_require__(394);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.find.js
var esnext_map_find = __webpack_require__(395);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.find-key.js
var esnext_map_find_key = __webpack_require__(396);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.includes.js
var esnext_map_includes = __webpack_require__(397);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.key-of.js
var esnext_map_key_of = __webpack_require__(398);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.map-keys.js
var esnext_map_map_keys = __webpack_require__(399);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.map-values.js
var esnext_map_map_values = __webpack_require__(400);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.merge.js
var esnext_map_merge = __webpack_require__(401);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.reduce.js
var esnext_map_reduce = __webpack_require__(402);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.some.js
var esnext_map_some = __webpack_require__(403);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.update.js
var esnext_map_update = __webpack_require__(404);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-field.vue + 4 modules
var crudy_form_field = __webpack_require__(504);

// EXTERNAL MODULE: ./utils/store-accessor.ts
var store_accessor = __webpack_require__(27);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/settings/index.vue?vue&type=script&lang=js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }










































/* harmony default export */ var settingsvue_type_script_lang_js = ({
  name: 'GeneralSettings',
  components: {
    AppMarkdownView: app_markdown_view["a" /* default */],
    CrudyFormField: crudy_form_field["a" /* default */]
  },
  data: function data() {
    return {
      selectedCategory: null,
      settings: [],
      originalValues: {},
      saving: false,
      saveStatus: 'saved',
      // Global save status for bottom indicator
      settingStatuses: {},
      // Individual setting statuses: { identifier: 'saved'|'saving'|'pending'|'error' }
      debounceTimers: {},
      lastSaveTime: null,
      settingSaveTimes: {} // Track last save time for each setting
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _this.loadSettings();
          case 2:
            _this.setupAutoSave();
          case 3:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  beforeDestroy: function beforeDestroy() {
    window.$events.$off('crudy:form:field:bounced');
    // Clear any pending debounce timers
    Object.values(this.debounceTimers).forEach(function (timer) {
      return clearTimeout(timer);
    });
  },
  methods: {
    loadSettings: function loadSettings() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var item, key, setting, _iterator, _step, _setting;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.next = 2;
              return _this2.$axios.$get('/person/settings/list');
            case 2:
              _this2.settings = _context2.sent;
              item = {}; // Filter out admin settings for non-admin users
              for (key in _this2.settings) {
                setting = _this2.settings[key];
                if (!_this2.user.admin && setting.staff_only) {
                  _this2.settings.splice(key, 1);
                }
              }

              // Process settings
              _iterator = _createForOfIteratorHelper(_this2.settings);
              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  _setting = _step.value;
                  _setting.label = _setting.name;
                  _setting.name = _setting.identifier;
                  item[_setting.identifier] = _setting.value;
                  _this2.originalValues[_setting.identifier] = _setting.value;
                  if (_setting.field_type === 'boolean' && _setting.value == 1) {
                    _this2.originalValues[_setting.id] = true;
                  } else if (_setting.field_type === 'boolean' && _setting.value == 0) {
                    _this2.originalValues[_setting.id] = false;
                  }
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
              store_accessor["b" /* CrudyFormStore */].setItemValues(item);
            case 8:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    selectCategory: function selectCategory(category) {
      this.selectedCategory = category;
    },
    setupAutoSave: function setupAutoSave() {
      // Listen for form field changes (bounced events from Crudy form system)
      window.$events.$on('crudy:form:field:bounced', this.handleFieldChange);
    },
    handleFieldChange: function handleFieldChange(data) {
      var fieldName = data.fieldName,
        value = data.value;
      var setting = this.settings.find(function (s) {
        return s.identifier === fieldName;
      });
      if (!setting) return;

      // Check if value actually changed
      if (this.originalValues[setting.identifier] === value) {
        return;
      }

      // Determine if this is a text field that needs debouncing
      var textFieldTypes = ['text', 'textarea', 'email', 'url', 'password'];
      var needsDebounce = textFieldTypes.includes(setting.field_type);
      if (needsDebounce) {
        this.debouncedAutoSave(setting.identifier, value);
      } else {
        // Immediate save for non-text fields (checkboxes, selects, etc.)
        this.autoSave(setting.identifier, value);
      }
    },
    debouncedAutoSave: function debouncedAutoSave(identifier, value) {
      var _this3 = this;
      // Clear existing timer for this field
      if (this.debounceTimers[identifier]) {
        clearTimeout(this.debounceTimers[identifier]);
      }

      // Set individual setting status to pending
      this.$set(this.settingStatuses, identifier, 'pending');
      this.updateGlobalSaveStatus();

      // Set new timer
      this.debounceTimers[identifier] = setTimeout(function () {
        _this3.autoSave(identifier, value);
        delete _this3.debounceTimers[identifier];
      }, 1000); // 1 second debounce
    },
    autoSave: function autoSave(identifier, value) {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var payload;
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.prev = 0;
              // Set individual setting status to saving
              _this4.$set(_this4.settingStatuses, identifier, 'saving');
              _this4.updateGlobalSaveStatus();
              payload = {};
              if (value === '') {
                payload[identifier] = 'removed';
              } else {
                payload[identifier] = value;
              }
              _context3.next = 7;
              return _this4.$axios.$post('/person/settings/update', payload);
            case 7:
              // Update original value
              _this4.originalValues[identifier] = value;

              // Set individual setting status to saved
              _this4.$set(_this4.settingStatuses, identifier, 'saved');
              _this4.$set(_this4.settingSaveTimes, identifier, new Date());
              _this4.lastSaveTime = new Date();
              _this4.updateGlobalSaveStatus();

              // Refresh user data
              _this4.$store.dispatch('auth/fetchUser', _this4.$axios);
              _this4.$store.dispatch('nui/syncSelfForce');

              // Clear the saved status after 3 seconds to clean up the UI
              setTimeout(function () {
                if (_this4.settingStatuses[identifier] === 'saved') {
                  _this4.$delete(_this4.settingStatuses, identifier);
                  _this4.updateGlobalSaveStatus();
                }
              }, 3000);
              _context3.next = 24;
              break;
            case 17:
              _context3.prev = 17;
              _context3.t0 = _context3["catch"](0);
              // Set individual setting status to error
              _this4.$set(_this4.settingStatuses, identifier, 'error');
              _this4.updateGlobalSaveStatus();
              _this4.$toast.error('Failed to save setting');
              console.error('Auto-save error:', _context3.t0);

              // Clear error status after 5 seconds
              setTimeout(function () {
                if (_this4.settingStatuses[identifier] === 'error') {
                  _this4.$delete(_this4.settingStatuses, identifier);
                  _this4.updateGlobalSaveStatus();
                }
              }, 5000);
            case 24:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[0, 17]]);
      }))();
    },
    updateGlobalSaveStatus: function updateGlobalSaveStatus() {
      var statuses = Object.values(this.settingStatuses);
      if (statuses.includes('error')) {
        this.saveStatus = 'error';
      } else if (statuses.includes('saving')) {
        this.saveStatus = 'saving';
      } else if (statuses.includes('pending')) {
        this.saveStatus = 'pending';
      } else {
        this.saveStatus = 'saved';
      }
    },
    getTimeAgo: function getTimeAgo(date) {
      var now = new Date();
      var diffInSeconds = Math.floor((now - date) / 1000);
      if (diffInSeconds < 60) {
        return 'just now';
      } else if (diffInSeconds < 3600) {
        var minutes = Math.floor(diffInSeconds / 60);
        return "".concat(minutes, " minute").concat(minutes !== 1 ? 's' : '', " ago");
      } else {
        var hours = Math.floor(diffInSeconds / 3600);
        return "".concat(hours, " hour").concat(hours !== 1 ? 's' : '', " ago");
      }
    },
    // Individual setting status methods
    getSettingStatus: function getSettingStatus(identifier) {
      return this.settingStatuses[identifier] || 'saved';
    },
    getSettingStatusIcon: function getSettingStatusIcon(identifier) {
      var status = this.getSettingStatus(identifier);
      switch (status) {
        case 'saving':
          return 'fa-solid fa-spinner fa-spin';
        case 'pending':
          return 'fa-solid fa-clock';
        case 'error':
          return 'fa-solid fa-exclamation-triangle';
        case 'saved':
        default:
          return 'fa-solid fa-check';
      }
    },
    getSettingStatusColor: function getSettingStatusColor(identifier) {
      var status = this.getSettingStatus(identifier);
      switch (status) {
        case 'saving':
          return '#2196F3';
        case 'pending':
          return '#FF9800';
        case 'error':
          return '#F44336';
        case 'saved':
        default:
          return '#4CAF50';
      }
    },
    getSettingStatusText: function getSettingStatusText(identifier) {
      var status = this.getSettingStatus(identifier);
      switch (status) {
        case 'saving':
          return 'Saving...';
        case 'pending':
          return 'Pending';
        case 'error':
          return 'Failed';
        case 'saved':
        default:
          return 'Saved';
      }
    }
  },
  computed: _objectSpread(_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    mode: 'system/mode'
  })), {}, {
    categories: function categories() {
      var _this5 = this;
      var categoryMap = new Map();
      var _iterator2 = _createForOfIteratorHelper(this.settings),
        _step2;
      try {
        var _loop = function _loop() {
          var setting = _step2.value;
          if (setting.category.includes('Bad')) return 1; // continue
          if (!categoryMap.has(setting.category)) {
            // Find a representative setting for icon and color
            var representativeSetting = _this5.settings.find(function (s) {
              return s.category === setting.category;
            });
            categoryMap.set(setting.category, {
              name: setting.category,
              icon: (representativeSetting === null || representativeSetting === void 0 ? void 0 : representativeSetting.icon) || 'fa-solid fa-cog',
              color: (representativeSetting === null || representativeSetting === void 0 ? void 0 : representativeSetting.color) || '#666',
              settingsCount: 0
            });
          }
          var category = categoryMap.get(setting.category);
          category.settingsCount++;
        };
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          if (_loop()) continue;
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return Array.from(categoryMap.values());
    },
    item: function item() {
      return store_accessor["b" /* CrudyFormStore */].item;
    },
    saveStatusIcon: function saveStatusIcon() {
      switch (this.saveStatus) {
        case 'saving':
          return 'fa-solid fa-spinner fa-spin';
        case 'pending':
          return 'fa-solid fa-clock';
        case 'error':
          return 'fa-solid fa-exclamation-triangle';
        case 'saved':
        default:
          return 'fa-solid fa-check';
      }
    },
    saveStatusColor: function saveStatusColor() {
      switch (this.saveStatus) {
        case 'saving':
          return '#2196F3';
        case 'pending':
          return '#FF9800';
        case 'error':
          return '#F44336';
        case 'saved':
        default:
          return '#4CAF50';
      }
    },
    saveStatusText: function saveStatusText() {
      switch (this.saveStatus) {
        case 'saving':
          return 'Saving...';
        case 'pending':
          return 'Changes pending...';
        case 'error':
          return 'Save failed';
        case 'saved':
        default:
          if (this.lastSaveTime) {
            var timeAgo = this.getTimeAgo(this.lastSaveTime);
            return "Saved ".concat(timeAgo);
          }
          return 'All changes saved';
      }
    }
  })
});
// CONCATENATED MODULE: ./pages/settings/index.vue?vue&type=script&lang=js
 /* harmony default export */ var pages_settingsvue_type_script_lang_js = (settingsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/settings/index.vue?vue&type=style&index=0&id=5726321f&prod&scoped=true&lang=css
var settingsvue_type_style_index_0_id_5726321f_prod_scoped_true_lang_css = __webpack_require__(1938);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/settings/index.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_settingsvue_type_script_lang_js,
  settingsvue_type_template_id_5726321f_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "5726321f",
  null
  
)

/* harmony default export */ var settings = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);