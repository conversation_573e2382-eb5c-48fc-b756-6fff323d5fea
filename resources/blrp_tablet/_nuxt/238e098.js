(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[11],{

/***/ 2234:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/ColumnDocumentActions.vue?vue&type=template&id=8f85b28e&scoped=true
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('span', {
    staticClass: "text-primary font-weight-bold",
    staticStyle: {
      "cursor": "pointer"
    },
    on: {
      "click": function click($event) {
        return _vm.openDocument(_vm.item.game_document_id);
      }
    }
  }, [_vm._v("\n    View\n  ")]), _vm._v(" "), _c('span', {
    staticClass: "text-warning font-weight-bold",
    staticStyle: {
      "margin-left": "12px",
      "cursor": "pointer"
    },
    on: {
      "click": function click($event) {
        return _vm.printDocument(_vm.item.game_document_id);
      }
    }
  }, [_vm._v("\n    Print\n  ")])]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/parts/ColumnDocumentActions.vue?vue&type=template&id=8f85b28e&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./config/factions.js
var factions = __webpack_require__(279);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/ColumnDocumentActions.vue?vue&type=script&lang=js







function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var ColumnDocumentActionsvue_type_script_lang_js = ({
  name: "ColumnDocumentActions",
  props: ['value', 'item'],
  methods: {
    openDocument: function openDocument(id) {
      fetch("https://blrp_tablet/openDocument", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: id
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    },
    printDocument: function printDocument(id) {
      fetch("https://blrp_tablet/printDocument", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: id
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    'user': 'auth/user',
    'hasAnyGroup': 'auth/hasAnyGroup'
  }))
});
// CONCATENATED MODULE: ./components/Common/parts/ColumnDocumentActions.vue?vue&type=script&lang=js
 /* harmony default export */ var parts_ColumnDocumentActionsvue_type_script_lang_js = (ColumnDocumentActionsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/parts/ColumnDocumentActions.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  parts_ColumnDocumentActionsvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "8f85b28e",
  null
  
)

/* harmony default export */ var ColumnDocumentActions = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);