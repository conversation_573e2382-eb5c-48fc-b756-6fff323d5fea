(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[17],{

/***/ 2238:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/RowNoteLink.vue?vue&type=template&id=0dba9a72&scoped=true
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-markdown-view', {
    staticClass: "mt-4",
    attrs: {
      "source": _vm.note.contents
    }
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/parts/RowNoteLink.vue?vue&type=template&id=0dba9a72&scoped=true

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-action-btn.vue + 4 modules
var app_action_btn = __webpack_require__(63);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/RowNoteLink.vue?vue&type=script&lang=js


/* harmony default export */ var RowNoteLinkvue_type_script_lang_js = ({
  name: "RowNoteLink",
  components: {
    AppActionBtn: app_action_btn["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */]
  },
  props: ['data'],
  computed: {
    note: function note() {
      return this.data.item.note;
    }
  }
});
// CONCATENATED MODULE: ./components/Common/parts/RowNoteLink.vue?vue&type=script&lang=js
 /* harmony default export */ var parts_RowNoteLinkvue_type_script_lang_js = (RowNoteLinkvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/parts/RowNoteLink.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  parts_RowNoteLinkvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "0dba9a72",
  null
  
)

/* harmony default export */ var RowNoteLink = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);