(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[55],{

/***/ 1572:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5);
var render, staticRenderFns
var script = {}


/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1679:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1763);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("14017390", content, true, {"sourceMap":false});

/***/ }),

/***/ 1762:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_dyanmic_settings_vue_vue_type_style_index_0_id_d84b45de_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1679);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_dyanmic_settings_vue_vue_type_style_index_0_id_d84b45de_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_dyanmic_settings_vue_vue_type_style_index_0_id_d84b45de_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1763:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".business-setting{background-color:#131313;border-bottom:.1px solid #202020;border-top:.1px solid #202020;box-shadow:0 0 .25em rgba(67,71,85,.27),0 .25em 1em rgba(90,125,188,.05);margin-bottom:10px;margin-top:10px;padding-bottom:10px;padding-top:10px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2027:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/settings.vue?vue&type=template&id=5f1a0a84


var settingsvue_type_template_id_5f1a0a84_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VAlert["a" /* default */], [_vm._v("\n    Only shown to members with manage computer permissions\n  ")]), _vm._v(" "), _c('app-dynamic-settings', {
    attrs: {
      "list-endpoint": "/business/settings/list/".concat(this.business.id),
      "save-endpoint": "/business/settings/update/".concat(this.business.id)
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/business/_id/settings.vue?vue&type=template&id=5f1a0a84

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabItem.js + 1 modules
var VTabItem = __webpack_require__(1554);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabsItems.js + 1 modules
var VTabsItems = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-dyanmic-settings.vue?vue&type=template&id=d84b45de















var app_dyanmic_settingsvue_type_template_id_d84b45de_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VTabs["a" /* default */], {
    model: {
      value: _vm.tab,
      callback: function callback($$v) {
        _vm.tab = $$v;
      },
      expression: "tab"
    }
  }, _vm._l(_vm.categories, function (cat) {
    return _c(VTab["a" /* default */], {
      key: cat,
      attrs: {
        "title": cat
      }
    }, [_vm._v("\n        " + _vm._s(cat) + "\n      ")]);
  }), 1), _vm._v(" "), _c(VTabsItems["a" /* default */], {
    staticStyle: {
      "background-color": "transparent"
    },
    model: {
      value: _vm.tab,
      callback: function callback($$v) {
        _vm.tab = $$v;
      },
      expression: "tab"
    }
  }, _vm._l(_vm.categories, function (cat, index) {
    return _c(VTabItem["a" /* default */], {
      key: index
    }, _vm._l(_vm.settings.filter(function (s) {
      return s.category === cat;
    }), function (setting) {
      return _c(VCard["a" /* default */], {
        key: setting.id,
        staticClass: "mt-3",
        attrs: {
          "color": "#1c1c1c"
        }
      }, [_c(components_VCard["b" /* VCardText */], [_c(VRow["a" /* default */], [_vm.mode === 'tablet' ? _c(VCol["a" /* default */], {
        style: "color: ".concat(setting.color),
        attrs: {
          "cols": "2",
          "align-self": "center",
          "align": "center"
        }
      }, [_c(VIcon["a" /* default */], {
        staticStyle: {
          "font-size": "96px"
        },
        attrs: {
          "color": setting.color,
          "x-large": ""
        }
      }, [_vm._v("\n                  " + _vm._s(setting.icon) + "\n                ")])], 1) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c('div', {
        staticClass: "setting-name mb-3 text-white-50",
        staticStyle: {
          "font-size": "20px"
        }
      }, [_c('i', {
        staticClass: "mr-3",
        class: setting.icon,
        style: "color: ".concat(setting.color)
      }), _vm._v("\n                  " + _vm._s(setting.label) + "\n                  "), setting.staff_only ? _c('span', {
        staticStyle: {
          "color": "red"
        }
      }, [_vm._v("\n                    (Admin Setting)\n                  ")]) : _vm._e()]), _vm._v(" "), _c('div', {
        staticClass: "setting-desc text-muted",
        staticStyle: {
          "font-size": "15px"
        }
      }, [_c('app-markdown-view', {
        attrs: {
          "source": setting.description
        }
      })], 1), _vm._v(" "), _c('div', {
        staticClass: "setting-field mt-4"
      }, [_c('crudy-form-field', {
        attrs: {
          "field": setting,
          "type": setting.field_type
        }
      })], 1)])], 1)], 1)], 1);
    }), 1);
  }), 1), _vm._v(" "), _c('div', {
    staticClass: "p-4"
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        $event.preventDefault();
        return _vm.save.apply(null, arguments);
      }
    }
  }, [_vm._v("\n        Save Changes\n      ")])], 1)], 1);
};
var app_dyanmic_settingsvue_type_template_id_d84b45de_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-dyanmic-settings.vue?vue&type=template&id=d84b45de

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(37);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(21);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.splice.js
var es_array_splice = __webpack_require__(133);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.entries.js
var es_object_entries = __webpack_require__(134);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./components/Common/Loader.vue + 4 modules
var Loader = __webpack_require__(190);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/Pages/Business/post-item.vue
var post_item = __webpack_require__(1572);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-field.vue + 4 modules
var crudy_form_field = __webpack_require__(504);

// EXTERNAL MODULE: ./utils/store-accessor.ts
var store_accessor = __webpack_require__(27);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-dyanmic-settings.vue?vue&type=script&lang=js




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }




























/* harmony default export */ var app_dyanmic_settingsvue_type_script_lang_js = ({
  name: 'app-dynamic-settings',
  components: {
    CrudyFormField: crudy_form_field["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppPage: AppPage["a" /* default */],
    PostItem: post_item["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */],
    Loader: Loader["a" /* default */]
  },
  props: ['listEndpoint', 'saveEndpoint', 'filter'],
  data: function data() {
    return {
      tab: 0,
      originalValues: {},
      settings: []
    };
  },
  mounted: function mounted() {
    this.index();

    // window.$events.$on('crudy:form:field:bounced', ({ fieldName, value }) => {
    //   this.save()
    // })
  },
  beforeDestroy: function beforeDestroy() {
    window.$events.$off('crudy:form:field:bounced');
  },
  methods: {
    index: function index() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        var item, key, setting, _iterator, _step, _setting;
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.$get(_this.listEndpoint);
            case 2:
              _this.settings = _context.sent;
              item = {};
              for (key in _this.settings) {
                setting = _this.settings[key];
                if (!_this.user.admin) {
                  if (setting.staff_only) {
                    _this.settings.splice(key, 1);
                  }
                }
              }
              _iterator = _createForOfIteratorHelper(_this.settings);
              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  _setting = _step.value;
                  _setting.label = _setting.name;
                  if (_this.$route.path.includes('business')) {
                    _setting.name = _setting.id;
                    item[_setting.id] = _setting.value;
                    _this.originalValues[_setting.id] = _setting.value;
                  } else {
                    _setting.name = _setting.identifier;
                    item[_setting.identifier] = _setting.value;
                    _this.originalValues[_setting.identifier] = _setting.value;
                  }
                  if (_setting.field_type === 'boolean' && _setting.value == 1) {
                    _this.originalValues[_setting.id] = true;
                  } else if (_setting.field_type === 'boolean' && _setting.value == 0) {
                    _this.originalValues[_setting.id] = false;
                  }
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
              store_accessor["b" /* CrudyFormStore */].setItemValues(item);
            case 8:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    save: function save() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var finalizedItem, _i, _Object$entries, _Object$entries$_i, key, value;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              finalizedItem = {};
              for (_i = 0, _Object$entries = Object.entries(_this2.item); _i < _Object$entries.length; _i++) {
                _Object$entries$_i = Object(slicedToArray["a" /* default */])(_Object$entries[_i], 2), key = _Object$entries$_i[0], value = _Object$entries$_i[1];
                if (_this2.originalValues[key] !== _this2.item[key]) {
                  if (Object(esm_typeof["a" /* default */])(value) !== 'object') {
                    if (value === '') {
                      finalizedItem[key] = 'removed';
                    } else {
                      finalizedItem[key] = value;
                    }
                  }
                  if (!value && typeof value === "boolean") {
                    // finalizedItem[key] = '0'
                  } else if (value && typeof value === "boolean") {
                    // finalizedItem[key] = '1'
                  }
                }
              }
              return _context2.abrupt("return", _this2.$axios.$post(_this2.saveEndpoint, finalizedItem).then(function () {
                _this2.index();
                if (_this2.saveEndpoint.includes('person/settings')) {
                  _this2.$store.dispatch('auth/fetchUser', _this2.$axios);
                  _this2.$store.dispatch('nui/syncSelfForce');
                }
              }));
            case 3:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    }
  },
  computed: _objectSpread({
    categories: function categories() {
      var cats = [];
      var _iterator2 = _createForOfIteratorHelper(this.settings),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var setting = _step2.value;
          if (setting.category.includes('Bad')) continue;
          if (!cats.includes(setting.category)) {
            if (this.filter) {
              if (setting.category.includes(this.filter)) {
                cats.push(setting.category);
              }
            } else {
              cats.push(setting.category);
            }
          }
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return cats;
    },
    item: function item() {
      return store_accessor["b" /* CrudyFormStore */].item;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    mode: 'system/mode',
    business: 'business/business',
    canBeSubmitted: 'crud/canBeSubmitted',
    hasAnyGroup: 'auth/hasAnyGroup'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-dyanmic-settings.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_dyanmic_settingsvue_type_script_lang_js = (app_dyanmic_settingsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-dyanmic-settings.vue?vue&type=style&index=0&id=d84b45de&prod&lang=scss
var app_dyanmic_settingsvue_type_style_index_0_id_d84b45de_prod_lang_scss = __webpack_require__(1762);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-dyanmic-settings.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_dyanmic_settingsvue_type_script_lang_js,
  app_dyanmic_settingsvue_type_template_id_d84b45de_render,
  app_dyanmic_settingsvue_type_template_id_d84b45de_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_dyanmic_settings = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/settings.vue?vue&type=script&lang=js



/* harmony default export */ var settingsvue_type_script_lang_js = ({
  components: {
    AppPage: AppPage["a" /* default */],
    AppDynamicSettings: app_dyanmic_settings
  },
  props: ['business']
});
// CONCATENATED MODULE: ./pages/business/_id/settings.vue?vue&type=script&lang=js
 /* harmony default export */ var _id_settingsvue_type_script_lang_js = (settingsvue_type_script_lang_js); 
// CONCATENATED MODULE: ./pages/business/_id/settings.vue





/* normalize component */

var settings_component = Object(componentNormalizer["a" /* default */])(
  _id_settingsvue_type_script_lang_js,
  settingsvue_type_template_id_5f1a0a84_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var settings = __webpack_exports__["default"] = (settings_component.exports);

/***/ })

}]);