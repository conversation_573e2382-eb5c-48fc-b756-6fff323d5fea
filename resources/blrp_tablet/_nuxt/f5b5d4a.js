(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[20],{

/***/ 2240:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/_archived/games/index.vue?vue&type=template&id=e6132e1e
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "m-4"
  }, [_vm._v("\n  Unfortunately, due to packaging, mazie had to be removed from the tablet. Sad Face.\n")]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/_archived/games/index.vue?vue&type=template&id=e6132e1e

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/_archived/games/index.vue

var script = {}


/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var games = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);