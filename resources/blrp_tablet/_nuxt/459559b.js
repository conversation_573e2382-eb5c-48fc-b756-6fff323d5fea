(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[57],{

/***/ 2069:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/time-logs.vue?vue&type=template&id=18c65fec


var time_logsvue_type_template_id_18c65fec_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VAlert["a" /* default */], [_vm._v("\n    Only shown to members with manage computer permissions. Time logs show an entry for each person, for each time they were clocked in.\n  ")]), _vm._v(" "), _c('crudy-table', {
    staticClass: "mt-4",
    attrs: {
      "name": "BusinessTimeLog",
      "view": "index",
      "filter": {
        business_id: _vm.$route.params.id
      }
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/business/_id/time-logs.vue?vue&type=template&id=18c65fec

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/time-logs.vue?vue&type=script&lang=js

/* harmony default export */ var time_logsvue_type_script_lang_js = ({
  components: {
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['business']
});
// CONCATENATED MODULE: ./pages/business/_id/time-logs.vue?vue&type=script&lang=js
 /* harmony default export */ var _id_time_logsvue_type_script_lang_js = (time_logsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/business/_id/time-logs.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _id_time_logsvue_type_script_lang_js,
  time_logsvue_type_template_id_18c65fec_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var time_logs = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);