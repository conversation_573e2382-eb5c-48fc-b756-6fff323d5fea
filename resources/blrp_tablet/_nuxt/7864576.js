(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[195],{

/***/ 2227:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/singles/audit.vue?vue&type=template&id=38df70fe
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('crudy-table', {
    attrs: {
      "name": "Audit",
      "view": "index",
      "filter": {
        auditable_id: this.$route.query.identifier,
        auditable_type: this.$route.query.model
      }
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/singles/audit.vue?vue&type=template&id=38df70fe

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/singles/audit.vue?vue&type=script&lang=ts

/* harmony default export */ var auditvue_type_script_lang_ts = ({
  components: {
    CrudyTable: crudy_table["a" /* default */]
  },
  data: function data() {
    return {
      audits: []
    };
  },
  mounted: function mounted() {
    var model = this.$route.query.model;
    var identifier = this.$route.query.identifier;
    // this.audits = this.$axios.$get(`/crudy/audit/${model}/${identifier}`)
  }
});
// CONCATENATED MODULE: ./pages/singles/audit.vue?vue&type=script&lang=ts
 /* harmony default export */ var singles_auditvue_type_script_lang_ts = (auditvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/singles/audit.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  singles_auditvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var audit = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);