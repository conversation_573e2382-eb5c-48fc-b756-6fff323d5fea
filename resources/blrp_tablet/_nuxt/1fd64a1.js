(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[18],{

/***/ 1556:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

var render = function render() {
  var _vm$realName, _vm$realView;
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _vm.ready ? _c('crudy-table', {
    attrs: {
      "name": (_vm$realName = _vm.realName) !== null && _vm$realName !== void 0 ? _vm$realName : _vm.name,
      "view": (_vm$realView = _vm.realView) !== null && _vm$realView !== void 0 ? _vm$realView : _vm.view
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-debug.vue + 9 modules
var crudy_form_debug = __webpack_require__(315);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts






/* harmony default export */ var crudy_genvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'crudy-gen',
  components: {
    CrudyFormDebug: crudy_form_debug["a" /* default */],
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['name', 'view', 'filter'],
  data: function data() {
    return {
      meta: null,
      ready: false,
      tableUUID: null,
      realName: null,
      realView: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var name, route;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            name = _this.$route.name;
            _context.next = 3;
            return _this.$router.resolve({
              name: name
            }).route;
          case 3:
            route = _context.sent;
            _this.meta = route === null || route === void 0 ? void 0 : route.meta;
            _this.realName = _this.meta.model;
            _this.realView = _this.meta.view;
            _this.ready = true;
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  }
}));
// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_gen_crudy_genvue_type_script_lang_ts = (crudy_genvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_gen_crudy_genvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_gen = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1562:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Documents/DocumentsList.vue?vue&type=template&id=257a5de0






var DocumentsListvue_type_template_id_257a5de0_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "p-top",
    attrs: {
      "no-gutters": ""
    }
  }, [!_vm.faction_type ? _c(VCol["a" /* default */], {
    staticClass: "ml-1 mb-3",
    attrs: {
      "md": "4"
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        _vm.createModal = true;
      }
    }
  }, [_vm._v("\n        Create Doodly Doc (Contract)\n      ")])], 1) : _vm._e()], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Create Contract",
      "hide-footer": ""
    },
    model: {
      value: _vm.createModal,
      callback: function callback($$v) {
        _vm.createModal = $$v;
      },
      expression: "createModal"
    }
  }, [_c('span', {
    staticClass: "p-top"
  }), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Title"
    }
  }, [_c(VTextField["a" /* default */], {
    model: {
      value: _vm.createForm.TITLE,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "TITLE", $$v);
      },
      expression: "createForm.TITLE"
    }
  })], 1), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Expiration"
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "placeholder": "N/A"
    },
    model: {
      value: _vm.createForm.EXPIRATION,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "EXPIRATION", $$v);
      },
      expression: "createForm.EXPIRATION"
    }
  })], 1), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Details"
    }
  }, [_c(VTextarea["a" /* default */], {
    attrs: {
      "filled": ""
    },
    model: {
      value: _vm.createForm.DETAILS,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "DETAILS", $$v);
      },
      expression: "createForm.DETAILS"
    }
  })], 1), _vm._v(" "), _c('app-form-group', [_c('span', {
    staticClass: "text-primary"
  }, [_vm._v("\n                      The contract will pop up for the "), _c('b', [_vm._v("closest player to you")]), _vm._v(".\n                    "), _c('br'), _vm._v("\n                      Once the player fills and submits the contract it will show up under "), _c('b', {
    staticClass: "text-warning"
  }, [_vm._v("My Documents")]), _vm._v(" "), _c('br'), _vm._v(" "), _c('span', {
    staticClass: "text-muted"
  }, [_vm._v("If the player does not fill or agree to the form, it will not show up")])])]), _vm._v(" "), _c('app-form-group', [_c(VBtn["a" /* default */], {
    attrs: {
      "variant": "success"
    },
    on: {
      "click": _vm.createContract
    }
  }, [_vm._v("\n        Offer to closest player\n      ")])], 1)], 1), _vm._v(" "), _vm.view ? _c('crudy-table', {
    attrs: {
      "name": "Document",
      "view": _vm.view
    }
  }) : _vm.tableFilter ? _c('crudy-table', {
    attrs: {
      "name": "Document",
      "view": "index",
      "filter": _vm.tableFilter
    }
  }) : _vm._e()], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue?vue&type=template&id=257a5de0

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue + 4 modules
var crudy_gen = __webpack_require__(1556);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Documents/DocumentsList.vue?vue&type=script&lang=js







/* harmony default export */ var DocumentsListvue_type_script_lang_js = ({
  name: 'DocumentsList',
  components: {
    CrudyTable: crudy_table["a" /* default */],
    CrudyGen: crudy_gen["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */]
  },
  props: ['faction_type', 'personal_id', 'business_id', 'character_number', 'view'],
  data: function data() {
    return {
      createModal: false,
      tableFilter: null,
      createForm: {
        TITLE: null,
        EXPIRATION: null,
        DETAILS: null
      }
    };
  },
  created: function created() {
    if (this.faction_type) {
      this.tableFilter = {
        faction_type: this.faction_type
      };
      this.createForm.faction_type = this.faction_type;
    }
    if (this.personal_id) {
      this.tableFilter = {
        requested_by_id: 5996
      };
      this.createForm.personal_id = this.personal_id;
    }
    if (this.business_id) {
      this.tableFilter = {
        business_id: this.business_id
      };
      this.createForm.business_id = this.business_id;
    }
  },
  methods: {
    createContract: function createContract() {
      this.createModal = false;
      if (!this.createForm.expiration) this.createForm.expiration = 'Not Applicable';
      fetch("https://blrp_tablet/createContract", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify(this.createForm)
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue?vue&type=script&lang=js
 /* harmony default export */ var Documents_DocumentsListvue_type_script_lang_js = (DocumentsListvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Documents_DocumentsListvue_type_script_lang_js,
  DocumentsListvue_type_template_id_257a5de0_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var DocumentsList = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1753:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2006);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("bf159aa8", content, true, {"sourceMap":false});

/***/ }),

/***/ 2005:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_20ee0772_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1753);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_20ee0772_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_20ee0772_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 2006:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".doj-cases .doj-header{padding-bottom:15px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2229:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/_archived/cases/index.vue?vue&type=template&id=20ee0772







var casesvue_type_template_id_20ee0772_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "doj-cases"
  }, [_c('b-alert', {
    attrs: {
      "variant": "danger",
      "show": "",
      "dismissible": ""
    }
  }, [_vm._v("\n      This page is deprecated. Please use this only for historical records. Use the new incidents system going forward.\n    ")]), _vm._v(" "), _c(VTabs["a" /* default */], {
    staticStyle: {
      "padding-bottom": "10px"
    },
    attrs: {
      "no-key-nav": "",
      "lazy": ""
    },
    model: {
      value: _vm.tabIndex,
      callback: function callback($$v) {
        _vm.tabIndex = $$v;
      },
      expression: "tabIndex"
    }
  }, [true ? _c(VTab["a" /* default */], {
    attrs: {
      "title": "Cases",
      "lazy": ""
    }
  }, [_c(VRow["a" /* default */], {
    staticStyle: {
      "padding-top": "15px"
    },
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    attrs: {
      "md": "4"
    }
  }, [_c('b-input', {
    staticStyle: {
      "width": "330px"
    },
    attrs: {
      "placeholder": "Search case title or author"
    },
    model: {
      value: _vm.searchTerm,
      callback: function callback($$v) {
        _vm.searchTerm = $$v;
      },
      expression: "searchTerm"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
    attrs: {
      "variant": "primary"
    },
    on: {
      "click": _vm.fetchCourtCases
    }
  }, [_vm._v("Search")])], 1)], 1), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], _vm._l(_vm.cases, function (c, index) {
    return _c('div', [_c(VRow["a" /* default */], {
      staticClass: "case-row",
      on: {
        "click": function click($event) {
          return _vm.showCase(index);
        }
      }
    }, [_c(VCol["a" /* default */], {
      attrs: {
        "md": "1"
      }
    }, [c.event_type === 'criminal_case' ? _c('b-badge', {
      staticClass: "t-badge-justice t-criminal-case",
      staticStyle: {
        "width": "110px"
      },
      attrs: {
        "variant": "dark",
        "size": "lg"
      }
    }, [_vm._v("CRIMINAL CASE")]) : c.event_type === 'civil_dispute' ? _c('b-badge', {
      staticClass: "t-badge-justice t-civil-dispute",
      staticStyle: {
        "width": "110px"
      },
      attrs: {
        "variant": "dark",
        "size": "lg"
      }
    }, [_vm._v("CIVIL DISPUTE")]) : c.event_type === 'faction_dispute' ? _c('b-badge', {
      staticClass: "t-badge-justice t-faction-dispute",
      staticStyle: {
        "width": "110px"
      },
      attrs: {
        "variant": "dark",
        "size": "lg"
      }
    }, [_vm._v("FACTION DISPUTE")]) : c.event_type === 'reform_program' ? _c('b-badge', {
      staticClass: "t-badge-justice t-reform-program",
      staticStyle: {
        "width": "110px"
      },
      attrs: {
        "variant": "dark",
        "size": "lg"
      }
    }, [_vm._v("REFORM PROGRAM")]) : c.event_type === 'inquiry' ? _c('b-badge', {
      staticClass: "t-badge-justice t-city-inquiry",
      staticStyle: {
        "width": "110px"
      },
      attrs: {
        "variant": "dark",
        "size": "lg"
      }
    }, [_vm._v("INQUIRY")]) : _c('b-badge', {
      staticStyle: {
        "width": "110px"
      },
      attrs: {
        "variant": "warning",
        "size": "lg"
      }
    }, [_vm._v(_vm._s(c.type))])], 1), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "float-right",
      attrs: {
        "md": "1"
      }
    }, [c.status === 'active' ? _c('b-badge', {
      staticStyle: {
        "margin-left": "30px",
        "width": "50px"
      },
      attrs: {
        "variant": "success",
        "size": "lg"
      }
    }, [_vm._v("Active")]) : _vm._e(), _vm._v(" "), c.status === 'closed' ? _c('b-badge', {
      staticStyle: {
        "margin-left": "30px",
        "width": "50px"
      },
      attrs: {
        "variant": "primary",
        "size": "lg"
      }
    }, [_vm._v("Closed")]) : _vm._e()], 1), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "font-weight-bolder text-primary",
      attrs: {
        "md": "1"
      }
    }, [_c('span', {
      staticClass: "float-right"
    }, [_vm._v("#" + _vm._s(c.id))])]), _vm._v(" "), _c(VCol["a" /* default */], {
      attrs: {
        "md": "7"
      }
    }, [_c('b', [_vm._v(_vm._s(c.name))])]), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n                  " + _vm._s(c.created_at) + "\n                ")])], 1), _vm._v(" "), c.selected ? _c(VRow["a" /* default */], {
      staticClass: "case-description"
    }, [_c(VCol["a" /* default */], [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
      attrs: {
        "md": "4"
      }
    }, [_c('h4', [_vm._v(_vm._s(c.name))]), _vm._v(" "), _c('span', {
      staticClass: "text-muted"
    }, [_vm._v("Case submitted by "), _c('b', {
      staticClass: "text-primary"
    }, [_vm._v(_vm._s(c.author.name))])])]), _vm._v(" "), _c(VCol["a" /* default */], [c.plaintiff ? _c('span', [_c('b', [_vm._v("Case Plaintiff:")]), _vm._v(" " + _vm._s(c.plaintiff) + " "), _c('br')]) : _vm._e(), _vm._v(" "), c.defense ? _c('span', [_c('b', [_vm._v("Case Defense:")]), _vm._v(" " + _vm._s(c.defense) + " "), _c('br')]) : _vm._e()])], 1), _vm._v(" "), _c('hr'), _vm._v(" "), _c('b', [_vm._v("Details")]), _vm._v(" "), _c('p', {
      staticClass: "text-muted"
    }, [_vm._v("\n                    " + _vm._s(c.details) + "\n                  ")]), _vm._v(" "), c.outcome ? _c('div', [_c('b', [_vm._v("Outcome")]), _vm._v(" "), _c('p', {
      staticClass: "text-muted"
    }, [_vm._v("\n                      " + _vm._s(c.outcome) + "\n                    ")])]) : _vm._e(), _vm._v(" "), c.evidence ? _c('div', [_c('b', [_vm._v("Evidence")]), _vm._v(" "), _c('p', {
      staticClass: "text-muted"
    }, [_vm._v("\n                      " + _vm._s(c.evidence) + "\n                    ")])]) : _vm._e(), _vm._v(" "), c.notes && c.notes.length > 0 ? _c('div', [_c('b', [_vm._v("Notes")]), _vm._v(" "), _c('br'), _vm._v(" "), _vm._l(c.notes, function (note) {
      return _c('p', [_vm._v("\n                      ["), _c('b', [_vm._v(_vm._s(note.author.name))]), _vm._v("] @ " + _vm._s(note.created_at) + " "), _c('b', [_vm._v(" > ")]), _vm._v(" "), _c('span', {
        staticClass: "text-warning"
      }, [_vm._v(_vm._s(note.body))]), _vm._v(" "), _c('br')]);
    })], 2) : _vm._e(), _vm._v(" "), _c('div', [_c('b-form-group', {
      attrs: {
        "label": "Add case note"
      }
    }, [_c('b-input', {
      model: {
        value: _vm.note,
        callback: function callback($$v) {
          _vm.note = $$v;
        },
        expression: "note"
      }
    })], 1), _vm._v(" "), _c('b-form-group', [_c('button', {
      directives: [{
        name: "promise-btn",
        rawName: "v-promise-btn"
      }],
      staticClass: "btn btn-success",
      on: {
        "click": function click($event) {
          return _vm.saveNote(c.id);
        }
      }
    }, [_vm._v("Add Note")]), _vm._v(" "), c.status !== 'closed' ? _c('button', {
      directives: [{
        name: "promise-btn",
        rawName: "v-promise-btn"
      }],
      staticClass: "btn btn-dark float-right",
      staticStyle: {
        "margin-left": "20px"
      },
      on: {
        "click": function click($event) {
          return _vm.editCourtCase(c.id);
        }
      }
    }, [_vm._v("Edit")]) : _vm._e(), _vm._v(" "), c.status !== 'closed' ? _c('button', {
      directives: [{
        name: "promise-btn",
        rawName: "v-promise-btn"
      }],
      staticClass: "btn btn-primary float-right",
      on: {
        "click": function click($event) {
          return _vm.closeCase(c.id);
        }
      }
    }, [_vm._v("Close Case")]) : _vm._e()])], 1)], 1)], 1) : _vm._e()], 1);
  }), 0)], 1)], 1) : undefined], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/_archived/cases/index.vue?vue&type=template&id=20ee0772

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue-markdown/dist/vue-markdown.common.js
var vue_markdown_common = __webpack_require__(496);
var vue_markdown_common_default = /*#__PURE__*/__webpack_require__.n(vue_markdown_common);

// EXTERNAL MODULE: ./components/Pages/Documents/DocumentsList.vue + 4 modules
var DocumentsList = __webpack_require__(1562);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/_archived/cases/index.vue?vue&type=script&lang=js







// import PolicePardonRecords from "../components/DOJ/PolicePardonRecords";
// import TicketingAid from '../components/Police/TicketAid';
/* harmony default export */ var casesvue_type_script_lang_js = ({
  data: function data() {
    return {
      activeType: null,
      form: {},
      searchTerm: null,
      personalNoticeForm: {},
      note: null,
      error: null,
      selected: null,
      cases: [],
      notices: [],
      tabIndex: 0
    };
  },
  components: {
    DocumentsList: DocumentsList["a" /* default */],
    VueMarkdown: vue_markdown_common_default.a
  },
  mounted: function mounted() {
    this.fetchCourtCases();
    // this.fetchNotices();
  },
  methods: {
    selectType: function selectType(type) {
      this.activeType = type;
    },
    fetchCourtCases: function fetchCourtCases() {
      var _this = this;
      var uri = this.searchTerm ? "/api/doj/court-cases/".concat(this.searchTerm) : "/doj/court-cases";
      if (!uri) return false;
      this.$axios.$get(uri).then(function (r) {
        _this.cases = r;
      });
    },
    fetchNotices: function fetchNotices() {
      var _this2 = this;
      this.$axios.$get("/doj/court-notices/".concat(this.user.name)).then(function (r) {
        _this2.notices = r;
      });
    },
    showCase: function showCase(index) {
      this.selected = this.cases[index];
      vue_runtime_esm["default"].set(this.cases[index], 'selected', !this.cases[index].selected);
    },
    saveCase: function saveCase() {
      var _this3 = this;
      this.form.event_type = this.activeType;
      if (!this.form.name) return this.error = 'Must have a name';
      if (this.activeType === 'criminal_case' || this.activeType === 'civil_dispute' || this.activeType === 'faction_dispute') {
        if (!this.form.plaintiff) return this.error = 'Must have a plaintiff';
        if (!this.form.details) return this.error = 'Must have details';
        if (!this.form.outcome) return this.error = 'Must have an outcome';
        if (!this.form.defense) return this.error = 'Must have a defense';
      }
      this.error = null;
      this.form.author = this.user.name;
      return this.$axios.$post('/doj/court-cases', this.form).then(function (r) {
        _this3.tabIndex = 0;
        _this3.fetchCourtCases();
        _this3.form = {};
      });
    },
    saveNote: function saveNote(courtCaseId) {
      var _this4 = this;
      return this.$axios.$post('/doj/court-case-notes', {
        body: this.note,
        author: this.user.name,
        court_case_id: courtCaseId
      }).then(function (r) {
        _this4.note = null;
        _this4.fetchCourtCases();
      });
    },
    editCourtCase: function editCourtCase(courtCaseId) {
      this.tabIndex = 1;
      this.form = this.cases.find(function (c) {
        return c.id === courtCaseId;
      });
      this.activeType = this.form.event_type;
    },
    closeCase: function closeCase(courtCaseId) {
      this.form = this.cases.find(function (c) {
        return c.id === courtCaseId;
      });
      this.form.status = 'closed';
      this.saveCase();
    },
    saveNotice: function saveNotice() {
      var _this5 = this;
      this.personalNoticeForm.author = this.user.name;
      return this.$axios.$post('/doj/court-notices', this.personalNoticeForm).then(function (r) {
        if (r.error) return false;
        window.$events.$emit('friends::chat::open', r.id);
        _this5.personalNoticeForm = {};
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./pages/_archived/cases/index.vue?vue&type=script&lang=js
 /* harmony default export */ var _archived_casesvue_type_script_lang_js = (casesvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/_archived/cases/index.vue?vue&type=style&index=0&id=20ee0772&prod&lang=scss
var casesvue_type_style_index_0_id_20ee0772_prod_lang_scss = __webpack_require__(2005);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/_archived/cases/index.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _archived_casesvue_type_script_lang_js,
  casesvue_type_template_id_20ee0772_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var cases = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);