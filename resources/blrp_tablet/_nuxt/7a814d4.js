(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[12],{

/***/ 2235:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/ColumnLink.vue?vue&type=template&id=1d248a92

var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.value ? _c('span', {
    staticClass: "text-white-50",
    staticStyle: {
      "cursor": "pointer"
    },
    on: {
      "click": function click($event) {
        return _vm.$router.push({
          path: _vm.value
        });
      }
    }
  }, [_vm.item.name ? _c('span', [_vm._v("\n          " + _vm._s(_vm.item.name) + "\n        ")]) : _vm._e(), _vm._v(" "), _vm.item.title ? _c('span', [_vm._v("\n          " + _vm._s(_vm.item.title) + "\n        ")]) : _vm._e()]) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/parts/ColumnLink.vue?vue&type=template&id=1d248a92

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/ColumnLink.vue?vue&type=script&lang=js
/* harmony default export */ var ColumnLinkvue_type_script_lang_js = ({
  name: "ColumnLink",
  props: ['value', 'item']
});
// CONCATENATED MODULE: ./components/Common/parts/ColumnLink.vue?vue&type=script&lang=js
 /* harmony default export */ var parts_ColumnLinkvue_type_script_lang_js = (ColumnLinkvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/parts/ColumnLink.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  parts_ColumnLinkvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var ColumnLink = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);