(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[136],{

/***/ 1710:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1827);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("51eed3a6", content, true, {"sourceMap":false});

/***/ }),

/***/ 1826:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_wanted_vue_vue_type_style_index_0_id_1857e294_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1710);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_wanted_vue_vue_type_style_index_0_id_1857e294_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_wanted_vue_vue_type_style_index_0_id_1857e294_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1827:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".warrant-items{display:flex;flex-direction:row;flex-wrap:wrap}.warrant-items .warrant-item{background-color:#2f2f2f;cursor:pointer;display:flex;flex-direction:column;margin:15px;width:275px}.warrant-items .warrant-item:hover{background-color:#242424}.warrant-items .warrant-item .warrant-top{background-color:#1f1f1f;font-size:26px;letter-spacing:15px;text-align:center}.warrant-items .warrant-item img{width:100%}.warrant-items .warrant-item .warrant-info{display:flex;flex-direction:column;padding:15px;text-align:center}.warrant-items .warrant-item .warrant-incident-title{font-size:14px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2125:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/wanted.vue?vue&type=template&id=1857e294
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('div', {
    staticClass: "custom-tabs"
  }, [_c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/wanted"
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-clock"
  }), _vm._v("\n      Recent\n    ")]), _vm._v(" "), _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/wanted/most-wanted"
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-crosshairs-simple"
  }), _vm._v("\n      Most Wanted\n    ")]), _vm._v(" "), _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/wanted/cold-cases"
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-snowflake"
  }), _vm._v("\n      Cold Cases\n    ")])], 1), _vm._v(" "), _c('NuxtChild')], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/wanted.vue?vue&type=template&id=1857e294

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/wanted.vue?vue&type=script&lang=js

/* harmony default export */ var wantedvue_type_script_lang_js = ({
  name: 'WantedPage',
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./pages/cad/wanted.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_wantedvue_type_script_lang_js = (wantedvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/cad/wanted.vue?vue&type=style&index=0&id=1857e294&prod&lang=scss
var wantedvue_type_style_index_0_id_1857e294_prod_lang_scss = __webpack_require__(1826);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/wanted.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_wantedvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var wanted = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);