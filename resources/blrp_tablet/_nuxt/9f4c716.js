(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[135],{

/***/ 2124:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/vehicles/index.vue?vue&type=template&id=7f94ebd7
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('crudy-table', {
    attrs: {
      "name": "UserCar",
      "view": "index"
    }
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/vehicles/index.vue?vue&type=template&id=7f94ebd7

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/vehicles/index.vue?vue&type=script&lang=js


/* harmony default export */ var vehiclesvue_type_script_lang_js = ({
  components: {
    CrudyTable: crudy_table["a" /* default */]
  }
});
// CONCATENATED MODULE: ./pages/cad/vehicles/index.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_vehiclesvue_type_script_lang_js = (vehiclesvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/vehicles/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_vehiclesvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var vehicles = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);