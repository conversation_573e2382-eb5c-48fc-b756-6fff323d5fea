(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[223],{

/***/ 1738:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1941);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("a32e9704", content, true, {"sourceMap":false});

/***/ }),

/***/ 1940:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_settings_vue_vue_type_style_index_0_id_7bd362b7_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1738);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_settings_vue_vue_type_style_index_0_id_7bd362b7_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_settings_vue_vue_type_style_index_0_id_7bd362b7_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1941:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".settings-container[data-v-7bd362b7]{background:#121212;color:#fff;height:100vh}.settings-header[data-v-7bd362b7]{align-items:center;background:#1e1e1e;border-bottom:1px solid #333;display:flex;min-height:48px;padding:8px 16px}.back-btn[data-v-7bd362b7]{margin-right:8px}.settings-title[data-v-7bd362b7]{color:#fff;font-size:18px;font-weight:500;margin:0}.categories-list .settings-title[data-v-7bd362b7]{font-size:20px;text-align:center;width:100%}.settings-list[data-v-7bd362b7]{background:transparent!important}.category-item[data-v-7bd362b7]{border-bottom:1px solid #333;min-height:48px;padding:8px 16px;transition:background-color .2s}.category-item[data-v-7bd362b7]:hover{background-color:#2a2a2a}.category-item[data-v-7bd362b7]:last-child{border-bottom:none}.category-title[data-v-7bd362b7]{color:#fff!important;font-size:14px;font-weight:500}.category-subtitle[data-v-7bd362b7]{color:#888!important;font-size:12px;margin-top:1px}.settings-content[data-v-7bd362b7]{max-height:calc(100vh - 56px);overflow-y:auto;padding:8px}.setting-card[data-v-7bd362b7]{background:#1e1e1e!important;border:1px solid #333;border-radius:6px}.setting-name[data-v-7bd362b7]{align-items:center;display:flex;gap:8px}.setting-desc[data-v-7bd362b7]{line-height:1.4}.save-section[data-v-7bd362b7]{background:#121212;border-top:1px solid #333;bottom:0;margin:0 -8px -8px;position:sticky}@media (max-width:600px){.settings-header[data-v-7bd362b7]{padding:6px 12px}.settings-title[data-v-7bd362b7]{font-size:16px}.categories-list .settings-title[data-v-7bd362b7]{font-size:18px}.category-item[data-v-7bd362b7]{min-height:40px;padding:6px 12px}.settings-content[data-v-7bd362b7]{padding:6px}.category-title[data-v-7bd362b7]{font-size:13px}.category-subtitle[data-v-7bd362b7]{font-size:11px}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2202:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemAction.js
var VListItemAction = __webpack_require__(463);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 2 modules
var components_VList = __webpack_require__(50);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemIcon.js
var VListItemIcon = __webpack_require__(260);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/settings.vue?vue&type=template&id=7bd362b7&scoped=true



















var settingsvue_type_template_id_7bd362b7_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "settings-container"
  }, [_vm.selectedCategory ? _c('div', {
    staticClass: "category-detail"
  }, [_c('div', {
    staticClass: "settings-header"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "back-btn",
    attrs: {
      "icon": ""
    },
    on: {
      "click": function click($event) {
        _vm.selectedCategory = null;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "white"
    }
  }, [_vm._v("fa-solid fa-chevron-left")])], 1), _vm._v(" "), _c('h2', {
    staticClass: "settings-title"
  }, [_vm._v(_vm._s(_vm.selectedCategory.name))])], 1), _vm._v(" "), _c('div', {
    staticClass: "settings-content"
  }, [_vm._l(_vm.filteredSettings.filter(function (s) {
    return s.category === _vm.selectedCategory.name;
  }), function (setting) {
    return _c(VCard["a" /* default */], {
      key: setting.id,
      staticClass: "setting-card mb-2",
      attrs: {
        "dark": ""
      }
    }, [_c(components_VCard["b" /* VCardText */], {
      staticClass: "pa-3"
    }, [_c(VRow["a" /* default */], {
      attrs: {
        "no-gutters": ""
      }
    }, [_vm.mode === 'tablet' ? _c(VCol["a" /* default */], {
      attrs: {
        "cols": "1",
        "align-self": "center",
        "align": "center"
      }
    }, [_c(VIcon["a" /* default */], {
      staticStyle: {
        "font-size": "24px"
      },
      attrs: {
        "color": setting.color
      }
    }, [_vm._v("\n                " + _vm._s(setting.icon) + "\n              ")])], 1) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c('div', {
      staticClass: "setting-name mb-1",
      staticStyle: {
        "font-size": "14px",
        "color": "white",
        "font-weight": "500"
      }
    }, [_vm.mode === 'phone' ? _c(VIcon["a" /* default */], {
      staticClass: "mr-1",
      attrs: {
        "color": setting.color,
        "size": "16"
      }
    }, [_vm._v("\n                  " + _vm._s(setting.icon) + "\n                ")]) : _vm._e(), _vm._v("\n                " + _vm._s(setting.label) + "\n                "), setting.staff_only ? _c('span', {
      staticStyle: {
        "color": "red",
        "font-size": "10px"
      }
    }, [_vm._v("\n                  (Admin)\n                ")]) : _vm._e()], 1), _vm._v(" "), _c('div', {
      staticClass: "setting-desc text--secondary mb-2",
      staticStyle: {
        "font-size": "12px",
        "line-height": "1.3"
      }
    }, [_c('app-markdown-view', {
      attrs: {
        "source": setting.description
      }
    })], 1), _vm._v(" "), _c('div', {
      staticClass: "setting-field"
    }, [_c('crudy-form-field', {
      attrs: {
        "field": setting,
        "type": setting.field_type
      }
    })], 1)])], 1)], 1)], 1);
  }), _vm._v(" "), _c('div', {
    staticClass: "save-section pa-2"
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "success",
      "block": "",
      "small": "",
      "loading": _vm.saving
    },
    on: {
      "click": function click($event) {
        $event.preventDefault();
        return _vm.save.apply(null, arguments);
      }
    }
  }, [_vm._v("\n          Save Changes\n        ")])], 1)], 2)]) : _c('div', {
    staticClass: "categories-list"
  }, [_vm._m(0), _vm._v(" "), _c(VList["a" /* default */], {
    staticClass: "settings-list",
    attrs: {
      "dark": ""
    }
  }, _vm._l(_vm.categories, function (category) {
    return _c(VListItem["a" /* default */], {
      key: category.name,
      staticClass: "category-item",
      on: {
        "click": function click($event) {
          return _vm.selectCategory(category);
        }
      }
    }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
      attrs: {
        "color": category.color,
        "size": "20"
      }
    }, [_vm._v("\n            " + _vm._s(category.icon) + "\n          ")])], 1), _vm._v(" "), _c(components_VList["a" /* VListItemContent */], [_c(components_VList["c" /* VListItemTitle */], {
      staticClass: "category-title"
    }, [_vm._v("\n            " + _vm._s(category.name) + "\n          ")]), _vm._v(" "), _c(components_VList["b" /* VListItemSubtitle */], {
      staticClass: "category-subtitle"
    }, [_vm._v("\n            " + _vm._s(category.settingsCount) + " setting" + _vm._s(category.settingsCount !== 1 ? 's' : '') + "\n          ")])], 1), _vm._v(" "), _c(VListItemAction["a" /* default */], [_c(VIcon["a" /* default */], {
      attrs: {
        "color": "grey"
      }
    }, [_vm._v("fa-solid fa-chevron-right")])], 1)], 1);
  }), 1)], 1)]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "settings-header"
  }, [_c('h1', {
    staticClass: "settings-title"
  }, [_vm._v("Life Invader Settings")])]);
}];

// CONCATENATED MODULE: ./pages/social/settings.vue?vue&type=template&id=7bd362b7&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/typeof.js
var esm_typeof = __webpack_require__(37);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(21);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.splice.js
var es_array_splice = __webpack_require__(133);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.map.js
var es_map = __webpack_require__(391);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.entries.js
var es_object_entries = __webpack_require__(134);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.delete-all.js
var esnext_map_delete_all = __webpack_require__(392);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.every.js
var esnext_map_every = __webpack_require__(393);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.filter.js
var esnext_map_filter = __webpack_require__(394);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.find.js
var esnext_map_find = __webpack_require__(395);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.find-key.js
var esnext_map_find_key = __webpack_require__(396);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.includes.js
var esnext_map_includes = __webpack_require__(397);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.key-of.js
var esnext_map_key_of = __webpack_require__(398);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.map-keys.js
var esnext_map_map_keys = __webpack_require__(399);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.map-values.js
var esnext_map_map_values = __webpack_require__(400);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.merge.js
var esnext_map_merge = __webpack_require__(401);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.reduce.js
var esnext_map_reduce = __webpack_require__(402);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.some.js
var esnext_map_some = __webpack_require__(403);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.map.update.js
var esnext_map_update = __webpack_require__(404);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-field.vue + 4 modules
var crudy_form_field = __webpack_require__(504);

// EXTERNAL MODULE: ./utils/store-accessor.ts
var store_accessor = __webpack_require__(27);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/settings.vue?vue&type=script&lang=js




function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }








































/* harmony default export */ var settingsvue_type_script_lang_js = ({
  name: 'SocialSettings',
  components: {
    AppMarkdownView: app_markdown_view["a" /* default */],
    CrudyFormField: crudy_form_field["a" /* default */]
  },
  data: function data() {
    return {
      selectedCategory: null,
      settings: [],
      originalValues: {},
      saving: false
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _this.loadSettings();
          case 2:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  beforeDestroy: function beforeDestroy() {
    window.$events.$off('crudy:form:field:bounced');
  },
  methods: {
    loadSettings: function loadSettings() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var item, key, setting, _iterator, _step, _setting;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.next = 2;
              return _this2.$axios.$get('/person/settings/list');
            case 2:
              _this2.settings = _context2.sent;
              item = {}; // Filter out admin settings for non-admin users
              for (key in _this2.settings) {
                setting = _this2.settings[key];
                if (!_this2.user.admin && setting.staff_only) {
                  _this2.settings.splice(key, 1);
                }
              }

              // Process settings
              _iterator = _createForOfIteratorHelper(_this2.settings);
              try {
                for (_iterator.s(); !(_step = _iterator.n()).done;) {
                  _setting = _step.value;
                  _setting.label = _setting.name;
                  _setting.name = _setting.identifier;
                  item[_setting.identifier] = _setting.value;
                  _this2.originalValues[_setting.identifier] = _setting.value;
                  if (_setting.field_type === 'boolean' && _setting.value == 1) {
                    _this2.originalValues[_setting.id] = true;
                  } else if (_setting.field_type === 'boolean' && _setting.value == 0) {
                    _this2.originalValues[_setting.id] = false;
                  }
                }
              } catch (err) {
                _iterator.e(err);
              } finally {
                _iterator.f();
              }
              store_accessor["b" /* CrudyFormStore */].setItemValues(item);
            case 8:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    selectCategory: function selectCategory(category) {
      this.selectedCategory = category;
    },
    save: function save() {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var finalizedItem, _i, _Object$entries, _Object$entries$_i, key, value;
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _this3.saving = true;
              _context3.prev = 1;
              finalizedItem = {};
              for (_i = 0, _Object$entries = Object.entries(_this3.item); _i < _Object$entries.length; _i++) {
                _Object$entries$_i = Object(slicedToArray["a" /* default */])(_Object$entries[_i], 2), key = _Object$entries$_i[0], value = _Object$entries$_i[1];
                if (_this3.originalValues[key] !== _this3.item[key]) {
                  if (Object(esm_typeof["a" /* default */])(value) !== 'object') {
                    if (value === '') {
                      finalizedItem[key] = 'removed';
                    } else {
                      finalizedItem[key] = value;
                    }
                  }
                }
              }
              _this3.$store.dispatch('nui/syncSelfForce');
              _context3.next = 7;
              return _this3.$axios.$post('/person/settings/update', finalizedItem);
            case 7:
              _context3.next = 9;
              return _this3.loadSettings();
            case 9:
              _this3.$store.dispatch('auth/fetchUser', _this3.$axios);

              // Show success message
              _this3.$toast.success('Settings saved successfully!');
              _context3.next = 16;
              break;
            case 13:
              _context3.prev = 13;
              _context3.t0 = _context3["catch"](1);
              _this3.$toast.error('Failed to save settings');
            case 16:
              _context3.prev = 16;
              _this3.saving = false;
              return _context3.finish(16);
            case 19:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[1, 13, 16, 19]]);
      }))();
    }
  },
  computed: _objectSpread(_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    mode: 'system/mode'
  })), {}, {
    filteredSettings: function filteredSettings() {
      return this.settings.filter(function (setting) {
        return setting.category.includes('Life Invader');
      });
    },
    categories: function categories() {
      var _this4 = this;
      var categoryMap = new Map();
      var _iterator2 = _createForOfIteratorHelper(this.filteredSettings),
        _step2;
      try {
        var _loop = function _loop() {
          var setting = _step2.value;
          if (setting.category.includes('Bad')) return 1; // continue
          if (!categoryMap.has(setting.category)) {
            // Find a representative setting for icon and color
            var representativeSetting = _this4.filteredSettings.find(function (s) {
              return s.category === setting.category;
            });
            categoryMap.set(setting.category, {
              name: setting.category,
              icon: (representativeSetting === null || representativeSetting === void 0 ? void 0 : representativeSetting.icon) || 'fa-solid fa-cog',
              color: (representativeSetting === null || representativeSetting === void 0 ? void 0 : representativeSetting.color) || '#666',
              settingsCount: 0
            });
          }
          var category = categoryMap.get(setting.category);
          category.settingsCount++;
        };
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          if (_loop()) continue;
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return Array.from(categoryMap.values());
    },
    item: function item() {
      return store_accessor["b" /* CrudyFormStore */].item;
    }
  })
});
// CONCATENATED MODULE: ./pages/social/settings.vue?vue&type=script&lang=js
 /* harmony default export */ var social_settingsvue_type_script_lang_js = (settingsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/social/settings.vue?vue&type=style&index=0&id=7bd362b7&prod&scoped=true&lang=css
var settingsvue_type_style_index_0_id_7bd362b7_prod_scoped_true_lang_css = __webpack_require__(1940);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/social/settings.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  social_settingsvue_type_script_lang_js,
  settingsvue_type_template_id_7bd362b7_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "7bd362b7",
  null
  
)

/* harmony default export */ var settings = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);