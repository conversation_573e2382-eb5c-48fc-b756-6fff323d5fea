(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[54],{

/***/ 1572:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(5);
var render, staticRenderFns
var script = {}


/* normalize component */

var component = Object(_node_modules_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_0__[/* default */ "a"])(
  script,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1678:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1761);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("f3385048", content, true, {"sourceMap":false});

/***/ }),

/***/ 1760:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_permissions_vue_vue_type_style_index_0_id_73fb4196_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1678);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_permissions_vue_vue_type_style_index_0_id_73fb4196_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_permissions_vue_vue_type_style_index_0_id_73fb4196_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1761:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".perm-nodes{font-size:13px;padding-top:3px}.listing-item{background-color:#1d1d1d;margin-bottom:10px;margin-top:10px;padding:15px}.listing-image{height:64px;width:64px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2068:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/permissions.vue?vue&type=template&id=73fb4196





var permissionsvue_type_template_id_73fb4196_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.business ? _c('app-page', [_vm.canManage ? _c('div', _vm._l(_vm.business.characters, function (character) {
    return character.person ? _c(VRow["a" /* default */], {
      key: character.id,
      staticClass: "case-row-no-hover"
    }, [character.person ? _c(VCol["a" /* default */], {
      staticClass: "ml-4",
      attrs: {
        "md": "2"
      }
    }, [_vm._v("\n        " + _vm._s(character.person.firstname) + " " + _vm._s(character.person.lastname.substring(0, 1)) + "\n      ")]) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], {
      staticClass: "perm-nodes"
    }, _vm._l(_vm.permissionNames, function (permissionName) {
      return _c('span', {
        key: permissionName
      }, [_c('span', [_vm._v(" ")]), _vm._v(" "), character[permissionName] ? _c('span', {
        staticClass: "text-success"
      }, [_vm._v("\n                      " + _vm._s(permissionName.replace('perm_', '').toUpperCase()) + "\n                  ")]) : _c('span', {
        staticClass: "text-muted"
      }, [_vm._v("\n                      " + _vm._s(permissionName.replace('perm_', '').toUpperCase()) + "\n                  ")])]);
    }), 0)], 1) : _vm._e();
  }), 1) : _c('div', [_c('div', {
    staticClass: "ml-3 mt-4"
  }, [_vm._v("\n      You do not have permission to view this information\n    ")])])]) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/business/_id/permissions.vue?vue&type=template&id=73fb4196

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(21);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.entries.js
var es_object_entries = __webpack_require__(134);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./components/Common/Loader.vue + 4 modules
var Loader = __webpack_require__(190);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/Pages/Business/post-item.vue
var post_item = __webpack_require__(1572);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./store/mutation-types.js
var mutation_types = __webpack_require__(45);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/permissions.vue?vue&type=script&lang=js





















function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






/* harmony default export */ var permissionsvue_type_script_lang_js = ({
  components: {
    AppPage: AppPage["a" /* default */],
    PostItem: post_item["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */],
    Loader: Loader["a" /* default */]
  },
  methods: {},
  computed: _objectSpread({
    permissionNames: function permissionNames() {
      var data = [];
      var _iterator = _createForOfIteratorHelper(this.business.characters),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var person = _step.value;
          for (var _i = 0, _Object$entries = Object.entries(person); _i < _Object$entries.length; _i++) {
            var _Object$entries$_i = Object(slicedToArray["a" /* default */])(_Object$entries[_i], 2),
              key = _Object$entries$_i[0],
              value = _Object$entries$_i[1];
            if (key.includes('perm_') && !data.includes(key)) {
              data.push(key);
            }
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      return data;
    },
    canManage: function canManage() {
      return this.hasBusinessPermission('management');
      //
      // const self = this.business.characters.find(c => c.id === this.user.character_number)
      //
      // if (!self) {
      //   return !!this.isAdmin;
      // }
      //
      // return !!self['perm_permissions'];
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    isAdmin: 'auth/isAdmin',
    business: 'business/business',
    hasBusinessPermission: 'business/hasBusinessPermission'
  }))
});
// CONCATENATED MODULE: ./pages/business/_id/permissions.vue?vue&type=script&lang=js
 /* harmony default export */ var _id_permissionsvue_type_script_lang_js = (permissionsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/business/_id/permissions.vue?vue&type=style&index=0&id=73fb4196&prod&lang=scss
var permissionsvue_type_style_index_0_id_73fb4196_prod_lang_scss = __webpack_require__(1760);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/business/_id/permissions.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _id_permissionsvue_type_script_lang_js,
  permissionsvue_type_template_id_73fb4196_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var permissions = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);