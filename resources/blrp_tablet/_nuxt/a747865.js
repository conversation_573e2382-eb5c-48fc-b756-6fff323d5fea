(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[169],{

/***/ 1742:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1950);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("5e2bd121", content, true, {"sourceMap":false});

/***/ }),

/***/ 1949:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_fault_vue_vue_type_style_index_0_id_c4960158_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1742);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_fault_vue_vue_type_style_index_0_id_c4960158_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_fault_vue_vue_type_style_index_0_id_c4960158_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1950:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".glitcher div{animation:glitch 1s linear infinite}@keyframes glitch{2%,64%{transform:translate(2px) skew(0deg)}4%,60%{transform:translate(-2px) skew(0deg)}62%{transform:translate(0) skew(5deg)}}.glitcher div:after,.glitcher div:before{content:attr(title);left:0;position:absolute}.glitcher div:before{animation:glitchTop 1s linear infinite;clip-path:polygon(0 0,100% 0,100% 33%,0 33%);-webkit-clip-path:polygon(0 0,100% 0,100% 33%,0 33%)}@keyframes glitchTop{2%,64%{transform:translate(2px,-2px)}4%,60%{transform:translate(-2px,2px)}62%{transform:translate(13px,-1px) skew(-13deg)}}.glitcher div:after{animation:glitchBotom 1.5s linear infinite;clip-path:polygon(0 67%,100% 67%,100% 100%,0 100%);-webkit-clip-path:polygon(0 67%,100% 67%,100% 100%,0 100%)}@keyframes glitchBotom{2%,64%{transform:translate(-2px)}4%,60%{transform:translate(-2px)}62%{transform:translate(-22px,5px) skew(21deg)}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2223:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/fault.vue?vue&type=template&id=c4960158
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "p-4 glitcher"
  }, [_vm._m(0), _vm._v(" "), _c('a', {
    staticClass: "mr-3",
    attrs: {
      "large": "",
      "color": "primary"
    },
    on: {
      "click": _vm.startTroubleShooter
    }
  }, [_vm._v("\n    BAD TROUBLESHOOTER\n  ")]), _vm._v(" "), _c('a', {
    attrs: {
      "large": "",
      "color": "primary"
    },
    on: {
      "click": function click($event) {
        _vm.$store.dispatch('system/exit');
        _vm.$router.push('/misc/install');
      }
    }
  }, [_vm._v("\n    RESTART\n  ")])]);
};
var staticRenderFns = [function () {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('h1', [_vm._v("\n      SYS.FAULT\n    ")]), _vm._v(" "), _c('h1', {}, [_vm._v("\n      A problem has been detected and BadOs has been shut down to prevent damage to this device.\n    ")])]);
}];

// CONCATENATED MODULE: ./pages/misc/fault.vue?vue&type=template&id=c4960158

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/fault.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var faultvue_type_script_lang_js = ({
  name: 'fault',
  components: {},
  props: [],
  data: function data() {
    return {
      troubleshooterEnabled: false
    };
  },
  mounted: function mounted() {
    document.getElementById('scroll-container').style.backgroundColor = 'blue';
  },
  beforeDestroy: function beforeDestroy() {
    document.getElementById('scroll-container').style.backgroundColor = 'revert';
  },
  methods: {
    startTroubleShooter: function startTroubleShooter() {
      this.troubleshooterEnabled = true;
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./pages/misc/fault.vue?vue&type=script&lang=js
 /* harmony default export */ var misc_faultvue_type_script_lang_js = (faultvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/misc/fault.vue?vue&type=style&index=0&id=c4960158&prod&lang=scss
var faultvue_type_style_index_0_id_c4960158_prod_lang_scss = __webpack_require__(1949);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/misc/fault.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  misc_faultvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var fault = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);