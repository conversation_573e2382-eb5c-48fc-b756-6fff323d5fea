(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[1],{

/***/ 1575:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-draggable.vue?vue&type=template&id=68e268e5&scoped=true
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    ref: "draggable",
    staticClass: "draggable-item d-flex justify-content-between",
    style: _vm.styleVars
  }, [_vm._t("default"), _vm._v(" "), _c('div', {
    staticClass: "d-flex align-items-center"
  }, [_vm._t("actions"), _vm._v(" "), _c('i', {
    staticClass: "fa-solid fa-bars",
    staticStyle: {
      "cursor": "move"
    },
    on: {
      "mousedown": _vm.dragMouseDown
    }
  })], 2)], 2);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-draggable.vue?vue&type=template&id=68e268e5&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-draggable.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var app_draggablevue_type_script_lang_js = ({
  name: "app-drager",
  props: ['type', 'identifier', 'onDropped'],
  data: function data() {
    return {
      originalParentNode: null,
      movePositions: {
        clientY: undefined,
        clientX: undefined,
        movementX: 0,
        movementY: 0
      }
    };
  },
  methods: {
    dragMouseDown: function dragMouseDown(event) {
      event.preventDefault();
      this.movePositions.clientX = event.clientX;
      this.movePositions.clientY = event.clientY;
      this.movementX = 0;
      this.movementY = 0;
      document.onmousemove = this.elementDrag;
      document.onmouseup = this.closeDragElement;

      // this.originalParentNode = this.$el.parentNode
      // this.$el.parentNode.removeChild(this.$el);
      // document.getElementById('tablet').appendChild(this.$el);

      this.$refs.draggable.style.position = 'absolute';
      this.$refs.draggable.style.zIndex = 9999999;
      var currentWidth = this.$refs.draggable.style.width;
      this.$refs.draggable.style.width = currentWidth;
      this.$refs.draggable.style.minWidth = currentWidth;
      this.$refs.draggable.style.maxWidth = currentWidth;
      this.$refs.draggable.classList.add('dragging');
      this.$store.commit('dispatch/START_DRAGGING', {
        id: this.identifier,
        type: this.type
      });
    },
    elementDrag: function elementDrag(event) {
      this.movePositions.movementX = this.movePositions.clientX - event.clientX;
      this.movePositions.movementY = this.movePositions.clientY - event.clientY;
      this.movePositions.clientX = event.clientX;
      this.movePositions.clientY = event.clientY;
      this.$refs.draggable.style.left = this.$refs.draggable.offsetLeft - this.movePositions.movementX + 'px';
      this.$refs.draggable.style.top = this.$refs.draggable.offsetTop - this.movePositions.movementY + 'px';
    },
    closeDragElement: function closeDragElement(event) {
      document.onmouseup = null;
      document.onmousemove = null;
      var droppableElement = null;
      var found = false;
      var landedElement = document.elementFromPoint(event.clientX, event.clientY);
      if (landedElement.dataset.droppable && !found) {
        found = true;
        droppableElement = landedElement;
      }
      if (landedElement.parentElement.dataset.droppable && !found) {
        found = true;
        droppableElement = landedElement.parentElement;
      }
      if (landedElement.parentElement.parentElement.dataset.droppable && !found) {
        found = true;
        droppableElement = landedElement.parentElement.parentElement;
      }
      if (landedElement.parentElement.parentElement.parentElement.dataset.droppable && !found) {
        found = true;
        droppableElement = landedElement.parentElement.parentElement.parentElement;
      }
      if (found && droppableElement) {
        var dataSet = droppableElement.dataset;
        var identifier = dataSet.identifier;
        var type = dataSet.type;

        // Figure out how to make this smarter, to prevent dragging to self.
        // Does not currently account for everything
        // if (!(identifier === this.draggingIdentifier && type === this.draggingType)) {
        // alert(`${type} ${identifier} | ${this.draggingType} ${this.draggingIdentifier}`);
        // }

        this.onDropped(this.draggingType, this.draggingIdentifier, type, identifier);
      }

      // this.originalParentNode.appendChild(this.$el);
      this.$refs.draggable.classList.remove('dragging');
      this.$refs.draggable.style.position = 'relative';
      this.$refs.draggable.style.left = 'revert';
      this.$refs.draggable.style.top = 'revert';
      this.$store.commit('dispatch/SET_DRAGGING', false);
      this.$forceUpdate();
    }
  },
  computed: _objectSpread({
    beingDragged: function beingDragged() {
      return this.dragging && this.draggingType === this.type && this.draggingIdentifier === this.identifier;
    },
    styleVars: function styleVars() {
      return {
        // 'position': this.beingDragged ? 'absolute' : 'relative',
        // 'z-index': this.beingDragged ? 9 : 0,
      };
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    dragging: 'dispatch/dragging',
    draggingType: 'dispatch/draggingType',
    draggingIdentifier: 'dispatch/draggingIdentifier'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-draggable.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_draggablevue_type_script_lang_js = (app_draggablevue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-draggable.vue?vue&type=style&index=0&id=68e268e5&prod&scoped=true&lang=css
var app_draggablevue_type_style_index_0_id_68e268e5_prod_scoped_true_lang_css = __webpack_require__(1791);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-draggable.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_draggablevue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "68e268e5",
  null
  
)

/* harmony default export */ var app_draggable = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1576:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.number.to-fixed.js
var es_number_to_fixed = __webpack_require__(385);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Dispatch/render-person.vue?vue&type=template&id=505b75de&scoped=true


var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticStyle: {
      "display": "inline"
    }
  }, [_vm.user.character_number === _vm.person.id ? _c('i', {
    staticClass: "fa-solid fa-star",
    staticStyle: {
      "color": "gold"
    }
  }) : _vm._e(), _vm._v(" "), _vm.user.isAlive === 0 ? _c('i', {
    staticClass: "fa-solid fa-biohazard text-danger"
  }) : _vm._e(), _vm._v(" "), _c('app-person-icon', {
    attrs: {
      "groups": _vm.person.groups
    }
  }), _vm._v(" "), _c('span', {
    staticClass: "text-white font-weight-bold"
  }, [_vm._v("\n      " + _vm._s(_vm.person.callsign) + "\n    ")]), _vm._v(" "), _c('span', {
    staticClass: "font-weight-bold",
    style: "color: ".concat(_vm.faction.color)
  }, [_vm._v("\n      " + _vm._s(_vm.name) + "\n    ")]), _vm._v(" "), _vm.person.location && !_vm.hideLocation ? _c('div', [_vm.person.distance_from_me ? _c('span', {
    staticClass: "text-primary"
  }, [_vm._v("\n        " + _vm._s(_vm.person.distance_from_me.toFixed(2)) + " mi\n      ")]) : _vm._e(), _vm._v(" "), _c('span', {
    staticClass: "ml-2"
  }, [_vm._v("\n        " + _vm._s(_vm.person.location.real_zone ? _vm.person.location.real_zone : _vm.person.location.zone) + "\n      ")])]) : _vm._e()], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Dispatch/render-person.vue?vue&type=template&id=505b75de&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules
var slicedToArray = __webpack_require__(21);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.entries.js
var es_object_entries = __webpack_require__(134);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./config/factions.js
var factions = __webpack_require__(279);

// EXTERNAL MODULE: ./components/Common/app-person-icon.vue + 4 modules
var app_person_icon = __webpack_require__(1673);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Dispatch/render-person.vue?vue&type=script&lang=js

















function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var render_personvue_type_script_lang_js = ({
  name: "render-person",
  components: {
    AppPersonIcon: app_person_icon["a" /* default */]
  },
  props: ['person', 'hideLocation'],
  computed: _objectSpread({
    name: function name() {
      if (this.person.name) return this.person.name;
      return this.person.firstname.substr(0, 1) + '. ' + this.person.lastname;
    },
    faction: function faction() {
      var _iterator = _createForOfIteratorHelper(factions["a" /* default */]),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var faction = _step.value;
          var _iterator2 = _createForOfIteratorHelper(faction.groups),
            _step2;
          try {
            for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
              var groupName = _step2.value;
              for (var _i = 0, _Object$entries = Object.entries(this.person.groups); _i < _Object$entries.length; _i++) {
                var _Object$entries$_i = Object(slicedToArray["a" /* default */])(_Object$entries[_i], 2),
                  key = _Object$entries$_i[0],
                  value = _Object$entries$_i[1];
                if (key === groupName && value) return faction;
              }
            }
          } catch (err) {
            _iterator2.e(err);
          } finally {
            _iterator2.f();
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Dispatch/render-person.vue?vue&type=script&lang=js
 /* harmony default export */ var Dispatch_render_personvue_type_script_lang_js = (render_personvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Dispatch/render-person.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Dispatch_render_personvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "505b75de",
  null
  
)

/* harmony default export */ var render_person = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1596:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/aemt.98dc3a1.png";

/***/ }),

/***/ 1597:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAACPUlEQVQ4ja2SX0sUYRTGf+/OvLM7s/9wE8FulNKLksgwwds2+gZ9ii66CBIzlGSTzKLu+gpB1EV/LqLQDIwIiRILSlYjSRNTc9fZmdmZ2Xm7CLbctbueu/N733MeOOeBBs1Nj6gXj8dUI39+f0i9eTbaxPVGULU9YmETxhA+vhc08X01++Rik9Pso2YGENsPmmaC+Zfj9Ya3UyNKJsx/O85NDauZB3sdlucn6vXSu4k9b9P3Lqi5qWEFIABmHg4rwgAzrRPXdaSZwrAiNtdsktk46WwG1w5w7TJhLcQph5w6e0PUBzSq+P6m8v1dNr5vI6VGri2HoRkcPjHU9F8UFyaVlBpBUMP9WcLZ9Yg0i5QVoSV0vIoLShKEGgCZjIlMSNAiVFRD13UNy4ywWk2q2RjlHYetHwHlioEZ+oDEdgysuEdbm0WqRRK34jgVD8c1iNnbDvaOi+8HSNNEaRn6ThdErFahWoXAD4kbNU6euSZqehppmvh+QKXkUilFv3cwP31ZWWlJd/8VAfDp9VWlGwFdfWPiw6uCSicVjic5MnBJLH+8owJ7Hbei6M0XRAzgeH5crHyxAVgr3lLtnSkOtOcASLVkybVnOdhhsrp4Wx3qOSdWv9r05gsC/gpSsjXDt8XrKmFEKKEIgxoAnUfPCwUooTATNVY+TyrMVP0K9dAP5EdFcWFSuSWbwPPw/T/ZWV3aQMZ1ZCJJR8/gnlPuiXLXsUHR3X9F7JZ0NtcdAJ7eHVI7WxHlUryp+b/oF1h29nweCsRbAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1598:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7DAAAOwwHHb6hkAAABkklEQVQ4y32ST0uVQRSHn5k5c29dEyuUbgvR1UVQEhetwiJw4aJ92+gD9QH6Bm4DCRRauJNKlATlluWyRVgk3D++M3NavK9xJ2jOdp55Zs6Pnznef6Pt6hPJeACcSVyOu6w9fWFopsRIGH1neX0DzByoghly+G6bySkxYgww6BP4AaqIH2GImaDEiPeBODok6TQAGsc8ePyI062e3l6M3Lo3T2f+CfHn64wR37oWRNL4iBBv1K9VFd9OIkvP+00GZ3z+sKML93PG+9Va4CSi1Rc01AGpBkSWshVKjDiXsBIxyTX7JqzNMygxNgVlPACsYluK9QpoJigxUg0NX98LriNMd5WpmUSsTCYoMWIdoIbhhWN4Ac4qrV52nxIjKYITEKNNy/LvA5QYCVHpzCpR09/DyzxDSoyEAHcWr0jS2Aks9DY53dqZKNIzfh+/yphf53otMHTmVtD2FKqKcMXHt7s8nCjSwe62riznTDiTWkAy+JmXcLMLmoAByt4/IfyfEdeepb+3j7q655YRmLt5kQrMH/CiBlfgPh5hAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1599:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/chief-paramedic.cc890e3.png";

/***/ }),

/***/ 1600:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/corporal.1b33e32.png";

/***/ }),

/***/ 1601:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAALcSURBVDhPVVNLTxNRFP5mWsY+KMhDoIUIKNBKIikk6o4t7NSg+AOqGxO2sMANiQiEkOjWBCILNxITEDCRhRo1BgoJEoGCFZDwRqBTygClM72ee0sNfJOZe+855/vuebQSI4AQPT6Gf3wcJyc6bK4CxI0YDrVDRMIq4kzCpeJcOLMsUBQLclOyYLVYOQ1CILi5iL3gNlLsCtxuD7SRDzBbrYgu/IYe08Ey0sEkGzLvPsD+1gq+T47CkZaG2pqahMDqxhoKnPlCkWPq3n3o0WNI4bBYrd4qsOJiyI980H4twGK9gDd9fWhtbYWZExyONEGMx+OQZRnOhz5sDw3BVl0NQzuAlJEJ+XoFjnb3IJkk8JrtdrvgyPxjkiRxSCKntha76en41NODydFRzL8bQObVKzCTTaILzCYTdF0XsSIDiR4Ofvt4ZyeODiJYHRigejchaRrU5WX4m5uhbW2hrKMDbH1dZCs44msYYjnc28PHxkb0P23FIQVUPmtDfHYWu2tr8Le3Y767G+rYGJR8F1LM4u6EwAGNjON9fT02aL1M5JP9fag01qw7t2GljqdyG/mmfT4xXhuVwyEEViIr4nCjqQneqipEaa/SrQeBAJw0ESU3F8dkyy4qgrulBZvBIFx5eYIjBJyWHOgGgz01VTSJV8cTVCgoMhvA0dISTDyYfCZFQSgUQqXXy6mnAtl5mAvtYKqrC1MTE3AIF/VkehrG7l8wSl+h8/biIgLNT5DpKkRpSYmIEQJCeT8CBzXwZk4OEgVRFpRRbGcHBo0szM/03up5DU9ZAaTT0f//L3C8Gh5G6eQPWCRGsx/EH78fNrLbCwvhqqtDiZwFue0xSs0XEwQOLpAE/ThY79t+sZ978Zy99HhYb3k5+9LQwMJk29Q1FjNiwp/EOYEkPn/9xiZnAuyE9qqmscHBEfYzMEOtiCcCzuBcCWexuraO2eACVDWECrcbnmvuU89ZAP8Aam+ACdrSn4IAAAAASUVORK5CYII="

/***/ }),

/***/ 1602:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/fire-cheif.f5a0d0a.png";

/***/ }),

/***/ 1603:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/fts.1b5e2e1.png";

/***/ }),

/***/ 1604:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/lieutenant.237accb.png";

/***/ }),

/***/ 1605:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/major.4937d58.png";

/***/ }),

/***/ 1606:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/officer.1a94d8b.png";

/***/ }),

/***/ 1607:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,/9j/4AAQSkZJRgABAQEASABIAAD/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAEAAAAAAAD/2wBDAAIBAQIBAQICAgICAgICAwUDAwMDAwYEBAMFBwYHBwcGBwcICQsJCAgKCAcHCg0KCgsMDAwMBwkODw0MDgsMDAz/2wBDAQICAgMDAwYDAwYMCAcIDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAwMDAz/wAARCAAQABADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9Pv2hPjxq3i74h32jw+I4bHw7eWyTWlusMe+3t1kijF+8LK5v1mklYLbrmOSDEhCOiieh8JP2mbr4R/ELQ9O1bxbpP/CNXV42mtbAQsNWuJYpJ2NpDGAbRrRYsSISsLRRPJGs0twPKuftH/AjxB4L8X6xq2jeHdNuNJitZhpt9KkU4TzYotlvM7urWsdvLbRmN1xbpEBHIGErtDnfAj9lvWPiFd+B7PxPpVnrnhnwuVvBqUs2UsLtYf372Uu4vcvePK32hyvkOjShGWSPzbnxZe29rpe9/O2/5f1sfq1JZX/Zqc3Hk5XdLl5r8r1/xbXTfVtfvEj/2Q=="

/***/ }),

/***/ 1608:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsIAAA7CARUoSoAAAAHVSURBVDhPY6ApiI6u5/PwmMgO5ZIOZGTCEp2di2qhXNLAlCn7eRQUIt9KSoY8mDlzJitUmHhgb18Yw8RU+J+JKee/mVlmKFSYOJCWVs8lIxP/koHh7n8Ghmv/hYVD7tjb17NApQkDO7v8KQwMs4Ga/wPxPyCe+t/EJKMPKo0f2NrmmHFzFwA1fQfi+0D8CIi//OfmzvqfmNjpA1WGHdTXz9USF096z8DwAKyJgSERiNOA+AcQ3/kvJBT5qr5+kQpUOSowNk5j5eIK3sXAcAKo+M9/RsbW/zY2ecetrfPOMTB0AcVAXjnyX1o65tLKlTsVodogoLt7jTw3d9A2BoY9UIWL/uvqJp/u7V3J2d8/X0BLK+kSA8NyoDgoTHb+l5GJPt3evlwBrFlTM0peUjLuOQPDQajmDf/l5KLOxcQUc4MVAEFUVLugrGzUVQaGrVBDDvwXE4t5qqQUKweUdopgYCgFCv4F4pX/5eWjL1lZlfJCtCJAQECFsLx8zA0GhrVAdSCLioEYpBdMpAP93PlfTS1+a35+vwBECyaorJwrqqmZsJ+BoROoOQthAAeH+x9b24IWiDLCwNGxqIOLyxNigKlptmVycocZVI5okJ7ea2ZjU2ANALneqwyoKPt6AAAAAElFTkSuQmCC"

/***/ }),

/***/ 1609:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAB3klEQVQ4jWNgoCVob68Q7GuplsanhgWfpLb4j2QBju9GDAwMUSTb3tFRzv/q2sQbX58u+j2rv8AAlzomXBJWWj+yRWV+qnMJfWEx0/2XS5IBnZ2dvJrKb6L+/D7H8PvLQQZlxWchC6aUaxPl9IaGBqY7B73X/n2t/P/RcavbN3e7nvj7Wvn/izNWV2ZPaBUn6AIvwyuNcooXgn5+/Mlw8Kxe9e7TJnHvnzJ9E5W+p+1ksG92Q0MD7oDfuyy24Os1hf+/70n9P7vRewlMfPfyqJpfd2T+/7gl8//S1qAZyHqYYYwNU0LKrfRP9bCy/mF48VT+wtYTdtG7Dhz4xsDAwKCsG3xEmPW3ujDfKx1+3hcmHtY2ikYyHtu3nzr1l3lqQwNPth/jDHPdS6VcHP8ZHj0Uu775vJNfcW3LM5jhBw4c+C+hHrOV+99XXQm+txpigs8N/v/9aKdvGneAOdxRONLdnbuRlV2Y4fgFsUWHbppFF9d2PUX33oEDB/5wSfqs//+D+7+0GI+9mq68/KvHr9mYg53UzcXEZHwPneHqOvtSI7e6oe0LrjA6derU35Xbb+xXVXV9JsQj6PvuzffLjFOass3///kllNs0ezvO0MUC2isyDPl4/suQooc2AACgxcG2ROO2ZAAAAABJRU5ErkJggg=="

/***/ }),

/***/ 1610:
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__.p + "img/sergeant.bb9191c.png";

/***/ }),

/***/ 1611:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjQ+jcx2AAABTUlEQVQ4T+3Tz07CMBwH8B2Mpid9A07e/MdFE+N7ePVsYvQVlCXAWTInMFw3OoV1f6BbRwzRbTAO3nwAT76AEQ+EkMwxspmZ8AY06aH9/Nr8+k3KMKuRSeBRaefaWN9dFovyFPvO0tg0vXvZIdb1sgJsmFekQ7MuNNB6TUDgvo6A03P80Wj0LooIRPugWpMBX4Vg4RDQHvWDIEg9qtlgRFE6lSX5y+yaM9uxQ0VRwnKpPOMq3NTAxsRyrEkTNqdzpzZNnb/jfxqCeB53e8MW99gC++G5XqipWth/7n96g+FRhauvzWehWNqfu/vqpu67w8PMU6NObJvaoaqq4Yvnvf3PQXiQIqaxu56bdSi1tiih3wM/uLjl+DPcxuOuQXLJJTJEm4SQP1fxWDfM1BkFtfI6Ng+SA1BE2wi1TpJ1E+G8qhmpSzD249UvWCTwC8t7zQZDErCoAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1612:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjQ+jcx2AAACw0lEQVQ4T3VSS08TURgtEJGEnSYGJFFkDQtjxEQtLnTFxoDhH5j4K0YJhSpQJCVqmb5nOnc6D+fBvKczfdFCpCnhB1jjI0ZYaA1hpQuutyTTlJhOMsm93zn33PPd8/l8HZ/ACf3oH+ysda4BCQZbnG64j+P5JxwnPutGkLfUp0SGnukqoOt6plSqmN0ItmEbhbybPIen0pk5SRQxXpCfW6bVLBaLf1hewNZCYYwEWYylWEwUBEzaUl9YtvW3kC/8pKgstrryGoslMnO+5dXw5XA47GTIDHQdF0bwCFwKLkGO5+qabsYt241vhDf2KYqCbt6F6+F1uBZag6Ik2oZmXTpzgx6nd3Fx0XYcB6qaCvdq9TqghQue1VQK9AeDwf3WBaZhwnqt7pJp0NtuJUOAXoEXGqbl/CJJ8tCxnGNeVgc8AkWCAUESjtHhQ4Ikmo7tfKEI0NcWoAF3U1XMmusUx1JJcpgg6YIgydMegcny07qmFxw7fxWPJcey2WxN4OVbbQECsENZmrvIMe/v87w8i+z1oUhHPIIgCiOozT5BUh4j3j207icpfui/tCRJWrctl+wWI3KR1jR95Rwei6UmKAD86NYp3dI/71R3muXtqh9QjD/4ctkfXA75WzhK6YFpmb8rlUoj5zhTyTTtj7yLTvg2o+l7eDTxQ1ZkiHKGqXQKzi/MwygePdE0rYFibMRisRNVVWEul4P4Jg4XAgswEU98pxnu7pmbjbdvhjAM288X8hC1AXd3dz8ajnXNs/pqJTQaCAQ+lctlKG0hvPrhQDfMK+1WUM49FGD3DMOA6IVPkdC3Vq1jDno4hvuq6dopzdKwlC8doJbbuA8A9rqiKM3tcnUG30w8RCJHqmbc9gQQPqkZ2lGhuP0ogsdneYZvKpJ6o3MOxmVRGfMKBMkOMyw32RHjHZbjh709GrxRkmLGW/t/plOw/lUY0UAAAAAASUVORK5CYII="

/***/ }),

/***/ 1613:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCB2My41LjQ+jcx2AAACG0lEQVQ4T52SPWgTYRzGQ0FnN10cihIEcXfSSXBy0s3ZoYiWiK1oGkmKUCQlH/SSuzb3kQtvkuYul+vVO99Lm6S9mK/Lx+AkBScHHcRdxTw9AoKiidgX3ul9+D3/5/m/Pt+ME9tgrqVSwuVZmplvqqbzcpFETgSQJXKqrJU/5+Tc2xMBeF6+QW2Keq0+VlT14n9DBEHk+m4fo+EI+7X646kAjs9ekCTizwhZf2aL93Oboj/Fpq+IovhpOByiP+jDsuyRaZiXyobp92JNbklR/VKWnPexbOZloVgY27aNSqUCY9eATW3ouo6BO4Bz6IDkCEieILYeQygcwtqLNaTY1PeMKD2cTPY0uHLreTj8RSkpsKgFraKB2+LQ73nutoWF+wtYfLCISDgCOSeDUvqhcehc/y1WNJqYX1pecoPPgpBECe1WG912d+Iej8eRSCbQbXXRafeoab0++9dOiExOBwKB7dXwKsxdE71OD/aejWa3iaN3R+j03HVPMzdzI6HQyhMxK8J1XeQLeeg7OkpKCcPBEAfN5r1/rpPZYAbOgQOvaaiaCjbNorZfg/nKhNvt05kAns/OeyWNG42GN777zWm+WU5xm1GGYX4YhoEqrX7V1J0zUyHJJPOIWhRNp/W+Vm9c/Sn0wDfTbPpjRaugtte4OxVQLCqdslJWvK/7hwsh2+cEUa4WCgVtKkCScndmZRRFMicI2du/ao4BpTVSysErnvgAAAAASUVORK5CYII="

/***/ }),

/***/ 1614:
/***/ (function(module, exports) {

module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABU0lEQVQ4jZXQP2obURDH8e/7s7uSZWEMEag0mJBCvoZbg6+QpDDkGDlD3OgKyQXsJmVwlTQpQiIwBNQYOUHKSm/37fulELgIr9hMOfzmM8MYMvXh+s3bF6f2MlEIwJlkfiz+fLq4mr/+N+tzwOGofT47fzXDTEACs2X1/t0ml80CIjbU34g8gIQvdqS0Db2Bsgx0u88kjfdgF6iKOhfNA4NhJIUvxG4AgI0t5cD1B6oqoPY7igUASZGiPOkPOCes7zBpv9WYhLMxC9hcs2sToQassKWwhYDU/4Kwtiw+etyBZzwVo6NEW2d35QFrARl2j47tCpwV7bHpD3QtOA/JCABnRFB2Pg+0MXHwTHRKT8DvX3khCzRBHJ80yAsBjsj93f88sXEMJ2dQjZCEpyHGbX/AyPvy6CUMp6AE1GDmRW9gF8vl19ubpdxYAJZgNmt+5rJ/AWjgj5luIA5yAAAAAElFTkSuQmCC"

/***/ }),

/***/ 1615:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1650);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("afc81cd0", content, true, {"sourceMap":false});

/***/ }),

/***/ 1648:
/***/ (function(module, exports, __webpack_require__) {

var map = {
	"./aemt.png": 1596,
	"./asst-fire-cheif.png": 1597,
	"./captain.png": 1598,
	"./chief-paramedic.png": 1599,
	"./corporal.png": 1600,
	"./emt.png": 1601,
	"./fire-cheif.png": 1602,
	"./fts.png": 1603,
	"./lieutenant.png": 1604,
	"./major.png": 1605,
	"./officer.png": 1606,
	"./paramedic.png": 1607,
	"./recruit.png": 1608,
	"./senior-deputy.png": 1609,
	"./sergeant.png": 1610,
	"./star-silver-double.png": 1611,
	"./star-silver-quad.png": 1612,
	"./star-silver-single.png": 1613,
	"./vert-gold-single.png": 1614
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = 1648;

/***/ }),

/***/ 1649:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_person_icon_vue_vue_type_style_index_0_id_6bbb1a2f_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1615);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_person_icon_vue_vue_type_style_index_0_id_6bbb1a2f_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_person_icon_vue_vue_type_style_index_0_id_6bbb1a2f_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1650:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".rank-image{filter:brightness(1.5)}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1673:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-person-icon.vue?vue&type=template&id=6bbb1a2f

var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.rankData && _vm.rankData.icon !== 'none' ? _c('img', {
    directives: [{
      name: "tooltip",
      rawName: "v-tooltip",
      value: _vm.rankData.name,
      expression: "rankData.name"
    }],
    staticClass: "mb-1 rank-image",
    attrs: {
      "src": __webpack_require__(1648)("./".concat(_vm.rankData.icon, ".png")),
      "alt": _vm.rankData.name
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-person-icon.vue?vue&type=template&id=6bbb1a2f

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-person-icon.vue?vue&type=script&lang=js


/* harmony default export */ var app_person_iconvue_type_script_lang_js = ({
  name: "app-person-icon",
  props: ['groups'],
  computed: {
    rankData: function rankData() {
      if (!this.groups) return false;
      if (this.groups['police_rank0']) return {
        name: 'Recruit',
        icon: 'recruit'
      };
      if (this.groups['police_rank1']) return {
        name: 'Officer',
        icon: 'officer'
      };
      if (this.groups['police_rank2']) return {
        name: 'Senior Officer',
        icon: 'senior-deputy'
      };
      if (this.groups['police_rank3']) return {
        name: 'Corporal',
        icon: 'corporal'
      };
      if (this.groups['police_rank4']) return {
        name: 'Sergeant',
        icon: 'sergeant'
      };
      if (this.groups['police_rank5']) return {
        name: 'Lieutenant',
        icon: 'vert-gold-single'
      };
      if (this.groups['police_rank6']) return {
        name: 'Captain',
        icon: 'captain'
      };
      if (this.groups['police_rank7']) return {
        name: 'Asst. Chief of Police',
        icon: 'star-silver-single'
      };
      if (this.groups['police_rank8']) return {
        name: 'Chief of Police',
        icon: 'star-silver-single'
      };
      if (this.groups['sheriff_rank0']) return {
        name: 'Probationary Deputy',
        icon: 'recruit'
      };
      if (this.groups['sheriff_rank1']) return {
        name: 'Deputy',
        icon: 'officer'
      };
      if (this.groups['sheriff_rank2']) return {
        name: 'Senior Deputy',
        icon: 'senior-deputy'
      };
      if (this.groups['sheriff_rank3']) return {
        name: 'Corporal',
        icon: 'corporal'
      };
      if (this.groups['sheriff_rank4']) return {
        name: 'Sergeant',
        icon: 'sergeant'
      };
      if (this.groups['sheriff_rank5']) return {
        name: 'Lieutenant',
        icon: 'vert-gold-single'
      };
      if (this.groups['sheriff_rank6']) return {
        name: 'Captain',
        icon: 'captain'
      };
      if (this.groups['sheriff_rank7']) return {
        name: 'Major',
        icon: 'major'
      };
      if (this.groups['sheriff_rank8']) return {
        name: 'Undersheriff',
        icon: 'star-silver-single'
      };
      if (this.groups['sheriff_rank9']) return {
        name: 'Sheriff',
        icon: 'star-silver-quad'
      };
      if (this.groups['ems_rank0']) return {
        name: 'EMT',
        icon: 'emt'
      };
      if (this.groups['ems_rank1']) return {
        name: 'A-EMT',
        icon: 'aemt'
      };
      if (this.groups['ems_rank2']) return {
        name: 'Paramedic',
        icon: 'paramedic'
      };
      if (this.groups['ems_rank3']) return {
        name: 'Senior Paramedic',
        icon: 'paramedic'
      };
      if (this.groups['ems_rank4']) return {
        name: 'Chief Paramedic',
        icon: 'chief-paramedic'
      };
      if (this.groups['ems_rank5']) return {
        name: 'Asst. Fire Chief',
        icon: 'asst-fire-cheif'
      };
      if (this.groups['ems_rank6']) return {
        name: 'Fire Chief',
        icon: 'fire-cheif'
      };
      if (this.groups['doc_rank0']) return {
        name: 'Recruit Corrections Officer',
        icon: 'recruit'
      };
      if (this.groups['doc_rank1']) return {
        name: 'Corrections Officer',
        icon: 'officer'
      };
      if (this.groups['doc_rank2']) return {
        name: 'Ranking Corrections Officer',
        icon: 'corporal'
      };
      if (this.groups['doc_rank3']) return {
        name: 'Corrections Operations Supervisor',
        icon: 'sergeant'
      };
      if (this.groups['doc_rank4']) return {
        name: 'Chief Prison Officer',
        icon: 'vert-gold-single'
      };
      if (this.groups['doc_rank5']) return {
        name: 'Captain',
        icon: 'star-silver-single'
      };
      if (this.groups['SAHP']) return {
        name: 'Trooper',
        icon: 'officer'
      };

      // No one yell at me for this

      if (Array.isArray(this.groups)) {
        if (this.groups.includes('police_rank0')) return {
          name: 'Recruit',
          icon: 'recruit'
        };
        if (this.groups.includes('police_rank1')) return {
          name: 'Officer',
          icon: 'officer'
        };
        if (this.groups.includes('police_rank2')) return {
          name: 'Senior Officer',
          icon: 'senior-deputy'
        };
        if (this.groups.includes('police_rank3')) return {
          name: 'Corporal',
          icon: 'corporal'
        };
        if (this.groups.includes('police_rank4')) return {
          name: 'Sergeant',
          icon: 'sergeant'
        };
        if (this.groups.includes('police_rank5')) return {
          name: 'Lieutenant',
          icon: 'vert-gold-single'
        };
        if (this.groups.includes('police_rank6')) return {
          name: 'Captain',
          icon: 'captain'
        };
        if (this.groups.includes('police_rank7')) return {
          name: 'Asst. Chief of Police',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('police_rank8')) return {
          name: 'Chief of Police',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('sheriff_rank0')) return {
          name: 'Probationary Deputy',
          icon: 'recruit'
        };
        if (this.groups.includes('sheriff_rank1')) return {
          name: 'Deputy',
          icon: 'officer'
        };
        if (this.groups.includes('sheriff_rank2')) return {
          name: 'Senior Deputy',
          icon: 'senior-deputy'
        };
        if (this.groups.includes('sheriff_rank3')) return {
          name: 'Corporal',
          icon: 'corporal'
        };
        if (this.groups.includes('sheriff_rank4')) return {
          name: 'Sergeant',
          icon: 'sergeant'
        };
        if (this.groups.includes('sheriff_rank5')) return {
          name: 'Lieutenant',
          icon: 'vert-gold-single'
        };
        if (this.groups.includes('sheriff_rank6')) return {
          name: 'Captain',
          icon: 'captain'
        };
        if (this.groups.includes('sheriff_rank7')) return {
          name: 'Major',
          icon: 'major'
        };
        if (this.groups.includes('sheriff_rank8')) return {
          name: 'Undersheriff',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('sheriff_rank9')) return {
          name: 'Sheriff',
          icon: 'star-silver-quad'
        };
        if (this.groups.includes('ems_rank0')) return {
          name: 'EMT',
          icon: 'emt'
        };
        if (this.groups.includes('ems_rank1')) return {
          name: 'A-EMT',
          icon: 'aemt'
        };
        if (this.groups.includes('ems_rank2')) return {
          name: 'Paramedic',
          icon: 'paramedic'
        };
        if (this.groups.includes('ems_rank3')) return {
          name: 'Senior Paramedic',
          icon: 'paramedic'
        };
        if (this.groups.includes('ems_rank4')) return {
          name: 'Chief Paramedic',
          icon: 'chief-paramedic'
        };
        if (this.groups.includes('ems_rank5')) return {
          name: 'Asst. Fire Chief',
          icon: 'asst-fire-cheif'
        };
        if (this.groups.includes('ems_rank6')) return {
          name: 'Fire Chief',
          icon: 'fire-cheif'
        };
        if (this.groups.includes('doc_rank0')) return {
          name: 'Recruit Corrections Officer',
          icon: 'recruit'
        };
        if (this.groups.includes('doc_rank1')) return {
          name: 'Corrections Officer',
          icon: 'officer'
        };
        if (this.groups.includes('doc_rank2')) return {
          name: 'Ranking Corrections Officer',
          icon: 'corporal'
        };
        if (this.groups.includes('doc_rank3')) return {
          name: 'Corrections Operations Supervisor',
          icon: 'sergeant'
        };
        if (this.groups.includes('doc_rank4')) return {
          name: 'Chief Prison Officer',
          icon: 'vert-gold-single'
        };
        if (this.groups.includes('doc_rank5')) return {
          name: 'Captain',
          icon: 'star-silver-single'
        };
        if (this.groups.includes('SAHP')) return {
          name: 'Trooper',
          icon: 'officer'
        };
      }
      return false;
    }
  }
});
// CONCATENATED MODULE: ./components/Common/app-person-icon.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_person_iconvue_type_script_lang_js = (app_person_iconvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-person-icon.vue?vue&type=style&index=0&id=6bbb1a2f&prod&lang=scss
var app_person_iconvue_type_style_index_0_id_6bbb1a2f_prod_lang_scss = __webpack_require__(1649);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-person-icon.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_person_iconvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_person_icon = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1693:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1792);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("08729256", content, true, {"sourceMap":false});

/***/ }),

/***/ 1791:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_draggable_vue_vue_type_style_index_0_id_68e268e5_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1693);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_draggable_vue_vue_type_style_index_0_id_68e268e5_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_draggable_vue_vue_type_style_index_0_id_68e268e5_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1792:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".test[data-v-68e268e5]{position:inherit}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

}]);