(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[49],{

/***/ 2063:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id.vue?vue&type=template&id=2fdbdb9e



var _idvue_type_template_id_2fdbdb9e_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('crudy-page', {
    ref: "page",
    attrs: {
      "name": "Business",
      "config": {
        hideHeader: true,
        hideRelationships: true,
        hideLoader: true
      }
    },
    on: {
      "loaded": function loaded($event) {
        _vm.business = $event;
      }
    },
    scopedSlots: _vm._u([{
      key: "body",
      fn: function fn(_ref) {
        var item = _ref.item,
          model = _ref.model,
          uuid = _ref.uuid,
          actions = _ref.actions;
        return [_c('app-page-header', {
          attrs: {
            "tabs": _vm.tabs
          },
          scopedSlots: _vm._u([{
            key: "title",
            fn: function fn() {
              return [_vm.mode === 'tablet' ? _c(VBtn["a" /* default */], {
                staticClass: "mr-3",
                attrs: {
                  "small": ""
                },
                on: {
                  "click": function click($event) {
                    return _vm.$router.back();
                  }
                }
              }, [_vm._v("\n          Back\n        ")]) : _vm._e(), _vm._v(" "), _c('NuxtLink', {
                attrs: {
                  "to": "/business/".concat(item.id, "/info")
                }
              }, [_vm._v("\n          " + _vm._s(item.name) + "\n        ")]), _vm._v(" "), _c('crudy-context-menu', {
                attrs: {
                  "uuid": uuid,
                  "name": model,
                  "item": item,
                  "actions": actions
                }
              })];
            },
            proxy: true
          }, {
            key: "actions",
            fn: function fn() {
              return [_vm.business.is_part ? _c(VBtn["a" /* default */], {
                staticClass: "text-right",
                attrs: {
                  "small": "",
                  "color": _vm.business.is_clocked_in ? 'red' : 'primary'
                },
                on: {
                  "click": _vm.toggleClockStatus
                }
              }, [_vm._v("\n          " + _vm._s(_vm.business.is_clocked_in ? 'Clock Out' : 'Clock In') + "\n        ")]) : _vm._e()];
            },
            proxy: true
          }], null, true)
        }), _vm._v(" "), _c('NuxtChild', {
          attrs: {
            "business": item
          }
        })];
      }
    }])
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/business/_id.vue?vue&type=template&id=2fdbdb9e

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Crudy/crudy-page/crudy-page.vue + 9 modules
var crudy_page = __webpack_require__(1555);

// EXTERNAL MODULE: ./components/Common/app-page-header.vue + 4 modules
var app_page_header = __webpack_require__(1666);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-context-menu.vue + 4 modules
var crudy_context_menu = __webpack_require__(74);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id.vue?vue&type=script&lang=js










function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/* harmony default export */ var _idvue_type_script_lang_js = ({
  components: {
    CrudyContextMenu: crudy_context_menu["a" /* default */],
    AppPageHeader: app_page_header["a" /* default */],
    CrudyPage: crudy_page["a" /* default */]
  },
  data: function data() {
    return {
      business: null
    };
  },
  methods: {
    toggleClockStatus: function toggleClockStatus() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.$post("/business/".concat(_this.business.id, "/clock-in"));
            case 2:
              _context.next = 4;
              return _this.$refs.page.refresh();
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    }
  },
  computed: _objectSpread({
    canManage: function canManage() {
      if (this.business) {
        var groupName = "".concat(this.business.name, "|perm_management");
        return this.hasAnyGroup([groupName]);
      }
    },
    cssVars: function cssVars() {
      var _this$business, _this$business2;
      return {
        '--bus-color-a': (_this$business = this.business) === null || _this$business === void 0 ? void 0 : _this$business.c_color_a,
        '--bus-color-b': (_this$business2 = this.business) === null || _this$business2 === void 0 ? void 0 : _this$business2.c_color_b
      };
    },
    tabs: function tabs() {
      var _this$business3,
        _this$business4,
        _this2 = this,
        _this$business5,
        _this$business6,
        _this$business7,
        _this$business8;
      return [{
        name: 'Overview',
        icon: 'fa-solid fa-star',
        route: "/business/".concat(this.$route.params.id, "/info"),
        iconColor: (_this$business3 = this.business) === null || _this$business3 === void 0 ? void 0 : _this$business3.c_color_a
      }, {
        name: 'Community',
        icon: 'fa-solid fa-heart',
        route: "/business/".concat(this.$route.params.id, "/community"),
        iconColor: (_this$business4 = this.business) === null || _this$business4 === void 0 ? void 0 : _this$business4.c_color_a,
        check: function check() {
          return _this2.business && _this2.business.is_part;
        }
      }, {
        name: 'Members',
        icon: 'fa-solid fa-person',
        route: "/business/".concat(this.$route.params.id, "/members"),
        check: function check() {
          return _this2.canManage || _this2.hasAnyGroup(['mod', 'admin']);
        },
        iconColor: (_this$business5 = this.business) === null || _this$business5 === void 0 ? void 0 : _this$business5.c_color_a
      }, {
        name: 'Settings',
        icon: 'fa-solid fa-cog',
        route: "/business/".concat(this.$route.params.id, "/settings"),
        check: function check() {
          return _this2.canManage;
        },
        iconColor: (_this$business6 = this.business) === null || _this$business6 === void 0 ? void 0 : _this$business6.c_color_a
      }, {
        name: 'Documents',
        icon: 'fa-solid fa-file',
        route: "/business/".concat(this.$route.params.id, "/documents"),
        check: function check() {
          return _this2.canManage;
        },
        iconColor: (_this$business7 = this.business) === null || _this$business7 === void 0 ? void 0 : _this$business7.c_color_a
      }, {
        name: 'Time Logs',
        icon: 'fa-solid fa-clock',
        route: "/business/".concat(this.$route.params.id, "/time-logs"),
        check: function check() {
          return _this2.canManage;
        },
        iconColor: (_this$business8 = this.business) === null || _this$business8 === void 0 ? void 0 : _this$business8.c_color_a
      }];
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    user: 'auth/user',
    isAdmin: 'auth/isAdmin',
    hasAnyGroup: 'auth/hasAnyGroup'
  }))
});
// CONCATENATED MODULE: ./pages/business/_id.vue?vue&type=script&lang=js
 /* harmony default export */ var business_idvue_type_script_lang_js = (_idvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/business/_id.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  business_idvue_type_script_lang_js,
  _idvue_type_template_id_2fdbdb9e_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);