(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[84],{

/***/ 2090:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/dashboard/index.vue?vue&type=template&id=e7f36136&scoped=true
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-page', [_c('div')]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/dashboard/index.vue?vue&type=template&id=e7f36136&scoped=true

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/dashboard/index.vue?vue&type=script&lang=js

/* harmony default export */ var dashboardvue_type_script_lang_js = ({
  name: "police-incidents-list-bolo",
  components: {
    AppPage: AppPage["a" /* default */]
  }
});
// CONCATENATED MODULE: ./pages/cad/dashboard/index.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_dashboardvue_type_script_lang_js = (dashboardvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/dashboard/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_dashboardvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "e7f36136",
  null
  
)

/* harmony default export */ var dashboard = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);