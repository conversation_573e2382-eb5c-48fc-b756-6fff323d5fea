(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[70],{

/***/ 1688:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1781);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("26470fd2", content, true, {"sourceMap":false});

/***/ }),

/***/ 1780:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_bolos_vue_vue_type_style_index_0_id_eb0262e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1688);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_bolos_vue_vue_type_style_index_0_id_eb0262e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_bolos_vue_vue_type_style_index_0_id_eb0262e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1781:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".truncate-title[data-v-eb0262e0]{display:block;font-style:italic;max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2080:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/bolos.vue?vue&type=template&id=eb0262e0&scoped=true







var bolosvue_type_template_id_eb0262e0_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-page', {}, [_vm.bolos.length ? _c('div', [_c(VRow["a" /* default */], {
    attrs: {
      "no-gutters": ""
    }
  }, _vm._l(_vm.bolos, function (record) {
    var _record$car$vehicle_t, _record$car$vehicle_t2, _ref, _record$car$vehicle_t3, _record$car$vehicle_t4;
    return _c(VCol["a" /* default */], {
      key: record.id,
      staticClass: "pa-2",
      attrs: {
        "cols": "3"
      }
    }, [_c(VCard["a" /* default */], {
      staticClass: "pa-3 d-flex flex-column align-center justify-start text-center",
      staticStyle: {
        "background-color": "#1e1e1e",
        "border-left": "4px solid #ff5555"
      },
      attrs: {
        "flat": "",
        "height": "240",
        "to": "/cad/incidents/view/".concat(record.incident.id)
      }
    }, [_c(VImg["a" /* default */], {
      staticClass: "mb-2 vehicle-image",
      attrs: {
        "src": (_record$car$vehicle_t = (_record$car$vehicle_t2 = record.car.vehicle_type) === null || _record$car$vehicle_t2 === void 0 ? void 0 : _record$car$vehicle_t2.image_link) !== null && _record$car$vehicle_t !== void 0 ? _record$car$vehicle_t : 'http://dev-res.blrp.net/img/default.webp',
        "height": "100",
        "width": "100%",
        "contain": "",
        "aspect-ratio": "1.6",
        "transition": false
      }
    }), _vm._v(" "), _c('div', {
      staticClass: "text-uppercase font-weight-bold text-info",
      staticStyle: {
        "font-size": "18px"
      }
    }, [_vm._v("\n            " + _vm._s(record.car.registration) + "\n          ")]), _vm._v(" "), _c('div', {
      staticClass: "text-grey-lighten-1",
      staticStyle: {
        "font-size": "14px"
      }
    }, [_vm._v("\n            " + _vm._s((_ref = (_record$car$vehicle_t3 = (_record$car$vehicle_t4 = record.car.vehicle_type) === null || _record$car$vehicle_t4 === void 0 ? void 0 : _record$car$vehicle_t4.name) !== null && _record$car$vehicle_t3 !== void 0 ? _record$car$vehicle_t3 : record.car.vehicle) !== null && _ref !== void 0 ? _ref : 'Unknown model') + "\n          ")]), _vm._v(" "), _c('div', {
      staticClass: "text-caption mt-1 text-grey truncate-title",
      attrs: {
        "title": record.incident.title
      }
    }, [_vm._v("\n            " + _vm._s(record.incident.title) + "\n          ")])], 1)], 1);
  }), 1)], 1) : _c('div', {
    staticClass: "d-flex align-center justify-center fill-height",
    staticStyle: {
      "margin-top": "40px"
    }
  }, [_c(VCard["a" /* default */], {
    staticClass: "pa-5 text-center",
    staticStyle: {
      "background": "radial-gradient(circle at top, #2c2c2c, #1b1b1b)",
      "border": "2px dashed #ff5555",
      "max-width": "500px"
    },
    attrs: {
      "elevation": "8"
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mb-3",
    attrs: {
      "color": "red lighten-2",
      "size": "64"
    }
  }, [_vm._v("mdi-police-badge")]), _vm._v(" "), _c('div', {
    staticClass: "text-h6 font-weight-bold mb-1",
    staticStyle: {
      "color": "#ffffff"
    }
  }, [_vm._v("\n        No active BOLOs at the moment\n      ")]), _vm._v(" "), _c('div', {
    staticClass: "text-caption text-grey-lighten-1 mb-4"
  }, [_vm._v("\n        The hot sheet's gone cold. Roads are quiet — for now.\n      ")]), _vm._v(" "), _c(VImg["a" /* default */], {
    staticClass: "mx-auto fade-in-out",
    staticStyle: {
      "opacity": "0.5",
      "margin-top": "32px"
    },
    attrs: {
      "src": "http://dev-res.blrp.net/img/crown_vic_silhouette.webp",
      "contain": "",
      "height": "200",
      "width": "400"
    }
  })], 1)], 1)]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/bolos.vue?vue&type=template&id=eb0262e0&scoped=true

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/bolos.vue?vue&type=script&lang=js


/* harmony default export */ var bolosvue_type_script_lang_js = ({
  name: 'BolosPage',
  components: {
    AppPage: AppPage["a" /* default */]
  },
  data: function data() {
    return {
      bolos: []
    };
  },
  mounted: function mounted() {
    this.loadBolos();
  },
  methods: {
    loadBolos: function loadBolos() {
      var _this = this;
      this.$axios.$get("/police/bolo-vehicles").then(function (r) {
        _this.bolos = r;
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./pages/cad/bolos.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_bolosvue_type_script_lang_js = (bolosvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/cad/bolos.vue?vue&type=style&index=0&id=eb0262e0&prod&scoped=true&lang=css
var bolosvue_type_style_index_0_id_eb0262e0_prod_scoped_true_lang_css = __webpack_require__(1780);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/bolos.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_bolosvue_type_script_lang_js,
  bolosvue_type_template_id_eb0262e0_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "eb0262e0",
  null
  
)

/* harmony default export */ var bolos = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);