(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[186],{

/***/ 1563:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(7);
/* harmony import */ var core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_symbol_js__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(9);
/* harmony import */ var core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_array_filter_js__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(14);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptor_js__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(15);
/* harmony import */ var core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_get_own_property_descriptors_js__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(8);
/* harmony import */ var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(6);
/* harmony import */ var core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_es_object_to_string_js__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(11);
/* harmony import */ var core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(core_js_modules_web_dom_collections_for_each_js__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(0);
/* harmony import */ var _mixins_measurable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(188);
/* harmony import */ var _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(100);
/* harmony import */ var _directives_intersect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(175);
/* harmony import */ var _util_mixins__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(16);
/* harmony import */ var _util_helpers__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(1);








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(_babel_runtime_helpers_esm_defineProperty__WEBPACK_IMPORTED_MODULE_7__[/* default */ "a"])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Mixins

 // Directives

 // Utilities



/* harmony default export */ __webpack_exports__["a"] = (Object(_util_mixins__WEBPACK_IMPORTED_MODULE_11__[/* default */ "a"])(_mixins_measurable__WEBPACK_IMPORTED_MODULE_8__[/* default */ "a"], _mixins_toggleable__WEBPACK_IMPORTED_MODULE_9__[/* default */ "a"]).extend({
  name: 'VLazy',
  directives: {
    intersect: _directives_intersect__WEBPACK_IMPORTED_MODULE_10__[/* default */ "a"]
  },
  props: {
    options: {
      type: Object,
      // For more information on types, navigate to:
      // https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API
      default: function _default() {
        return {
          root: undefined,
          rootMargin: undefined,
          threshold: undefined
        };
      }
    },
    tag: {
      type: String,
      default: 'div'
    },
    transition: {
      type: String,
      default: 'fade-transition'
    }
  },
  computed: {
    styles: function styles() {
      return _objectSpread({}, this.measurableStyles);
    }
  },
  methods: {
    genContent: function genContent() {
      var children = this.isActive && Object(_util_helpers__WEBPACK_IMPORTED_MODULE_12__[/* getSlot */ "s"])(this);
      return this.transition ? this.$createElement('transition', {
        props: {
          name: this.transition
        }
      }, children) : children;
    },
    onObserve: function onObserve(entries, observer, isIntersecting) {
      if (this.isActive) return;
      this.isActive = isIntersecting;
    }
  },
  render: function render(h) {
    return h(this.tag, {
      staticClass: 'v-lazy',
      attrs: this.$attrs,
      directives: [{
        name: 'intersect',
        value: {
          handler: this.onObserve,
          options: this.options
        }
      }],
      on: this.$listeners,
      style: this.styles
    }, [this.genContent()]);
  }
}));

/***/ }),

/***/ 1565:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1574);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("4b66da15", content, true, {"sourceMap":false});

/***/ }),

/***/ 1571:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VLazy/VLazy.js
var VLazy = __webpack_require__(1563);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=template&id=809e8f90


var app_card_itemsvue_type_template_id_809e8f90_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VLazy["a" /* default */], [_c('div', {
    staticClass: "page-sub-scroller no-drag"
  }, [_vm._t("header"), _vm._v(" "), _vm._l(_vm.items, function (item, index) {
    return _c('render-item', {
      key: item.id,
      class: {
        'item-selected': _vm.currentIndex === index,
        'socket-active': _vm.visible[_vm.currentIndex]
      },
      attrs: {
        "index": index
      },
      on: {
        "enteredScreen": _vm._enteredScreen,
        "leftScreen": _vm._leftScreen
      },
      scopedSlots: _vm._u([{
        key: "default",
        fn: function fn(_ref) {
          var ref = _ref.ref;
          return [_vm._t("render", null, {
            "item": item,
            "index": index
          })];
        }
      }], null, true)
    });
  })], 2)]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=template&id=809e8f90

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/intersect/index.js
var intersect = __webpack_require__(175);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=template&id=4ba6f808


var render_itemvue_type_template_id_4ba6f808_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    directives: [{
      def: intersect["a" /* default */],
      name: "intersect",
      rawName: "v-intersect",
      value: {
        handler: _vm._onItemShownScreen,
        options: {
          threshold: [0, 0.5, 1.0]
        }
      },
      expression: "{ handler: _onItemShownScreen, options: {  threshold: [0, 0.5, 1.0]  } }"
    }],
    ref: "item"
  }, [_vm._t("default")], 2);
};
var render_itemvue_type_template_id_4ba6f808_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=template&id=4ba6f808

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=script&lang=js

















function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

/* harmony default export */ var render_itemvue_type_script_lang_js = ({
  name: 'render-item',
  components: {},
  props: ['index'],
  data: function data() {
    return {
      isShowingOnScreen: false
    };
  },
  created: function created() {},
  methods: {
    _onItemShownScreen: function _onItemShownScreen(entries, observer) {
      var _iterator = _createForOfIteratorHelper(entries),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var entry = _step.value;
          this.isShowingOnScreen = entries[0].intersectionRatio >= 0.5;
          if (this.isShowingOnScreen) {
            this.$emit('enteredScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'red'
          } else {
            this.$emit('leftScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'none'
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_render_itemvue_type_script_lang_js = (render_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/render-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_render_itemvue_type_script_lang_js,
  render_itemvue_type_template_id_4ba6f808_render,
  render_itemvue_type_template_id_4ba6f808_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var render_item = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=script&lang=js








function app_card_itemsvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function app_card_itemsvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? app_card_itemsvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : app_card_itemsvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var app_card_itemsvue_type_script_lang_js = ({
  name: 'app-card-items',
  components: {
    RenderItem: render_item,
    AppCard: app_card["a" /* default */]
  },
  props: ['items', 'socketChannel'],
  data: function data() {
    return {
      visible: {},
      currentIndex: -1
    };
  },
  methods: {
    _onPrevious: function _onPrevious() {
      if (this.currentIndex > -2) {
        var _this$$refs$;
        this.currentIndex--;
        (_this$$refs$ = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$ === void 0 || _this$$refs$.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$2;
        this.currentIndex = this.items.length - 1;
        (_this$$refs$2 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$2 === void 0 || _this$$refs$2.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onNext: function _onNext() {
      if (this.currentIndex < this.items.length - 2) {
        var _this$$refs$3;
        this.currentIndex++;
        (_this$$refs$3 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$3 === void 0 || _this$$refs$3.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$4;
        this.currentIndex = 0;
        (_this$$refs$4 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$4 === void 0 || _this$$refs$4.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onEnter: function _onEnter() {
      var item = this.items[this.currentIndex];
      this.$emit('chosen', item);
    },
    _enteredScreen: function _enteredScreen(index) {
      if (this.visible[index]) {
        return;
      }
      this.visible[index] = true;
      this.$emit('enteredView', index);

      // if (this.socketChannel && this.items[index].uuid) {
      //   const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
      //   if ( window.echo ) {
      //     window.echo.listen(channel, 'SocialPostUpdated', ({ post, fromPerson }) => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'update',
      //         post: post,
      //       })
      //     })
      //
      //     window.echo.listen(channel, 'SocialPostRemoved', () => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'removed',
      //         post: {},
      //       })
      //     })
      //   }
      //
      // } else {
      //   console.error('socket hook failed - no uuid exists - record too old')
      // }
    },
    _leftScreen: function _leftScreen(index) {
      if (this.visible[index]) {
        // const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
        //
        // // ts-ignore
        // window.echo.leave(channel)
        //
        // delete this.visible[index]
      }
    }
  },
  beforeDestroy: function beforeDestroy() {
    // for (const [ index, value ] of Object.entries(this.visible)) {
    //   const channel = `${ this.socketChannel }.${ this.items[index].id }`
    //
    //   // ts-ignore
    //   window.echo.leave(channel)
    //
    //   delete this.visible[index]
    // }
  },
  watch: {
    currentIndex: function currentIndex(value) {
      this.$emit('index', this.currentIndex);
      if (this.currentIndex === -1) {
        this.$emit('nothingChosen');
      } else {
        this.$emit('stuffChosen');
      }
    }
  },
  computed: app_card_itemsvue_type_script_lang_js_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    focusing: 'system/focusing'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_card_itemsvue_type_script_lang_js = (app_card_itemsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-card-items.vue?vue&type=style&index=0&id=809e8f90&prod&lang=scss
var app_card_itemsvue_type_style_index_0_id_809e8f90_prod_lang_scss = __webpack_require__(1573);

// CONCATENATED MODULE: ./components/Common/app-card-items.vue






/* normalize component */

var app_card_items_component = Object(componentNormalizer["a" /* default */])(
  Common_app_card_itemsvue_type_script_lang_js,
  app_card_itemsvue_type_template_id_809e8f90_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_card_items = __webpack_exports__["a"] = (app_card_items_component.exports);

/***/ }),

/***/ 1573:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1565);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1574:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".item-selected{border-left:2px solid wheat!important}.socket-active{background-color:#ff5e5e!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1583:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1633);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("a73a2698", content, true, {"sourceMap":false});

/***/ }),

/***/ 1632:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_infinite_scroll_vue_vue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1583);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_infinite_scroll_vue_vue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_infinite_scroll_vue_vue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1633:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".infinite-scroll-container[data-v-0734d0e0]{position:relative}.load-trigger[data-v-0734d0e0]{bottom:0;pointer-events:none;position:absolute;width:100%}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1638:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressCircular/VProgressCircular.js
var VProgressCircular = __webpack_require__(300);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-infinite-scroll.vue?vue&type=template&id=0734d0e0&scoped=true



var app_infinite_scrollvue_type_template_id_0734d0e0_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "infinite-scroll-container"
  }, [_vm._l(_vm.paginatedItems, function (item, index) {
    return _c('div', {
      key: _vm.getItemKey(item, index)
    }, [_vm._t("item", null, {
      "item": item,
      "index": index
    })], 2);
  }), _vm._v(" "), _vm.loading ? _c('div', {
    staticClass: "text-center py-3"
  }, [_c(VProgressCircular["a" /* default */], {
    attrs: {
      "indeterminate": "",
      "color": "primary",
      "size": "24"
    }
  }), _vm._v(" "), _c('div', {
    staticClass: "text-caption mt-2"
  }, [_vm._v("Loading more...")])], 1) : _vm._e(), _vm._v(" "), _vm.hasMore && !_vm.loading ? _c('div', {
    staticClass: "text-center py-3"
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "text": "",
      "color": "primary",
      "small": ""
    },
    on: {
      "click": _vm.loadMore
    }
  }, [_vm._v("\n      Load More\n    ")])], 1) : _vm._e(), _vm._v(" "), !_vm.hasMore && _vm.paginatedItems.length > 0 ? _c('div', {
    staticClass: "text-center py-3"
  }, [_c('div', {
    staticClass: "text-caption text-muted"
  }, [_vm._v("No more items to load")])]) : _vm._e(), _vm._v(" "), _c('div', {
    ref: "loadTrigger",
    staticClass: "load-trigger",
    staticStyle: {
      "height": "1px"
    }
  })], 2);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-infinite-scroll.vue?vue&type=template&id=0734d0e0&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.number.constructor.js
var es_number_constructor = __webpack_require__(42);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-infinite-scroll.vue?vue&type=script&lang=js




/* harmony default export */ var app_infinite_scrollvue_type_script_lang_js = ({
  name: 'app-infinite-scroll',
  props: {
    items: {
      type: Array,
      required: true
    },
    itemsPerPage: {
      type: Number,
      default: 10
    },
    keyField: {
      type: String,
      default: 'id'
    },
    loading: {
      type: Boolean,
      default: false
    },
    serverSide: {
      type: Boolean,
      default: false
    },
    totalItems: {
      type: Number,
      default: null
    }
  },
  data: function data() {
    return {
      currentPage: 1,
      observer: null
    };
  },
  computed: {
    paginatedItems: function paginatedItems() {
      if (this.serverSide) {
        return this.items;
      }
      return this.items.slice(0, this.currentPage * this.itemsPerPage);
    },
    hasMore: function hasMore() {
      if (this.serverSide) {
        return this.totalItems ? this.items.length < this.totalItems : false;
      }
      return this.paginatedItems.length < this.items.length;
    }
  },
  mounted: function mounted() {
    this.setupIntersectionObserver();
  },
  beforeDestroy: function beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  watch: {
    items: function items() {
      // Reset pagination when items change
      this.currentPage = 1;
    }
  },
  methods: {
    setupIntersectionObserver: function setupIntersectionObserver() {
      var _this = this;
      if (!window.IntersectionObserver) {
        return; // Fallback to load more button
      }
      this.observer = new IntersectionObserver(function (entries) {
        entries.forEach(function (entry) {
          if (entry.isIntersecting && _this.hasMore && !_this.loading) {
            _this.loadMore();
          }
        });
      }, {
        rootMargin: '100px' // Start loading 100px before the trigger comes into view
      });
      if (this.$refs.loadTrigger) {
        this.observer.observe(this.$refs.loadTrigger);
      }
    },
    loadMore: function loadMore() {
      if (this.hasMore && !this.loading) {
        if (!this.serverSide) {
          this.currentPage++;
        }
        this.$emit('load-more', {
          page: this.serverSide ? Math.ceil(this.items.length / this.itemsPerPage) + 1 : this.currentPage,
          itemsPerPage: this.itemsPerPage,
          offset: this.items.length
        });
      }
    },
    getItemKey: function getItemKey(item, index) {
      return item[this.keyField] || index;
    },
    reset: function reset() {
      this.currentPage = 1;
    }
  }
});
// CONCATENATED MODULE: ./components/Common/app-infinite-scroll.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_infinite_scrollvue_type_script_lang_js = (app_infinite_scrollvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-infinite-scroll.vue?vue&type=style&index=0&id=0734d0e0&prod&scoped=true&lang=css
var app_infinite_scrollvue_type_style_index_0_id_0734d0e0_prod_scoped_true_lang_css = __webpack_require__(1632);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-infinite-scroll.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_infinite_scrollvue_type_script_lang_js,
  app_infinite_scrollvue_type_template_id_0734d0e0_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "0734d0e0",
  null
  
)

/* harmony default export */ var app_infinite_scroll = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1671:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-new-message.vue?vue&type=template&id=7992d4bf
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div');
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-new-message.vue?vue&type=template&id=7992d4bf

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-new-message.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/* harmony default export */ var text_new_messagevue_type_script_lang_js = ({
  name: "text-new-message",
  components: {
    AppFormGroup: app_form_group["a" /* default */],
    AppCard: app_card["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */]
  },
  mixins: [ContactFormMixin["a" /* default */]],
  props: [],
  data: function data() {
    return {
      error: null,
      showingNewMessageModal: false,
      form: {
        phone: null,
        message: null
      }
    };
  },
  created: function created() {
    var _this = this;
    this.$nextTick(function () {
      var self = _this;
      window.onkeydown = function (e) {
        var code = e.keyCode ? e.keyCode : e.which;
        if (code === 32) {
          // space
          self.showingNewMessageModal = true;
        }
      };
    });
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Pages/Text/text-new-message.vue?vue&type=script&lang=js
 /* harmony default export */ var Text_text_new_messagevue_type_script_lang_js = (text_new_messagevue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-new-message.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_new_messagevue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var text_new_message = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1734:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1933);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("7f34e784", content, true, {"sourceMap":false});

/***/ }),

/***/ 1932:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_sms_vue_vue_type_style_index_0_id_0f284070_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1734);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_sms_vue_vue_type_style_index_0_id_0f284070_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_sms_vue_vue_type_style_index_0_id_0f284070_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1933:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".sms-container{display:flex;flex-direction:column;height:100%;padding:0}.sms-header{align-items:center;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);background:rgba(0,0,0,.8);border-bottom:1px solid hsla(0,0%,100%,.1);display:flex;justify-content:space-between;padding:16px 20px 12px}.sms-header .header-title .messages-title{color:#fff;font-size:24px;font-weight:600;line-height:1.2;margin:0}.sms-header .header-title .thread-count{color:#8e8e93;font-size:13px;margin-top:2px}.sms-header .new-message-btn{background:linear-gradient(135deg,#007aff,#0056cc)!important;box-shadow:0 4px 12px rgba(0,122,255,.3)!important}.sms-header .new-message-btn:hover{box-shadow:0 6px 16px rgba(0,122,255,.4)!important;transform:scale(1.05)}.search-container{background:rgba(0,0,0,.8);border-bottom:1px solid hsla(0,0%,100%,.1);padding:8px 20px 12px}.search-container .search-input-wrapper{align-items:center;background:hsla(0,0%,100%,.1);border-radius:10px;display:flex;padding:8px 12px;position:relative;transition:all .2s ease}.search-container .search-input-wrapper:focus-within{background:hsla(0,0%,100%,.15);box-shadow:0 0 0 2px rgba(0,122,255,.3)}.search-container .search-input-wrapper .search-icon{margin-right:8px;opacity:.7}.search-container .search-input-wrapper .search-input{background:transparent;border:none;color:#fff;flex:1;font-family:inherit;font-size:16px;outline:none}.search-container .search-input-wrapper .search-input::-moz-placeholder{color:#8e8e93;opacity:1}.search-container .search-input-wrapper .search-input::placeholder{color:#8e8e93;opacity:1}.search-container .search-input-wrapper .search-input:focus{outline:none}.search-container .search-input-wrapper .clear-search-btn{background:transparent!important;box-shadow:none!important;margin-left:4px}.search-container .search-input-wrapper .clear-search-btn:hover{background:hsla(0,0%,100%,.1)!important}.messages-list{flex:1;overflow-y:auto;padding:4px 0}.messages-list .thread-item{border-radius:8px;cursor:pointer;margin:0 8px 4px;overflow:hidden;transition:all .2s ease}.messages-list .thread-item:hover{box-shadow:0 4px 12px rgba(0,0,0,.3);transform:translateY(-1px)}.messages-list .thread-item:active{transform:translateY(0)}.messages-list::-webkit-scrollbar{width:4px}.messages-list::-webkit-scrollbar-track{background:transparent}.messages-list::-webkit-scrollbar-thumb{background:hsla(0,0%,100%,.2);border-radius:2px}.messages-list::-webkit-scrollbar-thumb:hover{background:hsla(0,0%,100%,.3)}.empty-state{align-items:center;display:flex;flex:1;flex-direction:column;justify-content:center;padding:40px 20px;text-align:center}.empty-state .empty-icon{margin-bottom:16px;opacity:.6}.empty-state .empty-title{color:#fff;font-size:20px;font-weight:600;margin:0 0 8px}.empty-state .empty-subtitle{color:#8e8e93;font-size:14px;line-height:1.4;margin:0}.card-selected{color:var(--person-color);font-weight:bolder}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2174:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/sms.vue?vue&type=template&id=0f284070



var smsvue_type_template_id_0f284070_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "sms-container"
  }, [_c('div', {
    staticClass: "sms-header"
  }, [_c('div', {
    staticClass: "header-title"
  }, [_c('h2', {
    staticClass: "messages-title"
  }, [_vm._v("Messages")]), _vm._v(" "), _c('div', {
    staticClass: "thread-count"
  }, [_vm._v(_vm._s(_vm.filteredThreads.length) + " conversation" + _vm._s(_vm.filteredThreads.length !== 1 ? 's' : ''))])]), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "new-message-btn",
    attrs: {
      "color": "primary",
      "fab": "",
      "small": "",
      "elevation": "2"
    },
    on: {
      "click": _vm.sendNewMessage
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-plus")])], 1)], 1), _vm._v(" "), _c('div', {
    staticClass: "search-container"
  }, [_c('div', {
    staticClass: "search-input-wrapper"
  }, [_c(VIcon["a" /* default */], {
    staticClass: "search-icon",
    attrs: {
      "small": "",
      "color": "#8e8e93"
    }
  }, [_vm._v("fa-solid fa-search")]), _vm._v(" "), _c('input', {
    directives: [{
      name: "model",
      rawName: "v-model",
      value: _vm.searchQuery,
      expression: "searchQuery"
    }],
    staticClass: "search-input",
    attrs: {
      "placeholder": "Search messages or contacts...",
      "type": "text"
    },
    domProps: {
      "value": _vm.searchQuery
    },
    on: {
      "input": function input($event) {
        if ($event.target.composing) return;
        _vm.searchQuery = $event.target.value;
      }
    }
  }), _vm._v(" "), _vm.searchQuery ? _c(VBtn["a" /* default */], {
    staticClass: "clear-search-btn",
    attrs: {
      "icon": "",
      "x-small": ""
    },
    on: {
      "click": _vm.clearSearch
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "small": "",
      "color": "#8e8e93"
    }
  }, [_vm._v("fa-solid fa-times")])], 1) : _vm._e()], 1)]), _vm._v(" "), _c('div', {
    staticClass: "messages-list"
  }, [_c('app-infinite-scroll', {
    attrs: {
      "items": _vm.filteredThreads,
      "items-per-page": 10,
      "key-field": "phone",
      "loading": _vm.threadsLoading
    },
    on: {
      "load-more": _vm.onLoadMoreThreads
    },
    scopedSlots: _vm._u([{
      key: "item",
      fn: function fn(_ref) {
        var item = _ref.item,
          index = _ref.index;
        return [_c('div', {
          staticClass: "thread-item",
          on: {
            "click": function click($event) {
              return _vm.view(item.phone);
            }
          }
        }, [_c('text-thread', {
          ref: "thread-".concat(index),
          attrs: {
            "thread": item,
            "expanded": false
          }
        })], 1)];
      }
    }])
  })], 1), _vm._v(" "), _vm.filteredThreads.length === 0 && !_vm.searchQuery ? _c('div', {
    staticClass: "empty-state"
  }, [_c('div', {
    staticClass: "empty-icon"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "size": "64",
      "color": "grey"
    }
  }, [_vm._v("fa-regular fa-message")])], 1), _vm._v(" "), _c('h3', {
    staticClass: "empty-title"
  }, [_vm._v("No Messages")]), _vm._v(" "), _c('p', {
    staticClass: "empty-subtitle"
  }, [_vm._v("Start a conversation by tapping the + button")])]) : _vm._e(), _vm._v(" "), _vm.filteredThreads.length === 0 && _vm.searchQuery ? _c('div', {
    staticClass: "empty-state"
  }, [_c('div', {
    staticClass: "empty-icon"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "size": "64",
      "color": "grey"
    }
  }, [_vm._v("fa-solid fa-search")])], 1), _vm._v(" "), _c('h3', {
    staticClass: "empty-title"
  }, [_vm._v("No Results")]), _vm._v(" "), _c('p', {
    staticClass: "empty-subtitle"
  }, [_vm._v("No messages found for \"" + _vm._s(_vm.searchQuery) + "\"")])]) : _vm._e()]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/phone/sms.vue?vue&type=template&id=0f284070

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.starts-with.js
var es_string_starts_with = __webpack_require__(144);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.trim.js
var es_string_trim = __webpack_require__(103);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./components/Pages/Text/text-thread.vue + 4 modules
var text_thread = __webpack_require__(1670);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/Pages/Text/text-new-message.vue + 4 modules
var text_new_message = __webpack_require__(1671);

// EXTERNAL MODULE: ./components/Common/app-card-items.vue + 9 modules
var app_card_items = __webpack_require__(1571);

// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// EXTERNAL MODULE: ./components/Common/app-infinite-scroll.vue + 4 modules
var app_infinite_scroll = __webpack_require__(1638);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/sms.vue?vue&type=script&lang=js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }























/* harmony default export */ var smsvue_type_script_lang_js = ({
  scrollToTop: true,
  components: {
    AppInfiniteScroll: app_infinite_scroll["a" /* default */],
    AppCardItems: app_card_items["a" /* default */],
    TextNewMessage: text_new_message["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */],
    TextThread: text_thread["a" /* default */],
    AppCard: app_card["a" /* default */]
  },
  mixins: [ContactFormMixin["a" /* default */]],
  data: function data() {
    return {
      threadsLoading: false,
      searchQuery: '',
      searchDebounceTimer: null
    };
  },
  methods: {
    chosen: function chosen(item) {
      this.view(item.phone);
    },
    view: function view(phone) {
      this.$router.push("/phone/thread/".concat(phone));
    },
    // Event trigger method to open specific thread menu
    openThreadMenu: function openThreadMenu(index) {
      var threadRef = this.$refs["thread-".concat(index)];
      if (threadRef && threadRef[0]) {
        var contactActionsComponent = threadRef[0].$children.find(function (child) {
          return child.$options.name === 'text-contact-actions';
        });
        if (contactActionsComponent) {
          contactActionsComponent.openMenu();
        }
      }
    },
    // Handle infinite scroll load more
    onLoadMoreThreads: function onLoadMoreThreads(pagination) {
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              // Example of how to implement server-side pagination for threads
              // Uncomment and modify for actual API integration

              /*
              this.threadsLoading = true;
              try {
                const response = await this.$axios.$get('/api/text-threads', {
                  params: {
                    page: pagination.page,
                    limit: pagination.itemsPerPage,
                    offset: pagination.offset
                  }
                });
                 // Append new threads to existing ones for server-side pagination
                this.$store.commit('text/APPEND_THREADS', response.threads);
              } catch (error) {
                console.error('Failed to load more threads:', error);
              } finally {
                this.threadsLoading = false;
              }
              */

              // For now, just log the pagination info
              console.log('Loading more threads:', pagination);
            case 1:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    // Clear search input
    clearSearch: function clearSearch() {
      this.searchQuery = '';
    }
  },
  computed: _objectSpread(_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    threads: 'text/threads',
    keyedContacts: 'text/keyedContacts'
  })), {}, {
    // Debounced search functionality
    filteredThreads: function filteredThreads() {
      var _this = this;
      if (!this.searchQuery || this.searchQuery.trim() === '') {
        return this.threads;
      }
      var query = this.searchQuery.toLowerCase().trim();
      return this.threads.filter(function (thread) {
        // Search by contact name
        var contact = _this.keyedContacts[thread.phone];
        if (contact && contact.display && contact.display.toLowerCase().includes(query)) {
          return true;
        }

        // Search by phone number
        if (thread.phone && thread.phone.toLowerCase().includes(query)) {
          return true;
        }

        // Search by last message content
        if (thread.last && thread.last.message) {
          // Handle both regular messages and JSON messages
          var messageText = thread.last.message;
          try {
            // If it's a JSON message, try to extract text content
            if (messageText.startsWith('{')) {
              var parsed = JSON.parse(messageText);
              messageText = parsed.message || parsed.text || messageText;
            }
          } catch (e) {
            // If parsing fails, use original message
          }
          if (messageText.toLowerCase().includes(query)) {
            return true;
          }
        }

        // Search through all messages in the thread (if loaded)
        if (thread.messages && Array.isArray(thread.messages)) {
          return thread.messages.some(function (message) {
            if (message.message) {
              var _messageText = message.message;
              try {
                if (_messageText.startsWith('{')) {
                  var _parsed = JSON.parse(_messageText);
                  _messageText = _parsed.message || _parsed.text || _messageText;
                }
              } catch (e) {
                // If parsing fails, use original message
              }
              return _messageText.toLowerCase().includes(query);
            }
            return false;
          });
        }
        return false;
      });
    }
  }),
  watch: {
    // Debounce search query to prevent performance issues
    searchQuery: {
      handler: function handler(newVal, oldVal) {
        // Clear existing timer
        if (this.searchDebounceTimer) {
          clearTimeout(this.searchDebounceTimer);
        }

        // Set new timer for debouncing
        this.searchDebounceTimer = setTimeout(function () {
          // The computed property will automatically update when searchQuery changes
          // This timeout just prevents excessive filtering during rapid typing
        }, 300); // 300ms debounce
      },
      immediate: false
    }
  },
  beforeDestroy: function beforeDestroy() {
    // Clean up debounce timer
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }
  }
});
// CONCATENATED MODULE: ./pages/phone/sms.vue?vue&type=script&lang=js
 /* harmony default export */ var phone_smsvue_type_script_lang_js = (smsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/phone/sms.vue?vue&type=style&index=0&id=0f284070&prod&lang=scss
var smsvue_type_style_index_0_id_0f284070_prod_lang_scss = __webpack_require__(1932);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/phone/sms.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  phone_smsvue_type_script_lang_js,
  smsvue_type_template_id_0f284070_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var sms = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);