(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[231],{

/***/ 2217:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VDivider/VDivider.js
var VDivider = __webpack_require__(466);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanel.js
var VExpansionPanel = __webpack_require__(1528);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelContent.js
var VExpansionPanelContent = __webpack_require__(1530);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanelHeader.js
var VExpansionPanelHeader = __webpack_require__(1529);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VExpansionPanel/VExpansionPanels.js
var VExpansionPanels = __webpack_require__(1527);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/town-center/home.vue?vue&type=template&id=0f31eba6













var homevue_type_template_id_0f31eba6_render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _c('div', [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c(VCard["a" /* default */], [_c(components_VCard["c" /* VCardTitle */], [_c('div', [_c('i', {
    staticClass: "fa-solid fa-hand-wave mr-3"
  }), _vm._v("\n              First Flight In?\n            ")])]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_vm._v("\n            We're glad to hear you landed safely ✈️.\n            All processing paperwork has been completed. We are proud to call you a citizen!\n            Take a moment to check out some starting tips below.\n\n            "), _c('div', [_c('h4', {
    staticClass: "mt-4"
  }, [_vm._v("\n                What to do? 🤔\n              ")]), _vm._v(" "), _c('ul', [_c('li', [_vm._v("\n                  Read through our\n                  "), _c('nuxt-link', {
    attrs: {
      "to": "/town-center/state-laws"
    }
  }, [_vm._v("State Laws")]), _vm._v(".\n                ")], 1), _vm._v(" "), _c('li', [_vm._v("\n                  Visit "), _c('b', [_vm._v("Department of Licensing")]), _vm._v(" (in-person) to get your first drivers license.\n                ")]), _vm._v(" "), _c('li', [_vm._v("\n                  Visit a "), _c('b', [_vm._v("car dealership")]), _vm._v(" to purchase a car! If no dealerships are open, check out "), _c('b', [_vm._v("2nd Chance\n                  Auto's")]), _vm._v(".\n                ")])]), _vm._v(" "), _c(VAlert["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "orange",
      "icon": "fa-solid fa-lightbulb-exclamation-on"
    }
  }, [_vm._v("\n                Look around for people to talk to.\n                There are many business owners, and people who will be able to help you guide you in the right direction depending on what you aspire to be.\n              ")])], 1)])], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c(VCard["a" /* default */], [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n            My Citizenship\n          ")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c(VExpansionPanels["a" /* default */], [_c(VExpansionPanel["a" /* default */], [_c(VExpansionPanelHeader["a" /* default */], {
    attrs: {
      "color": "black"
    }
  }, [_c('span', [_c('i', {
    staticClass: "fa-solid fa-lock mr-3"
  }), _vm._v("\n                    View Secure Information\n                  ")])]), _vm._v(" "), _c(VExpansionPanelContent["a" /* default */], {
    attrs: {
      "color": "black"
    }
  }, [_c('item-detail', {
    attrs: {
      "label": "Full Legal Name",
      "value": _vm.user.name
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('item-detail', {
    attrs: {
      "label": "Drivers License #",
      "value": _vm.user.license_number
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('item-detail', {
    attrs: {
      "label": "Phone #",
      "value": _vm.user.phone
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */])], 1)], 1)], 1), _vm._v(" "), _c(VAlert["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "primary",
      "icon": "fa-solid fa-lightbulb-exclamation-on"
    }
  }, [_vm._v("\n              Visit "), _c('b', [_vm._v("the Department of Licensing")]), _vm._v(" in the middle the of city to apply for new license's, get a replacement license,\n              request a name change, and more. Department of Licensing is open 24/7.\n            ")])], 1)], 1), _vm._v(" "), _c(VCard["a" /* default */], {
    staticClass: "mt-4"
  }, [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n            Tablet Registration\n          ")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c('item-detail', {
    attrs: {
      "label": "Account Activated",
      "value": _vm.user.created_at
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('item-detail', {
    attrs: {
      "label": "Display Name",
      "value": _vm.user.display_name
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('item-detail', {
    attrs: {
      "label": "Operating System Version",
      "value": "LifeOS v".concat(_vm.user.version)
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('item-detail', {
    attrs: {
      "label": "Authentication Binding",
      "color": "green",
      "value": "Bound"
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('item-detail', {
    attrs: {
      "label": "Binding Location",
      "value": "Behind Neck"
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('item-detail', {
    attrs: {
      "label": "2-Factor",
      "value": "Tri-Layer Iris Scan"
    }
  }), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('span', [_vm._v("\n              Information provided by LifeOS Database.\n              "), _c('span', {
    staticClass: "text-danger"
  }, [_vm._v("\n                Attempt to remove medically bound authentication chip can result in permanent death.\n              ")])]), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('NuxtLink', {
    attrs: {
      "to": "/settings"
    }
  }, [_vm._v("\n              Manage Device & Account Settings\n            ")])], 1)], 1)], 1)], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/town-center/home.vue?vue&type=template&id=0f31eba6

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Common/ItemDetail.vue + 4 modules
var ItemDetail = __webpack_require__(151);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/town-center/home.vue?vue&type=script&lang=ts








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var homevue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: '',
  components: {
    ItemDetail: ItemDetail["a" /* default */]
  },
  props: {},
  data: function data() {
    return {};
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  }))
}));
// CONCATENATED MODULE: ./pages/town-center/home.vue?vue&type=script&lang=ts
 /* harmony default export */ var town_center_homevue_type_script_lang_ts = (homevue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/town-center/home.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  town_center_homevue_type_script_lang_ts,
  homevue_type_template_id_0f31eba6_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var home = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);