(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[145],{

/***/ 1556:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

var render = function render() {
  var _vm$realName, _vm$realView;
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _vm.ready ? _c('crudy-table', {
    attrs: {
      "name": (_vm$realName = _vm.realName) !== null && _vm$realName !== void 0 ? _vm$realName : _vm.name,
      "view": (_vm$realView = _vm.realView) !== null && _vm$realView !== void 0 ? _vm$realView : _vm.view
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-debug.vue + 9 modules
var crudy_form_debug = __webpack_require__(315);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts






/* harmony default export */ var crudy_genvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'crudy-gen',
  components: {
    CrudyFormDebug: crudy_form_debug["a" /* default */],
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['name', 'view', 'filter'],
  data: function data() {
    return {
      meta: null,
      ready: false,
      tableUUID: null,
      realName: null,
      realView: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var name, route;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            name = _this.$route.name;
            _context.next = 3;
            return _this.$router.resolve({
              name: name
            }).route;
          case 3:
            route = _context.sent;
            _this.meta = route === null || route === void 0 ? void 0 : route.meta;
            _this.realName = _this.meta.model;
            _this.realView = _this.meta.view;
            _this.ready = true;
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  }
}));
// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_gen_crudy_genvue_type_script_lang_ts = (crudy_genvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_gen_crudy_genvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_gen = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1562:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Documents/DocumentsList.vue?vue&type=template&id=257a5de0






var DocumentsListvue_type_template_id_257a5de0_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "p-top",
    attrs: {
      "no-gutters": ""
    }
  }, [!_vm.faction_type ? _c(VCol["a" /* default */], {
    staticClass: "ml-1 mb-3",
    attrs: {
      "md": "4"
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        _vm.createModal = true;
      }
    }
  }, [_vm._v("\n        Create Doodly Doc (Contract)\n      ")])], 1) : _vm._e()], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Create Contract",
      "hide-footer": ""
    },
    model: {
      value: _vm.createModal,
      callback: function callback($$v) {
        _vm.createModal = $$v;
      },
      expression: "createModal"
    }
  }, [_c('span', {
    staticClass: "p-top"
  }), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Title"
    }
  }, [_c(VTextField["a" /* default */], {
    model: {
      value: _vm.createForm.TITLE,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "TITLE", $$v);
      },
      expression: "createForm.TITLE"
    }
  })], 1), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Expiration"
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "placeholder": "N/A"
    },
    model: {
      value: _vm.createForm.EXPIRATION,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "EXPIRATION", $$v);
      },
      expression: "createForm.EXPIRATION"
    }
  })], 1), _vm._v(" "), _c('app-form-group', {
    attrs: {
      "label": "Contract Details"
    }
  }, [_c(VTextarea["a" /* default */], {
    attrs: {
      "filled": ""
    },
    model: {
      value: _vm.createForm.DETAILS,
      callback: function callback($$v) {
        _vm.$set(_vm.createForm, "DETAILS", $$v);
      },
      expression: "createForm.DETAILS"
    }
  })], 1), _vm._v(" "), _c('app-form-group', [_c('span', {
    staticClass: "text-primary"
  }, [_vm._v("\n                      The contract will pop up for the "), _c('b', [_vm._v("closest player to you")]), _vm._v(".\n                    "), _c('br'), _vm._v("\n                      Once the player fills and submits the contract it will show up under "), _c('b', {
    staticClass: "text-warning"
  }, [_vm._v("My Documents")]), _vm._v(" "), _c('br'), _vm._v(" "), _c('span', {
    staticClass: "text-muted"
  }, [_vm._v("If the player does not fill or agree to the form, it will not show up")])])]), _vm._v(" "), _c('app-form-group', [_c(VBtn["a" /* default */], {
    attrs: {
      "variant": "success"
    },
    on: {
      "click": _vm.createContract
    }
  }, [_vm._v("\n        Offer to closest player\n      ")])], 1)], 1), _vm._v(" "), _vm.view ? _c('crudy-table', {
    attrs: {
      "name": "Document",
      "view": _vm.view
    }
  }) : _vm.tableFilter ? _c('crudy-table', {
    attrs: {
      "name": "Document",
      "view": "index",
      "filter": _vm.tableFilter
    }
  }) : _vm._e()], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue?vue&type=template&id=257a5de0

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue + 4 modules
var crudy_gen = __webpack_require__(1556);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Documents/DocumentsList.vue?vue&type=script&lang=js







/* harmony default export */ var DocumentsListvue_type_script_lang_js = ({
  name: 'DocumentsList',
  components: {
    CrudyTable: crudy_table["a" /* default */],
    CrudyGen: crudy_gen["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */]
  },
  props: ['faction_type', 'personal_id', 'business_id', 'character_number', 'view'],
  data: function data() {
    return {
      createModal: false,
      tableFilter: null,
      createForm: {
        TITLE: null,
        EXPIRATION: null,
        DETAILS: null
      }
    };
  },
  created: function created() {
    if (this.faction_type) {
      this.tableFilter = {
        faction_type: this.faction_type
      };
      this.createForm.faction_type = this.faction_type;
    }
    if (this.personal_id) {
      this.tableFilter = {
        requested_by_id: 5996
      };
      this.createForm.personal_id = this.personal_id;
    }
    if (this.business_id) {
      this.tableFilter = {
        business_id: this.business_id
      };
      this.createForm.business_id = this.business_id;
    }
  },
  methods: {
    createContract: function createContract() {
      this.createModal = false;
      if (!this.createForm.expiration) this.createForm.expiration = 'Not Applicable';
      fetch("https://blrp_tablet/createContract", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify(this.createForm)
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  })
});
// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue?vue&type=script&lang=js
 /* harmony default export */ var Documents_DocumentsListvue_type_script_lang_js = (DocumentsListvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Documents/DocumentsList.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Documents_DocumentsListvue_type_script_lang_js,
  DocumentsListvue_type_template_id_257a5de0_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var DocumentsList = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1718:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1843);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("fc2b93ac", content, true, {"sourceMap":false});

/***/ }),

/***/ 1842:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_requested_contracts_vue_vue_type_style_index_0_id_49d535b4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1718);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_requested_contracts_vue_vue_type_style_index_0_id_49d535b4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_requested_contracts_vue_vue_type_style_index_0_id_49d535b4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1843:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".procedure-item{padding-bottom:15px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2148:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/contracts/requested-contracts.vue?vue&type=template&id=49d535b4
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('DocumentsList', {
    staticClass: "mt-3",
    attrs: {
      "personal_id": _vm.user.character_number,
      "view": "personalRequested"
    }
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/contracts/requested-contracts.vue?vue&type=template&id=49d535b4

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./components/Pages/Documents/DocumentsList.vue + 4 modules
var DocumentsList = __webpack_require__(1562);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/contracts/requested-contracts.vue?vue&type=script&lang=js







function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/* harmony default export */ var requested_contractsvue_type_script_lang_js = ({
  components: {
    AppPage: AppPage["a" /* default */],
    DocumentsList: DocumentsList["a" /* default */]
  },
  data: function data() {
    return {
      form: {
        TITLE: null,
        EXPIRATION: null,
        DETAILS: null
      },
      tabIndex: 0
    };
  },
  methods: {
    createContract: function createContract() {
      if (!this.form.expiration) this.form.expiration = 'Not Applicable';
      fetch("https://blrp_tablet/createContract", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify(this.form)
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  }))
});
// CONCATENATED MODULE: ./pages/contracts/requested-contracts.vue?vue&type=script&lang=js
 /* harmony default export */ var contracts_requested_contractsvue_type_script_lang_js = (requested_contractsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/contracts/requested-contracts.vue?vue&type=style&index=0&id=49d535b4&prod&lang=scss
var requested_contractsvue_type_style_index_0_id_49d535b4_prod_lang_scss = __webpack_require__(1842);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/contracts/requested-contracts.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  contracts_requested_contractsvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var requested_contracts = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);