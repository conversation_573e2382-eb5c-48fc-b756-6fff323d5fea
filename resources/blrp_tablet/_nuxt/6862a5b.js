(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[161],{

/***/ 2239:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/information/index.vue?vue&type=template&id=7cac0fd8



var informationvue_type_template_id_7cac0fd8_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {}, [_c(VTabs["a" /* default */], {
    attrs: {
      "no-fade": "",
      "no-key-nav": ""
    }
  }, [_c(VTab["a" /* default */], {
    attrs: {
      "title": "Server Hotkeys & Commands"
    }
  }, [_c('div', {
    staticClass: "m-4",
    staticStyle: {}
  }, [_c('h6', [_vm._v("Genera Hotkeys")]), _vm._v(" "), _c('ul', {
    staticClass: "info-list"
  }, [_c('li', [_c('kbd', [_vm._v("M")]), _vm._v(" Open your menu\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("B")]), _vm._v(" Toggle your ability to point\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("~")]), _vm._v(" Toggle your voice volume, CapsLock to talk on radio or phone\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("X")]), _vm._v(" Put your hands up/down\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("*Shift* F5")]), _vm._v(" Minimize HUD/Map\n          ")])]), _vm._v(" "), _c('h6', [_vm._v("Vehicle Hotkeys")]), _vm._v(" "), _c('ul', {
    staticClass: "info-list"
  }, [_vm._v(" tabNine::config\n          "), _c('li', [_c('kbd', [_vm._v("U")]), _vm._v(" Toggle the door locks on cars you own\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("G")]), _vm._v(" Toggle your vehicle's engine state\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("K")]), _vm._v(" Toggle seatbelt\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("F5")]), _vm._v(" Show/Hide map\n          ")])]), _vm._v(" "), _c('h6', [_vm._v("Genera Commands")]), _vm._v(" "), _c('ul', {
    staticClass: "info-list"
  }, [_c('li', [_c('kbd', [_vm._v("/em [emote]")]), _vm._v(" Use an emote\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/notepad")]), _vm._v(" Use the notepad\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/helmet [0, 1]")]), _vm._v(" Toggle your helmet\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/mask [0, 1]")]), _vm._v(" Toggle your mask\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/glasses [0, 1]")]), _vm._v(" Toggle your glasses\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/me [me]")]), _vm._v(" Print text on your character\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/walk [style]")]), _vm._v(" Change your walk style\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/face [style]")]), _vm._v(" Change your walk style\n          ")]), _vm._v(" "), _c('li', [_c('kbd', [_vm._v("/setemote [key (1-12)] [emote]")]), _vm._v(" Bind emote to "), _c('kbd', [_vm._v("insert + F[Key]")])]), _vm._v(" "), _c('li', [_vm._v("\n            ... a few more you will eventually figure out :)\n          ")])])])]), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "title": "Tablet Help"
    }
  }, [_c('div', {
    staticClass: "m-4"
  }, [_c('h4', [_vm._v("Bad Tablet")]), _vm._v("Recent Listings\n\n        "), _c('p', {
    staticClass: "text-muted"
  }, [_vm._v("\n          Welcome to the official help center for the new state of the art tablet.\n          "), _c('br'), _vm._v("This tablet was manufactured and produced by "), _c('b', [_vm._v("Badlands Inc.")])]), _vm._v(" "), _c('div', {
    staticClass: "question-group"
  }, [_c('h6', [_vm._v("How do I close this?")]), _vm._v(" "), _c('p', {
    staticClass: "text-muted"
  }, [_vm._v("\n            Simply press the "), _c('b', [_vm._v("escape button")]), _vm._v(", or click on "), _c('b', [_vm._v("\"Lock Tablet\" inside of the menu")])])]), _vm._v(" "), _c('div', {
    staticClass: "question-group"
  }, [_c('h6', [_vm._v("Why do the apps reset when I change applications?")]), _vm._v(" "), _c('p', {
    staticClass: "text-muted"
  }, [_vm._v("\n            When changing sections (applications), the current forms/data/position does not persist. This is to prevent the tablet from exploding in your pocket\n          ")])]), _vm._v(" "), _c('div', {
    staticClass: "question-group"
  }, [_c('h6', [_vm._v("Help! It is stuck in my eyes?")]), _vm._v(" "), _c('p', {
    staticClass: "text-muted"
  }, [_vm._v("\n            Simply open the \"F8\" console and type \"badfix\"\n          ")])]), _vm._v(" "), _c('div', {
    staticClass: "question-group"
  }, [_c('h4', [_vm._v("Bad Cloud")]), _vm._v(" "), _c('p', {
    staticClass: "text-muted"
  }, [_c('span', {
    staticClass: "text-primary font-weight-bold"
  }, [_vm._v("Bad Cloud")]), _vm._v(" is a state of the art systematic server based cloud system offering high speeds and never ending power.\n            "), _c('br'), _vm._v("This is protected by a patent owned by Badlands Inc.\n          ")])])])])], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/information/index.vue?vue&type=template&id=7cac0fd8

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/information/index.vue

var script = {}


/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  script,
  informationvue_type_template_id_7cac0fd8_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var information = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);