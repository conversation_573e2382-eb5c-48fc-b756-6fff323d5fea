(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[105],{

/***/ 1556:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

var render = function render() {
  var _vm$realName, _vm$realView;
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _vm.ready ? _c('crudy-table', {
    attrs: {
      "name": (_vm$realName = _vm.realName) !== null && _vm$realName !== void 0 ? _vm$realName : _vm.name,
      "view": (_vm$realView = _vm.realView) !== null && _vm$realView !== void 0 ? _vm$realView : _vm.view
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-debug.vue + 9 modules
var crudy_form_debug = __webpack_require__(315);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts






/* harmony default export */ var crudy_genvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'crudy-gen',
  components: {
    CrudyFormDebug: crudy_form_debug["a" /* default */],
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['name', 'view', 'filter'],
  data: function data() {
    return {
      meta: null,
      ready: false,
      tableUUID: null,
      realName: null,
      realView: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var name, route;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            name = _this.$route.name;
            _context.next = 3;
            return _this.$router.resolve({
              name: name
            }).route;
          case 3:
            route = _context.sent;
            _this.meta = route === null || route === void 0 ? void 0 : route.meta;
            _this.realName = _this.meta.model;
            _this.realView = _this.meta.view;
            _this.ready = true;
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  }
}));
// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_gen_crudy_genvue_type_script_lang_ts = (crudy_genvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_gen_crudy_genvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_gen = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1668:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/ChargeItem.vue?vue&type=template&id=4dff265c








var ChargeItemvue_type_template_id_4dff265c_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VTooltip["a" /* default */], {
    attrs: {
      "top": "",
      "attach": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref) {
        var on = _ref.on,
          attrs = _ref.attrs;
        return [_c(VRow["a" /* default */], _vm._g(_vm._b({
          staticClass: "charge-item",
          class: {
            selected: _vm.charge.selected
          },
          staticStyle: {
            "cursor": "pointer"
          },
          attrs: {
            "no-gutters": ""
          },
          on: {
            "click": function click($event) {
              return _vm.$emit('selected', {
                sectionIndex: _vm.sectionIndex,
                chargeIndex: _vm.chargeIndex
              });
            }
          }
        }, 'v-row', attrs, false), on), [_c(VCol["a" /* default */], {
          class: "charge-color ".concat(_vm.section.name),
          attrs: {
            "md": "7"
          }
        }, [_vm.charge.vehicle ? _c('span', [_c('i', {
          staticClass: "ml-4 text-muted fas fa-car-side fa-xs"
        }), _vm._v("  ")]) : _vm._e(), _vm._v(" "), _vm.charge.court ? _c('span', [_c('i', {
          staticClass: "ml-4 text-muted fas fa-gavel fa-xs"
        }), _vm._v("  ")]) : _vm._e(), _vm._v(" "), _vm.charge.selected ? _c('span', {
          class: _vm.section.name
        }, [_vm._v("  " + _vm._s(_vm.charge.name))]) : _c('span', [_vm._v("  " + _vm._s(_vm.charge.name))])]), _vm._v(" "), _c(VCol["a" /* default */], {
          staticClass: "text-success",
          attrs: {
            "md": "1"
          }
        }, [_vm.charge.fine ? _c('span', [_vm._v("$" + _vm._s(_vm.charge.fine.toLocaleString()))]) : _c('span', {
          staticClass: "text-muted"
        }, [_vm._v("--")])]), _vm._v(" "), _c(VCol["a" /* default */], {
          attrs: {
            "md": "1 ml-2"
          }
        }, [_vm.charge.months ? _c('span', [_vm._v(_vm._s(_vm.charge.months) + "m")]) : _c('span', {
          staticClass: "text-muted"
        }, [_vm._v("--")])]), _vm._v(" "), _c(VCol["a" /* default */], {
          staticClass: "text-danger",
          attrs: {
            "md": "1 mr-2"
          }
        }, [_vm.charge.restitution ? _c('span', [_vm._v("$" + _vm._s(_vm.charge.restitution.toLocaleString()))]) : _c('span', {
          staticClass: "text-muted"
        }, [_vm._v("--")])]), _vm._v(" "), _c(VCol["a" /* default */], {
          attrs: {
            "md": "1"
          }
        }, [_c(VTextField["a" /* default */], {
          staticStyle: {
            "height": "20px"
          },
          attrs: {
            "solo": "",
            "dense": "",
            "autocomplete": "off"
          },
          model: {
            value: _vm.charge.counts,
            callback: function callback($$v) {
              _vm.$set(_vm.charge, "counts", $$v);
            },
            expression: "charge.counts"
          }
        })], 1)], 1)];
      }
    }])
  }, [_vm._v(" "), _c('div', [_vm._v("\n    " + _vm._s(_vm.charge.description) + "\n  ")])]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/ChargeItem.vue?vue&type=template&id=4dff265c

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/ChargeItem.vue?vue&type=script&lang=js
// v-if="charge.name.toLowerCase().includes(filter.toLowerCase())"
/* harmony default export */ var ChargeItemvue_type_script_lang_js = ({
  name: 'ChargeItem',
  props: ['charge', 'chargeIndex', 'sectionIndex', 'section', 'showSelectedOnly'],
  data: function data() {
    return {
      colors: {
        Infractions: 'greenyellow',
        Misdemeanors: 'yellow',
        Felonies: 'lightcoral',
        Aggravated: 'red'
      }
    };
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/ChargeItem.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_ChargeItemvue_type_script_lang_js = (ChargeItemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Police/ChargeItem.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Police_ChargeItemvue_type_script_lang_js,
  ChargeItemvue_type_template_id_4dff265c_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var ChargeItem = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1699:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1809);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("51fabbd2", content, true, {"sourceMap":false});

/***/ }),

/***/ 1808:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_incidents_vue_vue_type_style_index_0_id_7822c46d_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1699);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_incidents_vue_vue_type_style_index_0_id_7822c46d_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_incidents_vue_vue_type_style_index_0_id_7822c46d_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1809:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".ticket{font-size:10px;margin-left:10px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2103:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/incidents.vue?vue&type=template&id=7822c46d
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('div', {
    staticClass: "custom-tabs"
  }, [_c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/incidents"
    }
  }, [_vm._v("\n      All Incidents\n    ")]), _vm._v(" "), _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/incidents/bolos"
    }
  }, [_vm._v("\n      BOLOs\n    ")]), _vm._v(" "), _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/incidents/tickets"
    }
  }, [_vm._v("\n      Tickets\n    ")]), _vm._v(" "), _vm.hasAnyGroup(['LEO INV']) ? _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/incidents/incident-variations/41"
    }
  }, [_vm._v("\n      INV\n    ")]) : _vm._e(), _vm._v(" "), _vm.hasAnyGroup(['LEO GTF']) ? _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/incidents/incident-types/20"
    }
  }, [_vm._v("\n      GTF\n    ")]) : _vm._e(), _vm._v(" "), _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/incidents/incident-types"
    }
  }, [_vm._v("\n      Incident Types\n    ")]), _vm._v(" "), _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/incidents/incident-variations"
    }
  }, [_vm._v("\n      Incident Variations\n    ")])], 1), _vm._v(" "), _c('NuxtChild', {
    key: _vm.$route.fullPath
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/incidents.vue?vue&type=template&id=7822c46d

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(51);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.join.js
var es_array_join = __webpack_require__(126);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Pages/Police/ChargeItem.vue + 4 modules
var ChargeItem = __webpack_require__(1668);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue + 4 modules
var crudy_gen = __webpack_require__(1556);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/incidents.vue?vue&type=script&lang=js



function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


























/* harmony default export */ var incidentsvue_type_script_lang_js = ({
  name: 'TicketingAid',
  components: {
    CrudyGen: crudy_gen["a" /* default */],
    AppPage: AppPage["a" /* default */],
    ChargeItem: ChargeItem["a" /* default */]
  },
  data: function data() {
    return {
      filter: null,
      sections: null,
      stackIssues: [],
      notes: null,
      name: null,
      showSelectedOnly: false,
      incidentTypeId: null
    };
  },
  mounted: function mounted() {
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  } // await this.$axios.$post('/police/incidents/init-ticketing-aid').then(res => {
  //   this.incidentTypeId = res.id
  // })
  ,
  methods: {
    reset: function reset() {
      for (var sIndex in this.sections) {
        for (var cIndex in this.sections[sIndex]['charges']) {
          var charge = this.sections[sIndex]['charges'][cIndex];
          if (charge.selected) {
            vue_runtime_esm["default"].set(this.sections[sIndex]['charges'][cIndex], 'selected', false);
          }
        }
      }
    },
    selectCharge: function selectCharge(e) {
      vue_runtime_esm["default"].set(this.sections[e.sectionIndex]['charges'][e.chargeIndex], 'selected', !this.sections[e.sectionIndex]['charges'][e.chargeIndex].selected);
    },
    copyCharges: function copyCharges() {
      var list = this.sentence.charges.map(function (c) {
        return c.vName;
      }).join(', ');
      this.$copyText(list).then(function (e) {}, function (e) {});
    },
    parseMax: function parseMax(type, amount) {
      if (type === 'fines' && amount >= 15000) return 15000;
      if (type === 'time' && amount >= 60) return 60;
      if (type === 'res' && amount >= 10000) return 10000;
      return amount;
    }
  },
  computed: _objectSpread(_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    currentFaction: 'auth/currentFaction',
    hasAnyGroup: 'auth/hasAnyGroup'
  })), {}, {
    sentence: function sentence() {
      this.stackIssues = [];
      var preventStacking = [];
      var _iterator = _createForOfIteratorHelper(this.sections),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var section = _step.value;
          var _iterator3 = _createForOfIteratorHelper(section.charges),
            _step3;
          try {
            for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {
              var charge = _step3.value;
              if (charge.cantStack && charge.selected) {
                preventStacking.push.apply(preventStacking, Object(toConsumableArray["a" /* default */])(charge.cantStack));
              }
            }
          } catch (err) {
            _iterator3.e(err);
          } finally {
            _iterator3.f();
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      var data = {
        fines: 0,
        time: 0,
        res: 0,
        man: 0,
        nonman: 0,
        felfines: 0,
        fineres: 0,
        charges: []
      };
      var _iterator2 = _createForOfIteratorHelper(this.sections),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var _section = _step2.value;
          var _iterator4 = _createForOfIteratorHelper(_section.charges),
            _step4;
          try {
            for (_iterator4.s(); !(_step4 = _iterator4.n()).done;) {
              var _charge = _step4.value;
              if (_charge.selected) {
                var counts = parseInt(_charge.counts);
                if (!counts || isNaN(counts)) {
                  counts = 1;
                }
                _charge.vName = "(".concat(_section.name.slice(0, 3), ") ").concat(_charge.name);
                data.charges.push(_charge);
                if (_charge.fine) data.fines += _charge.fine * counts;
                if (_charge.months) data.time += _charge.months * counts;
                if (_charge.restitution) data.res += _charge.restitution * counts;
                if (_charge.restitution && !_charge.fine) data.fineres += _charge.restitution * counts; // add to "if they want to pay fines" if the charge has no fine and mandatory rest
                if (!_charge.fine && _charge.months) data.man += _charge.months * counts;
                if (_charge.fine) data.nonman += _charge.months * counts;
                if (_section.name === 'Felonies') data.felfines += _charge.fine * counts;
                if (preventStacking.includes(_charge.name)) {
                  this.stackIssues.push("".concat(_charge.name, " conflicts with another charge, please make sure you are not stacking non-stackable charges"));
                }
              }
            }
          } catch (err) {
            _iterator4.e(err);
          } finally {
            _iterator4.f();
          }
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return data;
    }
  })
});
// CONCATENATED MODULE: ./pages/cad/incidents.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_incidentsvue_type_script_lang_js = (incidentsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/cad/incidents.vue?vue&type=style&index=0&id=7822c46d&prod&lang=scss
var incidentsvue_type_style_index_0_id_7822c46d_prod_lang_scss = __webpack_require__(1808);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/incidents.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_incidentsvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var incidents = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);