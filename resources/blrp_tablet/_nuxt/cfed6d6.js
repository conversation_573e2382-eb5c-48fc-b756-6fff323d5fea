(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[128],{

/***/ 1669:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.search.js
var es_string_search = __webpack_require__(192);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/PolicePersonSelector.vue?vue&type=template&id=931dbd24







var PolicePersonSelectorvue_type_template_id_931dbd24_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VRow["a" /* default */], {
    attrs: {
      "no-gutters": ""
    }
  }, [!_vm.scanSuccessful ? _c(VCol["a" /* default */], {
    attrs: {
      "md": "6"
    }
  }, [_c('multiselect', {
    attrs: {
      "options": _vm.options,
      "loading": _vm.isLoading,
      "internal-search": false
    },
    on: {
      "search-change": _vm.search,
      "select": function select($event) {
        return _vm.$emit('selected', $event.id);
      }
    },
    scopedSlots: _vm._u([{
      key: "singleLabel",
      fn: function fn(props) {
        return [_c('span', {
          staticClass: "option__title"
        }, [_vm._v(_vm._s(props.option.name))])];
      }
    }, {
      key: "option",
      fn: function fn(props) {
        return [_c('div', {
          staticClass: "option__desc"
        }, [_c('span', {
          staticClass: "option__title"
        }, [_vm._v(_vm._s(props.option.name))]), _vm._v(" "), _c('span', [_vm._v(" - ")]), _vm._v(" "), _c('span', {
          staticClass: "option__small"
        }, [_vm._v(_vm._s(props.option.license))])])];
      }
    }], null, false, **********),
    model: {
      value: _vm.value,
      callback: function callback($$v) {
        _vm.value = $$v;
      },
      expression: "value"
    }
  })], 1) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "ml-3",
    attrs: {
      "disabled": _vm.scanSuccessful,
      "color": _vm.scanSuccessful ? 'success' : 'primary'
    },
    on: {
      "click": _vm.scanId
    }
  }, [_vm.scanSuccessful ? _c('i', {
    staticClass: "fa-solid fa-badge-check mr-2"
  }) : _vm._e(), _vm._v("\n      " + _vm._s(_vm.buttonLabel) + "\n    ")]), _vm._v(" "), _vm.scanSuccessful ? _c(VBtn["a" /* default */], {
    staticClass: "ml-4 btn btn-outline-danger",
    on: {
      "click": _vm.reset
    }
  }, [_vm._v("\n      Reset\n    ")]) : _vm._e()], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/PolicePersonSelector.vue?vue&type=template&id=931dbd24

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/vue-multiselect/dist/vue-multiselect.min.js
var vue_multiselect_min = __webpack_require__(202);
var vue_multiselect_min_default = /*#__PURE__*/__webpack_require__.n(vue_multiselect_min);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/PolicePersonSelector.vue?vue&type=script&lang=js




/* harmony default export */ var PolicePersonSelectorvue_type_script_lang_js = ({
  name: "PolicePersonSelector",
  components: {
    Multiselect: vue_multiselect_min_default.a
  },
  props: ['current'],
  data: function data() {
    return {
      value: null,
      options: [],
      isLoading: false,
      scannedDL: null,
      scanSuccessful: false,
      buttonLabel: 'Scan ID'
    };
  },
  methods: {
    clear: function clear() {
      this.value = null;
    },
    search: function search(searchQuery) {
      var _this = this;
      if (searchQuery.length > 2) {
        this.isLoading = true;
        if (this.$route.path.includes('cad')) {
          this.$axios.$get("/police/persons/simple-search/".concat(searchQuery)).then(function (r) {
            _this.isLoading = false;
            _this.options = r;
          });
        } else {
          this.$axios.$get("/social/persons/simple-search/".concat(searchQuery)).then(function (r) {
            _this.isLoading = false;
            _this.options = r;
          });
        }
      }
    },
    scanId: function scanId() {
      var _this2 = this;
      this.isLoading = true;
      return this.$axios.$post('https://blrp_tablet/scanID').then(function (data) {
        var DL = data.DL;
        _this2.$axios.$get("/police/persons/simple-search/".concat(DL)).then(function (data) {
          _this2.isLoading = false;
          _this2.options = data;
          if (_this2.options.length > 0) {
            _this2.value = _this2.options[0].id;
            _this2.$emit('selected', _this2.value);
            _this2.buttonLabel = _this2.options[0].name;
            _this2.scanSuccessful = true;
          } else {
            _this2.$store.dispatch('audio/play', '/audio/alert.mp3');
          }
        });
      });
    },
    reset: function reset() {
      this.buttonLabel = 'Scan ID';
      this.value = null;
      this.scanSuccessful = false;
    }
  }
});
// CONCATENATED MODULE: ./components/Common/PolicePersonSelector.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_PolicePersonSelectorvue_type_script_lang_js = (PolicePersonSelectorvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/PolicePersonSelector.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_PolicePersonSelectorvue_type_script_lang_js,
  PolicePersonSelectorvue_type_template_id_931dbd24_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var PolicePersonSelector = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1707:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1821);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("77855892", content, true, {"sourceMap":false});

/***/ }),

/***/ 1708:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1823);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("73f92563", content, true, {"sourceMap":false});

/***/ }),

/***/ 1820:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LocationSelector_vue_vue_type_style_index_0_id_1deea812_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1707);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LocationSelector_vue_vue_type_style_index_0_id_1deea812_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_LocationSelector_vue_vue_type_style_index_0_id_1deea812_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1821:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".location-item{border:1px solid gray;margin-bottom:5px;margin-right:5px;margin-top:5px;padding:5px}.location-item:hover{background-color:gray!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1822:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tip_vue_vue_type_style_index_0_id_fcce6220_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1708);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tip_vue_vue_type_style_index_0_id_fcce6220_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_tip_vue_vue_type_style_index_0_id_fcce6220_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1823:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".tip-content{background-color:#181818;padding:15px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2026:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/submit-incident.vue?vue&type=template&id=b4bd8d9a












var submit_incidentvue_type_template_id_b4bd8d9a_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.user ? _c('app-page', {
    attrs: {
      "pad": true
    }
  }, [_c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c('item-detail', {
    attrs: {
      "label": "Current Location",
      "value": "".concat(this.position.current_zone, " / ").concat(this.position.current_street_a, " / ").concat(this.position.current_street_b)
    }
  }), _vm._v(" "), _c('br'), _vm._v(" "), this.currentFaction ? _c('item-detail', {
    staticClass: "font-weight-bold",
    attrs: {
      "label": "Current Representation",
      "value": "".concat(this.currentFaction.name),
      "color": this.currentFaction.color
    }
  }) : _vm._e()], 1)], 1), _vm._v(" "), _c(VCard["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "black"
    }
  }, [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n      Incident Details\n    ")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c(VForm["a" /* default */], {
    on: {
      "submit": _vm.create
    }
  }, [_vm.user.admin_bypass ? _c('hr') : _vm._e(), _vm._v(" "), _vm.user.admin_bypass ? _c('div', [_c('div', {
    staticClass: "text-muted ml-2"
  }, [_vm._v("\n            Only shown to admins\n          ")]), _vm._v(" "), _c(VRow["a" /* default */], {
    attrs: {
      "no-gutters": ""
    }
  }, _vm._l(_vm.factions, function (faction, index) {
    return _c(VCol["a" /* default */], {
      key: index,
      staticClass: "record-type",
      class: {
        selected: _vm.form.faction === faction.identifier
      },
      style: "color: ".concat(faction.color),
      on: {
        "click": function click($event) {
          _vm.form.faction = faction.identifier;
        }
      }
    }, [_vm._v("\n              " + _vm._s(faction.short) + "\n            ")]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _vm.user.admin_bypass ? _c('hr') : _vm._e(), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c(VTextField["a" /* default */], {
    attrs: {
      "label": "Incident Title",
      "placeholder": "Leave blank to autofill"
    },
    model: {
      value: _vm.form.title,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "title", $$v);
      },
      expression: "form.title"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c('location-selector', {
    attrs: {
      "current": _vm.form.location_name
    },
    on: {
      "input": function input($event) {
        _vm.form.location_name = $event;
      }
    }
  })], 1)], 1), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_vm.form.faction ? _c('app-form-group', [_c('label', [_vm._v("Incident Category")]), _vm._v(" "), _c('crud-select', {
    ref: "incidentTypeSearch",
    attrs: {
      "model": "IncidentType",
      "column": "name",
      "haltload": true
    },
    on: {
      "changed": function changed($event) {
        return _vm.$refs.iv.doSearch('incident_type_id', _vm.form.incident_type_id);
      }
    },
    model: {
      value: _vm.form.incident_type_id,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "incident_type_id", $$v);
      },
      expression: "form.incident_type_id"
    }
  })], 1) : _vm._e()], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c('app-form-group', [_c('label', [_vm._v("Variation")]), _vm._v(" "), _c('crud-select', {
    ref: "iv",
    attrs: {
      "model": "IncidentVariation",
      "column": "name",
      "haltload": true
    },
    model: {
      value: _vm.form.incident_variation_id,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "incident_variation_id", $$v);
      },
      expression: "form.incident_variation_id"
    }
  })], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c('app-form-group', [_c('label', [_vm._v("Crime Type")]), _vm._v(" "), _c('crud-select', {
    attrs: {
      "model": "IncidentCrime",
      "pre-selected": 1,
      "column": "name"
    },
    model: {
      value: _vm.form.incident_crime_id,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "incident_crime_id", $$v);
      },
      expression: "form.incident_crime_id"
    }
  })], 1)], 1)], 1), _vm._v(" "), _vm.currentFaction.for === 'police' ? _c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c('h6', [_vm._v("\n              The Suspect.\n            ")]), _vm._v(" "), _c('police-person-selector', {
    attrs: {
      "current": _vm.form.suspect_id
    },
    on: {
      "selected": function selected($event) {
        _vm.form.suspect_id = $event;
      }
    }
  })], 1)], 1) : _vm._e(), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], [_c('h6', [_vm._v("\n              Incident Summary. (Do not make suspects wait while filling this out. Field is optional)\n            ")]), _vm._v(" "), _c('crudy-editor', {
    attrs: {
      "simplified": true
    },
    model: {
      value: _vm.form.content,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "content", $$v);
      },
      expression: "form.content"
    }
  })], 1)], 1)], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "align": "right"
    }
  }, [_vm.currentFaction.for === 'police' ? _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "mt-5",
    attrs: {
      "large": "",
      "color": "primary"
    },
    on: {
      "click": function click($event) {
        return _vm.create('auto_add_self_primary');
      }
    }
  }, [_vm._v("Create Incident (Primary)\n          ")]) : _vm._e(), _vm._v(" "), _vm.currentFaction.for === 'police' ? _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "mt-5",
    attrs: {
      "large": ""
    },
    on: {
      "click": function click($event) {
        return _vm.create('auto_add_self_secondary');
      }
    }
  }, [_vm._v("Create Incident (Secondary)\n          ")]) : _vm._e(), _vm._v(" "), _vm.currentFaction.for === 'police' ? _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "mt-5",
    attrs: {
      "large": ""
    },
    on: {
      "click": function click($event) {
        return _vm.create('auto_add_self_supervisor');
      }
    }
  }, [_vm._v("Create Incident (Supervisor)\n          ")]) : _vm._e(), _vm._v(" "), _c(VBtn["a" /* default */], {
    directives: [{
      name: "promise-btn",
      rawName: "v-promise-btn"
    }],
    staticClass: "mt-5",
    attrs: {
      "large": "",
      "color": "success"
    },
    on: {
      "click": function click($event) {
        return _vm.create();
      }
    }
  }, [_vm._v("Create Incident (--)\n          ")])], 1)], 1)], 1)], 1)], 1) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/submit-incident.vue?vue&type=template&id=b4bd8d9a

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/ItemDetail.vue + 4 modules
var ItemDetail = __webpack_require__(151);

// EXTERNAL MODULE: ./components/Crud/parts/CrudSelect.vue + 4 modules
var CrudSelect = __webpack_require__(203);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/LocationSelector.vue?vue&type=template&id=1deea812



var LocationSelectorvue_type_template_id_1deea812_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VTextField["a" /* default */], {
    attrs: {
      "label": "Location"
    },
    on: {
      "input": _vm.index
    },
    model: {
      value: _vm.value,
      callback: function callback($$v) {
        _vm.value = $$v;
      },
      expression: "value"
    }
  }), _vm._v(" "), _vm.options.length > 1 ? _c('div', _vm._l(_vm.options, function (option) {
    return _c('div', {
      key: option.id,
      staticClass: "location-item",
      on: {
        "click": function click($event) {
          return _vm.selectLocation(option.name);
        }
      }
    }, [_vm._v("\n            " + _vm._s(option.name) + "\n        ")]);
  }), 0) : _vm._e()], 1);
};
var LocationSelectorvue_type_template_id_1deea812_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/LocationSelector.vue?vue&type=template&id=1deea812

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vue-multiselect/dist/vue-multiselect.min.js
var vue_multiselect_min = __webpack_require__(202);
var vue_multiselect_min_default = /*#__PURE__*/__webpack_require__.n(vue_multiselect_min);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/LocationSelector.vue?vue&type=script&lang=js





/* harmony default export */ var LocationSelectorvue_type_script_lang_js = ({
  name: "LocationSelector",
  components: {
    AppFormGroup: app_form_group["a" /* default */],
    Multiselect: vue_multiselect_min_default.a
  },
  props: ['current'],
  data: function data() {
    return {
      value: null,
      isLoading: false,
      options: []
    };
  },
  mounted: function mounted() {
    this.value = this.current;
  },
  methods: {
    debounce: function debounce(func) {
      var _this = this;
      var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 300;
      var timer;
      return function () {
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        clearTimeout(timer);
        timer = setTimeout(function () {
          func.apply(_this, args);
        }, timeout);
      };
    },
    index: function index(query) {
      var _this2 = this;
      this.$emit('input', query);
      this.$axios.$get("/police/locations/".concat(query)).then(function (r) {
        _this2.options = r.map(function (loc) {
          return {
            name: loc.name,
            value: loc.name
          };
        });
      });
    },
    selectLocation: function selectLocation(name) {
      this.value = name;
      this.$emit('input', name);
      this.options = [];
    }
  }
});
// CONCATENATED MODULE: ./components/Common/LocationSelector.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_LocationSelectorvue_type_script_lang_js = (LocationSelectorvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/LocationSelector.vue?vue&type=style&index=0&id=1deea812&prod&lang=scss
var LocationSelectorvue_type_style_index_0_id_1deea812_prod_lang_scss = __webpack_require__(1820);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/LocationSelector.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_LocationSelectorvue_type_script_lang_js,
  LocationSelectorvue_type_template_id_1deea812_render,
  LocationSelectorvue_type_template_id_1deea812_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var LocationSelector = (component.exports);
// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-tip.vue?vue&type=template&id=fcce6220
var app_tipvue_type_template_id_fcce6220_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "tip-content"
  }, [_c('div', {
    staticClass: "tip-title"
  }, [_c('i', {
    staticClass: "fa-solid fa-cloud-question"
  }), _vm._v(" "), _c('span', [_vm._v("\n        " + _vm._s(_vm.title) + "\n      ")])]), _vm._v(" "), _c('div', {
    staticClass: "mt-3"
  }, [_vm._t("default")], 2)]);
};
var app_tipvue_type_template_id_fcce6220_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-tip.vue?vue&type=template&id=fcce6220

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-tip.vue?vue&type=script&lang=js
/* harmony default export */ var app_tipvue_type_script_lang_js = ({
  name: "app-tip",
  props: ['title']
});
// CONCATENATED MODULE: ./components/Common/app-tip.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_tipvue_type_script_lang_js = (app_tipvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-tip.vue?vue&type=style&index=0&id=fcce6220&prod&lang=scss
var app_tipvue_type_style_index_0_id_fcce6220_prod_lang_scss = __webpack_require__(1822);

// CONCATENATED MODULE: ./components/Common/app-tip.vue






/* normalize component */

var app_tip_component = Object(componentNormalizer["a" /* default */])(
  Common_app_tipvue_type_script_lang_js,
  app_tipvue_type_template_id_fcce6220_render,
  app_tipvue_type_template_id_fcce6220_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_tip = (app_tip_component.exports);
// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./components/Common/PolicePersonSelector.vue + 4 modules
var PolicePersonSelector = __webpack_require__(1669);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/submit-incident.vue?vue&type=script&lang=js












/* harmony default export */ var submit_incidentvue_type_script_lang_js = ({
  components: {
    CrudyEditor: crudy_editor["a" /* default */],
    PolicePersonSelector: PolicePersonSelector["a" /* default */],
    AppPage: AppPage["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppTip: app_tip,
    LocationSelector: LocationSelector,
    CrudSelect: CrudSelect["a" /* default */],
    ItemDetail: ItemDetail["a" /* default */]
  },
  data: function data() {
    return {
      form: {
        location_name: 'test',
        title: null,
        type: 'N/A',
        sub_type: 'REPORT',
        faction: null,
        variation: 'OTHER',
        incident_crime_id: null,
        incident_type_id: null,
        incident_variation_id: null,
        suspect_id: null,
        content: null,
        auto_add_self_primary: false,
        auto_add_self_secondary: false,
        auto_add_self_supervisor: false
      }
    };
  },
  created: function created() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var _this$currentFaction;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _this.filterIncidentTypes((_this$currentFaction = _this.currentFaction) === null || _this$currentFaction === void 0 ? void 0 : _this$currentFaction.for);
            if (_this.position) {
              _this.form.location_name = "".concat(_this.position.current_zone, " / ").concat(_this.position.current_street_a, " / ").concat(_this.position.current_street_b);
            }
            _this.form.faction = _this.currentFaction.identifier;
            _this.$store.dispatch('cad/fetchFactions').then(function (r) {});
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  methods: {
    create: function create(myselfPersonTypeToggle) {
      var _this2 = this;
      if (myselfPersonTypeToggle) {
        this.form[myselfPersonTypeToggle] = true;
      }
      return this.$axios.$post("/police/incidents/create", this.form).then(function (r) {
        _this2.$router.push({
          path: "/cad/incidents/view/".concat(r.id)
        });
      });
    },
    filterIncidentTypes: function filterIncidentTypes() {
      var forName = this.currentFaction['for'];
      if (this.$refs.incidentTypeSearch) {
        this.$refs.incidentTypeSearch.doSearch('for', forName);
      } else {
        console.error('this.$refs.incidentTypeSearch not mounted');
      }
    }
  },
  watch: {
    position: function position() {
      this.form.location_name = "".concat(this.position.current_zone, " / ").concat(this.position.current_street_a, " / ").concat(this.position.current_street_b);
    },
    'form.faction': function formFaction() {
      this.filterIncidentTypes();
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    currentFaction: 'auth/currentFaction',
    hasGroup: 'auth/hasGroup',
    hasAnyGroup: 'auth/hasAnyGroup',
    position: 'location/position',
    factions: 'cad/factions'
  })
});
// CONCATENATED MODULE: ./pages/cad/submit-incident.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_submit_incidentvue_type_script_lang_js = (submit_incidentvue_type_script_lang_js); 
// CONCATENATED MODULE: ./pages/cad/submit-incident.vue





/* normalize component */

var submit_incident_component = Object(componentNormalizer["a" /* default */])(
  cad_submit_incidentvue_type_script_lang_js,
  submit_incidentvue_type_template_id_b4bd8d9a_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var submit_incident = __webpack_exports__["default"] = (submit_incident_component.exports);

/***/ })

}]);