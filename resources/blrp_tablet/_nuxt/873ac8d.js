(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[62],{

/***/ 1564:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-auto-menu.vue?vue&type=template&id=225d44ea





var app_auto_menuvue_type_template_id_225d44ea_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VTabs["a" /* default */], {
    attrs: {
      "grow": ""
    },
    model: {
      value: _vm.currentTab,
      callback: function callback($$v) {
        _vm.currentTab = $$v;
      },
      expression: "currentTab"
    }
  }, _vm._l(_vm.items, function (route, index) {
    return !route.cName.includes('a_') ? _c(VTab["a" /* default */], {
      key: index,
      attrs: {
        "to": route.cPath
      }
    }, [_vm._v("\n      " + _vm._s(route.cName) + "\n    ")]) : _vm._e();
  }), 1), _vm._v(" "), _c('NuxtChild', {
    key: _vm.$route.fullPath
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-auto-menu.vue?vue&type=template&id=225d44ea

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(51);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// EXTERNAL MODULE: ./node_modules/core-js/modules/esnext.string.replace-all.js
var esnext_string_replace_all = __webpack_require__(494);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-auto-menu.vue?vue&type=script&lang=ts














function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }










function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var app_auto_menuvue_type_script_lang_ts = ({
  name: 'app-auto-menu',
  components: {},
  props: ['first', 'second', 'pre'],
  data: function data() {
    return {
      currentTab: '/cad/system/admin/charges'
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({
    identifier: function identifier() {
      if (!this.second) return this.first;
      return this.first + '-' + this.second;
    },
    items: function items() {
      var _this = this;
      var appRoutes = Object(toConsumableArray["a" /* default */])(this.$router.options.routes.find(function (r) {
        return r.name === _this.first;
      }).children.filter(function (r) {
        return !r.path.includes(':');
      }));
      // console.log('all routes', this.$router.options.routes)
      // const dynamicRoutes =
      //         this.$router.options.routes.filter(r => r.name && r.name.includes(this.first))
      // console.log('appRoutes', dynamicRoutes)
      if (!this.second) {
        var _iterator = _createForOfIteratorHelper(appRoutes),
          _step;
        try {
          for (_iterator.s(); !(_step = _iterator.n()).done;) {
            var route = _step.value;
            route.cPath = "/".concat(this.pre, "/").concat(route.path);
            route.cName = route.name.replace("".concat(this.identifier, "-"), '').replaceAll('-', ' ');
          }
        } catch (err) {
          _iterator.e(err);
        } finally {
          _iterator.f();
        }
        return appRoutes;
      }
      var appChildRoutes = appRoutes.find(function (r) {
        return r.path === _this.second;
      }).children.filter(function (r) {
        return !r.path.includes(':');
      });
      var _iterator2 = _createForOfIteratorHelper(appChildRoutes),
        _step2;
      try {
        for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {
          var _route = _step2.value;
          _route.cPath = "/".concat(this.pre, "/").concat(_route.path);
          _route.cName = _route.name.replace("".concat(this.identifier, "-"), '').replaceAll('-', ' ').replace('incident ', '');
        }
      } catch (err) {
        _iterator2.e(err);
      } finally {
        _iterator2.f();
      }
      return appChildRoutes;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Common/app-auto-menu.vue?vue&type=script&lang=ts
 /* harmony default export */ var Common_app_auto_menuvue_type_script_lang_ts = (app_auto_menuvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-auto-menu.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_auto_menuvue_type_script_lang_ts,
  app_auto_menuvue_type_template_id_225d44ea_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_auto_menu = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 2072:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/admin.vue?vue&type=template&id=01d9c6e9
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-auto-menu', {
    attrs: {
      "pre": "cad/admin",
      "first": "cad",
      "second": "admin"
    }
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/admin.vue?vue&type=template&id=01d9c6e9

// EXTERNAL MODULE: ./components/Common/app-auto-menu.vue + 4 modules
var app_auto_menu = __webpack_require__(1564);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/admin.vue?vue&type=script&lang=js


/* harmony default export */ var adminvue_type_script_lang_js = ({
  components: {
    AppPage: AppPage["a" /* default */],
    AppAutoMenu: app_auto_menu["a" /* default */]
  }
});
// CONCATENATED MODULE: ./pages/cad/admin.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_adminvue_type_script_lang_js = (adminvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/admin.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_adminvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var admin = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);