(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[126],{

/***/ 2141:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/persons/_id/involved-incidents.vue?vue&type=template&id=35780351
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-page', [_c('div')]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/persons/_id/involved-incidents.vue?vue&type=template&id=35780351

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/persons/_id/involved-incidents.vue?vue&type=script&lang=js

/* harmony default export */ var involved_incidentsvue_type_script_lang_js = ({
  components: {
    AppPage: AppPage["a" /* default */]
  }
});
// CONCATENATED MODULE: ./pages/cad/persons/_id/involved-incidents.vue?vue&type=script&lang=js
 /* harmony default export */ var _id_involved_incidentsvue_type_script_lang_js = (involved_incidentsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/persons/_id/involved-incidents.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _id_involved_incidentsvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var involved_incidents = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);