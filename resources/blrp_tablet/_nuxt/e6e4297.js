(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[99],{

/***/ 1555:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressLinear/VProgressLinear.js
var VProgressLinear = __webpack_require__(460);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-page/crudy-page.vue?vue&type=template&id=a90d2eb4












var crudy_pagevue_type_template_id_a90d2eb4_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.instance && _vm.instance._responseCode === 401 ? _c('div', [_c('app-no-perms')], 1) : _vm.instance ? _c('div', [!_vm.instance._loadingFirstTime && _vm.instance._item ? _c('div', [!_vm.config.hideHeader ? _c(VCard["a" /* default */], [_vm.image ? _c(VImg["a" /* default */], {
    attrs: {
      "src": _vm.image
    }
  }) : _vm._e(), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    staticClass: "text-h3",
    attrs: {
      "cols": "2",
      "align": "center"
    }
  }, [_c('i', {
    class: _vm.instance._meta.icon,
    style: "color: ".concat(_vm.instance._item.color)
  })]), _vm._v(" "), _c(VCol["a" /* default */], [_c('div', {
    staticClass: "mb-2",
    staticStyle: {
      "font-size": "25px"
    }
  }, [_vm.$route.path.includes('cad') ? _c('span', [_vm._v("\n                " + _vm._s(_vm.instance._item.id) + " -\n              ")]) : _vm._e(), _vm._v("\n\n              " + _vm._s(_vm.title) + "\n\n              "), _c('crudy-context-menu', {
    attrs: {
      "uuid": _vm.uuid,
      "name": _vm.name,
      "actions": _vm.actions,
      "item": _vm.instance._item
    }
  })], 1), _vm._v(" "), _c('div', {
    staticClass: "text-muted",
    staticStyle: {
      "font-size": "20px"
    }
  }, [_vm.instance._item.created_at ? _c('app-timestamp', {
    attrs: {
      "stamp": _vm.instance._item.created_at
    }
  }) : _vm._e(), _vm._v(" "), _vm.instance._item.created_at ? _c('span', [_vm._v("•")]) : _vm._e(), _vm._v(" " + _vm._s(_vm.instance._meta.title) + "\n            ")], 1)])], 1)], 1)], 1) : _vm._e(), _vm._v(" "), !_vm.config.hideHeader ? _c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c(VRow["a" /* default */], [_vm.logo ? _c(VCol["a" /* default */], {
    attrs: {
      "cols": "3"
    }
  }, [_c(VImg["a" /* default */], {
    attrs: {
      "src": _vm.logo,
      "height": "128",
      "contain": ""
    }
  })], 1) : _vm._e(), _vm._v(" "), _c(VCol["a" /* default */], [_c(VRow["a" /* default */], _vm._l(_vm.infoColumns, function (column) {
    return _c(VCol["a" /* default */], {
      key: "a-body-field-".concat(column.key),
      attrs: {
        "cols": "3"
      }
    }, [_c('div', [_vm._v("\n                  " + _vm._s(column.label) + "\n                ")]), _vm._v(" "), _c('div', {
      staticClass: "text-white"
    }, [column.key !== 'actions' ? _c('crudy-table-column-component', {
      attrs: {
        "item": _vm.instance._item,
        "value": _vm.instance._item[column.key],
        "column": column
      }
    }) : _vm._e()], 1)]);
  }), 1), _vm._v(" "), _vm._l(_vm.contentColumns, function (column) {
    return _c('div', {
      key: "b-body-field-".concat(column.key),
      staticClass: "mt-5",
      attrs: {
        "cols": "3"
      }
    }, [column.key !== 'actions' ? _c('crudy-table-column-component', {
      attrs: {
        "name": _vm.name,
        "item": _vm.instance._item,
        "value": _vm.instance._item[column.key],
        "column": column
      }
    }) : _vm._e()], 1);
  })], 2)], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_vm._t("actions")], 2)], 1) : _vm._e(), _vm._v(" "), _vm.instance._item.contents ? _c('div', {
    staticClass: "mt-3"
  }, [_c('crudy-editor', {
    model: {
      value: _vm.instance._item.contents,
      callback: function callback($$v) {
        _vm.$set(_vm.instance._item, "contents", $$v);
      },
      expression: "instance._item.contents"
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        return _vm.triggerFieldUpdate('contents', _vm.instance._item.contents);
      }
    }
  }, [_vm._v("\n        Save Changes\n      ")])], 1) : _vm._e(), _vm._v(" "), _vm.instance._item.content ? _c('div', {
    staticClass: "mt-3"
  }, [_c('crudy-editor', {
    model: {
      value: _vm.instance._item.content,
      callback: function callback($$v) {
        _vm.$set(_vm.instance._item, "content", $$v);
      },
      expression: "instance._item.content"
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        return _vm.triggerFieldUpdate('contents', _vm.instance._item.content);
      }
    }
  }, [_vm._v("\n        Save Changes\n      ")])], 1) : _vm._e(), _vm._v(" "), !_vm.config.hideRelationships ? _c('crudy-relations-renderer', {
    attrs: {
      "relationships": _vm.instance._relationships
    }
  }) : _vm._e(), _vm._v(" "), _vm._t("body", null, {
    "item": _vm.instance._item,
    "uuid": _vm.uuid,
    "actions": _vm.actions,
    "model": _vm.name
  })], 2) : _c('div', {
    staticClass: "p-5"
  }, [_c(VProgressLinear["a" /* default */], {
    attrs: {
      "active": true,
      "indeterminate": true
    }
  })], 1)]) : !_vm.instance ? _c('div', {
    staticClass: "p-5"
  }, [_c(VProgressLinear["a" /* default */], {
    attrs: {
      "active": true,
      "indeterminate": true
    }
  })], 1) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-page/crudy-page.vue?vue&type=template&id=a90d2eb4

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./utils/store-accessor.ts
var store_accessor = __webpack_require__(27);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table-items-component.vue + 4 modules
var crudy_table_items_component = __webpack_require__(492);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table-column-component/crudy-table-column-component.vue + 4 modules
var crudy_table_column_component = __webpack_require__(269);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabItem.js + 1 modules
var VTabItem = __webpack_require__(1554);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabsItems.js + 1 modules
var VTabsItems = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=template&id=5de72f5d







var crudy_relations_renderervue_type_template_id_5de72f5d_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "mt-4"
  }, [_c(VTabs["a" /* default */], {
    staticClass: "mt-4",
    attrs: {
      "grow": "",
      "background-color": "transparent",
      "slider-color": "white"
    },
    model: {
      value: _vm.selectedRelationshipTab,
      callback: function callback($$v) {
        _vm.selectedRelationshipTab = $$v;
      },
      expression: "selectedRelationshipTab"
    }
  }, _vm._l(_vm.relationships, function (relationship) {
    return relationship.type === 'hasMany' && relationship.relatedModelSchemaMeta ? _c(VTab["a" /* default */], {
      key: "a-".concat(relationship.knowsModel, "-").concat(relationship.foreignKeyName)
    }, [relationship.relatedModelSchemaMeta.icon ? _c('i', {
      staticClass: "mr-2",
      class: relationship.relatedModelSchemaMeta.icon
    }) : _vm._e(), _vm._v(" "), _c('span', [_vm._v("\n        " + _vm._s(relationship.relatedModelSchemaMeta.title) + "\n      ")])]) : _vm._e();
  }), 1), _vm._v(" "), _c(VTabsItems["a" /* default */], {
    staticStyle: {
      "background-color": "transparent"
    },
    model: {
      value: _vm.selectedRelationshipTab,
      callback: function callback($$v) {
        _vm.selectedRelationshipTab = $$v;
      },
      expression: "selectedRelationshipTab"
    }
  }, _vm._l(_vm.relationships, function (relationship) {
    return relationship.type === 'hasMany' && relationship.relatedModelSchemaMeta ? _c(VTabItem["a" /* default */], {
      key: "b-".concat(relationship.knowsModel, "-").concat(relationship.foreignKeyName),
      staticStyle: {
        "background-color": "transparent"
      }
    }, [_c('crudy-table', {
      attrs: {
        "name": relationship.knowsModel,
        "view": "index",
        "autofill": Object(defineProperty["a" /* default */])({}, relationship.foreignKeyName, _vm.$route.params.id),
        "filter": Object(defineProperty["a" /* default */])({}, relationship.foreignKeyName, _vm.$route.params.id)
      }
    })], 1) : _vm._e();
  }), 1)], 1);
};
var crudy_relations_renderervue_type_template_id_5de72f5d_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=template&id=5de72f5d

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var crudy_relations_renderervue_type_script_lang_js = ({
  name: 'crudy-relations-renderer',
  components: {
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['relationships'],
  data: function data() {
    return {
      selectedRelationshipTab: null
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Crudy/crudy-parts/crudy-relations-renderer.vue?vue&type=script&lang=js
 /* harmony default export */ var crudy_parts_crudy_relations_renderervue_type_script_lang_js = (crudy_relations_renderervue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-parts/crudy-relations-renderer.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_parts_crudy_relations_renderervue_type_script_lang_js,
  crudy_relations_renderervue_type_template_id_5de72f5d_render,
  crudy_relations_renderervue_type_template_id_5de72f5d_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_relations_renderer = (component.exports);
// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-context-menu.vue + 4 modules
var crudy_context_menu = __webpack_require__(74);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// EXTERNAL MODULE: ./components/Common/app-no-perms.vue + 4 modules
var app_no_perms = __webpack_require__(491);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-page/crudy-page.vue?vue&type=script&lang=ts


function crudy_pagevue_type_script_lang_ts_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function crudy_pagevue_type_script_lang_ts_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? crudy_pagevue_type_script_lang_ts_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : crudy_pagevue_type_script_lang_ts_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





















/* harmony default export */ var crudy_pagevue_type_script_lang_ts = ({
  name: 'crudy-page',
  components: {
    AppNoPerms: app_no_perms["a" /* default */],
    CrudyEditor: crudy_editor["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */],
    CrudyContextMenu: crudy_context_menu["a" /* default */],
    CrudyRelationsRenderer: crudy_relations_renderer,
    CrudyTableColumnComponent: crudy_table_column_component["default"],
    CrudyTableItemsComponent: crudy_table_items_component["a" /* default */],
    CrudyTable: crudy_table["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */]
  },
  props: {
    name: {},
    view: {},
    config: {
      default: function _default() {
        return {
          hideHeader: false,
          hideRelationships: false,
          hideLoader: false
        };
      }
    }
  },
  data: function data() {
    return {
      uuid: null,
      identifier: null,
      selectedRelationshipTab: null
    };
  },
  created: function created() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var _a, item;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _this.identifier = _this.$route.params.id;
            _context.next = 3;
            return store_accessor["d" /* CrudyStore */].activatePage({
              name: _this.name,
              view: (_a = _this.view) !== null && _a !== void 0 ? _a : 'view',
              pk: _this.$route.params.id
            });
          case 3:
            _this.uuid = _context.sent;
            // Get item from CrudyStore
            item = store_accessor["c" /* CrudyPageStore */].instances[_this.uuid]._item; // @ts-ignore
            window.$events.$on('crudy:form:finish', function () {
              store_accessor["c" /* CrudyPageStore */].refreshCrudyPage(_this.uuid);
            });
          case 6:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  beforeDestroy: function beforeDestroy() {
    // @ts-ignore
    window.$events.$off('crudy:form:finish');
  },
  methods: {
    tableFilter: function tableFilter(key, value) {
      var filter = {};
      filter[key] = value;
      return filter;
    },
    debounce: function debounce(func) {
      var _this2 = this;
      var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 2000;
      var timer;
      return function () {
        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
          args[_key] = arguments[_key];
        }
        clearTimeout(timer);
        timer = setTimeout(function () {
          func.apply(_this2, args);
        }, timeout);
      };
    },
    triggerFieldUpdate: function triggerFieldUpdate(fieldName, fieldValue) {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var values;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              values = {
                id: _this3.instance._item.id
              };
              values[fieldName] = fieldValue;
              _context2.next = 4;
              return store_accessor["d" /* CrudyStore */].sendItemFieldUpdate({
                name: _this3.name,
                values: values
              });
            case 4:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    refresh: function refresh() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.next = 2;
              return store_accessor["c" /* CrudyPageStore */].refreshCrudyPage(_this4.uuid);
            case 2:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    }
  },
  computed: crudy_pagevue_type_script_lang_ts_objectSpread({
    instance: function instance() {
      if (this.uuid) {
        return store_accessor["c" /* CrudyPageStore */].instances[this.uuid];
      }
    },
    title: function title() {
      if (this.instance._item.name) return this.instance._item.name;
      if (this.instance._item.title) return this.instance._item.title;
      if (this.instance._item.address) return this.instance._item.address;
      if (this.instance._item.registration) return this.instance._item.registration;
      if (this.instance._item.display_name) return this.instance._item.display_name;
    },
    image: function image() {
      if (this.instance._item.main_picture) return this.instance._item.main_picture;
      if (this.instance._item.image_link) return this.instance._item.image_link;
    },
    logo: function logo() {
      var _a, _b;
      if ((_a = this.instance._item.department) === null || _a === void 0 ? void 0 : _a.logo) return (_b = this.instance._item.department) === null || _b === void 0 ? void 0 : _b.logo;
      if (this.instance._item.avatar_url) return this.instance._item.avatar_url;
    },
    infoColumns: function infoColumns() {
      return this.instance._table.columns.filter(function (c) {
        return c.key !== 'description' && c.key !== 'created_at' && c.key !== 'updated_at' && c.key !== 'deleted_at' && c.key !== 'name' && c.key !== 'title' && c.key !== 'content' && c.key !== 'contents';
      });
    },
    contentColumns: function contentColumns() {
      return this.instance._table.columns.filter(function (c) {
        return c.key === 'content' || c.key === 'contents' || c.key === 'description';
      });
    },
    actions: function actions() {
      return store_accessor["d" /* CrudyStore */].instances[this.name]._schema.actions;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode'
  })),
  watch: {
    'instance._item': function instance_item() {
      this.$emit('loaded', this.instance._item);
      var item = this.instance._item;
      console.log('item', item);
      // if item has title, or name add it to the history
      if (item.title || item.name) {
        var title = item.title || item.name;
        // Dispatch to store
        this.$store.dispatch('system/addHistoryItem', {
          name: title,
          path: this.$route.fullPath,
          icon: item.icon || this.instance._meta.icon
        });
      }
    }
  }
});
// CONCATENATED MODULE: ./components/Crudy/crudy-page/crudy-page.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_page_crudy_pagevue_type_script_lang_ts = (crudy_pagevue_type_script_lang_ts); 
// CONCATENATED MODULE: ./components/Crudy/crudy-page/crudy-page.vue





/* normalize component */

var crudy_page_component = Object(componentNormalizer["a" /* default */])(
  crudy_page_crudy_pagevue_type_script_lang_ts,
  crudy_pagevue_type_template_id_a90d2eb4_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_page = __webpack_exports__["a"] = (crudy_page_component.exports);

/* nuxt-component-imports */
installComponents(crudy_page_component, {CrudyTableColumnComponent: __webpack_require__(269).default})


/***/ }),

/***/ 2136:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/incident-person-types/_id.vue?vue&type=template&id=09187ecd
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('crudy-page', {
    attrs: {
      "name": "PoliceIncidentPersonType"
    }
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/incident-person-types/_id.vue?vue&type=template&id=09187ecd

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Crudy/crudy-page/crudy-page.vue + 9 modules
var crudy_page = __webpack_require__(1555);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/incident-person-types/_id.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var _idvue_type_script_lang_js = ({
  components: {
    CrudyPage: crudy_page["a" /* default */]
  },
  props: [],
  data: function data() {
    return {};
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./pages/cad/incident-person-types/_id.vue?vue&type=script&lang=js
 /* harmony default export */ var incident_person_types_idvue_type_script_lang_js = (_idvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/incident-person-types/_id.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  incident_person_types_idvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);