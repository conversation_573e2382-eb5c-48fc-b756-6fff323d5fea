(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[170],{

/***/ 1748:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1996);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("337a180f", content, true, {"sourceMap":false});

/***/ }),

/***/ 1951:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1952);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("232fc0a5", content, true, {"sourceMap":false});

/***/ }),

/***/ 1952:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".theme--light.v-stepper{background:#fff}.theme--light.v-stepper .v-stepper__step:not(.v-stepper__step--active):not(.v-stepper__step--complete):not(.v-stepper__step--error) .v-stepper__step__step{background:rgba(0,0,0,.38)}.theme--light.v-stepper .v-stepper__step__step,.theme--light.v-stepper .v-stepper__step__step .v-icon{color:#fff}.theme--light.v-stepper .v-stepper__header .v-divider{border-color:rgba(0,0,0,.12)}.theme--light.v-stepper .v-stepper__step--active .v-stepper__label{text-shadow:0 0 0 #000}.theme--light.v-stepper .v-stepper__step--editable:hover{background:rgba(0,0,0,.06)}.theme--light.v-stepper .v-stepper__step--editable:hover .v-stepper__label{text-shadow:0 0 0 #000}.theme--light.v-stepper .v-stepper__step--complete .v-stepper__label{color:rgba(0,0,0,.87)}.theme--light.v-stepper .v-stepper__step--inactive.v-stepper__step--editable:not(.v-stepper__step--error):hover .v-stepper__step__step{background:rgba(0,0,0,.54)}.theme--light.v-stepper .v-stepper__label{color:rgba(0,0,0,.38)}.theme--light.v-stepper .v-stepper__label small,.theme--light.v-stepper--non-linear .v-stepper__step:not(.v-stepper__step--complete):not(.v-stepper__step--error) .v-stepper__label{color:rgba(0,0,0,.6)}.v-application--is-ltr .theme--light.v-stepper--vertical .v-stepper__content:not(:last-child){border-left:1px solid rgba(0,0,0,.12)}.v-application--is-rtl .theme--light.v-stepper--vertical .v-stepper__content:not(:last-child){border-right:1px solid rgba(0,0,0,.12)}.theme--dark.v-stepper{background:#303030}.theme--dark.v-stepper .v-stepper__step:not(.v-stepper__step--active):not(.v-stepper__step--complete):not(.v-stepper__step--error) .v-stepper__step__step{background:hsla(0,0%,100%,.5)}.theme--dark.v-stepper .v-stepper__step__step,.theme--dark.v-stepper .v-stepper__step__step .v-icon{color:#fff}.theme--dark.v-stepper .v-stepper__header .v-divider{border-color:hsla(0,0%,100%,.12)}.theme--dark.v-stepper .v-stepper__step--active .v-stepper__label{text-shadow:0 0 0 #fff}.theme--dark.v-stepper .v-stepper__step--editable:hover{background:hsla(0,0%,100%,.06)}.theme--dark.v-stepper .v-stepper__step--editable:hover .v-stepper__label{text-shadow:0 0 0 #fff}.theme--dark.v-stepper .v-stepper__step--complete .v-stepper__label{color:hsla(0,0%,100%,.87)}.theme--dark.v-stepper .v-stepper__step--inactive.v-stepper__step--editable:not(.v-stepper__step--error):hover .v-stepper__step__step{background:hsla(0,0%,100%,.75)}.theme--dark.v-stepper .v-stepper__label{color:hsla(0,0%,100%,.5)}.theme--dark.v-stepper .v-stepper__label small,.theme--dark.v-stepper--non-linear .v-stepper__step:not(.v-stepper__step--complete):not(.v-stepper__step--error) .v-stepper__label{color:hsla(0,0%,100%,.7)}.v-application--is-ltr .theme--dark.v-stepper--vertical .v-stepper__content:not(:last-child){border-left:1px solid hsla(0,0%,100%,.12)}.v-application--is-rtl .theme--dark.v-stepper--vertical .v-stepper__content:not(:last-child){border-right:1px solid hsla(0,0%,100%,.12)}.v-sheet.v-stepper{border-radius:4px}.v-sheet.v-stepper:not(.v-sheet--outlined){box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12)}.v-sheet.v-stepper.v-sheet--shaped{border-radius:16px 4px}.v-stepper{border-radius:4px;overflow:hidden;position:relative}.v-stepper__header{align-items:stretch;box-shadow:0 3px 1px -2px rgba(0,0,0,.2),0 2px 2px 0 rgba(0,0,0,.14),0 1px 5px 0 rgba(0,0,0,.12);display:flex;flex-wrap:wrap;height:72px;justify-content:space-between}.v-stepper__header .v-divider{align-self:center;margin:0 -16px}.v-stepper__items{overflow:hidden;position:relative}.v-stepper__step__step{align-items:center;border-radius:50%;display:inline-flex;font-size:.75rem;height:24px;justify-content:center;min-width:24px;transition:.3s cubic-bezier(.25,.8,.25,1);width:24px}.v-application--is-ltr .v-stepper__step__step{margin-right:8px}.v-application--is-rtl .v-stepper__step__step{margin-left:8px}.v-stepper__step__step .v-icon.v-icon{font-size:1.25rem}.v-stepper__step__step .v-icon.v-icon.v-icon--svg{height:1.25rem;width:1.25rem}.v-stepper__step{align-items:center;display:flex;flex-direction:row;padding:24px;position:relative}.v-stepper__step--active .v-stepper__label{transition:.3s cubic-bezier(.4,0,.6,1)}.v-stepper__step--editable{cursor:pointer}.v-stepper__step.v-stepper__step--error .v-stepper__step__step{background:transparent;color:inherit}.v-stepper__step.v-stepper__step--error .v-stepper__step__step .v-icon{color:inherit;font-size:1.5rem}.v-stepper .v-stepper__step.v-stepper__step--error .v-stepper__label{color:inherit;font-weight:500;text-shadow:none}.v-stepper .v-stepper__step.v-stepper__step--error .v-stepper__label small{color:inherit}.v-stepper__label{display:block;flex-grow:1;line-height:1}.v-application--is-ltr .v-stepper__label{text-align:left}.v-application--is-rtl .v-stepper__label{text-align:right}.v-stepper__label small{display:block;font-size:.75rem;font-weight:300;text-shadow:none}.v-stepper__wrapper{overflow:hidden;transition:none}.v-stepper__content{flex:1 0 auto;padding:24px 24px 16px;top:0;width:100%}.v-stepper__content>.v-btn{margin:24px 8px 8px 0}.v-stepper--flat{box-shadow:0 0 0 0 rgba(0,0,0,.2),0 0 0 0 rgba(0,0,0,.14),0 0 0 0 rgba(0,0,0,.12)!important}.v-stepper--is-booted .v-stepper__content,.v-stepper--is-booted .v-stepper__wrapper{transition:.3s cubic-bezier(.25,.8,.5,1)}.v-stepper--vertical{padding-bottom:36px}.v-stepper--vertical .v-stepper__content{width:auto}.v-application--is-ltr .v-stepper--vertical .v-stepper__content{padding:16px 60px 16px 23px}.v-application--is-rtl .v-stepper--vertical .v-stepper__content{padding:16px 23px 16px 60px}.v-application--is-ltr .v-stepper--vertical .v-stepper__content{margin:-8px -36px -16px 36px}.v-application--is-rtl .v-stepper--vertical .v-stepper__content{margin:-8px 36px -16px -36px}.v-stepper--vertical .v-stepper__step{padding:24px 24px 16px}.v-application--is-ltr .v-stepper--vertical .v-stepper__step__step{margin-right:12px}.v-application--is-rtl .v-stepper--vertical .v-stepper__step__step{margin-left:12px}.v-stepper--alt-labels .v-stepper__header{height:auto}.v-stepper--alt-labels .v-stepper__header .v-divider{align-self:flex-start;margin:35px -67px 0}.v-stepper--alt-labels .v-stepper__step{align-items:center;flex-basis:175px;flex-direction:column;justify-content:flex-start}.v-stepper--alt-labels .v-stepper__step small{text-align:center}.v-stepper--alt-labels .v-stepper__step__step{margin-bottom:11px;margin-left:0;margin-right:0}@media only screen and (max-width:959.98px){.v-stepper:not(.v-stepper--vertical) .v-stepper__label{display:none}.v-stepper:not(.v-stepper--vertical) .v-stepper__step__step{margin-left:0;margin-right:0}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1995:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_install_vue_vue_type_style_index_0_id_59b3a0ea_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1748);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_install_vue_vue_type_style_index_0_id_59b3a0ea_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_install_vue_vue_type_style_index_0_id_59b3a0ea_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1996:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".vue-terminal-wrapper *{background-color:transparent!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2031:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtnToggle/VBtnToggle.js + 1 modules
var VBtnToggle = __webpack_require__(1553);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VDivider/VDivider.js
var VDivider = __webpack_require__(466);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(78);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 2 modules
var components_VList = __webpack_require__(50);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemGroup.js
var VListItemGroup = __webpack_require__(465);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemIcon.js
var VListItemIcon = __webpack_require__(260);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressLinear/VProgressLinear.js
var VProgressLinear = __webpack_require__(460);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VSpacer.js
var VSpacer = __webpack_require__(1523);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.flat.js
var es_array_flat = __webpack_require__(177);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.unscopables.flat.js
var es_array_unscopables_flat = __webpack_require__(178);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.number.constructor.js
var es_number_constructor = __webpack_require__(42);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VStepper/VStepper.sass
var VStepper = __webpack_require__(1951);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSheet/index.js
var VSheet = __webpack_require__(166);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/registrable/index.js
var registrable = __webpack_require__(127);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/proxyable/index.js
var proxyable = __webpack_require__(252);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/mixins.js
var mixins = __webpack_require__(16);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/console.js
var console = __webpack_require__(32);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/util/helpers.js
var helpers = __webpack_require__(1);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VStepper/VStepper.js












function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
// Styles
 // Extensions

 // Mixins


 // Utilities




var baseMixins = Object(mixins["a" /* default */])(VSheet["a" /* default */], Object(registrable["b" /* provide */])('stepper'), proxyable["a" /* default */]);
/* @vue/component */

/* harmony default export */ var VStepper_VStepper = (baseMixins.extend({
  name: 'v-stepper',
  provide: function provide() {
    return {
      stepClick: this.stepClick,
      isVertical: this.vertical
    };
  },
  props: {
    altLabels: Boolean,
    nonLinear: Boolean,
    flat: Boolean,
    vertical: Boolean
  },
  data: function data() {
    var data = {
      isBooted: false,
      steps: [],
      content: [],
      isReverse: false
    };
    data.internalLazyValue = this.value != null ? this.value : (data[0] || {}).step || 1;
    return data;
  },
  computed: {
    classes: function classes() {
      return _objectSpread({
        'v-stepper--flat': this.flat,
        'v-stepper--is-booted': this.isBooted,
        'v-stepper--vertical': this.vertical,
        'v-stepper--alt-labels': this.altLabels,
        'v-stepper--non-linear': this.nonLinear
      }, VSheet["a" /* default */].options.computed.classes.call(this));
    },
    styles: function styles() {
      return _objectSpread({}, VSheet["a" /* default */].options.computed.styles.call(this));
    }
  },
  watch: {
    internalValue: function internalValue(val, oldVal) {
      this.isReverse = Number(val) < Number(oldVal);
      oldVal && (this.isBooted = true);
      this.updateView();
    }
  },
  created: function created() {
    /* istanbul ignore next */
    if (this.$listeners.input) {
      Object(console["a" /* breaking */])('@input', '@change', this);
    }
  },
  mounted: function mounted() {
    this.updateView();
  },
  methods: {
    register: function register(item) {
      if (item.$options.name === 'v-stepper-step') {
        this.steps.push(item);
      } else if (item.$options.name === 'v-stepper-content') {
        item.isVertical = this.vertical;
        this.content.push(item);
      }
    },
    unregister: function unregister(item) {
      if (item.$options.name === 'v-stepper-step') {
        this.steps = this.steps.filter(function (i) {
          return i !== item;
        });
      } else if (item.$options.name === 'v-stepper-content') {
        item.isVertical = this.vertical;
        this.content = this.content.filter(function (i) {
          return i !== item;
        });
      }
    },
    stepClick: function stepClick(step) {
      var _this = this;
      this.$nextTick(function () {
        return _this.internalValue = step;
      });
    },
    updateView: function updateView() {
      for (var index = this.steps.length; --index >= 0;) {
        this.steps[index].toggle(this.internalValue);
      }
      for (var _index = this.content.length; --_index >= 0;) {
        this.content[_index].toggle(this.internalValue, this.isReverse);
      }
    }
  },
  render: function render(h) {
    return h(this.tag, {
      staticClass: 'v-stepper',
      class: this.classes,
      style: this.styles
    }, Object(helpers["s" /* getSlot */])(this));
  }
}));
// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VStepper/VStepperContent.js




// Components
 // Mixins

 // Helpers

 // Utilities


var VStepperContent_baseMixins = Object(mixins["a" /* default */])(Object(registrable["a" /* inject */])('stepper', 'v-stepper-content', 'v-stepper'));
/* @vue/component */

/* harmony default export */ var VStepperContent = (VStepperContent_baseMixins.extend().extend({
  name: 'v-stepper-content',
  inject: {
    isVerticalProvided: {
      from: 'isVertical'
    }
  },
  props: {
    step: {
      type: [Number, String],
      required: true
    }
  },
  data: function data() {
    return {
      height: 0,
      // Must be null to allow
      // previous comparison
      isActive: null,
      isReverse: false,
      isVertical: this.isVerticalProvided
    };
  },
  computed: {
    computedTransition: function computedTransition() {
      // Fix for #8978
      var reverse = this.$vuetify.rtl ? !this.isReverse : this.isReverse;
      return reverse ? transitions["j" /* VTabReverseTransition */] : transitions["k" /* VTabTransition */];
    },
    styles: function styles() {
      if (!this.isVertical) return {};
      return {
        height: Object(helpers["h" /* convertToUnit */])(this.height)
      };
    }
  },
  watch: {
    isActive: function isActive(current, previous) {
      // If active and the previous state
      // was null, is just booting up
      if (current && previous == null) {
        this.height = 'auto';
        return;
      }
      if (!this.isVertical) return;
      if (this.isActive) this.enter();else this.leave();
    }
  },
  mounted: function mounted() {
    this.$refs.wrapper.addEventListener('transitionend', this.onTransition, false);
    this.stepper && this.stepper.register(this);
  },
  beforeDestroy: function beforeDestroy() {
    this.$refs.wrapper.removeEventListener('transitionend', this.onTransition, false);
    this.stepper && this.stepper.unregister(this);
  },
  methods: {
    onTransition: function onTransition(e) {
      if (!this.isActive || e.propertyName !== 'height') return;
      this.height = 'auto';
    },
    enter: function enter() {
      var _this = this;
      var scrollHeight = 0; // Render bug with height

      requestAnimationFrame(function () {
        scrollHeight = _this.$refs.wrapper.scrollHeight;
      });
      this.height = 0; // Give the collapsing element time to collapse

      setTimeout(function () {
        return _this.isActive && (_this.height = scrollHeight || 'auto');
      }, 450);
    },
    leave: function leave() {
      var _this2 = this;
      this.height = this.$refs.wrapper.clientHeight;
      setTimeout(function () {
        return _this2.height = 0;
      }, 10);
    },
    toggle: function toggle(step, reverse) {
      this.isActive = step.toString() === this.step.toString();
      this.isReverse = reverse;
    }
  },
  render: function render(h) {
    var contentData = {
      staticClass: 'v-stepper__content'
    };
    var wrapperData = {
      staticClass: 'v-stepper__wrapper',
      style: this.styles,
      ref: 'wrapper'
    };
    if (!this.isVertical) {
      contentData.directives = [{
        name: 'show',
        value: this.isActive
      }];
    }
    var wrapper = h('div', wrapperData, Object(helpers["s" /* getSlot */])(this));
    var content = h('div', contentData, [wrapper]);
    return h(this.computedTransition, {
      on: this.$listeners
    }, [content]);
  }
}));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/index.js
var components_VIcon = __webpack_require__(49);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/mixins/colorable/index.js
var colorable = __webpack_require__(41);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/ripple/index.js
var ripple = __webpack_require__(99);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VStepper/VStepperStep.js



// Components
 // Mixins


 // Directives

 // Utilities



var VStepperStep_baseMixins = Object(mixins["a" /* default */])(colorable["a" /* default */], Object(registrable["a" /* inject */])('stepper', 'v-stepper-step', 'v-stepper'));
/* @vue/component */

/* harmony default export */ var VStepperStep = (VStepperStep_baseMixins.extend().extend({
  name: 'v-stepper-step',
  directives: {
    ripple: ripple["a" /* default */]
  },
  inject: ['stepClick'],
  props: {
    color: {
      type: String,
      default: 'primary'
    },
    complete: Boolean,
    completeIcon: {
      type: String,
      default: '$complete'
    },
    editable: Boolean,
    editIcon: {
      type: String,
      default: '$edit'
    },
    errorIcon: {
      type: String,
      default: '$error'
    },
    rules: {
      type: Array,
      default: function _default() {
        return [];
      }
    },
    step: [Number, String]
  },
  data: function data() {
    return {
      isActive: false,
      isInactive: true
    };
  },
  computed: {
    classes: function classes() {
      return {
        'v-stepper__step--active': this.isActive,
        'v-stepper__step--editable': this.editable,
        'v-stepper__step--inactive': this.isInactive,
        'v-stepper__step--error error--text': this.hasError,
        'v-stepper__step--complete': this.complete
      };
    },
    hasError: function hasError() {
      return this.rules.some(function (validate) {
        return validate() !== true;
      });
    }
  },
  mounted: function mounted() {
    this.stepper && this.stepper.register(this);
  },
  beforeDestroy: function beforeDestroy() {
    this.stepper && this.stepper.unregister(this);
  },
  methods: {
    click: function click(e) {
      e.stopPropagation();
      this.$emit('click', e);
      if (this.editable) {
        this.stepClick(this.step);
      }
    },
    genIcon: function genIcon(icon) {
      return this.$createElement(components_VIcon["a" /* default */], icon);
    },
    genLabel: function genLabel() {
      return this.$createElement('div', {
        staticClass: 'v-stepper__label'
      }, Object(helpers["s" /* getSlot */])(this));
    },
    genStep: function genStep() {
      var color = !this.hasError && (this.complete || this.isActive) ? this.color : false;
      return this.$createElement('span', this.setBackgroundColor(color, {
        staticClass: 'v-stepper__step__step'
      }), this.genStepContent());
    },
    genStepContent: function genStepContent() {
      var children = [];
      if (this.hasError) {
        children.push(this.genIcon(this.errorIcon));
      } else if (this.complete) {
        if (this.editable) {
          children.push(this.genIcon(this.editIcon));
        } else {
          children.push(this.genIcon(this.completeIcon));
        }
      } else {
        children.push(String(this.step));
      }
      return children;
    },
    keyboardClick: function keyboardClick(e) {
      if (e.keyCode === helpers["x" /* keyCodes */].space) {
        this.click(e);
      }
    },
    toggle: function toggle(step) {
      this.isActive = step.toString() === this.step.toString();
      this.isInactive = Number(step) < Number(this.step);
    }
  },
  render: function render(h) {
    return h('div', {
      attrs: {
        tabindex: this.editable ? 0 : -1
      },
      staticClass: 'v-stepper__step',
      class: this.classes,
      directives: [{
        name: 'ripple',
        value: this.editable
      }],
      on: {
        click: this.click,
        keydown: this.keyboardClick
      }
    }, [this.genStep(), this.genLabel()]);
  }
}));
// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VStepper/index.js




var VStepperHeader = Object(helpers["j" /* createSimpleFunctional */])('v-stepper__header');
var VStepperItems = Object(helpers["j" /* createSimpleFunctional */])('v-stepper__items');

/* harmony default export */ var components_VStepper = ({
  $_vuetify_subcomponents: {
    VStepper: VStepper_VStepper,
    VStepperContent: VStepperContent,
    VStepperStep: VStepperStep,
    VStepperHeader: VStepperHeader,
    VStepperItems: VStepperItems
  }
});
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VToolbar/VToolbar.js
var VToolbar = __webpack_require__(172);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/install.vue?vue&type=template&id=59b3a0ea






























var installvue_type_template_id_59b3a0ea_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "d-flex flex-column-reverse no-drag"
  }, [_c(transitions["f" /* VScrollYReverseTransition */], [_vm.current === 'uninstalled' || _vm.current === 'installing' ? _c('div', {
    staticStyle: {
      "font-family": "monospace, monospace"
    }
  }, [_c('div', {
    staticClass: "terminal"
  }, [_c('span', {
    domProps: {
      "innerHTML": _vm._s(_vm.pointerText)
    }
  })]), _vm._v(" "), _c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.onCommand.apply(null, arguments);
      }
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "autofocus": "",
      "color": "gray",
      "loading": _vm.current === 'installing'
    },
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.onCommand.apply(null, arguments);
      }
    },
    scopedSlots: _vm._u([{
      key: "progress",
      fn: function fn() {
        return [_vm.current === 'installing' ? _c(VProgressLinear["a" /* default */], {
          attrs: {
            "value": _vm.percent,
            "color": "primary",
            "absolute": "",
            "height": "7"
          }
        }) : _vm._e()];
      },
      proxy: true
    }], null, false, 734554093),
    model: {
      value: _vm.terminalText,
      callback: function callback($$v) {
        _vm.terminalText = $$v;
      },
      expression: "terminalText"
    }
  })], 1)], 1) : _vm._e()]), _vm._v(" "), _c(transitions["a" /* VExpandTransition */], [_vm.current === 'unconfigured' ? _c('div', [_c(VToolbar["a" /* default */], {
    staticStyle: {
      "background-color": "#212121"
    }
  }, [_c('h4', [_vm._v("\n          BadOS Installer\n        ")]), _vm._v(" "), _c(VSpacer["a" /* default */]), _vm._v(" "), _c(VImg["a" /* default */], {
    staticClass: "end",
    attrs: {
      "max-width": "32",
      "src": "https://badlandsrp.com/uploads/monthly_2022_02/logo.png.22e03e08cc44e917269dd7667ecc2222.png"
    }
  })], 1), _vm._v(" "), _c(VStepper_VStepper, {
    staticStyle: {
      "background-color": "var(--primary)"
    },
    attrs: {
      "alt-labels": "",
      "elevation": "12",
      "color": "primary"
    },
    model: {
      value: _vm.currentStepperStep,
      callback: function callback($$v) {
        _vm.currentStepperStep = $$v;
      },
      expression: "currentStepperStep"
    }
  }, [_c(VStepperHeader, [_c(VStepperStep, {
    attrs: {
      "color": "secondary",
      "step": "1",
      "complete": _vm.currentStepperStep > 1
    }
  }), _vm._v(" "), _c(VStepperStep, {
    attrs: {
      "color": "secondary",
      "step": "2",
      "complete": _vm.currentStepperStep > 2
    }
  }), _vm._v(" "), _c(VStepperStep, {
    attrs: {
      "color": "secondary",
      "step": "3",
      "complete": _vm.currentStepperStep > 3
    }
  }), _vm._v(" "), _c(VStepperStep, {
    attrs: {
      "color": "secondary",
      "step": "4",
      "complete": _vm.currentStepperStep > 4
    }
  }), _vm._v(" "), _c(VStepperStep, {
    attrs: {
      "color": "secondary",
      "step": "5",
      "complete": _vm.currentStepperStep > 5
    }
  })], 1), _vm._v(" "), _c(VStepperItems, [_c(VStepperContent, {
    attrs: {
      "step": "1"
    }
  }, [_c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c('div', [_c('h4', [_vm._v("\n                    BadOS Installation\n                  ")]), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('h5', [_vm._v("\n                    Welcome to "), _c('b', [_vm._v("BadOS.")]), _vm._v(" BadOS is the worlds #1 leading operating system, specializing in a large\n                    number of problems and sustainability issues.\n                  ")])], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary"
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 2;
      }
    }
  }, [_vm._v("\n                    Start Installation\n                  ")])], 1)], 1)], 1)], 1), _vm._v(" "), _c(VStepperContent, {
    attrs: {
      "step": "2"
    }
  }, [_c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c('div', [_c('h4', [_vm._v("\n                    Installation Drive\n                  ")]), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('h5', [_vm._v("\n                    Select a drive from the following list to install BadOS on.\n                    "), _c('br'), _vm._v(" "), _c('br'), _vm._v(" "), _c(VAlert["a" /* default */], {
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n                      The selected drive will be completely wiped. Do not forget to backup any data first.\n                    ")])], 1), _vm._v(" "), _c(VList["a" /* default */], [_c(VListItemGroup["a" /* default */], {
    attrs: {
      "mandatory": ""
    },
    model: {
      value: _vm.selectedDrive,
      callback: function callback($$v) {
        _vm.selectedDrive = $$v;
      },
      expression: "selectedDrive"
    }
  }, _vm._l(_vm.drives, function (d, i) {
    return _c(VListItem["a" /* default */], {
      key: d.name
    }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
      domProps: {
        "textContent": _vm._s('fa-solid fa-hard-drive')
      }
    })], 1), _vm._v(" "), _c(components_VList["a" /* VListItemContent */], [_c(components_VList["c" /* VListItemTitle */], {
      domProps: {
        "textContent": _vm._s(d.name)
      }
    }), _vm._v(" "), _c(components_VList["a" /* VListItemContent */], {
      domProps: {
        "textContent": _vm._s("".concat(d.used, " / ").concat(d.free))
      }
    })], 1)], 1);
  }), 1)], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VBtn["a" /* default */], {
    attrs: {
      "disabled": !_vm.selectedDrive,
      "color": "primary"
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 3;
      }
    }
  }, [_vm._v("\n                    Continue\n                  ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "text": ""
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 1;
      }
    }
  }, [_vm._v("\n                    Back\n                  ")])], 1)], 1)], 1)], 1), _vm._v(" "), _c(VStepperContent, {
    attrs: {
      "step": "3"
    }
  }, [_c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c('div', [_c('h4', [_vm._v("\n                    My Social.\n                  ")]), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('h5', [_vm._v("\n                    BadOS Syncs all user information to the installed hardware.\n                    This includes your profile information, device data, and more.\n                    Because of this, there is no longer a need to login to technology.\n                    "), _c('br'), _vm._v(" "), _c('br'), _vm._v(" "), _c('h5', {
    staticClass: "font-weight-bold"
  }, [_vm._v("My Display Name")]), _vm._v(" "), _c(VAlert["a" /* default */], {
    attrs: {
      "color": "black"
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-info text-danger mr-2"
  }), _vm._v("\n                      Your display name can only be changed once every 15 days, may not include any special character or spaces, and must be unique.\n                    ")]), _vm._v(" "), _c(VTextField["a" /* default */], {
    attrs: {
      "outlined": "",
      "hint": "Your display name represents who you are, your nickname or whatever you like.",
      "label": "Display Name"
    },
    model: {
      value: _vm.installForm.displayName,
      callback: function callback($$v) {
        _vm.$set(_vm.installForm, "displayName", $$v);
      },
      expression: "installForm.displayName"
    }
  }), _vm._v(" "), _vm.displayNameTaken ? _c(VAlert["a" /* default */], {
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n                      Name is already taken or is invalid.\n                    ")]) : _vm._e()], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VBtn["a" /* default */], {
    attrs: {
      "disabled": !_vm.selectedDrive,
      "color": "primary"
    },
    on: {
      "click": _vm.verifyDisplayName
    }
  }, [_vm._v("\n                    Continue\n                  ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 4;
      }
    }
  }, [_vm._v("\n                    Fill Out Later\n                  ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "text": ""
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 2;
      }
    }
  }, [_vm._v("\n                    Back\n                  ")])], 1)], 1)], 1)], 1), _vm._v(" "), _c(VStepperContent, {
    attrs: {
      "step": "4"
    }
  }, [_c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c('div', [_c('h4', [_vm._v("\n                    Presence Status.\n                  ")]), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('h5', [_vm._v("\n                    How would you like to display for other users?\n                    "), _c('br'), _vm._v(" "), _c('br'), _vm._v(" "), _c('layout-person-presence-picker')], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary"
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 5;
      }
    }
  }, [_vm._v("\n                    Continue\n                  ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "success"
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 5;
      }
    }
  }, [_vm._v("\n                    Fill Out Later\n                  ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "text": ""
    },
    on: {
      "click": function click($event) {
        _vm.currentStepperStep = 3;
      }
    }
  }, [_vm._v("\n                    Back\n                  ")])], 1)], 1)], 1)], 1), _vm._v(" "), _c(VStepperContent, {
    attrs: {
      "step": "5"
    }
  }, [_c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c('div', [_c('h4', [_vm._v("\n                    Final Step.\n                  ")]), _vm._v(" "), _c(VDivider["a" /* default */]), _vm._v(" "), _c('h5', [_vm._v("\n                    Looks like the system is ready to install. Select the button below to finally begin installation.\n                    If you have trouble finding the option you can click "), _c('a', {
    attrs: {
      "href": ""
    },
    on: {
      "click": function click($event) {
        $event.preventDefault();
        return _vm.$router.push('/misc/fault');
      }
    }
  }, [_vm._v("here")]), _vm._v(" to contact BadOS Support.\n                    "), _c('hr'), _vm._v(" "), _c('i', {
    staticClass: "fa-solid fa-user-robot-xmarks mr-2"
  }), _vm._v("\n                    To Prove you are human, please answer the following security question: "), _c('span', {
    staticClass: "text-white"
  }, [_vm._v("\n                    \"When going AFK, what do you say?\"\n                  ")]), _vm._v(" "), _c(VBtnToggle["a" /* default */], {
    staticClass: "mt-3",
    model: {
      value: _vm.isHuman,
      callback: function callback($$v) {
        _vm.isHuman = $$v;
      },
      expression: "isHuman"
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "value": 1
    }
  }, [_vm._v("\n                        \"I have to go in my head.\"\n                      ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "value": 2
    }
  }, [_vm._v("\n                        \"I was in my head.\"\n                      ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "value": 3
    }
  }, [_vm._v("\n                        \"I have to think about something.\"\n                      ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "value": 4
    }
  }, [_vm._v("\n                        \"I was in my head.\"\n                      ")])], 1), _vm._v(" "), _vm.isHuman && _vm.isHuman !== 3 ? _c('div', {
    staticClass: "mt-4 text-danger"
  }, [_vm._v("\n                      What are you, some kind of robot? Humans can not physically go inside their head.\n                    ")]) : _vm._e()], 1)], 1), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VBtn["a" /* default */], {
    attrs: {
      "disabled": _vm.isHuman !== 3,
      "right": "",
      "color": "primary"
    },
    on: {
      "click": _vm.finallyFinish
    }
  }, [_vm._v("\n                    Install BadOS\n                  ")])], 1)], 1)], 1)], 1)], 1)], 1)], 1) : _vm._e()])], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/misc/install.vue?vue&type=template&id=59b3a0ea

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.number.to-fixed.js
var es_number_to_fixed = __webpack_require__(385);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array-buffer.constructor.js
var es_array_buffer_constructor = __webpack_require__(1953);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array-buffer.slice.js
var es_array_buffer_slice = __webpack_require__(1958);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.data-view.js
var es_data_view = __webpack_require__(1959);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.uint8-array.js
var es_typed_array_uint8_array = __webpack_require__(1961);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.copy-within.js
var es_typed_array_copy_within = __webpack_require__(1969);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.every.js
var es_typed_array_every = __webpack_require__(1971);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.fill.js
var es_typed_array_fill = __webpack_require__(1972);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.filter.js
var es_typed_array_filter = __webpack_require__(1973);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.find.js
var es_typed_array_find = __webpack_require__(1975);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.find-index.js
var es_typed_array_find_index = __webpack_require__(1976);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.for-each.js
var es_typed_array_for_each = __webpack_require__(1977);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.includes.js
var es_typed_array_includes = __webpack_require__(1978);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.index-of.js
var es_typed_array_index_of = __webpack_require__(1979);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.iterator.js
var es_typed_array_iterator = __webpack_require__(1980);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.join.js
var es_typed_array_join = __webpack_require__(1981);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.last-index-of.js
var es_typed_array_last_index_of = __webpack_require__(1982);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.map.js
var es_typed_array_map = __webpack_require__(1984);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.reduce.js
var es_typed_array_reduce = __webpack_require__(1985);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.reduce-right.js
var es_typed_array_reduce_right = __webpack_require__(1986);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.reverse.js
var es_typed_array_reverse = __webpack_require__(1987);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.set.js
var es_typed_array_set = __webpack_require__(1988);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.slice.js
var es_typed_array_slice = __webpack_require__(1989);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.some.js
var es_typed_array_some = __webpack_require__(1990);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.sort.js
var es_typed_array_sort = __webpack_require__(1991);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.subarray.js
var es_typed_array_subarray = __webpack_require__(1992);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.to-locale-string.js
var es_typed_array_to_locale_string = __webpack_require__(1993);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.typed-array.to-string.js
var es_typed_array_to_string = __webpack_require__(1994);

// CONCATENATED MODULE: ./helpers/texter.js































var stepper = function stepper() {
  var text = "";
  text += "install.bad > preparing files ".concat(uuidV4());
};
var uuidV4 = function uuidV4() {
  return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, function (c) {
    return (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16);
  });
};
// EXTERNAL MODULE: ./components/Layout/layout-person-presence-picker.vue + 4 modules
var layout_person_presence_picker = __webpack_require__(255);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/install.vue?vue&type=script&lang=js


function installvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function installvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? installvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : installvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
















/* harmony default export */ var installvue_type_script_lang_js = ({
  components: {
    LayoutPersonPresencePicker: layout_person_presence_picker["a" /* default */]
  },
  props: [],
  data: function data() {
    return {
      isHuman: null,
      currentStepperStep: 1,
      terminalText: null,
      current: 'uninstalled',
      interval: null,
      currentStep: 0,
      totalSteps: 10,
      percent: 0,
      messages: [],
      displayNameTaken: false,
      pointerText: 'NO BOOT DRIVE FOUND \n INSTALL BOS? ( y / n )',
      installForm: {
        displayName: null,
        presence: null
      },
      selectedDrive: null,
      drives: [{
        name: 'Brain',
        used: '8.14 GB',
        free: '162.00 TB',
        noInstall: true
      }, {
        name: 'Right Arm',
        used: '0.00 GB',
        free: '18.00 GB'
      }, {
        name: 'Left Arm',
        used: '0.00 GB',
        free: '19.00 GB'
      }, {
        name: 'Chest',
        used: '0.00 GB',
        free: '52.00 GB'
      }]
    };
  },
  methods: {
    onCommand: function onCommand() {
      if (this.terminalText === 'y' || this.terminalText === 'yes') {
        this.install();
      }
      if (this.terminalText === 'n' || this.terminalText === 'no') {
        this.$store.dispatch('system/exit');
      }
      this.terminalText = null;
    },
    install: function install() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        var messagesEachRun, names, _loop, key;
        return regeneratorRuntime.wrap(function _callee$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              messagesEachRun = 100;
              names = ['preparing files', 'unpacking files and stuff'];
              _this.totalSteps = names.length * messagesEachRun;
              _this.current = 'installing';
              _loop = /*#__PURE__*/regeneratorRuntime.mark(function _loop(key) {
                return regeneratorRuntime.wrap(function _loop$(_context) {
                  while (1) switch (_context.prev = _context.next) {
                    case 0:
                      _context.next = 2;
                      return _this.runMessages(messagesEachRun, 50, function (percent) {
                        return "(".concat(parseInt(key) + 1, " / ").concat(names.length, ") ").concat(names[key], " ").concat(uuidV4(), ".encrypted");
                      });
                    case 2:
                    case "end":
                      return _context.stop();
                  }
                }, _loop);
              });
              _context2.t0 = regeneratorRuntime.keys(names);
            case 6:
              if ((_context2.t1 = _context2.t0()).done) {
                _context2.next = 11;
                break;
              }
              key = _context2.t1.value;
              return _context2.delegateYield(_loop(key), "t2", 9);
            case 9:
              _context2.next = 6;
              break;
            case 11:
              _this.installForm.displayName = _this.user.display_name;
              _this.current = 'unconfigured';
            case 13:
            case "end":
              return _context2.stop();
          }
        }, _callee);
      }))();
    },
    runMessages: function runMessages(runs, delay, generator) {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _context3.next = 2;
              return new Promise(function (resolve) {
                var localStep = 0;
                _this2.interval = setInterval(function () {
                  _this2.currentStep++;
                  localStep++;
                  if (localStep > runs) {
                    clearInterval(_this2.interval);
                    resolve();
                  }
                  _this2.percent = (_this2.currentStep / _this2.totalSteps * 100).toFixed(2);
                  _this2.pointerText = "install.bad > ".concat(_this2.percent, "% ").concat(generator());
                }, delay);
              });
            case 2:
            case "end":
              return _context3.stop();
          }
        }, _callee2);
      }))();
    },
    shouldFault: function shouldFault() {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _context4.next = 2;
              return _this3.$router.push('/misc/fault');
            case 2:
            case "end":
              return _context4.stop();
          }
        }, _callee3);
      }))();
    },
    verifyDisplayName: function verifyDisplayName() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        var data;
        return regeneratorRuntime.wrap(function _callee4$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              if (!(_this4.installForm.displayName === _this4.user.display_name)) {
                _context5.next = 2;
                break;
              }
              return _context5.abrupt("return", _this4.currentStepperStep = 4);
            case 2:
              _context5.next = 4;
              return _this4.$axios.$get("/persons/display-name-exists/".concat(_this4.installForm.displayName));
            case 4:
              data = _context5.sent;
              if (data.exists) {
                _this4.displayNameTaken = true;
              } else {
                _this4.currentStepperStep = 4;
              }
            case 6:
            case "end":
              return _context5.stop();
          }
        }, _callee4);
      }))();
    },
    finallyFinish: function finallyFinish() {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee5() {
        return regeneratorRuntime.wrap(function _callee5$(_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              _context6.next = 2;
              return _this5.$axios.$post('persons/me/install', _this5.installForm);
            case 2:
              _this5.$store.commit('auth/SET_USER_PROPERTY', {
                name: 'installed',
                value: true
              });
              _this5.$store.commit('auth/SET_USER_PROPERTY', {
                name: 'version',
                value: true
              });
              _this5.$router.push({
                path: '/'
              });
            case 5:
            case "end":
              return _context6.stop();
          }
        }, _callee5);
      }))();
    }
  },
  computed: installvue_type_script_lang_js_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  }))
});
// CONCATENATED MODULE: ./pages/misc/install.vue?vue&type=script&lang=js
 /* harmony default export */ var misc_installvue_type_script_lang_js = (installvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/misc/install.vue?vue&type=style&index=0&id=59b3a0ea&prod&lang=scss
var installvue_type_style_index_0_id_59b3a0ea_prod_lang_scss = __webpack_require__(1995);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/misc/install.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  misc_installvue_type_script_lang_js,
  installvue_type_template_id_59b3a0ea_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var misc_install = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);