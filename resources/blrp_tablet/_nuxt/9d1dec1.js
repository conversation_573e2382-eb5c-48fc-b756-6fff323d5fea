(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[140],{

/***/ 1715:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1837);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("26ac8808", content, true, {"sourceMap":false});

/***/ }),

/***/ 1836:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_calculator_vue_vue_type_style_index_0_id_9f75ad54_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1715);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_calculator_vue_vue_type_style_index_0_id_9f75ad54_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_calculator_vue_vue_type_style_index_0_id_9f75ad54_prod_lang_scss_scoped_true__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1837:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".calculator-container[data-v-9f75ad54]{display:flex;flex-direction:column;padding:0}.tabs-content[data-v-9f75ad54]{flex:1;height:calc(100vh - 48px)}.calculator-card[data-v-9f75ad54],.random-generator-card[data-v-9f75ad54]{border-radius:0!important;height:100%;width:100%}.calculator-display[data-v-9f75ad54]{background:#1a1a1a;border-bottom:1px solid #444;padding:20px;text-align:right}.display-secondary[data-v-9f75ad54]{color:#888;font-size:16px;margin-bottom:5px;min-height:20px}.display-secondary .memory-indicator[data-v-9f75ad54]{color:#ff5252;font-weight:700;margin-right:10px}.display-primary[data-v-9f75ad54]{color:#fff;font-size:32px;font-weight:300;word-break:break-all}.calculator-grid[data-v-9f75ad54]{max-width:100%}.calculator-btn[data-v-9f75ad54]{border-radius:8px!important;font-size:18px!important;font-weight:500!important}.calculator-btn.number-btn[data-v-9f75ad54]{color:#fff!important}.calculator-btn.operation-btn[data-v-9f75ad54]{color:#fff!important;font-size:20px!important}.calculator-btn.function-btn[data-v-9f75ad54]{color:#000!important}.calculator-btn.clear-btn[data-v-9f75ad54]{color:#fff!important}.calculator-btn.memory-btn[data-v-9f75ad54]{color:#fff!important;font-size:14px!important}.result-display[data-v-9f75ad54]{background:#1a1a1a!important;min-height:120px}.result-display .result-title[data-v-9f75ad54]{color:#888;font-size:14px;margin-bottom:8px}.result-display .result-content[data-v-9f75ad54]{color:#fff;font-family:monospace;font-size:16px;white-space:pre-line;word-break:break-all}.action-btn[data-v-9f75ad54]{font-size:16px!important}.action-btn[data-v-9f75ad54],.quick-btn[data-v-9f75ad54]{border-radius:8px!important;font-weight:500!important}.quick-btn[data-v-9f75ad54]{flex-direction:column!important;font-size:10px!important;text-align:center!important}.quick-btn .v-icon[data-v-9f75ad54]{margin-bottom:2px!important}@media(max-width:600px){.calculator-container[data-v-9f75ad54]{height:calc(100vh - 60px)}.calculator-card[data-v-9f75ad54],.random-generator-card[data-v-9f75ad54]{margin:0;width:100%}.display-primary[data-v-9f75ad54]{font-size:28px}.calculator-btn[data-v-9f75ad54]{font-size:16px!important;height:45px!important}.action-btn[data-v-9f75ad54]{font-size:14px!important;height:45px!important}.quick-btn[data-v-9f75ad54]{font-size:8px!important;height:35px!important}.result-display[data-v-9f75ad54]{min-height:100px}.result-display .result-content[data-v-9f75ad54]{font-size:14px}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2143:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js + 4 modules
var VSelect = __webpack_require__(375);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabItem.js + 1 modules
var VTabItem = __webpack_require__(1554);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabsItems.js + 1 modules
var VTabsItems = __webpack_require__(892);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/calculator.vue?vue&type=template&id=9f75ad54&scoped=true













var calculatorvue_type_template_id_9f75ad54_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "calculator-container"
  }, [_c(VTabs["a" /* default */], {
    attrs: {
      "dark": "",
      "height": "48"
    },
    model: {
      value: _vm.activeTab,
      callback: function callback($$v) {
        _vm.activeTab = $$v;
      },
      expression: "activeTab"
    }
  }, [_c(VTab["a" /* default */], [_vm._v("Calculator")]), _vm._v(" "), _c(VTab["a" /* default */], [_vm._v("Random Generator")])], 1), _vm._v(" "), _c(VTabsItems["a" /* default */], {
    staticClass: "tabs-content",
    model: {
      value: _vm.activeTab,
      callback: function callback($$v) {
        _vm.activeTab = $$v;
      },
      expression: "activeTab"
    }
  }, [_c(VTabItem["a" /* default */], [_c(VCard["a" /* default */], {
    staticClass: "calculator-card",
    attrs: {
      "flat": ""
    }
  }, [_c(components_VCard["b" /* VCardText */], {
    staticClass: "pa-0"
  }, [_c('div', {
    staticClass: "calculator-display"
  }, [_c('div', {
    staticClass: "display-secondary"
  }, [_vm.memory !== 0 ? _c('span', {
    staticClass: "memory-indicator"
  }, [_vm._v("M")]) : _vm._e(), _vm._v("\n          " + _vm._s(_vm.previousValue) + " " + _vm._s(_vm.operator) + "\n        ")]), _vm._v(" "), _c('div', {
    staticClass: "display-primary"
  }, [_vm._v(_vm._s(_vm.displayValue))])])]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], {
    staticClass: "pa-2"
  }, [_c(VRow["a" /* default */], {
    staticClass: "calculator-grid",
    attrs: {
      "no-gutters": ""
    }
  }, [_vm._l(_vm.memoryRow, function (btn) {
    return _c(VCol["a" /* default */], {
      key: btn.label,
      staticClass: "pa-1",
      attrs: {
        "cols": "3"
      }
    }, [_c(VBtn["a" /* default */], {
      staticClass: "calculator-btn",
      class: btn.class,
      attrs: {
        "color": btn.color,
        "height": "50",
        "block": "",
        "disabled": btn.disabled
      },
      on: {
        "click": function click($event) {
          return _vm.handleButtonClick(btn.action, btn.value);
        }
      }
    }, [_vm._v("\n            " + _vm._s(btn.label) + "\n          ")])], 1);
  }), _vm._v(" "), _vm._l(_vm.row1, function (btn) {
    return _c(VCol["a" /* default */], {
      key: btn.label,
      staticClass: "pa-1",
      attrs: {
        "cols": "3"
      }
    }, [_c(VBtn["a" /* default */], {
      staticClass: "calculator-btn",
      class: btn.class,
      attrs: {
        "color": btn.color,
        "height": "50",
        "block": ""
      },
      on: {
        "click": function click($event) {
          return _vm.handleButtonClick(btn.action, btn.value);
        }
      }
    }, [_vm._v("\n            " + _vm._s(btn.label) + "\n          ")])], 1);
  }), _vm._v(" "), _vm._l(_vm.row2, function (btn) {
    return _c(VCol["a" /* default */], {
      key: btn.label,
      staticClass: "pa-1",
      attrs: {
        "cols": "3"
      }
    }, [_c(VBtn["a" /* default */], {
      staticClass: "calculator-btn",
      class: btn.class,
      attrs: {
        "color": btn.color,
        "height": "50",
        "block": ""
      },
      on: {
        "click": function click($event) {
          return _vm.handleButtonClick(btn.action, btn.value);
        }
      }
    }, [_vm._v("\n            " + _vm._s(btn.label) + "\n          ")])], 1);
  }), _vm._v(" "), _vm._l(_vm.row3, function (btn) {
    return _c(VCol["a" /* default */], {
      key: btn.label,
      staticClass: "pa-1",
      attrs: {
        "cols": "3"
      }
    }, [_c(VBtn["a" /* default */], {
      staticClass: "calculator-btn",
      class: btn.class,
      attrs: {
        "color": btn.color,
        "height": "50",
        "block": ""
      },
      on: {
        "click": function click($event) {
          return _vm.handleButtonClick(btn.action, btn.value);
        }
      }
    }, [_vm._v("\n            " + _vm._s(btn.label) + "\n          ")])], 1);
  }), _vm._v(" "), _vm._l(_vm.row4, function (btn) {
    return _c(VCol["a" /* default */], {
      key: btn.label,
      staticClass: "pa-1",
      attrs: {
        "cols": "3"
      }
    }, [_c(VBtn["a" /* default */], {
      staticClass: "calculator-btn",
      class: btn.class,
      attrs: {
        "color": btn.color,
        "height": "50",
        "block": ""
      },
      on: {
        "click": function click($event) {
          return _vm.handleButtonClick(btn.action, btn.value);
        }
      }
    }, [_vm._v("\n            " + _vm._s(btn.label) + "\n          ")])], 1);
  }), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "pa-1",
    attrs: {
      "cols": "6"
    }
  }, [_c(VBtn["a" /* default */], {
    staticClass: "calculator-btn number-btn",
    attrs: {
      "color": "grey darken-2",
      "height": "50",
      "block": ""
    },
    on: {
      "click": function click($event) {
        return _vm.handleButtonClick('number', '0');
      }
    }
  }, [_vm._v("\n            0\n          ")])], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "pa-1",
    attrs: {
      "cols": "3"
    }
  }, [_c(VBtn["a" /* default */], {
    staticClass: "calculator-btn number-btn",
    attrs: {
      "color": "grey darken-2",
      "height": "50",
      "block": ""
    },
    on: {
      "click": function click($event) {
        return _vm.handleButtonClick('decimal', '.');
      }
    }
  }, [_vm._v("\n            .\n          ")])], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "pa-1",
    attrs: {
      "cols": "3"
    }
  }, [_c(VBtn["a" /* default */], {
    staticClass: "calculator-btn operation-btn",
    attrs: {
      "color": "red darken-1",
      "height": "50",
      "block": ""
    },
    on: {
      "click": function click($event) {
        return _vm.handleButtonClick('equals', '=');
      }
    }
  }, [_vm._v("\n            =\n          ")])], 1)], 2)], 1)], 1)], 1), _vm._v(" "), _c(VTabItem["a" /* default */], [_c(VCard["a" /* default */], {
    staticClass: "random-generator-card",
    attrs: {
      "flat": ""
    }
  }, [_c(components_VCard["b" /* VCardText */], {
    staticClass: "pa-2"
  }, [_c(VRow["a" /* default */], {
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "pa-1",
    attrs: {
      "cols": "12"
    }
  }, [_c(VSelect["a" /* default */], {
    attrs: {
      "items": _vm.randomCategories,
      "item-text": "name",
      "item-value": "key",
      "label": "Select Category",
      "dense": "",
      "outlined": "",
      "dark": ""
    },
    on: {
      "change": _vm.generateRandom
    },
    model: {
      value: _vm.selectedCategory,
      callback: function callback($$v) {
        _vm.selectedCategory = $$v;
      },
      expression: "selectedCategory"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "pa-1",
    attrs: {
      "cols": "12"
    }
  }, [_c(VCard["a" /* default */], {
    staticClass: "result-display",
    attrs: {
      "dark": ""
    }
  }, [_c(components_VCard["b" /* VCardText */], [_c('div', {
    staticClass: "result-title"
  }, [_vm._v(_vm._s(_vm.selectedCategoryName))]), _vm._v(" "), _c('div', {
    staticClass: "result-content"
  }, [_vm._v(_vm._s(_vm.generatedResult))])])], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "pa-1 mb-3",
    attrs: {
      "cols": "12"
    }
  }, [_c(VBtn["a" /* default */], {
    staticClass: "action-btn",
    attrs: {
      "color": "red darken-3",
      "height": "50",
      "block": "",
      "disabled": !_vm.selectedCategory
    },
    on: {
      "click": _vm.generateRandom
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "left": ""
    }
  }, [_vm._v("fa-solid fa-dice")]), _vm._v("\n              Generate\n            ")], 1)], 1), _vm._v(" "), _vm._l(_vm.quickCategories, function (category) {
    return _c(VCol["a" /* default */], {
      key: category.key,
      staticClass: "pa-1",
      attrs: {
        "cols": "4"
      }
    }, [_c(VBtn["a" /* default */], {
      staticClass: "quick-btn",
      attrs: {
        "color": category.color,
        "height": "45",
        "block": ""
      },
      on: {
        "click": function click($event) {
          return _vm.quickGenerate(category.key);
        }
      }
    }, [_c(VIcon["a" /* default */], {
      staticClass: "mb-1 mr-3",
      attrs: {
        "small": ""
      }
    }, [_vm._v(_vm._s(category.icon))]), _vm._v(" "), _c('br'), _vm._v(" "), _c('span', {
      staticStyle: {
        "font-size": "10px"
      }
    }, [_vm._v(_vm._s(category.name))])], 1)], 1);
  })], 2)], 1)], 1)], 1)], 1), _vm._v(" "), _vm._l(_vm.keyboardListeners, function (key) {
    return _c('span', {
      directives: [{
        name: "shortkey",
        rawName: "v-shortkey",
        value: [key.key],
        expression: "[key.key]"
      }],
      key: key.key,
      on: {
        "shortkey": function shortkey($event) {
          return _vm.handleKeyboard(key.action, key.value);
        }
      }
    });
  })], 2);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/calculator.vue?vue&type=template&id=9f75ad54&scoped=true

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.join.js
var es_array_join = __webpack_require__(126);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.sort.js
var es_array_sort = __webpack_require__(273);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.number.to-fixed.js
var es_number_to_fixed = __webpack_require__(385);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.pad-start.js
var es_string_pad_start = __webpack_require__(902);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.trim.js
var es_string_trim = __webpack_require__(103);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/calculator.vue?vue&type=script&lang=js













/* harmony default export */ var calculatorvue_type_script_lang_js = ({
  name: 'calculator',
  data: function data() {
    return {
      activeTab: 0,
      // Calculator data
      displayValue: '0',
      previousValue: '',
      operator: '',
      waitingForOperand: false,
      memory: 0,
      // Random generator data
      selectedCategory: '',
      generatedResult: 'Select a category and click Generate',
      randomCategories: [{
        key: 'creditCard',
        name: 'Credit Card Number'
      }, {
        key: 'bankAccount',
        name: 'Bank Account Number'
      }, {
        key: 'ssn',
        name: 'Social Security Number'
      }, {
        key: 'address',
        name: 'Random Address'
      }, {
        key: 'phone',
        name: 'Phone Number'
      }, {
        key: 'email',
        name: 'Email Address'
      }, {
        key: 'name',
        name: 'Full Name'
      }, {
        key: 'license',
        name: 'License Plate'
      }, {
        key: 'vin',
        name: 'Vehicle VIN'
      }, {
        key: 'uuid',
        name: 'UUID'
      }, {
        key: 'password',
        name: 'Secure Password'
      }, {
        key: 'color',
        name: 'Hex Color'
      }, {
        key: 'date',
        name: 'Random Date'
      }, {
        key: 'number',
        name: 'Random Number (1-1000)'
      }, {
        key: 'company',
        name: 'Company Name'
      }, {
        key: 'job',
        name: 'Job Title'
      }, {
        key: 'website',
        name: 'Website URL'
      }, {
        key: 'ipAddress',
        name: 'IP Address'
      }, {
        key: 'macAddress',
        name: 'MAC Address'
      }, {
        key: 'bitcoin',
        name: 'Bitcoin Address'
      }, {
        key: 'ethereum',
        name: 'Ethereum Address'
      }, {
        key: 'isbn',
        name: 'ISBN Number'
      }, {
        key: 'imei',
        name: 'IMEI Number'
      }, {
        key: 'coordinates',
        name: 'GPS Coordinates'
      }, {
        key: 'bloodType',
        name: 'Blood Type'
      }, {
        key: 'zodiac',
        name: 'Zodiac Sign'
      }, {
        key: 'userAgent',
        name: 'User Agent String'
      }, {
        key: 'lorem',
        name: 'Lorem Ipsum Text'
      }, {
        key: 'quote',
        name: 'Random Quote'
      }, {
        key: 'fact',
        name: 'Random Fact'
      }],
      quickCategories: [{
        key: 'creditCard',
        name: 'Card',
        icon: 'fa-solid fa-credit-card',
        color: 'blue darken-1'
      }, {
        key: 'phone',
        name: 'Phone',
        icon: 'fa-solid fa-phone',
        color: 'green darken-1'
      }, {
        key: 'address',
        name: 'Address',
        icon: 'fa-solid fa-home',
        color: 'orange darken-1'
      }, {
        key: 'name',
        name: 'Name',
        icon: 'fa-solid fa-user',
        color: 'purple darken-1'
      }, {
        key: 'password',
        name: 'Password',
        icon: 'fa-solid fa-lock',
        color: 'red darken-2'
      }, {
        key: 'email',
        name: 'Email',
        icon: 'fa-solid fa-envelope',
        color: 'teal darken-1'
      }, {
        key: 'company',
        name: 'Company',
        icon: 'fa-solid fa-building',
        color: 'indigo darken-1'
      }, {
        key: 'ipAddress',
        name: 'IP',
        icon: 'fa-solid fa-network-wired',
        color: 'cyan darken-1'
      }, {
        key: 'coordinates',
        name: 'GPS',
        icon: 'fa-solid fa-map-marker-alt',
        color: 'amber darken-1'
      }],
      // Button configurations
      memoryRow: [{
        label: 'MC',
        action: 'memoryClear',
        value: '',
        color: 'grey darken-3',
        class: 'memory-btn',
        disabled: false
      }, {
        label: 'MR',
        action: 'memoryRecall',
        value: '',
        color: 'grey darken-3',
        class: 'memory-btn',
        disabled: false
      }, {
        label: 'M+',
        action: 'memoryAdd',
        value: '',
        color: 'grey darken-3',
        class: 'memory-btn',
        disabled: false
      }, {
        label: 'M-',
        action: 'memorySubtract',
        value: '',
        color: 'grey darken-3',
        class: 'memory-btn',
        disabled: false
      }],
      row1: [{
        label: 'C',
        action: 'clear',
        value: '',
        color: 'red darken-2',
        class: 'clear-btn'
      }, {
        label: '±',
        action: 'toggleSign',
        value: '',
        color: 'grey darken-1',
        class: 'function-btn'
      }, {
        label: '%',
        action: 'percent',
        value: '',
        color: 'grey darken-1',
        class: 'function-btn'
      }, {
        label: '÷',
        action: 'operation',
        value: '/',
        color: 'red darken-1',
        class: 'operation-btn'
      }],
      row2: [{
        label: '7',
        action: 'number',
        value: '7',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '8',
        action: 'number',
        value: '8',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '9',
        action: 'number',
        value: '9',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '×',
        action: 'operation',
        value: '*',
        color: 'red darken-1',
        class: 'operation-btn'
      }],
      row3: [{
        label: '4',
        action: 'number',
        value: '4',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '5',
        action: 'number',
        value: '5',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '6',
        action: 'number',
        value: '6',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '−',
        action: 'operation',
        value: '-',
        color: 'red darken-1',
        class: 'operation-btn'
      }],
      row4: [{
        label: '1',
        action: 'number',
        value: '1',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '2',
        action: 'number',
        value: '2',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '3',
        action: 'number',
        value: '3',
        color: 'grey darken-2',
        class: 'number-btn'
      }, {
        label: '+',
        action: 'operation',
        value: '+',
        color: 'red darken-1',
        class: 'operation-btn'
      }],
      // Keyboard mappings
      keyboardListeners: [
      // Regular number keys
      {
        key: '0',
        action: 'number',
        value: '0'
      }, {
        key: '1',
        action: 'number',
        value: '1'
      }, {
        key: '2',
        action: 'number',
        value: '2'
      }, {
        key: '3',
        action: 'number',
        value: '3'
      }, {
        key: '4',
        action: 'number',
        value: '4'
      }, {
        key: '5',
        action: 'number',
        value: '5'
      }, {
        key: '6',
        action: 'number',
        value: '6'
      }, {
        key: '7',
        action: 'number',
        value: '7'
      }, {
        key: '8',
        action: 'number',
        value: '8'
      }, {
        key: '9',
        action: 'number',
        value: '9'
      },
      // Numpad keys
      {
        key: 'numpad0',
        action: 'number',
        value: '0'
      }, {
        key: 'numpad1',
        action: 'number',
        value: '1'
      }, {
        key: 'numpad2',
        action: 'number',
        value: '2'
      }, {
        key: 'numpad3',
        action: 'number',
        value: '3'
      }, {
        key: 'numpad4',
        action: 'number',
        value: '4'
      }, {
        key: 'numpad5',
        action: 'number',
        value: '5'
      }, {
        key: 'numpad6',
        action: 'number',
        value: '6'
      }, {
        key: 'numpad7',
        action: 'number',
        value: '7'
      }, {
        key: 'numpad8',
        action: 'number',
        value: '8'
      }, {
        key: 'numpad9',
        action: 'number',
        value: '9'
      },
      // Operation keys
      {
        key: '+',
        action: 'operation',
        value: '+'
      }, {
        key: '-',
        action: 'operation',
        value: '-'
      }, {
        key: '*',
        action: 'operation',
        value: '*'
      }, {
        key: '/',
        action: 'operation',
        value: '/'
      },
      // Numpad operation keys
      {
        key: 'numpadadd',
        action: 'operation',
        value: '+'
      }, {
        key: 'numpadsubtract',
        action: 'operation',
        value: '-'
      }, {
        key: 'numpadmultiply',
        action: 'operation',
        value: '*'
      }, {
        key: 'numpaddivide',
        action: 'operation',
        value: '/'
      },
      // Equals and decimal
      {
        key: 'enter',
        action: 'equals',
        value: '='
      }, {
        key: 'numpadenter',
        action: 'equals',
        value: '='
      }, {
        key: '=',
        action: 'equals',
        value: '='
      }, {
        key: '.',
        action: 'decimal',
        value: '.'
      }, {
        key: 'numpaddecimal',
        action: 'decimal',
        value: '.'
      },
      // Control keys
      {
        key: 'escape',
        action: 'clear',
        value: ''
      }, {
        key: 'backspace',
        action: 'backspace',
        value: ''
      }, {
        key: 'delete',
        action: 'clear',
        value: ''
      }]
    };
  },
  computed: {
    selectedCategoryName: function selectedCategoryName() {
      var _this = this;
      var category = this.randomCategories.find(function (c) {
        return c.key === _this.selectedCategory;
      });
      return category ? category.name : 'Random Generator';
    }
  },
  mounted: function mounted() {
    // Add native keyboard event listener for better numpad support
    document.addEventListener('keydown', this.handleNativeKeyboard);
  },
  beforeDestroy: function beforeDestroy() {
    // Clean up event listener
    document.removeEventListener('keydown', this.handleNativeKeyboard);
  },
  methods: {
    handleNativeKeyboard: function handleNativeKeyboard(event) {
      // Prevent default behavior for calculator keys
      var key = event.key;
      var code = event.code;

      // Map keyboard events to calculator actions
      var action = null;
      var value = null;

      // Numbers (both regular and numpad)
      if (/^[0-9]$/.test(key) || /^Numpad[0-9]$/.test(code)) {
        action = 'number';
        value = /^Numpad[0-9]$/.test(code) ? code.slice(-1) : key;
        event.preventDefault();
      }
      // Operations
      else if (key === '+' || code === 'NumpadAdd') {
        action = 'operation';
        value = '+';
        event.preventDefault();
      } else if (key === '-' || code === 'NumpadSubtract') {
        action = 'operation';
        value = '-';
        event.preventDefault();
      } else if (key === '*' || code === 'NumpadMultiply') {
        action = 'operation';
        value = '*';
        event.preventDefault();
      } else if (key === '/' || code === 'NumpadDivide') {
        action = 'operation';
        value = '/';
        event.preventDefault();
      }
      // Decimal
      else if (key === '.' || code === 'NumpadDecimal') {
        action = 'decimal';
        value = '.';
        event.preventDefault();
      }
      // Equals
      else if (key === 'Enter' || code === 'NumpadEnter' || key === '=') {
        action = 'equals';
        value = '=';
        event.preventDefault();
      }
      // Clear
      else if (key === 'Escape' || key === 'Delete') {
        action = 'clear';
        value = '';
        event.preventDefault();
      }
      // Backspace
      else if (key === 'Backspace') {
        action = 'backspace';
        value = '';
        event.preventDefault();
      }

      // Perform the action if one was determined
      if (action) {
        this.performAction(action, value);
      }
    },
    handleButtonClick: function handleButtonClick(action, value) {
      this.performAction(action, value);
    },
    handleKeyboard: function handleKeyboard(action, value) {
      this.performAction(action, value);
    },
    performAction: function performAction(action, value) {
      switch (action) {
        case 'number':
          this.inputNumber(value);
          break;
        case 'operation':
          this.inputOperation(value);
          break;
        case 'equals':
          this.calculate();
          break;
        case 'decimal':
          this.inputDecimal();
          break;
        case 'clear':
          this.clear();
          break;
        case 'backspace':
          this.backspace();
          break;
        case 'toggleSign':
          this.toggleSign();
          break;
        case 'percent':
          this.percent();
          break;
        case 'memoryClear':
          this.memoryClear();
          break;
        case 'memoryRecall':
          this.memoryRecall();
          break;
        case 'memoryAdd':
          this.memoryAdd();
          break;
        case 'memorySubtract':
          this.memorySubtract();
          break;
      }
    },
    inputNumber: function inputNumber(num) {
      if (this.waitingForOperand) {
        this.displayValue = num;
        this.waitingForOperand = false;
      } else {
        // Limit display length to prevent overflow
        if (this.displayValue.length < 12) {
          this.displayValue = this.displayValue === '0' ? num : this.displayValue + num;
        }
      }
    },
    inputOperation: function inputOperation(nextOperator) {
      // Clear error state
      if (this.displayValue === 'Error') {
        this.clear();
        return;
      }
      var inputValue = parseFloat(this.displayValue);
      if (this.previousValue === '') {
        this.previousValue = inputValue;
      } else if (this.operator) {
        var currentValue = this.previousValue || 0;
        var newValue = this.performCalculation(currentValue, inputValue, this.operator);
        if (newValue === 'Error') {
          this.displayValue = 'Error';
          this.previousValue = '';
          this.operator = '';
          this.waitingForOperand = true;
          return;
        }
        this.displayValue = String(newValue);
        this.previousValue = newValue;
      }
      this.waitingForOperand = true;
      this.operator = nextOperator;
    },
    calculate: function calculate() {
      var inputValue = parseFloat(this.displayValue);
      if (this.previousValue !== '' && this.operator) {
        var currentValue = this.previousValue || 0;
        var newValue = this.performCalculation(currentValue, inputValue, this.operator);
        if (newValue === 'Error') {
          this.displayValue = 'Error';
          this.previousValue = '';
          this.operator = '';
          this.waitingForOperand = true;
        } else {
          this.displayValue = String(newValue);
          this.previousValue = '';
          this.operator = '';
          this.waitingForOperand = true;
        }
      }
    },
    performCalculation: function performCalculation(firstValue, secondValue, operator) {
      var result;
      switch (operator) {
        case '+':
          result = firstValue + secondValue;
          break;
        case '-':
          result = firstValue - secondValue;
          break;
        case '*':
          result = firstValue * secondValue;
          break;
        case '/':
          if (secondValue === 0) {
            return 'Error';
          }
          result = firstValue / secondValue;
          break;
        default:
          result = secondValue;
      }

      // Handle very large or very small numbers
      if (!isFinite(result)) {
        return 'Error';
      }

      // Round to prevent floating point precision issues
      if (result % 1 !== 0) {
        result = parseFloat(result.toFixed(10));
      }
      return result;
    },
    inputDecimal: function inputDecimal() {
      if (this.waitingForOperand) {
        this.displayValue = '0.';
        this.waitingForOperand = false;
      } else if (this.displayValue.indexOf('.') === -1) {
        this.displayValue += '.';
      }
    },
    clear: function clear() {
      this.displayValue = '0';
      this.previousValue = '';
      this.operator = '';
      this.waitingForOperand = false;
    },
    backspace: function backspace() {
      if (this.displayValue.length > 1) {
        this.displayValue = this.displayValue.slice(0, -1);
      } else {
        this.displayValue = '0';
      }
    },
    toggleSign: function toggleSign() {
      if (this.displayValue !== '0') {
        this.displayValue = this.displayValue.charAt(0) === '-' ? this.displayValue.substr(1) : '-' + this.displayValue;
      }
    },
    percent: function percent() {
      var value = parseFloat(this.displayValue);
      this.displayValue = String(value / 100);
    },
    memoryClear: function memoryClear() {
      this.memory = 0;
    },
    memoryRecall: function memoryRecall() {
      this.displayValue = String(this.memory);
      this.waitingForOperand = true;
    },
    memoryAdd: function memoryAdd() {
      this.memory += parseFloat(this.displayValue);
    },
    memorySubtract: function memorySubtract() {
      this.memory -= parseFloat(this.displayValue);
    },
    // Random Generator Methods
    generateRandom: function generateRandom() {
      if (!this.selectedCategory) return;
      switch (this.selectedCategory) {
        case 'creditCard':
          this.generatedResult = this.generateCreditCard();
          break;
        case 'bankAccount':
          this.generatedResult = this.generateBankAccount();
          break;
        case 'ssn':
          this.generatedResult = this.generateSSN();
          break;
        case 'address':
          this.generatedResult = this.generateAddress();
          break;
        case 'phone':
          this.generatedResult = this.generatePhone();
          break;
        case 'email':
          this.generatedResult = this.generateEmail();
          break;
        case 'name':
          this.generatedResult = this.generateName();
          break;
        case 'license':
          this.generatedResult = this.generateLicense();
          break;
        case 'vin':
          this.generatedResult = this.generateVIN();
          break;
        case 'uuid':
          this.generatedResult = this.generateUUID();
          break;
        case 'password':
          this.generatedResult = this.generatePassword();
          break;
        case 'color':
          this.generatedResult = this.generateColor();
          break;
        case 'date':
          this.generatedResult = this.generateDate();
          break;
        case 'number':
          this.generatedResult = this.generateNumber();
          break;
        case 'company':
          this.generatedResult = this.generateCompany();
          break;
        case 'job':
          this.generatedResult = this.generateJob();
          break;
        case 'website':
          this.generatedResult = this.generateWebsite();
          break;
        case 'ipAddress':
          this.generatedResult = this.generateIPAddress();
          break;
        case 'macAddress':
          this.generatedResult = this.generateMACAddress();
          break;
        case 'bitcoin':
          this.generatedResult = this.generateBitcoinAddress();
          break;
        case 'ethereum':
          this.generatedResult = this.generateEthereumAddress();
          break;
        case 'isbn':
          this.generatedResult = this.generateISBN();
          break;
        case 'imei':
          this.generatedResult = this.generateIMEI();
          break;
        case 'coordinates':
          this.generatedResult = this.generateCoordinates();
          break;
        case 'bloodType':
          this.generatedResult = this.generateBloodType();
          break;
        case 'zodiac':
          this.generatedResult = this.generateZodiac();
          break;
        case 'userAgent':
          this.generatedResult = this.generateUserAgent();
          break;
        case 'lorem':
          this.generatedResult = this.generateLorem();
          break;
        case 'quote':
          this.generatedResult = this.generateQuote();
          break;
        case 'fact':
          this.generatedResult = this.generateFact();
          break;
        default:
          this.generatedResult = 'Unknown category';
      }
    },
    quickGenerate: function quickGenerate(category) {
      this.selectedCategory = category;
      this.generateRandom();
    },
    copyToClipboard: function copyToClipboard() {
      var _this2 = this;
      if (this.generatedResult && this.generatedResult !== 'Select a category and click Generate') {
        navigator.clipboard.writeText(this.generatedResult).then(function () {
          _this2.showToast('Copied to clipboard!');
        }).catch(function () {
          // Fallback for older browsers
          var textArea = document.createElement('textarea');
          textArea.value = _this2.generatedResult;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          _this2.showToast('Copied to clipboard!');
        });
      }
    },
    showToast: function showToast(message) {
      // Try to use toast if available, otherwise use alert
      if (this.$toast && this.$toast.success) {
        this.$toast.success(message);
      } else {
        alert(message);
      }
    },
    // Individual generation methods
    generateCreditCard: function generateCreditCard() {
      // Generate a valid-looking credit card number (not real)
      var prefixes = ['4', '5', '37', '6'];
      var prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
      var number = prefix;
      var targetLength = prefix === '37' ? 15 : 16;
      while (number.length < targetLength - 1) {
        number += Math.floor(Math.random() * 10);
      }

      // Add Luhn check digit
      var sum = 0;
      for (var i = 0; i < number.length; i++) {
        var digit = parseInt(number[i]);
        if (i % 2 === 0) {
          digit *= 2;
          if (digit > 9) digit -= 9;
        }
        sum += digit;
      }
      var checkDigit = (10 - sum % 10) % 10;
      number += checkDigit;
      return number.replace(/(.{4})/g, '$1 ').trim();
    },
    generateBankAccount: function generateBankAccount() {
      // Generate routing number (9 digits) and account number (10-12 digits)
      var routing = Math.floor(********* + Math.random() * *********);
      var accountLength = 10 + Math.floor(Math.random() * 3);
      var account = '';
      for (var i = 0; i < accountLength; i++) {
        account += Math.floor(Math.random() * 10);
      }
      return "Routing: ".concat(routing, "\nAccount: ").concat(account);
    },
    generateSSN: function generateSSN() {
      var area = Math.floor(100 + Math.random() * 900);
      var group = Math.floor(10 + Math.random() * 90);
      var serial = Math.floor(1000 + Math.random() * 9000);
      return "".concat(area, "-").concat(group, "-").concat(serial);
    },
    generateAddress: function generateAddress() {
      var streets = ['Main St', 'Oak Ave', 'Pine Rd', 'Elm Dr', 'Cedar Ln', 'Maple Way', 'Park Blvd', 'First St', 'Second Ave', 'Third St'];
      var cities = ['Los Santos', 'San Fierro', 'Las Venturas', 'Paleto Bay', 'Sandy Shores', 'Grapeseed', 'Harmony', 'Desert'];
      var states = ['SA', 'SF', 'LV', 'BC', 'RC'];
      var number = Math.floor(1 + Math.random() * 9999);
      var street = streets[Math.floor(Math.random() * streets.length)];
      var city = cities[Math.floor(Math.random() * cities.length)];
      var state = states[Math.floor(Math.random() * states.length)];
      var zip = Math.floor(10000 + Math.random() * 90000);
      return "".concat(number, " ").concat(street, "\n").concat(city, ", ").concat(state, " ").concat(zip);
    },
    generatePhone: function generatePhone() {
      var area = Math.floor(200 + Math.random() * 800);
      var exchange = Math.floor(200 + Math.random() * 800);
      var number = Math.floor(1000 + Math.random() * 9000);
      return "(".concat(area, ") ").concat(exchange, "-").concat(number);
    },
    generateEmail: function generateEmail() {
      var firstNames = ['john', 'jane', 'mike', 'sarah', 'david', 'lisa', 'chris', 'amy', 'tom', 'kate'];
      var lastNames = ['smith', 'johnson', 'brown', 'davis', 'miller', 'wilson', 'moore', 'taylor', 'anderson', 'thomas'];
      var domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com', 'icloud.com'];
      var firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      var lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      var domain = domains[Math.floor(Math.random() * domains.length)];
      var number = Math.floor(Math.random() * 1000);
      return "".concat(firstName, ".").concat(lastName).concat(number, "@").concat(domain);
    },
    generateName: function generateName() {
      var firstNames = ['John', 'Jane', 'Michael', 'Sarah', 'David', 'Lisa', 'Christopher', 'Amy', 'Thomas', 'Kate', 'Robert', 'Emily', 'James', 'Jessica', 'William'];
      var lastNames = ['Smith', 'Johnson', 'Brown', 'Davis', 'Miller', 'Wilson', 'Moore', 'Taylor', 'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Martin', 'Thompson'];
      var firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      var lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      return "".concat(firstName, " ").concat(lastName);
    },
    generateLicense: function generateLicense() {
      var letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      var numbers = '**********';
      var plate = '';
      // Format: ABC 1234
      for (var i = 0; i < 3; i++) {
        plate += letters[Math.floor(Math.random() * letters.length)];
      }
      plate += ' ';
      for (var _i = 0; _i < 4; _i++) {
        plate += numbers[Math.floor(Math.random() * numbers.length)];
      }
      return plate;
    },
    generateVIN: function generateVIN() {
      var chars = 'ABCDEFGHJKLMNPRSTUVWXYZ**********';
      var vin = '';
      for (var i = 0; i < 17; i++) {
        vin += chars[Math.floor(Math.random() * chars.length)];
      }
      return vin;
    },
    generateUUID: function generateUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0;
        var v = c === 'x' ? r : r & 0x3 | 0x8;
        return v.toString(16);
      });
    },
    generatePassword: function generatePassword() {
      var lowercase = 'abcdefghijklmnopqrstuvwxyz';
      var uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      var numbers = '**********';
      var symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
      var allChars = lowercase + uppercase + numbers + symbols;
      var password = '';
      // Ensure at least one of each type
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      password += symbols[Math.floor(Math.random() * symbols.length)];

      // Fill the rest randomly (12 characters total)
      for (var i = 4; i < 12; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }

      // Shuffle the password
      return password.split('').sort(function () {
        return Math.random() - 0.5;
      }).join('');
    },
    generateColor: function generateColor() {
      var hex = Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0');
      return "#".concat(hex.toUpperCase());
    },
    generateDate: function generateDate() {
      var start = new Date(1950, 0, 1);
      var end = new Date(2030, 11, 31);
      var randomDate = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
      return randomDate.toLocaleDateString();
    },
    generateNumber: function generateNumber() {
      return Math.floor(1 + Math.random() * 1000).toString();
    },
    generateCompany: function generateCompany() {
      var prefixes = ['Global', 'United', 'Advanced', 'Premier', 'Elite', 'Dynamic', 'Innovative', 'Strategic', 'Digital', 'Smart'];
      var types = ['Solutions', 'Systems', 'Technologies', 'Industries', 'Enterprises', 'Corporation', 'Group', 'Holdings', 'Partners', 'Associates'];
      var suffixes = ['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Co.', '& Associates', 'International', 'Worldwide'];
      var prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
      var type = types[Math.floor(Math.random() * types.length)];
      var suffix = suffixes[Math.floor(Math.random() * suffixes.length)];
      return "".concat(prefix, " ").concat(type, " ").concat(suffix);
    },
    generateJob: function generateJob() {
      var jobs = ['Software Engineer', 'Data Scientist', 'Product Manager', 'Marketing Director', 'Sales Representative', 'Financial Analyst', 'Human Resources Manager', 'Operations Manager', 'Business Analyst', 'Project Manager', 'Graphic Designer', 'Content Writer', 'Customer Service Representative', 'Account Executive', 'Research Scientist', 'Quality Assurance Engineer', 'Network Administrator', 'Database Administrator', 'Security Analyst', 'DevOps Engineer', 'UX/UI Designer', 'Social Media Manager', 'Legal Counsel', 'Procurement Specialist', 'Training Coordinator'];
      return jobs[Math.floor(Math.random() * jobs.length)];
    },
    generateWebsite: function generateWebsite() {
      var domains = ['example', 'demo', 'test', 'sample', 'placeholder', 'mock', 'fake', 'dummy'];
      var tlds = ['.com', '.org', '.net', '.io', '.co', '.app', '.dev', '.tech'];
      var domain = domains[Math.floor(Math.random() * domains.length)];
      var tld = tlds[Math.floor(Math.random() * tlds.length)];
      var subdomain = Math.random() > 0.5 ? 'www.' : '';
      return "https://".concat(subdomain).concat(domain).concat(Math.floor(Math.random() * 1000)).concat(tld);
    },
    generateIPAddress: function generateIPAddress() {
      var octets = [];
      for (var i = 0; i < 4; i++) {
        octets.push(Math.floor(Math.random() * 256));
      }
      return octets.join('.');
    },
    generateMACAddress: function generateMACAddress() {
      var hex = '**********ABCDEF';
      var mac = '';
      for (var i = 0; i < 6; i++) {
        if (i > 0) mac += ':';
        mac += hex[Math.floor(Math.random() * 16)];
        mac += hex[Math.floor(Math.random() * 16)];
      }
      return mac;
    },
    generateBitcoinAddress: function generateBitcoinAddress() {
      var chars = '**********************************************************';
      var address = Math.random() > 0.5 ? '1' : '3'; // Legacy or P2SH
      for (var i = 1; i < 34; i++) {
        address += chars[Math.floor(Math.random() * chars.length)];
      }
      return address;
    },
    generateEthereumAddress: function generateEthereumAddress() {
      var hex = '**********abcdef';
      var address = '0x';
      for (var i = 0; i < 40; i++) {
        address += hex[Math.floor(Math.random() * 16)];
      }
      return address;
    },
    generateISBN: function generateISBN() {
      // Generate ISBN-13
      var isbn = '978';
      for (var i = 0; i < 9; i++) {
        isbn += Math.floor(Math.random() * 10);
      }

      // Calculate check digit
      var sum = 0;
      for (var _i2 = 0; _i2 < 12; _i2++) {
        sum += parseInt(isbn[_i2]) * (_i2 % 2 === 0 ? 1 : 3);
      }
      var checkDigit = (10 - sum % 10) % 10;
      isbn += checkDigit;
      return isbn.replace(/(.{3})(.{1})(.{5})(.{3})(.{1})/, '$1-$2-$3-$4-$5');
    },
    generateIMEI: function generateIMEI() {
      // Generate 14 digits, then calculate Luhn check digit
      var imei = '';
      for (var i = 0; i < 14; i++) {
        imei += Math.floor(Math.random() * 10);
      }

      // Calculate Luhn check digit
      var sum = 0;
      for (var _i3 = 0; _i3 < 14; _i3++) {
        var digit = parseInt(imei[_i3]);
        if (_i3 % 2 === 1) {
          digit *= 2;
          if (digit > 9) digit -= 9;
        }
        sum += digit;
      }
      var checkDigit = (10 - sum % 10) % 10;
      imei += checkDigit;
      return imei;
    },
    generateCoordinates: function generateCoordinates() {
      // Generate coordinates around Los Santos area
      var lat = (33.5 + Math.random() * 1.5).toFixed(6); // 33.5 to 35.0
      var lng = (-119.0 + Math.random() * 1.5).toFixed(6); // -119.0 to -117.5
      return "".concat(lat, ", ").concat(lng);
    },
    generateBloodType: function generateBloodType() {
      var types = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
      return types[Math.floor(Math.random() * types.length)];
    },
    generateZodiac: function generateZodiac() {
      var signs = ['Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo', 'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'];
      return signs[Math.floor(Math.random() * signs.length)];
    },
    generateUserAgent: function generateUserAgent() {
      var browsers = ['Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'];
      return browsers[Math.floor(Math.random() * browsers.length)];
    },
    generateLorem: function generateLorem() {
      var words = ['lorem', 'ipsum', 'dolor', 'sit', 'amet', 'consectetur', 'adipiscing', 'elit', 'sed', 'do', 'eiusmod', 'tempor', 'incididunt', 'ut', 'labore', 'et', 'dolore', 'magna', 'aliqua', 'enim', 'ad', 'minim', 'veniam', 'quis', 'nostrud', 'exercitation', 'ullamco', 'laboris', 'nisi', 'aliquip', 'ex', 'ea', 'commodo'];
      var sentenceLength = 8 + Math.floor(Math.random() * 12);
      var sentence = '';
      for (var i = 0; i < sentenceLength; i++) {
        if (i > 0) sentence += ' ';
        var word = words[Math.floor(Math.random() * words.length)];
        sentence += i === 0 ? word.charAt(0).toUpperCase() + word.slice(1) : word;
      }
      return sentence + '.';
    },
    generateQuote: function generateQuote() {
      var quotes = ["The only way to do great work is to love what you do. - Steve Jobs", "Innovation distinguishes between a leader and a follower. - Steve Jobs", "Life is what happens to you while you're busy making other plans. - John Lennon", "The future belongs to those who believe in the beauty of their dreams. - Eleanor Roosevelt", "It is during our darkest moments that we must focus to see the light. - Aristotle", "Success is not final, failure is not fatal: it is the courage to continue that counts. - Winston Churchill", "The only impossible journey is the one you never begin. - Tony Robbins", "In the middle of difficulty lies opportunity. - Albert Einstein", "Believe you can and you're halfway there. - Theodore Roosevelt", "The best time to plant a tree was 20 years ago. The second best time is now. - Chinese Proverb"];
      return quotes[Math.floor(Math.random() * quotes.length)];
    },
    generateFact: function generateFact() {
      var facts = ["Honey never spoils. Archaeologists have found pots of honey in ancient Egyptian tombs that are over 3,000 years old and still edible.", "A group of flamingos is called a 'flamboyance'.", "Octopuses have three hearts and blue blood.", "Bananas are berries, but strawberries aren't.", "A shrimp's heart is in its head.", "Wombat poop is cube-shaped.", "The shortest war in history lasted only 38-45 minutes between Britain and Zanzibar in 1896.", "A cloud can weigh more than a million pounds.", "There are more possible games of chess than there are atoms in the observable universe.", "Dolphins have names for each other.", "The human brain uses about 20% of the body's total energy.", "A day on Venus is longer than its year.", "Sharks have been around longer than trees.", "The Great Wall of China isn't visible from space with the naked eye.", "Cleopatra lived closer in time to the Moon landing than to the construction of the Great Pyramid of Giza."];
      return facts[Math.floor(Math.random() * facts.length)];
    }
  }
});
// CONCATENATED MODULE: ./pages/calculator.vue?vue&type=script&lang=js
 /* harmony default export */ var pages_calculatorvue_type_script_lang_js = (calculatorvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/calculator.vue?vue&type=style&index=0&id=9f75ad54&prod&lang=scss&scoped=true
var calculatorvue_type_style_index_0_id_9f75ad54_prod_lang_scss_scoped_true = __webpack_require__(1836);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/calculator.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_calculatorvue_type_script_lang_js,
  calculatorvue_type_template_id_9f75ad54_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "9f75ad54",
  null
  
)

/* harmony default export */ var calculator = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);