(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[160],{

/***/ 1750:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2000);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("302891b4", content, true, {"sourceMap":false});

/***/ }),

/***/ 1751:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2002);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("779e3115", content, true, {"sourceMap":false});

/***/ }),

/***/ 1999:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_layout_application_item_vue_vue_type_style_index_0_id_59eadf94_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1750);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_layout_application_item_vue_vue_type_style_index_0_id_59eadf94_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_layout_application_item_vue_vue_type_style_index_0_id_59eadf94_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 2000:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".app-icon-image{border-radius:var(--tablet-applications-border-radius);display:block;height:100%;-o-object-fit:contain;object-fit:contain;transform:scale(1.1);transition:all .2s ease;width:100%}.icon-menu-item:hover .app-icon-image{transform:scale(1.15)}.icon-menu-item.selected .app-icon-image{transform:scale(1.2)}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2001:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_fa3e2028_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1751);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_fa3e2028_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_fa3e2028_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 2002:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".scroll-container{scrollbar-width:none;-ms-overflow-style:none}.scroll-container::-webkit-scrollbar{display:none}.applications-wrapper{box-sizing:border-box;display:flex;height:100%;justify-content:center;padding:15px!important;width:100%}.applications-grid{align-content:start;align-items:start;display:grid;height:-moz-fit-content;height:fit-content;justify-items:center;max-width:1200px;width:100%;grid-gap:var(--tablet-applications-margin);gap:var(--tablet-applications-margin);justify-content:center}.applications-grid>div{justify-self:center;width:var(--tablet-applications-width)}.tablet-phone .applications-wrapper{max-height:calc(100vh - 80px);overflow-y:auto;padding:15px 0;scrollbar-width:none;-ms-overflow-style:none}.tablet-phone .applications-wrapper::-webkit-scrollbar{display:none}.tablet-phone .applications-grid{grid-template-columns:repeat(4,1fr);max-width:100%;padding:0 calc(var(--tablet-applications-margin)/2)}.tablet:not(.tablet-phone) .applications-wrapper{max-height:calc(var(--tablet-height) - 120px);overflow-y:auto;padding:20px;scrollbar-width:none;-ms-overflow-style:none}.tablet:not(.tablet-phone) .applications-wrapper::-webkit-scrollbar{display:none}.tablet:not(.tablet-phone) .applications-grid{grid-template-columns:repeat(6,1fr);max-width:calc(var(--tablet-width) - 40px)}.icon-menu-item-outer:before{box-shadow:inset 0 0 2000px hsla(0,0%,100%,.5);filter:blur(10px)}.icon-menu-item-outer{cursor:pointer;margin:var(--tablet-applications-margin);padding-bottom:calc(var(--tablet-applications-padding) - 3px);transition:all .2s}.icon-menu-item-outer:hover{transform:scale(1.05)}.icon-menu-item{align-self:center;border-radius:10px;box-shadow:0 5px 15px 0 rgba(0,0,0,.15);cursor:pointer;display:flex;height:var(--tablet-applications-height);justify-content:center;justify-items:center;justify-self:center;overflow:hidden;position:relative;transition:all .2s;width:var(--tablet-applications-width)}.icon-menu-item i{align-items:center;display:flex;font-size:var(--tablet-applications-icon-size);margin-top:5px}.icon-menu-item.selected{border:3px solid var(--person-color);box-shadow:0 0 15px hsla(0,0%,100%,.3)}.icon-menu-item:hover{box-shadow:0 8px 20px 0 rgba(0,0,0,.25);transform:scale(1.02)}.icon-menu-item:hover i{font-size:calc(var(--tablet-applications-icon-size) + 5px)}.menu-block-item{align-self:center;background-color:#181818;border:5px solid #2c2c2c;border-radius:var(--tablet-applications-border-radius);box-shadow:0 5px 15px 0 rgba(0,0,0,.15);display:flex;filter:grayscale(100%);height:var(--tablet-applications-height);justify-content:center;justify-items:center;justify-self:center;margin:var(--tablet-applications-margin);padding:var(--tablet-applications-padding);width:var(--tablet-applications-width)}.menu-block-item.selected,.menu-block-item:hover{background-color:#111;border:5px solid var(--person-color);filter:none}.menu-block-item img{display:flex;justify-content:center;justify-items:center;justify-self:center;text-align:center;width:var(--tablet-applications-image-height)}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2032:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(78);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/index.vue?vue&type=template&id=fa3e2028



var lib_vue_loader_options_pagesvue_type_template_id_fa3e2028_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(transitions["g" /* VScrollYTransition */], [!_vm.settingUp ? _c('div', [_vm.mode === 'phone' ? _c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: ['arrowleft'],
      expression: "['arrowleft']"
    }],
    on: {
      "shortkey": _vm._arrowLeft
    }
  }) : _vm._e(), _vm._v(" "), _vm.mode === 'phone' ? _c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: ['arrowright'],
      expression: "['arrowright']"
    }],
    on: {
      "shortkey": _vm._arrowRight
    }
  }) : _vm._e(), _vm._v(" "), _vm.mode === 'phone' ? _c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: ['arrowup'],
      expression: "['arrowup']"
    }],
    on: {
      "shortkey": _vm._arrowUp
    }
  }) : _vm._e(), _vm._v(" "), _vm.mode === 'phone' ? _c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: ['arrowdown'],
      expression: "['arrowdown']"
    }],
    on: {
      "shortkey": _vm._arrowDown
    }
  }) : _vm._e(), _vm._v(" "), _vm.mode === 'phone' ? _c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: ['enter'],
      expression: "['enter']"
    }],
    on: {
      "shortkey": _vm._enter
    }
  }) : _vm._e(), _vm._v(" "), _c('div', {
    staticClass: "applications-wrapper"
  }, [_c('div', {
    staticClass: "applications-grid"
  }, _vm._l(_vm.moreFiltered, function (item, index) {
    return item.name !== 'Home Screen' ? _c('div', {
      key: item.name
    }, [_c('layout-application-item', {
      attrs: {
        "current-index": _vm.currentIndex,
        "item-index": index,
        "item": item
      },
      on: {
        "input": function input($event) {
          return _vm.changeApplication(item);
        }
      }
    })], 1) : _vm._e();
  }), 0)])]) : _vm._e()]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/index.vue?vue&type=template&id=fa3e2028

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./config/applications.js
var applications = __webpack_require__(128);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBadge/VBadge.js
var VBadge = __webpack_require__(1525);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Layout/layout-application-item.vue?vue&type=template&id=59eadf94





var layout_application_itemvue_type_template_id_59eadf94_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VTooltip["a" /* default */], {
    attrs: {
      "bottom": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref) {
        var on = _ref.on,
          attrs = _ref.attrs;
        return [_c(VBadge["a" /* default */], {
          attrs: {
            "value": _vm.item.notifications > 0,
            "color": "red",
            "offset-y": "26",
            "offset-x": "25",
            "content": _vm.item.notifications
          }
        }, [[_c('div', {
          staticClass: "icon-menu-item-outer",
          on: {
            "mouseenter": function mouseenter($event) {
              _vm.hovering = true;
            },
            "mouseleave": function mouseleave($event) {
              _vm.hovering = false;
            },
            "click": function click($event) {
              return _vm.$emit('input', _vm.item);
            }
          }
        }, [_c('div', {
          staticClass: "icon-menu-item",
          class: {
            'selected': _vm.currentIndex === _vm.itemIndex
          },
          style: "background-color: ".concat(_vm.backgroundColor, "; color: ").concat(_vm.textColor)
        }, [_vm.item.styles ? _c('i', {
          staticClass: "d-flex justify-content-center",
          class: _vm.item.styles.icon
        }) : _vm._e()]), _vm._v(" "), _c('div', {
          staticClass: "d-flex justify-content-center mt-1",
          style: "font-size: ".concat(_vm.fontSize)
        }, [_vm._v("\n            " + _vm._s(_vm.item.name) + "\n          ")])])]], 2)];
      }
    }])
  }, [_vm._v(" "), _c('span', {
    staticClass: "text-h4"
  }, [_vm._v("\n    " + _vm._s(_vm.item.name) + "\n  ")])]);
};
var layout_application_itemvue_type_template_id_59eadf94_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Layout/layout-application-item.vue?vue&type=template&id=59eadf94

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Layout/layout-application-item.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var layout_application_itemvue_type_script_lang_js = ({
  name: 'layout-application-item',
  components: {},
  props: ['item', 'itemIndex', 'currentIndex'],
  data: function data() {
    return {
      hovering: false
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({
    textColor: function textColor() {
      if (this.hovering) return this.item.styles.background;
      return this.item.styles.color;
    },
    backgroundColor: function backgroundColor() {
      if (this.hovering) return this.item.styles.color;
      return this.item.styles.background;
    },
    fontSize: function fontSize() {
      if (this.mode === 'phone') {
        return '13px';
      }
      return '19px';
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode'
  }))
});
// CONCATENATED MODULE: ./components/Layout/layout-application-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Layout_layout_application_itemvue_type_script_lang_js = (layout_application_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Layout/layout-application-item.vue?vue&type=style&index=0&id=59eadf94&prod&lang=scss
var layout_application_itemvue_type_style_index_0_id_59eadf94_prod_lang_scss = __webpack_require__(1999);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Layout/layout-application-item.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Layout_layout_application_itemvue_type_script_lang_js,
  layout_application_itemvue_type_template_id_59eadf94_render,
  layout_application_itemvue_type_template_id_59eadf94_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var layout_application_item = (component.exports);
// EXTERNAL MODULE: ./mixins/UsesApplications.js
var UsesApplications = __webpack_require__(356);

// EXTERNAL MODULE: ./utils/store-accessor.ts
var store_accessor = __webpack_require__(27);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/index.vue?vue&type=script&lang=js

















function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
function lib_vue_loader_options_pagesvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function lib_vue_loader_options_pagesvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? lib_vue_loader_options_pagesvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : lib_vue_loader_options_pagesvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/* harmony default export */ var lib_vue_loader_options_pagesvue_type_script_lang_js = ({
  transition: {
    name: 'zoom',
    mode: 'out-in'
  },
  components: {
    LayoutApplicationItem: layout_application_item
  },
  mixins: [UsesApplications["a" /* default */]],
  layout: 'default',
  data: function data() {
    return {
      applications: applications["b" /* default */],
      currentIndex: null,
      showingAllApps: false,
      unreadTextMessages: 0
    };
  },
  mounted: function mounted() {
    this.fetchIconNumbers();
  },
  methods: {
    doTest: function doTest() {},
    _arrowLeft: function _arrowLeft() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },
    _arrowRight: function _arrowRight() {
      if (this.currentIndex < this.filtered.length - 1) {
        this.currentIndex++;
      }
    },
    _arrowDown: function _arrowDown() {
      if (this.currentIndex + 3 < this.filtered.length - 1) {
        this.currentIndex = this.currentIndex + 4;
      }
    },
    _arrowUp: function _arrowUp() {
      if (this.currentIndex - 3 > 0) {
        this.currentIndex = this.currentIndex - 4;
      }
    },
    _enter: function _enter() {
      this.changeApplication(this.filtered[this.currentIndex]);
    },
    fetchIconNumbers: function fetchIconNumbers() {
      var _this = this;
      this.$axios.$post('https://blrp_tablet/getUnreadTextMessages').then(function (res) {
        if (res) {
          _this.unreadTextMessages = res.unread_messages;
        }
      });
    }
  },
  //
  computed: lib_vue_loader_options_pagesvue_type_script_lang_js_objectSpread({
    moreFiltered: function moreFiltered() {
      if (this.showingAllApps) {
        return this.filtered;
      }
      var _iterator = _createForOfIteratorHelper(this.filtered),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var app = _step.value;
          if (app.name === 'SMS') {
            app.notifications = this.unreadTextMessages;
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
      return this.filtered;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    mode: 'system/mode',
    hasAnyGroup: 'auth/hasAnyGroup',
    settingUp: 'system/settingUp'
  }))
});
// CONCATENATED MODULE: ./pages/index.vue?vue&type=script&lang=js
 /* harmony default export */ var pagesvue_type_script_lang_js = (lib_vue_loader_options_pagesvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/index.vue?vue&type=style&index=0&id=fa3e2028&prod&lang=scss
var pagesvue_type_style_index_0_id_fa3e2028_prod_lang_scss = __webpack_require__(2001);

// CONCATENATED MODULE: ./pages/index.vue






/* normalize component */

var pages_component = Object(componentNormalizer["a" /* default */])(
  pagesvue_type_script_lang_js,
  lib_vue_loader_options_pagesvue_type_template_id_fa3e2028_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var pages = __webpack_exports__["default"] = (pages_component.exports);

/***/ })

}]);