(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[173],{

/***/ 2226:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/register.vue?vue&type=template&id=2f9b3393











var registervue_type_template_id_2f9b3393_render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _c('div', {
    staticClass: "p-5"
  }, [_c(VCard["a" /* default */], [_c(components_VCard["c" /* VCardTitle */], [_c(VIcon["a" /* default */], {
    attrs: {
      "right": "",
      "color": "red",
      "small": ""
    }
  }, [_vm._v("\n        fa-solid fa-l\n      ")]), _vm._v(" "), _c('span', {
    staticClass: "ml-4"
  }, [_vm._v("\n        Account Registration\n      ")])], 1), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c('span', [_c(VAlert["a" /* default */], {
    attrs: {
      "color": "primary"
    }
  }, [_vm._v("\n          Your device has not yet been registered with LifeInvader.\n          "), _c('br'), _vm._v("Please fill out the following fields.\n          If you have already filled these fields out, you can simply press the register button.\n        ")])], 1), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "cols": "4"
    }
  }, [_c(VImg["a" /* default */], {
    attrs: {
      "src": "https://wiki.glife.fr/uploads/images/gallery/2022-01/image-*************.png"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], [_c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.submit.apply(null, arguments);
      }
    }
  }, [_c(VTextField["a" /* default */], {
    attrs: {
      "hint": "Think hard! Choose a name that best fits you. If you want, even put your real name. We definitely don't care.",
      "label": "Display Name"
    },
    model: {
      value: _vm.form.display_name,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "display_name", $$v);
      },
      expression: "form.display_name"
    }
  })], 1)], 1)], 1)], 1)], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/misc/register.vue?vue&type=template&id=2f9b3393

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/register.vue?vue&type=script&lang=ts








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var registervue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  components: {},
  props: {},
  data: function data() {
    return {
      form: {
        license: null,
        displayName: null,
        agree: false
      }
    };
  },
  mounted: function mounted() {
    this.form.displayName = this.user.display_name;
    this.form.license = this.generateProductKey();
  },
  methods: {
    submit: function submit() {}
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user'
  }))
}));
// CONCATENATED MODULE: ./pages/misc/register.vue?vue&type=script&lang=ts
 /* harmony default export */ var misc_registervue_type_script_lang_ts = (registervue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/misc/register.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  misc_registervue_type_script_lang_ts,
  registervue_type_template_id_2f9b3393_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var register = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);