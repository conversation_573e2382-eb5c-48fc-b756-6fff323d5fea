(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[219],{

/***/ 2209:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/people/_id/profile.vue?vue&type=template&id=6a52bb8e







var profilevue_type_template_id_6a52bb8e_render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _vm.person ? _c('div', {
    style: "background-image: url('".concat(_vm.mode === 'tablet' ? _vm.person.c_tablet_background : _vm.person.c_phone_background, "');")
  }, [_c(VCard["a" /* default */], {
    staticClass: "mb-4",
    attrs: {
      "color": _vm.person.c_color
    }
  }, [_vm.person.c_bw_bio_title ? _c(components_VCard["b" /* VCardText */], {
    staticClass: "text-h3 text-center font-italic"
  }, [_vm._v("\n      \"" + _vm._s(_vm.person.c_bw_bio_title) + "\"\n    ")]) : _vm._e()], 1), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "cols": _vm.mode === 'phone' ? 12 : 3
    }
  }, [_c(VCard["a" /* default */], {
    attrs: {
      "outlined": ""
    }
  }, [_c(VImg["a" /* default */], {
    attrs: {
      "src": _vm.person.avatar_url,
      "contain": ""
    }
  })], 1)], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "align-self": "center"
    }
  }, [_c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "cols": _vm.mode === 'phone' ? 12 : 4
    }
  }, [_c('app-details-box', {
    attrs: {
      "icon": "fa-solid fa-envelopes-bulk",
      "title": "".concat(_vm.person.posts_count),
      "content": "posts created",
      "color": "primary"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "cols": _vm.mode === 'phone' ? 12 : 4
    }
  }, [_c('app-details-box', {
    attrs: {
      "icon": "fa-solid fa-reply-all",
      "title": "".concat(_vm.person.comments_count),
      "content": "posts replied to",
      "color": "primary"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "cols": _vm.mode === 'phone' ? 12 : 4
    }
  }, [_c('app-details-box', {
    attrs: {
      "icon": "fa-solid fa-glasses",
      "title": "".concat(_vm.person.posts_read_count),
      "content": "posts read",
      "color": "primary"
    }
  })], 1)], 1), _vm._v(" "), _c(VRow["a" /* default */], [_c(VCol["a" /* default */], {
    attrs: {
      "cols": _vm.mode === 'phone' ? 12 : 6
    }
  }, [_c('app-details-box', {
    attrs: {
      "icon": "fa-solid fa-quote-left",
      "title": "".concat(_vm.person.mentions_count),
      "content": "times i have mentioned others",
      "color": "secondary"
    }
  })], 1), _vm._v(" "), _c(VCol["a" /* default */], {
    attrs: {
      "cols": _vm.mode === 'phone' ? 12 : 6
    }
  }, [_c('app-details-box', {
    attrs: {
      "icon": "fa-solid fa-square-quote",
      "title": "".concat(_vm.person.mentioned_count),
      "content": "times i have been mentioned",
      "color": "secondary"
    }
  })], 1)], 1)], 1)], 1), _vm._v(" "), _c(VCard["a" /* default */], {
    staticClass: "mt-4"
  }, [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n      My Biography\n    ")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c('app-markdown-view', {
    attrs: {
      "source": _vm.person.c_bw_bio_content
    }
  })], 1)], 1)], 1) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/social/people/_id/profile.vue?vue&type=template&id=6a52bb8e

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Common/app-details-box.vue + 4 modules
var app_details_box = __webpack_require__(318);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/people/_id/profile.vue?vue&type=script&lang=ts








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/* harmony default export */ var profilevue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'index.vue',
  components: {
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppDetailsBox: app_details_box["a" /* default */]
  },
  props: {
    person: {
      type: Object
    }
  },
  data: function data() {
    return {};
  },
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode'
  }))
}));
// CONCATENATED MODULE: ./pages/social/people/_id/profile.vue?vue&type=script&lang=ts
 /* harmony default export */ var _id_profilevue_type_script_lang_ts = (profilevue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/social/people/_id/profile.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _id_profilevue_type_script_lang_ts,
  profilevue_type_template_id_6a52bb8e_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var profile = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);