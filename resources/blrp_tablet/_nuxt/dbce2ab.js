(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[53],{

/***/ 2067:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/notices.vue?vue&type=template&id=1ad65bb5
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('crudy-table', {
    staticClass: "mt-4",
    attrs: {
      "name": "BusinessCharacter",
      "view": "index",
      "filter": {
        business_id: _vm.$route.params.id
      }
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/business/_id/notices.vue?vue&type=template&id=1ad65bb5

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/business/_id/notices.vue?vue&type=script&lang=js

/* harmony default export */ var noticesvue_type_script_lang_js = ({
  components: {
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['business'],
  data: function data() {
    return {
      notices: []
    };
  },
  mounted: function mounted() {
    this.index();
  },
  methods: {
    index: function index() {
      var _this = this;
      this.$axios.$get("/business/notices/".concat(business.i)).then(function (res) {
        _this.notices = res;
      });
    }
  }
});
// CONCATENATED MODULE: ./pages/business/_id/notices.vue?vue&type=script&lang=js
 /* harmony default export */ var _id_noticesvue_type_script_lang_js = (noticesvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/business/_id/notices.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _id_noticesvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var notices = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);