(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[148,147],{

/***/ 1752:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(2004);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("93ece5bc", content, true, {"sourceMap":false});

/***/ }),

/***/ 1757:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(78);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=template&id=22ac7036


var crew_chat_message_itemvue_type_template_id_22ac7036_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "mt-2",
    on: {
      "mouseenter": function mouseenter($event) {
        _vm.hovering = true;
      },
      "mouseleave": function mouseleave($event) {
        _vm.hovering = false;
      }
    }
  }, [_c('social-person-tag', {
    attrs: {
      "person": _vm.message.person,
      "nickname": _vm.message.nickname,
      "color": "transparent",
      "small": true
    },
    scopedSlots: _vm._u([{
      key: "details",
      fn: function fn() {
        return [_c('app-markdown-view', {
          attrs: {
            "source": _vm.message.message
          }
        }), _vm._v(" "), _c(transitions["g" /* VScrollYTransition */], [_vm.hovering ? _c('div', [_c('app-timestamp', {
          attrs: {
            "stamp": _vm.message.created_at
          }
        }), _vm._v(" ago\n        ")], 1) : _vm._e()])];
      },
      proxy: true
    }])
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=template&id=22ac7036

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Pages/Social/social-person-tag.vue + 4 modules
var social_person_tag = __webpack_require__(142);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/* harmony default export */ var crew_chat_message_itemvue_type_script_lang_js = ({
  name: 'crew-chat-message-item',
  components: {
    AppTimestamp: app_timestamp["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    SocialPersonTag: social_person_tag["a" /* default */]
  },
  props: ['message'],
  data: function data() {
    return {
      hovering: false
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=script&lang=js
 /* harmony default export */ var _id_crew_chat_message_itemvue_type_script_lang_js = (crew_chat_message_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/crew-chat/_id/crew-chat-message-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _id_crew_chat_message_itemvue_type_script_lang_js,
  crew_chat_message_itemvue_type_template_id_22ac7036_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crew_chat_message_item = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 2003:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_6d7e80b2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1752);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_6d7e80b2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_6d7e80b2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 2004:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".crew-chat-messages-container{background-color:#000;max-height:calc(var(--tablet-height) - 300px);overflow-y:scroll}.tablet-phone .crew-chat-messages-container{max-height:calc(var(--tablet-height) - 380px)}.crew-chat-messages-container .text-message{display:flex;justify-content:flex-start;margin:7px 15px;word-wrap:break-word}.crew-chat-messages-container .text-message .message-body{background-color:#1d1d1d;border-radius:15px;line-height:19px;max-width:70%;padding:6px 15px}.crew-chat-messages-container .text-message .message-body.owner{background-color:#16a8c6;color:#fff}.send-area-crew-chat{bottom:calc(var(--tablet-toolbar-height) + 39px);position:absolute;width:100%}.send-area-crew-chat .text-reply-hint{bottom:5px;position:absolute;right:5px}.send-area-crew-chat textarea{font-size:13px;resize:none;-webkit-text-decoration:none;text-decoration:none}.send-area-crew-chat textarea:focus{box-shadow:none}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2033:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAvatar/VAvatar.js
var VAvatar = __webpack_require__(464);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VImg/VImg.js + 2 modules
var VImg = __webpack_require__(890);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItemIcon.js
var VListItemIcon = __webpack_require__(260);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 2 modules
var components_VList = __webpack_require__(50);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/VMenu.js
var VMenu = __webpack_require__(461);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressCircular/VProgressCircular.js
var VProgressCircular = __webpack_require__(300);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VSpacer.js
var VSpacer = __webpack_require__(1523);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextField/VTextField.js + 2 modules
var VTextField = __webpack_require__(155);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTooltip/VTooltip.js
var VTooltip = __webpack_require__(1522);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/_id/index.vue?vue&type=template&id=6d7e80b2




















var _idvue_type_template_id_6d7e80b2_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.chat && _vm.user ? _c('div', [_c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: ['enter'],
      expression: "['enter']"
    }],
    on: {
      "shortkey": _vm._enter
    }
  }), _vm._v(" "), _c(VCard["a" /* default */], [_c(components_VCard["b" /* VCardText */], [_c('div', [_c('h4', [_c(VTooltip["a" /* default */], {
    attrs: {
      "bottom": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref) {
        var on = _ref.on,
          attrs = _ref.attrs;
        return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
          staticClass: "mr-3",
          attrs: {
            "icon": "",
            "small": ""
          },
          on: {
            "click": function click($event) {
              return _vm.$router.back();
            }
          }
        }, 'v-btn', attrs, false), on), [_c(VIcon["a" /* default */], {
          attrs: {
            "small": ""
          }
        }, [_vm._v("fa-regular fa-arrow-left")])], 1)];
      }
    }], null, false, 1265791055)
  }, [_vm._v(" "), _c('span', [_vm._v("Go Back")])]), _vm._v(" "), _c(VTooltip["a" /* default */], {
    attrs: {
      "bottom": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref2) {
        var on = _ref2.on,
          attrs = _ref2.attrs;
        return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
          staticClass: "mr-3",
          attrs: {
            "icon": "",
            "small": ""
          },
          on: {
            "click": function click($event) {
              _vm.showSettingsSidebar = true;
            }
          }
        }, 'v-btn', attrs, false), on), [_c(VIcon["a" /* default */], {
          attrs: {
            "small": ""
          }
        }, [_vm._v("fa-regular fa-cog")])], 1)];
      }
    }], null, false, 1721936909)
  }, [_vm._v(" "), _c('span', [_vm._v("Chat Settings")])]), _vm._v(" "), _c('app-toggle-notification', {
    staticClass: "mr-3",
    attrs: {
      "type": "crew-chat",
      "identifier": _vm.chat.id
    }
  }), _vm._v(" "), _vm.chat.group && _vm.chat.group.owner_person_id !== _vm.user.id ? _c(VTooltip["a" /* default */], {
    attrs: {
      "bottom": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref3) {
        var on = _ref3.on,
          attrs = _ref3.attrs;
        return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
          staticClass: "mr-3",
          attrs: {
            "icon": "",
            "small": "",
            "color": "warning",
            "loading": _vm.leaving
          },
          on: {
            "click": _vm.leaveChat
          }
        }, 'v-btn', attrs, false), on), [_c(VIcon["a" /* default */], {
          attrs: {
            "small": ""
          }
        }, [_vm._v("fa-solid fa-sign-out-alt")])], 1)];
      }
    }], null, false, 383517893)
  }, [_vm._v(" "), _c('span', [_vm._v("Leave Crew Chat")])]) : _vm._e(), _vm._v("\n\n          " + _vm._s(_vm.chat.name) + "\n        ")], 1), _vm._v(" "), _c(VSpacer["a" /* default */])], 1), _vm._v(" "), _c('app-socket-channel-presence-listener', {
    staticStyle: {
      "position": "absolute",
      "right": "10px"
    },
    attrs: {
      "channel": "crew-chat.".concat(_vm.chat.id),
      "listeners": {
        'SocialGroupCrewMessageSent': _vm.handleSocialGroupCrewMessageSent
      },
      "show-avatars": true
    }
  })], 1)], 1), _vm._v(" "), _c('div', {
    directives: [{
      name: "chat-scroll",
      rawName: "v-chat-scroll",
      value: {
        always: false,
        smooth: true,
        notSmoothOnInit: true
      },
      expression: "{ always: false, smooth: true, notSmoothOnInit: true}"
    }],
    staticClass: "crew-chat-messages-container mt-4"
  }, _vm._l(_vm.messages, function (message) {
    return _c('div', {
      key: message.id,
      staticClass: "text-message",
      class: {
        'd-flex justify-content-end': message.person_id === _vm.user.id
      }
    }, [_c('crew-chat-message-item', {
      attrs: {
        "message": message
      }
    })], 1);
  }), 0), _vm._v(" "), _c('div', {
    staticClass: "send-area-crew-chat"
  }, [_c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm._enter.apply(null, arguments);
      }
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "fab": "",
      "icon": "",
      "right": "",
      "absolute": "",
      "x-small": "",
      "color": "primary"
    },
    on: {
      "click": _vm._enter
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-arrow-right"
  })]), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mr-5",
    attrs: {
      "fab": "",
      "icon": "",
      "right": "",
      "absolute": "",
      "x-small": "",
      "color": "secondary"
    },
    on: {
      "click": _vm.takePicture
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-camera"
  })]), _vm._v(" "), _c('crudy-editor', {
    attrs: {
      "disabled": _vm.submitting,
      "placeholder": "send Message",
      "autofocus": true,
      "character-limit": 255,
      "simplified": true
    },
    model: {
      value: _vm.content,
      callback: function callback($$v) {
        _vm.content = $$v;
      },
      expression: "content"
    }
  })], 1)], 1), _vm._v(" "), _c('app-roll-out', {
    attrs: {
      "title": "Chat Settings"
    },
    model: {
      value: _vm.showSettingsSidebar,
      callback: function callback($$v) {
        _vm.showSettingsSidebar = $$v;
      },
      expression: "showSettingsSidebar"
    }
  }, [_vm.chat ? _c('div', {
    staticClass: "pa-3"
  }, [_c('div', {
    staticClass: "text-h6 mb-3 ml-4"
  }, [_vm._v(_vm._s(_vm.chat.name))]), _vm._v(" "), _c(VCard["a" /* default */], {
    staticClass: "mb-4"
  }, [_c(components_VCard["c" /* VCardTitle */], {
    staticClass: "text-subtitle-1"
  }, [_vm._v("Share Code")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_vm.chat.share_code ? _c('div', [!_vm.showShareCode ? _c('div', [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary",
      "block": ""
    },
    on: {
      "click": function click($event) {
        _vm.showShareCode = true;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-eye")]), _vm._v("\n                Show Share Code\n              ")], 1)], 1) : _c('div', [_c(VTextField["a" /* default */], {
    attrs: {
      "value": _vm.chat.share_code,
      "label": "Share Code",
      "readonly": "",
      "hint": "Share this 5-character code (##AA#) to let others join"
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-2",
    attrs: {
      "color": "secondary",
      "block": ""
    },
    on: {
      "click": function click($event) {
        _vm.showShareCode = false;
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-eye-slash")]), _vm._v("\n                Hide Share Code\n              ")], 1)], 1)]) : _c('div', [_c(VAlert["a" /* default */], {
    attrs: {
      "type": "info",
      "dense": ""
    }
  }, [_vm._v("\n              No share code has been generated for this chat yet.\n            ")])], 1)])], 1), _vm._v(" "), _vm.chat.can_manage ? _c('div', [_c(VCard["a" /* default */], {
    staticClass: "mb-4"
  }, [_c(components_VCard["c" /* VCardTitle */], {
    staticClass: "text-subtitle-1"
  }, [_vm._v("Manage Share Code")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_vm.chat.share_code ? _c('div', [_c(VBtn["a" /* default */], {
    staticClass: "mb-2",
    attrs: {
      "color": "warning",
      "loading": _vm.refreshing,
      "block": ""
    },
    on: {
      "click": _vm.refreshShareCode
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-refresh")]), _vm._v("\n                Refresh Code\n              ")], 1), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "error",
      "loading": _vm.resetting,
      "block": ""
    },
    on: {
      "click": _vm.resetShareCode
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-trash")]), _vm._v("\n                Remove Code\n              ")], 1)], 1) : _c('div', [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary",
      "loading": _vm.generating,
      "block": ""
    },
    on: {
      "click": _vm.generateShareCode
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-plus")]), _vm._v("\n                Generate Share Code\n              ")], 1)], 1)])], 1), _vm._v(" "), _c(VCard["a" /* default */], {
    staticClass: "mb-4"
  }, [_c(components_VCard["c" /* VCardTitle */], {
    staticClass: "text-subtitle-1"
  }, [_vm._v("\n            Members (" + _vm._s(_vm.members.length) + ")\n            "), _c(VSpacer["a" /* default */]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "icon": "",
      "small": "",
      "loading": _vm.loadingMembers
    },
    on: {
      "click": _vm.loadMembers
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-refresh")])], 1)], 1), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_vm.loadingMembers ? _c('div', {
    staticClass: "text-center"
  }, [_c(VProgressCircular["a" /* default */], {
    attrs: {
      "indeterminate": ""
    }
  })], 1) : _c('div', _vm._l(_vm.members, function (member) {
    return _c('div', {
      key: member.id,
      staticClass: "d-flex align-center mb-2"
    }, [_c(VAvatar["a" /* default */], {
      staticClass: "mr-3",
      attrs: {
        "size": "32"
      }
    }, [_c(VImg["a" /* default */], {
      attrs: {
        "src": member.person.avatar_url
      }
    })], 1), _vm._v(" "), _c('div', {
      staticClass: "flex-grow-1"
    }, [_c('div', {
      staticClass: "text-body-2"
    }, [member.nickname ? _c('span', [_vm._v(_vm._s(member.nickname))]) : _c('span', [_vm._v(_vm._s(member.person.display_name))]), _vm._v(" "), member.nickname ? _c('span', {
      staticClass: "text-caption text-muted ml-1"
    }, [_vm._v("(" + _vm._s(member.person.display_name) + ")")]) : _vm._e()]), _vm._v(" "), _c('div', {
      staticClass: "text-caption text-muted"
    }, [member.person_id === _vm.chat.group.owner_person_id ? _c('span', [_vm._v("Owner")]) : member.can_manage ? _c('span', [_vm._v("Manager")]) : _c('span', [_vm._v("Member")])])]), _vm._v(" "), member.person_id !== _vm.chat.group.owner_person_id ? _c('div', [_c(VMenu["a" /* default */], {
      scopedSlots: _vm._u([{
        key: "activator",
        fn: function fn(_ref4) {
          var on = _ref4.on,
            attrs = _ref4.attrs;
          return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
            attrs: {
              "icon": "",
              "small": ""
            }
          }, 'v-btn', attrs, false), on), [_c(VIcon["a" /* default */], {
            attrs: {
              "small": ""
            }
          }, [_vm._v("fa-solid fa-ellipsis-v")])], 1)];
        }
      }], null, true)
    }, [_vm._v(" "), _c(VList["a" /* default */], [_vm.chat.group.owner_person_id === _vm.user.id ? [_c(VListItem["a" /* default */], {
      on: {
        "click": function click($event) {
          return _vm.transferOwnership(member.person_id);
        }
      }
    }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
      attrs: {
        "small": ""
      }
    }, [_vm._v("fa-solid fa-crown")])], 1), _vm._v(" "), _c(components_VList["c" /* VListItemTitle */], [_vm._v("Make Owner")])], 1), _vm._v(" "), !member.can_manage ? _c(VListItem["a" /* default */], {
      on: {
        "click": function click($event) {
          return _vm.promoteToManager(member.person_id);
        }
      }
    }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
      attrs: {
        "small": ""
      }
    }, [_vm._v("fa-solid fa-user-shield")])], 1), _vm._v(" "), _c(components_VList["c" /* VListItemTitle */], [_vm._v("Make Manager")])], 1) : _vm._e(), _vm._v(" "), member.can_manage ? _c(VListItem["a" /* default */], {
      on: {
        "click": function click($event) {
          return _vm.demoteManager(member.person_id);
        }
      }
    }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
      attrs: {
        "small": ""
      }
    }, [_vm._v("fa-solid fa-user-minus")])], 1), _vm._v(" "), _c(components_VList["c" /* VListItemTitle */], [_vm._v("Remove Manager")])], 1) : _vm._e()] : _vm._e(), _vm._v(" "), _vm.chat.can_manage ? _c(VListItem["a" /* default */], {
      staticClass: "error--text",
      on: {
        "click": function click($event) {
          return _vm.removeMember(member.person_id);
        }
      }
    }, [_c(VListItemIcon["a" /* default */], [_c(VIcon["a" /* default */], {
      attrs: {
        "small": "",
        "color": "error"
      }
    }, [_vm._v("fa-solid fa-trash")])], 1), _vm._v(" "), _c(components_VList["c" /* VListItemTitle */], [_vm._v("Remove")])], 1) : _vm._e()], 2)], 1)], 1) : _vm._e()], 1);
  }), 0)])], 1), _vm._v(" "), _vm.chat.is_owner && _vm.members.length <= 1 ? _c(VCard["a" /* default */], {
    staticClass: "mb-4"
  }, [_c(components_VCard["c" /* VCardTitle */], {
    staticClass: "text-subtitle-1 error--text"
  }, [_vm._v("Danger Zone")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c(VAlert["a" /* default */], {
    staticClass: "mb-3",
    attrs: {
      "type": "warning",
      "dense": ""
    }
  }, [_vm._v("\n              This will permanently delete the crew chat and cannot be undone.\n            ")]), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "error",
      "loading": _vm.deleting,
      "block": ""
    },
    on: {
      "click": _vm.deleteChat
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-2",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-trash")]), _vm._v("\n              Delete Crew Chat\n            ")], 1)], 1)], 1) : _vm._e()], 1) : _vm._e()], 1) : _vm._e()])], 1) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/crew-chat/_id/index.vue?vue&type=template&id=6d7e80b2

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find-index.js
var es_array_find_index = __webpack_require__(113);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// EXTERNAL MODULE: ./components/Common/app-socket-channel-presence-listener.vue + 4 modules
var app_socket_channel_presence_listener = __webpack_require__(497);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Pages/Social/social-person-tag.vue + 4 modules
var social_person_tag = __webpack_require__(142);

// EXTERNAL MODULE: ./pages/crew-chat/_id/crew-chat-message-item.vue + 4 modules
var crew_chat_message_item = __webpack_require__(1757);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-toggle-notification.vue?vue&type=template&id=f7b44380



var app_toggle_notificationvue_type_template_id_f7b44380_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return !_vm.hasNotificationsDisabled(_vm.type, _vm.identifier) ? _c(VBtn["a" /* default */], {
    attrs: {
      "small": "",
      "fab": "",
      "text": ""
    }
  }, [_c(VIcon["a" /* default */], {
    on: {
      "click": _vm.toggle
    }
  }, [_vm._v("\n    fa-regular fa-bell\n  ")])], 1) : _c(VBtn["a" /* default */], {
    attrs: {
      "small": "",
      "fab": "",
      "text": ""
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "red"
    },
    on: {
      "click": _vm.toggle
    }
  }, [_vm._v("\n    fa-regular fa-bell-slash\n  ")])], 1);
};
var app_toggle_notificationvue_type_template_id_f7b44380_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-toggle-notification.vue?vue&type=template&id=f7b44380

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-toggle-notification.vue?vue&type=script&lang=js









function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var app_toggle_notificationvue_type_script_lang_js = ({
  name: 'app-toggle-notification',
  components: {},
  props: ['type', 'identifier'],
  data: function data() {
    return {};
  },
  created: function created() {},
  methods: {
    toggle: function toggle() {
      var _this = this;
      this.$axios.$post("/me/silence-notification/".concat(this.type, "/").concat(this.identifier)).then(/*#__PURE__*/Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$store.dispatch('auth/fetchUser', {
                force: true
              });
            case 2:
              _this.$forceUpdate();
            case 3:
            case "end":
              return _context.stop();
          }
        }, _callee);
      })));
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    'hasNotificationsDisabled': 'auth/hasNotificationsDisabled'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-toggle-notification.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_toggle_notificationvue_type_script_lang_js = (app_toggle_notificationvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-toggle-notification.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_toggle_notificationvue_type_script_lang_js,
  app_toggle_notificationvue_type_template_id_f7b44380_render,
  app_toggle_notificationvue_type_template_id_f7b44380_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_toggle_notification = (component.exports);
// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/_id/index.vue?vue&type=script&lang=js












function _idvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _idvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? _idvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : _idvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }











/* harmony default export */ var _idvue_type_script_lang_js = ({
  components: {
    AppToggleNotification: app_toggle_notification,
    CrewChatMessageItem: crew_chat_message_item["default"],
    SocialPersonTag: social_person_tag["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppSocketChannelPresenceListener: app_socket_channel_presence_listener["a" /* default */],
    CrudyEditor: crudy_editor["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    AppRollOut: app_roll_out["a" /* default */]
  },
  data: function data() {
    return {
      chat: null,
      messages: [],
      content: null,
      submitting: false,
      showSettingsSidebar: false,
      showShareCode: false,
      members: [],
      loadingMembers: false,
      generating: false,
      refreshing: false,
      resetting: false,
      deleting: false,
      leaving: false,
      showNicknameDialog: false,
      selectedMember: null,
      nicknameForm: {
        nickname: ''
      },
      updatingNickname: false
    };
  },
  mounted: function mounted() {
    this.index();
  },
  methods: {
    index: function index() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.$get("/social/chat/view/".concat(_this.$route.params.id));
            case 2:
              _this.chat = _context.sent;
              _this.messages = _this.chat.messages;
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    _enter: function _enter() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (!_this2.submitting) {
                _context2.next = 2;
                break;
              }
              return _context2.abrupt("return");
            case 2:
              _this2.submitting = true;
              _context2.next = 5;
              return _this2.$axios.$post("/social/chat/add-message/".concat(_this2.$route.params.id), {
                message: _this2.content
              });
            case 5:
              _this2.content = null;
              _this2.submitting = false;
            case 7:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    takePicture: function takePicture() {
      var _this3 = this;
      this.$axios.$post('https://blrp_tablet/takePictureWithResult').then(function (response) {
        _this3.content = response;
        _this3.submit();
      });
    },
    handleSocialGroupCrewMessageSent: function handleSocialGroupCrewMessageSent(_ref) {
      var chat = _ref.chat,
        message = _ref.message;
      this.messages.push(message);
    },
    loadMembers: function loadMembers() {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var response, _error$response;
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _this4.loadingMembers = true;
              _context3.prev = 1;
              _context3.next = 4;
              return _this4.$axios.$get("/social/chat/members/".concat(_this4.$route.params.id));
            case 4:
              response = _context3.sent;
              _this4.members = response.members;
              // Update chat data with latest info
              if (response.chat) {
                _this4.chat = _idvue_type_script_lang_js_objectSpread(_idvue_type_script_lang_js_objectSpread({}, _this4.chat), response.chat);
              }
              _context3.next = 12;
              break;
            case 9:
              _context3.prev = 9;
              _context3.t0 = _context3["catch"](1);
              _this4.$toast.error(((_error$response = _context3.t0.response) === null || _error$response === void 0 || (_error$response = _error$response.data) === null || _error$response === void 0 ? void 0 : _error$response.message) || 'Failed to load members');
            case 12:
              _this4.loadingMembers = false;
            case 13:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[1, 9]]);
      }))();
    },
    generateShareCode: function generateShareCode() {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        var response, _error$response2;
        return regeneratorRuntime.wrap(function _callee4$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              _this5.generating = true;
              _context4.prev = 1;
              _context4.next = 4;
              return _this5.$axios.$post("/social/chat/generate-share-code/".concat(_this5.$route.params.id));
            case 4:
              response = _context4.sent;
              // Use Vue.set to ensure reactivity
              _this5.$set(_this5.chat, 'share_code', response.share_code);
              _this5.$toast.success('Share code generated!');
              _context4.next = 12;
              break;
            case 9:
              _context4.prev = 9;
              _context4.t0 = _context4["catch"](1);
              _this5.$toast.error(((_error$response2 = _context4.t0.response) === null || _error$response2 === void 0 || (_error$response2 = _error$response2.data) === null || _error$response2 === void 0 ? void 0 : _error$response2.message) || 'Failed to generate share code');
            case 12:
              _this5.generating = false;
            case 13:
            case "end":
              return _context4.stop();
          }
        }, _callee4, null, [[1, 9]]);
      }))();
    },
    refreshShareCode: function refreshShareCode() {
      var _this6 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee5() {
        var response, _error$response3;
        return regeneratorRuntime.wrap(function _callee5$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              _this6.refreshing = true;
              _context5.prev = 1;
              _context5.next = 4;
              return _this6.$axios.$post("/social/chat/refresh-share-code/".concat(_this6.$route.params.id));
            case 4:
              response = _context5.sent;
              // Use Vue.set to ensure reactivity
              _this6.$set(_this6.chat, 'share_code', response.share_code);
              _this6.$toast.success('Share code refreshed!');
              _context5.next = 12;
              break;
            case 9:
              _context5.prev = 9;
              _context5.t0 = _context5["catch"](1);
              _this6.$toast.error(((_error$response3 = _context5.t0.response) === null || _error$response3 === void 0 || (_error$response3 = _error$response3.data) === null || _error$response3 === void 0 ? void 0 : _error$response3.message) || 'Failed to refresh share code');
            case 12:
              _this6.refreshing = false;
            case 13:
            case "end":
              return _context5.stop();
          }
        }, _callee5, null, [[1, 9]]);
      }))();
    },
    resetShareCode: function resetShareCode() {
      var _this7 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee6() {
        var _error$response4;
        return regeneratorRuntime.wrap(function _callee6$(_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              _this7.resetting = true;
              _context6.prev = 1;
              _context6.next = 4;
              return _this7.$axios.$post("/social/chat/reset-share-code/".concat(_this7.$route.params.id));
            case 4:
              // Use Vue.set to ensure reactivity
              _this7.$set(_this7.chat, 'share_code', null);
              _this7.$toast.success('Share code removed!');
              _context6.next = 11;
              break;
            case 8:
              _context6.prev = 8;
              _context6.t0 = _context6["catch"](1);
              _this7.$toast.error(((_error$response4 = _context6.t0.response) === null || _error$response4 === void 0 || (_error$response4 = _error$response4.data) === null || _error$response4 === void 0 ? void 0 : _error$response4.message) || 'Failed to reset share code');
            case 11:
              _this7.resetting = false;
            case 12:
            case "end":
              return _context6.stop();
          }
        }, _callee6, null, [[1, 8]]);
      }))();
    },
    removeMember: function removeMember(memberId) {
      var _this8 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee7() {
        var result, _error$response5;
        return regeneratorRuntime.wrap(function _callee7$(_context7) {
          while (1) switch (_context7.prev = _context7.next) {
            case 0:
              _context7.next = 2;
              return _this8.$swal.fire({
                title: 'Remove Member',
                text: 'Are you sure you want to remove this member from the crew chat?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, remove them',
                cancelButtonText: 'Cancel'
              });
            case 2:
              result = _context7.sent;
              if (result.isConfirmed) {
                _context7.next = 5;
                break;
              }
              return _context7.abrupt("return");
            case 5:
              _context7.prev = 5;
              _context7.next = 8;
              return _this8.$axios.$post("/social/chat/remove-member/".concat(_this8.$route.params.id, "/").concat(memberId));
            case 8:
              _this8.$toast.success('Member removed successfully!');
              _context7.next = 11;
              return _this8.loadMembers();
            case 11:
              _context7.next = 16;
              break;
            case 13:
              _context7.prev = 13;
              _context7.t0 = _context7["catch"](5);
              _this8.$toast.error(((_error$response5 = _context7.t0.response) === null || _error$response5 === void 0 || (_error$response5 = _error$response5.data) === null || _error$response5 === void 0 ? void 0 : _error$response5.message) || 'Failed to remove member');
            case 16:
            case "end":
              return _context7.stop();
          }
        }, _callee7, null, [[5, 13]]);
      }))();
    },
    transferOwnership: function transferOwnership(newOwnerId) {
      var _this9 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee8() {
        var result, _error$response6;
        return regeneratorRuntime.wrap(function _callee8$(_context8) {
          while (1) switch (_context8.prev = _context8.next) {
            case 0:
              _context8.next = 2;
              return _this9.$swal.fire({
                title: 'Transfer Ownership',
                text: 'Are you sure you want to transfer ownership? You will lose management privileges.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#f39c12',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, transfer ownership',
                cancelButtonText: 'Cancel'
              });
            case 2:
              result = _context8.sent;
              if (result.isConfirmed) {
                _context8.next = 5;
                break;
              }
              return _context8.abrupt("return");
            case 5:
              _context8.prev = 5;
              _context8.next = 8;
              return _this9.$axios.$post("/social/chat/transfer-ownership/".concat(_this9.$route.params.id, "/").concat(newOwnerId));
            case 8:
              _this9.$toast.success('Ownership transferred successfully!');
              _context8.next = 11;
              return _this9.index();
            case 11:
              _context8.next = 13;
              return _this9.loadMembers();
            case 13:
              _context8.next = 18;
              break;
            case 15:
              _context8.prev = 15;
              _context8.t0 = _context8["catch"](5);
              _this9.$toast.error(((_error$response6 = _context8.t0.response) === null || _error$response6 === void 0 || (_error$response6 = _error$response6.data) === null || _error$response6 === void 0 ? void 0 : _error$response6.message) || 'Failed to transfer ownership');
            case 18:
            case "end":
              return _context8.stop();
          }
        }, _callee8, null, [[5, 15]]);
      }))();
    },
    deleteChat: function deleteChat() {
      var _this10 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee9() {
        var result, _error$response7;
        return regeneratorRuntime.wrap(function _callee9$(_context9) {
          while (1) switch (_context9.prev = _context9.next) {
            case 0:
              _context9.next = 2;
              return _this10.$swal.fire({
                title: 'Delete Crew Chat',
                text: 'Are you sure you want to permanently delete this crew chat? This will delete ALL messages and cannot be undone.',
                icon: 'error',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, delete permanently',
                cancelButtonText: 'Cancel'
              });
            case 2:
              result = _context9.sent;
              if (result.isConfirmed) {
                _context9.next = 5;
                break;
              }
              return _context9.abrupt("return");
            case 5:
              _this10.deleting = true;
              _context9.prev = 6;
              _context9.next = 9;
              return _this10.$axios.$post("/social/chat/delete/".concat(_this10.$route.params.id));
            case 9:
              _this10.$toast.success('Crew chat deleted successfully!');
              _this10.showSettingsSidebar = false;
              // Navigate back to crew chat list
              _this10.$router.push('/crew-chat');
              _context9.next = 17;
              break;
            case 14:
              _context9.prev = 14;
              _context9.t0 = _context9["catch"](6);
              _this10.$toast.error(((_error$response7 = _context9.t0.response) === null || _error$response7 === void 0 || (_error$response7 = _error$response7.data) === null || _error$response7 === void 0 ? void 0 : _error$response7.message) || 'Failed to delete crew chat');
            case 17:
              _this10.deleting = false;
            case 18:
            case "end":
              return _context9.stop();
          }
        }, _callee9, null, [[6, 14]]);
      }))();
    },
    leaveChat: function leaveChat() {
      var _this11 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee10() {
        var result, _error$response8;
        return regeneratorRuntime.wrap(function _callee10$(_context10) {
          while (1) switch (_context10.prev = _context10.next) {
            case 0:
              _context10.next = 2;
              return _this11.$swal.fire({
                title: 'Leave Crew Chat',
                text: 'Are you sure you want to leave this crew chat? You will lose access to all messages.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#f39c12',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, leave chat',
                cancelButtonText: 'Cancel'
              });
            case 2:
              result = _context10.sent;
              if (result.isConfirmed) {
                _context10.next = 5;
                break;
              }
              return _context10.abrupt("return");
            case 5:
              _this11.leaving = true;
              _context10.prev = 6;
              _context10.next = 9;
              return _this11.$axios.$post("/social/chat/leave/".concat(_this11.$route.params.id));
            case 9:
              _this11.$toast.success('You have left the crew chat');
              _this11.showSettingsSidebar = false;
              // Navigate back to crew chat list
              _this11.$router.push('/crew-chat');
              _context10.next = 17;
              break;
            case 14:
              _context10.prev = 14;
              _context10.t0 = _context10["catch"](6);
              _this11.$toast.error(((_error$response8 = _context10.t0.response) === null || _error$response8 === void 0 || (_error$response8 = _error$response8.data) === null || _error$response8 === void 0 ? void 0 : _error$response8.message) || 'Failed to leave crew chat');
            case 17:
              _this11.leaving = false;
            case 18:
            case "end":
              return _context10.stop();
          }
        }, _callee10, null, [[6, 14]]);
      }))();
    },
    editNickname: function editNickname(member) {
      this.selectedMember = member;
      this.nicknameForm.nickname = member.nickname || '';
      this.showNicknameDialog = true;
    },
    updateNickname: function updateNickname() {
      var _this12 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee11() {
        var response, memberIndex, _error$response9;
        return regeneratorRuntime.wrap(function _callee11$(_context11) {
          while (1) switch (_context11.prev = _context11.next) {
            case 0:
              _this12.updatingNickname = true;
              _context11.prev = 1;
              _context11.next = 4;
              return _this12.$axios.$post("/social/chat/update-nickname/".concat(_this12.$route.params.id, "/").concat(_this12.selectedMember.person_id), {
                nickname: _this12.nicknameForm.nickname || null
              });
            case 4:
              response = _context11.sent;
              // Update the member in the local list
              memberIndex = _this12.members.findIndex(function (m) {
                return m.person_id === _this12.selectedMember.person_id;
              });
              if (memberIndex !== -1) {
                _this12.$set(_this12.members, memberIndex, response.member);
              }
              _this12.showNicknameDialog = false;
              _this12.$toast.success('Nickname updated successfully!');
              _context11.next = 14;
              break;
            case 11:
              _context11.prev = 11;
              _context11.t0 = _context11["catch"](1);
              _this12.$toast.error(((_error$response9 = _context11.t0.response) === null || _error$response9 === void 0 || (_error$response9 = _error$response9.data) === null || _error$response9 === void 0 ? void 0 : _error$response9.message) || 'Failed to update nickname');
            case 14:
              _this12.updatingNickname = false;
            case 15:
            case "end":
              return _context11.stop();
          }
        }, _callee11, null, [[1, 11]]);
      }))();
    },
    editMyNickname: function editMyNickname() {
      var _this13 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee12() {
        var myMembership, tempMembership;
        return regeneratorRuntime.wrap(function _callee12$(_context12) {
          while (1) switch (_context12.prev = _context12.next) {
            case 0:
              console.log('editMyNickname called');

              // Ensure members are loaded
              if (!(!_this13.members || _this13.members.length === 0)) {
                _context12.next = 5;
                break;
              }
              console.log('Loading members...');
              _context12.next = 5;
              return _this13.loadMembers();
            case 5:
              myMembership = _this13.members.find(function (m) {
                return m.person_id === _this13.user.id;
              });
              if (myMembership) {
                console.log('Found membership, opening dialog', myMembership);
                _this13.editNickname(myMembership);
              } else {
                console.error('Could not find user membership', {
                  userId: _this13.user.id,
                  members: _this13.members,
                  chat: _this13.chat
                });

                // Fallback: create a temporary membership object for editing
                tempMembership = {
                  person_id: _this13.user.id,
                  person: _this13.user,
                  nickname: null
                };
                console.log('Using fallback membership', tempMembership);
                _this13.editNickname(tempMembership);
              }
            case 7:
            case "end":
              return _context12.stop();
          }
        }, _callee12);
      }))();
    },
    promoteToManager: function promoteToManager(memberId) {
      var _this14 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee13() {
        var result, _error$response10;
        return regeneratorRuntime.wrap(function _callee13$(_context13) {
          while (1) switch (_context13.prev = _context13.next) {
            case 0:
              _context13.next = 2;
              return _this14.$swal.fire({
                title: 'Promote to Manager',
                text: 'Are you sure you want to make this member a manager? They will be able to manage share codes and remove members.',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, promote them',
                cancelButtonText: 'Cancel'
              });
            case 2:
              result = _context13.sent;
              if (result.isConfirmed) {
                _context13.next = 5;
                break;
              }
              return _context13.abrupt("return");
            case 5:
              _context13.prev = 5;
              _context13.next = 8;
              return _this14.$axios.$post("/social/chat/promote-manager/".concat(_this14.$route.params.id, "/").concat(memberId));
            case 8:
              _this14.$toast.success('Member promoted to manager!');
              _context13.next = 11;
              return _this14.loadMembers();
            case 11:
              _context13.next = 16;
              break;
            case 13:
              _context13.prev = 13;
              _context13.t0 = _context13["catch"](5);
              _this14.$toast.error(((_error$response10 = _context13.t0.response) === null || _error$response10 === void 0 || (_error$response10 = _error$response10.data) === null || _error$response10 === void 0 ? void 0 : _error$response10.message) || 'Failed to promote member');
            case 16:
            case "end":
              return _context13.stop();
          }
        }, _callee13, null, [[5, 13]]);
      }))();
    },
    demoteManager: function demoteManager(memberId) {
      var _this15 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee14() {
        var result, _error$response11;
        return regeneratorRuntime.wrap(function _callee14$(_context14) {
          while (1) switch (_context14.prev = _context14.next) {
            case 0:
              _context14.next = 2;
              return _this15.$swal.fire({
                title: 'Remove Manager',
                text: 'Are you sure you want to remove manager privileges from this member?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#f39c12',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Yes, remove privileges',
                cancelButtonText: 'Cancel'
              });
            case 2:
              result = _context14.sent;
              if (result.isConfirmed) {
                _context14.next = 5;
                break;
              }
              return _context14.abrupt("return");
            case 5:
              _context14.prev = 5;
              _context14.next = 8;
              return _this15.$axios.$post("/social/chat/demote-manager/".concat(_this15.$route.params.id, "/").concat(memberId));
            case 8:
              _this15.$toast.success('Manager privileges removed!');
              _context14.next = 11;
              return _this15.loadMembers();
            case 11:
              _context14.next = 16;
              break;
            case 13:
              _context14.prev = 13;
              _context14.t0 = _context14["catch"](5);
              _this15.$toast.error(((_error$response11 = _context14.t0.response) === null || _error$response11 === void 0 || (_error$response11 = _error$response11.data) === null || _error$response11 === void 0 ? void 0 : _error$response11.message) || 'Failed to demote manager');
            case 16:
            case "end":
              return _context14.stop();
          }
        }, _callee14, null, [[5, 13]]);
      }))();
    }
  },
  watch: {
    showSettingsSidebar: function showSettingsSidebar(newValue) {
      if (newValue && this.chat) {
        this.loadMembers(); // Load members for everyone to get nickname info
      }
      if (!newValue) {
        this.showShareCode = false; // Reset share code visibility when sidebar closes
      }
    }
  },
  computed: _idvue_type_script_lang_js_objectSpread(_idvue_type_script_lang_js_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    'user': 'auth/user',
    'mode': 'system/mode',
    'hasNotificationsDisabled': 'auth/hasNotificationsDisabled'
  })), {}, {
    myNickname: function myNickname() {
      var _this16 = this;
      if (!this.members || !this.user || this.members.length === 0) return null;
      var myMembership = this.members.find(function (m) {
        return m.person_id === _this16.user.id;
      });
      return myMembership && myMembership.nickname ? myMembership.nickname : null;
    }
  })
});
// CONCATENATED MODULE: ./pages/crew-chat/_id/index.vue?vue&type=script&lang=js
 /* harmony default export */ var crew_chat_idvue_type_script_lang_js = (_idvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/crew-chat/_id/index.vue?vue&type=style&index=0&id=6d7e80b2&prod&lang=scss
var _idvue_type_style_index_0_id_6d7e80b2_prod_lang_scss = __webpack_require__(2003);

// CONCATENATED MODULE: ./pages/crew-chat/_id/index.vue






/* normalize component */

var _id_component = Object(componentNormalizer["a" /* default */])(
  crew_chat_idvue_type_script_lang_js,
  _idvue_type_template_id_6d7e80b2_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (_id_component.exports);

/***/ })

}]);