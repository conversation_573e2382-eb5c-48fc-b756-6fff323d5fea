(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[87],{

/***/ 1580:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1623);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("587dc6ca", content, true, {"sourceMap":false});

/***/ }),

/***/ 1622:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_circle_vue_vue_type_style_index_0_id_7f61f193_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1580);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_circle_vue_vue_type_style_index_0_id_7f61f193_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_circle_vue_vue_type_style_index_0_id_7f61f193_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1623:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".pulsating-circle{height:30px;left:50%;position:absolute;top:50%;transform:translateX(-50%) translateY(-50%);width:30px}.pulsating-circle:before{animation:pulse-ring 1.25s cubic-bezier(.215,.61,.355,1) infinite;background-color:var(--pulse-color);border-radius:45px;box-sizing:border-box;content:\"\";display:block;height:300%;margin-left:-100%;margin-top:-100%;position:relative;width:300%}.pulsating-circle:after{animation:pulse-dot 1.25s cubic-bezier(.455,.03,.515,.955) -.4s infinite;background-color:var(--circle-color);border-radius:15px;box-shadow:0 0 8px rgba(0,0,0,.3);content:\"\";display:block;height:100%;left:0;position:absolute;top:0;width:100%}@keyframes pulse-ring{0%{transform:scale(.33)}80%,to{opacity:0}}@keyframes pulse-dot{0%{transform:scale(.8)}50%{transform:scale(1)}to{transform:scale(.8)}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1635:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-circle.vue?vue&type=template&id=7f61f193
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "pulsating-circle",
    style: _vm.styleVars
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-circle.vue?vue&type=template&id=7f61f193

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-circle.vue?vue&type=script&lang=js
/* harmony default export */ var app_circlevue_type_script_lang_js = ({
  name: 'app-circle',
  props: ['circle', 'pulse'],
  computed: {
    styleVars: function styleVars() {
      return {
        '--circle-color': this.circle,
        '--pulse-color': this.pulse
      };
    }
  }
});
// CONCATENATED MODULE: ./components/Common/app-circle.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_circlevue_type_script_lang_js = (app_circlevue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-circle.vue?vue&type=style&index=0&id=7f61f193&prod&lang=scss
var app_circlevue_type_style_index_0_id_7f61f193_prod_lang_scss = __webpack_require__(1622);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-circle.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_circlevue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_circle = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1674:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/map/app-map-character.vue?vue&type=template&id=b6510e34&scoped=true
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div');
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/map/app-map-character.vue?vue&type=template&id=b6510e34&scoped=true

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LTooltip.js
var LTooltip = __webpack_require__(2014);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LIcon.js
var LIcon = __webpack_require__(2015);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LMap.js
var LMap = __webpack_require__(1541);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LTileLayer.js
var LTileLayer = __webpack_require__(1542);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LMarker.js
var LMarker = __webpack_require__(1543);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LCircleMarker.js
var LCircleMarker = __webpack_require__(2016);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LCircle.js
var LCircle = __webpack_require__(2017);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LPopup.js
var LPopup = __webpack_require__(2018);

// EXTERNAL MODULE: ./components/Common/app-action-btn.vue + 4 modules
var app_action_btn = __webpack_require__(63);

// EXTERNAL MODULE: ./components/Pages/Dispatch/render-person.vue + 4 modules
var render_person = __webpack_require__(1576);

// EXTERNAL MODULE: ./components/Common/app-draggable.vue + 4 modules
var app_draggable = __webpack_require__(1575);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/map/app-map-character.vue?vue&type=script&lang=js




/* harmony default export */ var app_map_charactervue_type_script_lang_js = ({
  name: "app-map-character",
  props: ['point'],
  components: {
    AppDrager: app_draggable["a" /* default */],
    RenderPerson: render_person["a" /* default */],
    LTooltip: LTooltip["a" /* default */],
    LIcon: LIcon["a" /* default */],
    LMap: LMap["a" /* default */],
    LTileLayer: LTileLayer["a" /* default */],
    LMarker: LMarker["a" /* default */],
    LCircleMarker: LCircleMarker["a" /* default */],
    LCircle: LCircle["a" /* default */],
    LPopup: LPopup["a" /* default */]
  },
  methods: {
    onDropped: function onDropped(fromType, fromIdentifier, toType, toIdentifier) {
      this.$store.dispatch('dispatch/handleDropped', {
        fromType: fromType,
        fromIdentifier: fromIdentifier,
        toType: toType,
        toIdentifier: toIdentifier
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Common/map/app-map-character.vue?vue&type=script&lang=js
 /* harmony default export */ var map_app_map_charactervue_type_script_lang_js = (app_map_charactervue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/map/app-map-character.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  map_app_map_charactervue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  "b6510e34",
  null
  
)

/* harmony default export */ var app_map_character = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1695:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1801);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("1d9d8a96", content, true, {"sourceMap":false});

/***/ }),

/***/ 1696:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1803);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("21fd9472", content, true, {"sourceMap":false});

/***/ }),

/***/ 1800:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_map_vue_vue_type_style_index_0_id_8eb57b42_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1695);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_map_vue_vue_type_style_index_0_id_8eb57b42_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_map_vue_vue_type_style_index_0_id_8eb57b42_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1801:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".map-button{cursor:pointer;padding:10px 10px 15px 15px}.character-marker-class{background-color:rgba(0,0,0,.81);font-size:15px;font-weight:700;padding:5px;width:200px}.character-marker-class:before{color:#404040;content:\"•\";font-size:44px;left:-8px;position:absolute;top:-30px}.leaflet-popup-content,.leaflet-popup-content-wrapper{margin:0!important;padding:0!important}.leaflet-popup-content{color:#f5f5f5}.leaflet-tooltip{background-color:rgba(0,0,0,.81);border:3px solid #000;color:#f5f5f5;font-size:15px;margin:0!important;padding:1px 6px!important}.leaflet-control-attribution{display:none!important}.leaflet-popup-close-button{display:none}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1802:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_map_vue_vue_type_style_index_0_id_daf37bb2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1696);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_map_vue_vue_type_style_index_0_id_daf37bb2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_map_vue_vue_type_style_index_0_id_daf37bb2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1803:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, "#dispatchCanvas{border:1px solid gray}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2028:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/dispatch/map.vue?vue&type=template&id=daf37bb2
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('app-map', {
    attrs: {
      "points": _vm.points
    }
  }), _vm._v(" "), _c('div')], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/dispatch/map.vue?vue&type=template&id=daf37bb2

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(51);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-map.vue?vue&type=template&id=8eb57b42




var app_mapvue_type_template_id_8eb57b42_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticStyle: {
      "overflow-x": "hidden",
      "overflow-y": "hidden"
    }
  }, [_vm._l(_vm.toggles, function (value, name) {
    return _c('span', {
      staticClass: "map-button",
      class: {
        'text-primary': value
      },
      on: {
        "click": function click($event) {
          _vm.toggles[name] = !value;
        }
      }
    }, [_vm._v("\n      " + _vm._s(name.replace('_', ' ').toUpperCase()) + "\n    ")]);
  }), _vm._v(" "), _c('l-map', {
    ref: "map",
    staticStyle: {
      "height": "740px",
      "width": "100%"
    },
    attrs: {
      "zoom": _vm.zoom,
      "options": _vm.mapOptions
    },
    on: {
      "ready": _vm.onReady
    }
  }, [_c('l-tile-layer', {
    attrs: {
      "url": _vm.url,
      "options": _vm.tileOptions,
      "attribution": _vm.attribution
    }
  }), _vm._v(" "), _vm._l(_vm.points.filter(function (p) {
    return p.type === 'character';
  }), function (point, index) {
    return _vm.toggles.police_officers ? _c('l-marker', {
      key: index,
      attrs: {
        "index": index,
        "lat-lng": point.coords
      }
    }, [_c('l-icon', {
      attrs: {
        "icon-anchor": [0, 0]
      }
    }, [!point.character.active_in_dispatch ? _c('app-circle', {
      attrs: {
        "circle": "blue",
        "pulse": "limegreen"
      }
    }) : point.character.active_in_dispatch ? _c('app-circle', {
      attrs: {
        "circle": "blue",
        "pulse": "yellow"
      }
    }) : _vm._e()], 1), _vm._v(" "), _c('l-tooltip', {
      attrs: {
        "options": {
          permanent: true,
          direction: 'left'
        }
      }
    }, [_c('render-person', {
      attrs: {
        "person": point.character,
        "hideLocation": true
      }
    })], 1)], 1) : _vm._e();
  }), _vm._v(" "), _vm._l(_vm.points.filter(function (p) {
    return p.type === 'alert';
  }), function (point, index) {
    return _vm.toggles.police_alerts ? _c('l-marker', {
      key: index,
      attrs: {
        "index": index,
        "lat-lng": point.coords
      }
    }, [_c('l-icon', {
      attrs: {
        "icon-anchor": [21, 15],
        "iconSize": [32, 32],
        "icon-url": "http://dev-res.blrp.net/map/dot.png"
      }
    }), _vm._v(" "), _c('l-tooltip', {
      attrs: {
        "icon-anchor": [0, 0],
        "options": {
          permanent: true,
          direction: 'right'
        }
      }
    }, [_vm._v("\n          " + _vm._s(point.alert.badge) + "\n        ")]), _vm._v(" "), _c('l-popup', {
      attrs: {
        "options": {
          minWidth: 600,
          maxWidth: 600,
          className: 'map-popup'
        }
      }
    })], 1) : _vm._e();
  })], 2)], 2);
};
var app_mapvue_type_template_id_8eb57b42_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-map.vue?vue&type=template&id=8eb57b42

// EXTERNAL MODULE: ./node_modules/leaflet/dist/leaflet-src.js
var leaflet_src = __webpack_require__(105);
var leaflet_src_default = /*#__PURE__*/__webpack_require__.n(leaflet_src);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LTooltip.js
var LTooltip = __webpack_require__(2014);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LIcon.js
var LIcon = __webpack_require__(2015);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LMap.js
var LMap = __webpack_require__(1541);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LTileLayer.js
var LTileLayer = __webpack_require__(1542);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LMarker.js
var LMarker = __webpack_require__(1543);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LCircleMarker.js
var LCircleMarker = __webpack_require__(2016);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LCircle.js
var LCircle = __webpack_require__(2017);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LPopup.js
var LPopup = __webpack_require__(2018);

// EXTERNAL MODULE: ./node_modules/leaflet/dist/leaflet.css
var leaflet = __webpack_require__(1653);

// EXTERNAL MODULE: ./components/Pages/Dispatch/render-person.vue + 4 modules
var render_person = __webpack_require__(1576);

// EXTERNAL MODULE: ./components/Common/app-action-btn.vue + 4 modules
var app_action_btn = __webpack_require__(63);

// EXTERNAL MODULE: ./components/Common/app-draggable.vue + 4 modules
var app_draggable = __webpack_require__(1575);

// EXTERNAL MODULE: ./components/Common/map/app-map-character.vue + 4 modules
var app_map_character = __webpack_require__(1674);

// EXTERNAL MODULE: ./components/Common/app-circle.vue + 4 modules
var app_circle = __webpack_require__(1635);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-map.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }










var center_x = 117.3;
var center_y = 172.8;
var scale_x = 0.02072;
var scale_y = 0.0205;
var CUSTOM_CRS = leaflet_src_default.a.extend({}, leaflet_src_default.a.CRS.Simple, {
  projection: leaflet_src_default.a.Projection.LonLat,
  scale: function scale(zoom) {
    return Math.pow(2, zoom);
  },
  zoom: function zoom(sc) {
    return Math.log(sc) / 0.6931471805599453;
  },
  distance: function distance(pos1, pos2) {
    var x_difference = pos2.lng - pos1.lng;
    var y_difference = pos2.lat - pos1.lat;
    return Math.sqrt(x_difference * x_difference + y_difference * y_difference);
  },
  transformation: new leaflet_src_default.a.Transformation(scale_x, center_x, -scale_y, center_y),
  infinite: true
});
/* harmony default export */ var app_mapvue_type_script_lang_js = ({
  name: "app-map",
  components: {
    AppCircle: app_circle["a" /* default */],
    AppMapCharacter: app_map_character["a" /* default */],
    AppDrager: app_draggable["a" /* default */],
    AppActionBtn: app_action_btn["a" /* default */],
    RenderPerson: render_person["a" /* default */],
    LTooltip: LTooltip["a" /* default */],
    LIcon: LIcon["a" /* default */],
    LMap: LMap["a" /* default */],
    LTileLayer: LTileLayer["a" /* default */],
    LMarker: LMarker["a" /* default */],
    LCircleMarker: LCircleMarker["a" /* default */],
    LCircle: LCircle["a" /* default */],
    LPopup: LPopup["a" /* default */]
  },
  props: ['points'],
  data: function data() {
    return {
      toggles: {
        police_officers: true,
        police_alerts: true
      },
      map: null,
      zoom: 3,
      center: Object(leaflet_src["latLng"])(47.56, 7.59),
      url: 'http://dev-res.blrp.net/map/mapStyles/styleAtlas/{z}/{x}/{y}.jpg',
      attribution: '',
      mapOptions: {
        crs: CUSTOM_CRS,
        zoomSnap: 0.4
      },
      tileOptions: {
        minZoom: 1.8,
        maxZoom: 11,
        maxNativeZoom: 5
      }
    };
  },
  methods: {
    onReady: function onReady() {
      this.map = this.$refs.map;

      // this.map.on('zoomed', function () {
      //   var zoomlevel = map.getZoom();
      //   if (zoomlevel  <10){
      //     if (map.hasLayer(points)) {
      //       map.removeLayer(points);
      //     } else {
      //       console.log("no point layer active");
      //     }
      //   }
      //   if (zoomlevel >= 10){
      //     if (map.hasLayer(points)){
      //       console.log("layer already added");
      //     } else {
      //       map.addLayer(points);
      //     }
      //   }
      //   console.log("Current Zoom Level =" + zoomlevel)
      //
      // });
      // console.log('map is ready!')
    },
    onDropped: function onDropped(fromType, fromIdentifier, toType, toIdentifier) {
      this.$store.dispatch('dispatch/handleDropped', {
        fromType: fromType,
        fromIdentifier: fromIdentifier,
        toType: toType,
        toIdentifier: toIdentifier
      });
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    getCharacter: 'dispatch/getCharacter',
    characters: 'dispatch/characters'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-map.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_mapvue_type_script_lang_js = (app_mapvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-map.vue?vue&type=style&index=0&id=8eb57b42&prod&lang=scss
var app_mapvue_type_style_index_0_id_8eb57b42_prod_lang_scss = __webpack_require__(1800);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-map.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_mapvue_type_script_lang_js,
  app_mapvue_type_template_id_8eb57b42_render,
  app_mapvue_type_template_id_8eb57b42_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_map = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/dispatch/map.vue?vue&type=script&lang=js







function mapvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function mapvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? mapvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : mapvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }








/* harmony default export */ var mapvue_type_script_lang_js = ({
  components: {
    AppMap: app_map
  },
  data: function data() {
    return {
      interval: null
    };
  },
  created: function created() {
    var _this = this;
    this.interval = setInterval(function () {
      _this.$store.dispatch('crud/fetchItems');
    }, 1000);
  },
  beforeDestroy: function beforeDestroy() {
    clearInterval(this.interval);
  },
  computed: mapvue_type_script_lang_js_objectSpread({
    points: function points() {
      var characters = this.characters.map(function (char) {
        return {
          coords: Object(leaflet_src["latLng"])(char.synced_position.y, char.synced_position.x),
          type: 'character',
          character: char
        };
      });
      var alerts = this.alerts.filter(function (alert) {
        return alert.pos && alert.pos.x;
      }).map(function (alert) {
        return {
          coords: Object(leaflet_src["latLng"])(alert.pos.y, alert.pos.x),
          type: 'alert',
          alert: alert
        };
      });
      return [].concat(Object(toConsumableArray["a" /* default */])(characters), Object(toConsumableArray["a" /* default */])(alerts));
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    enabled: 'dispatch/enabled',
    characters: 'dispatch/characters',
    alerts: 'crud/items',
    globalRadioChannels: 'dispatch/globalRadioChannels',
    getCharacter: 'dispatch/getCharacter'
  }))
});
// CONCATENATED MODULE: ./pages/cad/dispatch/map.vue?vue&type=script&lang=js
 /* harmony default export */ var dispatch_mapvue_type_script_lang_js = (mapvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/cad/dispatch/map.vue?vue&type=style&index=0&id=daf37bb2&prod&lang=scss
var mapvue_type_style_index_0_id_daf37bb2_prod_lang_scss = __webpack_require__(1802);

// CONCATENATED MODULE: ./pages/cad/dispatch/map.vue






/* normalize component */

var map_component = Object(componentNormalizer["a" /* default */])(
  dispatch_mapvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var map = __webpack_exports__["default"] = (map_component.exports);

/***/ })

}]);