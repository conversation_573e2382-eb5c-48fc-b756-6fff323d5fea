(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[0],{

/***/ 1565:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1574);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("4b66da15", content, true, {"sourceMap":false});

/***/ }),

/***/ 1569:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSkeletonLoader/VSkeletonLoader.js
var VSkeletonLoader = __webpack_require__(1535);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(78);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts.vue?vue&type=template&id=e731c186






var social_postsvue_type_template_id_e731c186_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "social-posts-container"
  }, [_c(transitions["i" /* VSlideYTransition */], [_vm.showNewPostBanner && !_vm.isAtTop ? _c('div', {
    staticClass: "new-post-notification",
    on: {
      "click": _vm.scrollToTop
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "notification-icon",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-arrow-up")]), _vm._v(" "), _c('span', {
    staticClass: "notification-text"
  }, [_vm._v(_vm._s(_vm.newPostCount) + " new post" + _vm._s(_vm.newPostCount > 1 ? 's' : ''))]), _vm._v(" "), _c(VIcon["a" /* default */], {
    staticClass: "notification-close",
    attrs: {
      "small": ""
    },
    on: {
      "click": function click($event) {
        $event.stopPropagation();
        return _vm.resetNewPostCounter.apply(null, arguments);
      }
    }
  }, [_vm._v("fa-solid fa-times")])], 1) : _vm._e()]), _vm._v(" "), _vm.thread ? _c('app-socket-channel-presence-listener', {
    attrs: {
      "channel": "threads.".concat(_vm.thread.id),
      "listeners": {
        'SocialPostUpdated': _vm.handleSocialPostUpdated,
        'SocialPostRemoved': _vm.handleSocialPostRemoved,
        'SocialPostAdded': _vm.handleSocialPostAdded
      },
      "show-avatars": false
    }
  }) : _c('app-socket-channel-presence-listener', {
    attrs: {
      "channel": "life-invader",
      "listeners": {
        'SocialPostUpdated': _vm.handleSocialPostUpdated,
        'SocialPostRemoved': _vm.handleSocialPostRemoved,
        'SocialPostAdded': _vm.handleSocialPostAdded
      },
      "show-avatars": false
    }
  }), _vm._v(" "), _vm.thread && _vm.thread._can_create_posts && _vm.allowCreate ? _c('social-create-post', {
    ref: "creator",
    attrs: {
      "hotkeyActive": _vm.hotkeyActive,
      "preAppend": _vm.preAppend,
      "thread": _vm.thread,
      "limit": _vm.limit,
      "privacyType": _vm.privacyType
    },
    on: {
      "created": _vm.onPostCreated
    }
  }) : _vm._e(), _vm._v(" "), _vm.currentFilter ? _c('div', {
    staticClass: "d-flex mt-3 justify-content-center ml-3"
  }, [_vm._l(_vm.filters, function (filter, index) {
    return _c(VBtn["a" /* default */], {
      key: index,
      staticClass: "mr-3",
      attrs: {
        "small": "",
        "color": filter.name === _vm.currentFilter.name ? 'primary' : 'dark'
      },
      on: {
        "click": function click($event) {
          _vm.currentFilter = filter;
        }
      }
    }, [_c(VIcon["a" /* default */], {
      attrs: {
        "x-small": ""
      }
    }, [_vm._v("\n          " + _vm._s(filter.icon) + "\n        ")]), _vm._v(" "), _c('span', {
      staticClass: "ml-2"
    }, [_vm._v("\n          " + _vm._s(filter.name) + "\n        ")])], 1);
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "small": ""
    },
    on: {
      "click": _vm.reset
    }
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "x-small": ""
    }
  }, [_vm._v("\n          fa-solid fa-arrows-rotate\n        ")])], 1)], 2) : _vm._e(), _vm._v(" "), _vm.loading ? _c('div', [_c(VSkeletonLoader["a" /* default */], {
    staticClass: "mt-3",
    staticStyle: {
      "background-color": "black"
    },
    attrs: {
      "type": "card"
    }
  }), _vm._v(" "), _c(VSkeletonLoader["a" /* default */], {
    staticClass: "mt-3",
    staticStyle: {
      "background-color": "black"
    },
    attrs: {
      "type": "card"
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.currentFilter ? _c('app-card-items', {
    staticClass: "mt-3",
    attrs: {
      "items": _vm.items
    },
    on: {
      "chosen": _vm.chosen,
      "nothingChosen": function nothingChosen($event) {
        _vm.hotkeyActive = true;
      },
      "stuffChosen": function stuffChosen($event) {
        _vm.hotkeyActive = false;
      },
      "enteredView": _vm._enteredView
    },
    scopedSlots: _vm._u([{
      key: "render",
      fn: function fn(_ref) {
        var item = _ref.item;
        return [item ? _c('social-posts-item', {
          ref: "item-".concat(item.id),
          attrs: {
            "item": item,
            "thread": _vm.thread || item.thread
          }
        }) : _vm._e()];
      }
    }], null, false, 336494870)
  }) : _vm._e(), _vm._v(" "), _c('infinite-loading', {
    attrs: {
      "spinner": "bubbles"
    },
    on: {
      "infinite": _vm.index
    }
  }, [_c('div', {
    staticClass: "p-5 text-truncate",
    attrs: {
      "slot": "no-results"
    },
    slot: "no-results"
  }, [_vm._v("No more entries")])])], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-posts.vue?vue&type=template&id=e731c186

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find-index.js
var es_array_find_index = __webpack_require__(113);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.splice.js
var es_array_splice = __webpack_require__(133);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./components/Pages/Social/social-posts-item.vue + 34 modules
var social_posts_item = __webpack_require__(1663);

// EXTERNAL MODULE: ./node_modules/vue-infinite-loading/dist/vue-infinite-loading.js
var vue_infinite_loading = __webpack_require__(1568);
var vue_infinite_loading_default = /*#__PURE__*/__webpack_require__.n(vue_infinite_loading);

// EXTERNAL MODULE: ./components/Common/app-auto-textarea.vue + 4 modules
var app_auto_textarea = __webpack_require__(1642);

// EXTERNAL MODULE: ./components/Pages/Social/social-create-post.vue + 9 modules
var social_create_post = __webpack_require__(1664);

// EXTERNAL MODULE: ./components/Common/app-card-items.vue + 9 modules
var app_card_items = __webpack_require__(1571);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./mixins/Reloadable.js
var Reloadable = __webpack_require__(1639);

// EXTERNAL MODULE: ./components/Common/app-socket-channel-presence-listener.vue + 4 modules
var app_socket_channel_presence_listener = __webpack_require__(497);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts.vue?vue&type=script&lang=js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }






























/* harmony default export */ var social_postsvue_type_script_lang_js = ({
  name: "social-posts",
  components: {
    AppSocketChannelPresenceListener: app_socket_channel_presence_listener["a" /* default */],
    AppCardItems: app_card_items["a" /* default */],
    SocialCreatePost: social_create_post["a" /* default */],
    AppAutoTextarea: app_auto_textarea["a" /* default */],
    SocialPostsItem: social_posts_item["a" /* default */],
    InfiniteLoading: vue_infinite_loading_default.a
  },
  props: ['thread', 'privacyType', 'preAppend', 'showFilters', 'allowCreate', 'endpoint', 'limit', 'filterPosterPersonId'],
  mixins: [Reloadable["a" /* default */]],
  data: function data() {
    return {
      items: [],
      filter: null,
      loading: false,
      filterIndex: null,
      toMarkRead: [],
      showNewPostBanner: false,
      newPostBannerTimer: null,
      newPostCount: 0,
      isAtTop: false,
      // Default to false so banner shows
      currentFilter: {
        name: 'Recent',
        icon: 'fa-solid fa-star-of-life'
      },
      filters: [{
        name: 'Recent',
        icon: 'fa-solid fa-arrow-up',
        selected: 1
      }, {
        name: 'Oldest',
        icon: 'fa-solid fa-arrow-down',
        selected: 0
      }, {
        name: 'Reactions',
        icon: 'fa-solid fa-filter',
        selected: 0
      }
      // { name: 'top-posts', icon: 'fa-solid fa-trophy-star', type: 'sort', value: 'desc' },
      // { name: 'top-mentions', icon: 'fa-solid fa-at', type: 'sort', value: 'desc' },

      // { name: 'text', icon: 'fa-solid fa-align-left', type: 'filter-type', value: 'text' },
      // { name: 'images', icon: 'fa-solid fa-image', type: 'filter-type', value: 'image' },
      // { name: 'locations ', icon: 'fa-solid fa-location-arrow', type: 'filter-type', value: 'locations' },
      ],
      page: 1,
      nextPage: 1,
      lastPage: 1,
      perPage: 50,
      isInit: true,
      hotkeyActive: true,
      ready: false,
      interval: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _this.currentFilter = _this.filters[0];
            _this.setupScrollListener();

            // Fetch possible posters once for all post items
            _context.next = 4;
            return _this.$store.dispatch('auth/fetchPossiblePosters');
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  methods: {
    index: function index($state) {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var _this2$thread;
        var url, _this2$thread2, res, _iterator, _step, _loop;
        return regeneratorRuntime.wrap(function _callee2$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              if (!_this2.loading) {
                _context3.next = 2;
                break;
              }
              return _context3.abrupt("return", console.log('✔ Ignore indexing posts because it was already requested'));
            case 2:
              if (!$state) _this2.nextPage--;
              if ((_this2$thread = _this2.thread) !== null && _this2$thread !== void 0 && _this2$thread.id) {
                _context3.next = 5;
                break;
              }
              return _context3.abrupt("return", console.log('✔ Ignore indexing posts because there is no thread'));
            case 5:
              if (_this2.endpoint === '/social/list') {
                // For general social list endpoint, don't append thread ID
                url = "".concat(_this2.endpoint, "?page=").concat(_this2.nextPage, "&perPage=").concat(_this2.perPage, "&filter=").concat(_this2.currentFilter.name);

                // Add filterPosterPersonId if provided
                if (_this2.filterPosterPersonId) {
                  url += "&filterPosterPersonId=".concat(_this2.filterPosterPersonId);
                }
              } else {
                // For other endpoints, append thread ID as before
                url = "".concat(_this2.endpoint).concat((_this2$thread2 = _this2.thread) === null || _this2$thread2 === void 0 ? void 0 : _this2$thread2.id, "?page=").concat(_this2.nextPage, "&perPage=").concat(_this2.perPage, "&filter=").concat(_this2.currentFilter.name);
              }
              _context3.next = 8;
              return _this2.$axios.$get(url);
            case 8:
              res = _context3.sent;
              _this2.nextPage = _this2.nextPage + 1;
              _this2.loading = false;
              _iterator = _createForOfIteratorHelper(res.data);
              _context3.prev = 12;
              _loop = /*#__PURE__*/regeneratorRuntime.mark(function _loop() {
                var item, index;
                return regeneratorRuntime.wrap(function _loop$(_context2) {
                  while (1) switch (_context2.prev = _context2.next) {
                    case 0:
                      item = _step.value;
                      index = _this2.items.findIndex(function (i) {
                        return i.id === item.id;
                      });
                      if (index > -1) {
                        _this2.$set(_this2.items, index, item);
                      } else {
                        _this2.items.push(item);
                      }
                    case 3:
                    case "end":
                      return _context2.stop();
                  }
                }, _loop);
              });
              _iterator.s();
            case 15:
              if ((_step = _iterator.n()).done) {
                _context3.next = 19;
                break;
              }
              return _context3.delegateYield(_loop(), "t0", 17);
            case 17:
              _context3.next = 15;
              break;
            case 19:
              _context3.next = 24;
              break;
            case 21:
              _context3.prev = 21;
              _context3.t1 = _context3["catch"](12);
              _iterator.e(_context3.t1);
            case 24:
              _context3.prev = 24;
              _iterator.f();
              return _context3.finish(24);
            case 27:
              if ($state) {
                if (!res.next_page_url) {
                  $state.complete();
                } else {
                  $state.loaded();
                }
              }
            case 28:
            case "end":
              return _context3.stop();
          }
        }, _callee2, null, [[12, 21, 24, 27]]);
      }))();
    },
    // Socket Events
    handleSocialPostUpdated: function handleSocialPostUpdated(_ref) {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var post, fromPerson, index;
        return regeneratorRuntime.wrap(function _callee3$(_context4) {
          while (1) switch (_context4.prev = _context4.next) {
            case 0:
              post = _ref.post, fromPerson = _ref.fromPerson;
              index = _this3.items.findIndex(function (p) {
                return p.id === post.id;
              });
              if (index > -1) {
                vue_runtime_esm["default"].set(_this3.items[index], 'reactions', post.reactions);
                vue_runtime_esm["default"].set(_this3.items[index], 'comments', post.comments);
                vue_runtime_esm["default"].set(_this3.items[index], 'content', post.content);
                vue_runtime_esm["default"].set(_this3.items[index], 'mentions', post.mentions);
                vue_runtime_esm["default"].set(_this3.items[index], 'is_public', post.is_public);
              }
            case 3:
            case "end":
              return _context4.stop();
          }
        }, _callee3);
      }))();
    },
    handleSocialPostRemoved: function handleSocialPostRemoved(_ref2) {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee4() {
        var uuid, fromPerson, index;
        return regeneratorRuntime.wrap(function _callee4$(_context5) {
          while (1) switch (_context5.prev = _context5.next) {
            case 0:
              uuid = _ref2.uuid, fromPerson = _ref2.fromPerson;
              index = _this4.items.findIndex(function (p) {
                return p.uuid === uuid;
              });
              if (index > -1) {
                _this4.items.splice(index, 1);
              }
            case 3:
            case "end":
              return _context5.stop();
          }
        }, _callee4);
      }))();
    },
    handleSocialPostAdded: function handleSocialPostAdded(data) {
      var _this5 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee5() {
        return regeneratorRuntime.wrap(function _callee5$(_context6) {
          while (1) switch (_context6.prev = _context6.next) {
            case 0:
              _this5.items.unshift(data.entity);

              // Increment counter
              _this5.newPostCount++;

              // Show banner if user is scrolled down
              if (!_this5.isAtTop) {
                _this5.showNewPostBanner = true;

                // Clear any existing timer and restart
                if (_this5.newPostBannerTimer) {
                  clearTimeout(_this5.newPostBannerTimer);
                }

                // Auto-hide banner after 8 seconds
                _this5.newPostBannerTimer = setTimeout(function () {
                  _this5.hideNewPostBanner();
                }, 8000);
              }
            case 3:
            case "end":
              return _context6.stop();
          }
        }, _callee5);
      }))();
    },
    _enteredView: function _enteredView(index) {
      var _this6 = this;
      if (index > -1) {
        if (!this.items[index].read) {
          this.toMarkRead.push(this.items[index].id);
          setTimeout(function () {
            _this6.items[index].read = true;
          }, 1200);
        }
      }
    },
    chosen: function chosen(item) {
      if (!item) {
        this.$refs.creator.focusInput();
        this.focusing = true;
      } else {
        this.$refs["item-".concat(item.id)].startReplying();
      }
    },
    _enter: function _enter() {
      // this.$refs.creator.focusInput()
    },
    changeFilter: function changeFilter(filter) {
      var _this7 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee6() {
        return regeneratorRuntime.wrap(function _callee6$(_context7) {
          while (1) switch (_context7.prev = _context7.next) {
            case 0:
              if (!(_this7.filter === filter)) {
                _context7.next = 2;
                break;
              }
              return _context7.abrupt("return", false);
            case 2:
              _this7.filter = filter;
              _this7.reset();
            case 4:
            case "end":
              return _context7.stop();
          }
        }, _callee6);
      }))();
    },
    refresh: function refresh() {
      var _this8 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee7() {
        return regeneratorRuntime.wrap(function _callee7$(_context8) {
          while (1) switch (_context8.prev = _context8.next) {
            case 0:
              _this8.reset();
            case 1:
            case "end":
              return _context8.stop();
          }
        }, _callee7);
      }))();
    },
    reset: function reset() {
      var _this9 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee8() {
        return regeneratorRuntime.wrap(function _callee8$(_context9) {
          while (1) switch (_context9.prev = _context9.next) {
            case 0:
              _this9.nextPage = 1;
              _this9.items = [];
              _this9.resetNewPostCounter(); // Clear banner and counter when resetting
              _this9.index();
            case 4:
            case "end":
              return _context9.stop();
          }
        }, _callee8);
      }))();
    },
    onPostCreated: function onPostCreated() {
      // Reset the posts list to show the new post
      this.reset();

      // Scroll to top to show the new post
      this.scrollToTop();
    },
    syncIndex: function syncIndex() {
      var _this10 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee9() {
        return regeneratorRuntime.wrap(function _callee9$(_context10) {
          while (1) switch (_context10.prev = _context10.next) {
            case 0:
              _this10.index(null);
            case 1:
            case "end":
              return _context10.stop();
          }
        }, _callee9);
      }))();
    },
    setupScrollListener: function setupScrollListener() {
      var _this11 = this;
      // Use a simpler approach - listen to all scroll events globally
      this.$nextTick(function () {
        // Start with isAtTop = false so banner shows initially
        _this11.isAtTop = false;
        var handleScroll = function handleScroll(event) {
          // Try to get scroll position from various sources
          var scrollTop = 0;
          if (event.target === document) {
            scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          } else if (event.target.scrollTop !== undefined) {
            scrollTop = event.target.scrollTop;
          }
          _this11.isAtTop = scrollTop < 50; // Consider "at top" if within 50px of top

          // Reset counter if user scrolls to top (they'll see new posts)
          if (_this11.isAtTop && _this11.showNewPostBanner) {
            _this11.resetNewPostCounter();
          }
        };

        // Listen to ALL scroll events globally
        document.addEventListener('scroll', handleScroll, {
          passive: true,
          capture: true
        });
        window.addEventListener('scroll', handleScroll, {
          passive: true
        });

        // Also listen to specific elements that might scroll
        var potentialScrollers = document.querySelectorAll('*');
        potentialScrollers.forEach(function (element) {
          var style = window.getComputedStyle(element);
          if (style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') {
            element.addEventListener('scroll', handleScroll, {
              passive: true
            });
          }
        });

        // Store for cleanup
        _this11.handleScroll = handleScroll;
        _this11.globalScrollSetup = true;
      });
    },
    hideNewPostBanner: function hideNewPostBanner() {
      this.showNewPostBanner = false;
      if (this.newPostBannerTimer) {
        clearTimeout(this.newPostBannerTimer);
        this.newPostBannerTimer = null;
      }
    },
    resetNewPostCounter: function resetNewPostCounter() {
      this.newPostCount = 0;
      this.hideNewPostBanner();
    },
    scrollToTop: function scrollToTop() {
      var _this12 = this;
      // Find ALL elements that are currently scrolled and try to scroll them
      var allElements = document.querySelectorAll('*');
      var scrolledAny = false;

      // Find and scroll elements that are currently scrolled
      allElements.forEach(function (element) {
        if (element.scrollTop > 0) {
          try {
            element.scrollTo({
              top: 0,
              behavior: 'smooth'
            });
            scrolledAny = true;
          } catch (e) {
            try {
              element.scrollTop = 0;
              scrolledAny = true;
            } catch (e2) {
              // Continue to next element
            }
          }
        }
      });

      // Also try window scroll
      try {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
        scrolledAny = true;
      } catch (e) {
        // Continue
      }

      // If nothing worked, try force scrolling overflow elements
      if (!scrolledAny) {
        allElements.forEach(function (element) {
          var style = window.getComputedStyle(element);
          if ((style.overflow === 'auto' || style.overflow === 'scroll' || style.overflowY === 'auto' || style.overflowY === 'scroll') && element.scrollHeight > element.clientHeight) {
            try {
              element.scrollTop = 0;
            } catch (e) {
              // Continue
            }
          }
        });
      }

      // Reset counter after scrolling (user will see new posts)
      setTimeout(function () {
        _this12.resetNewPostCounter();
      }, 500);
    },
    _onSocketUpdate: function _onSocketUpdate(_ref3) {
      var index = _ref3.index,
        action = _ref3.action,
        post = _ref3.post;
      if (action === 'removed') {
        this.items.splice(index, 1);
      }
    }
  },
  beforeDestroy: function beforeDestroy() {
    // Mark posts as read before destroying component
    if (this.toMarkRead.length > 0) {
      this.$axios.$post("/social/mark-post-read/".concat(this.toMarkRead));
    }

    // Clean up the banner timer
    if (this.newPostBannerTimer) {
      clearTimeout(this.newPostBannerTimer);
    }

    // Clean up scroll listeners
    if (this.globalScrollSetup && this.handleScroll) {
      document.removeEventListener('scroll', this.handleScroll, {
        capture: true
      });
      window.removeEventListener('scroll', this.handleScroll);
    }
  },
  watch: {
    currentFilter: function currentFilter() {
      this.reset();
    },
    filterIndex: function filterIndex() {
      this.changeFilter(this.filters[this.filterIndex]);
    },
    filterTop: function filterTop() {
      this.reset();
    },
    filterTopMentions: function filterTopMentions() {
      this.reset();
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    focusing: 'system/focusing'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Social/social-posts.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_postsvue_type_script_lang_js = (social_postsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Social/social-posts.vue?vue&type=style&index=0&id=e731c186&prod&lang=scss
var social_postsvue_type_style_index_0_id_e731c186_prod_lang_scss = __webpack_require__(1776);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Social/social-posts.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Social_social_postsvue_type_script_lang_js,
  social_postsvue_type_template_id_e731c186_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_posts = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1571:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VLazy/VLazy.js
var VLazy = __webpack_require__(1563);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=template&id=809e8f90


var app_card_itemsvue_type_template_id_809e8f90_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VLazy["a" /* default */], [_c('div', {
    staticClass: "page-sub-scroller no-drag"
  }, [_vm._t("header"), _vm._v(" "), _vm._l(_vm.items, function (item, index) {
    return _c('render-item', {
      key: item.id,
      class: {
        'item-selected': _vm.currentIndex === index,
        'socket-active': _vm.visible[_vm.currentIndex]
      },
      attrs: {
        "index": index
      },
      on: {
        "enteredScreen": _vm._enteredScreen,
        "leftScreen": _vm._leftScreen
      },
      scopedSlots: _vm._u([{
        key: "default",
        fn: function fn(_ref) {
          var ref = _ref.ref;
          return [_vm._t("render", null, {
            "item": item,
            "index": index
          })];
        }
      }], null, true)
    });
  })], 2)]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=template&id=809e8f90

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/directives/intersect/index.js
var intersect = __webpack_require__(175);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=template&id=4ba6f808


var render_itemvue_type_template_id_4ba6f808_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    directives: [{
      def: intersect["a" /* default */],
      name: "intersect",
      rawName: "v-intersect",
      value: {
        handler: _vm._onItemShownScreen,
        options: {
          threshold: [0, 0.5, 1.0]
        }
      },
      expression: "{ handler: _onItemShownScreen, options: {  threshold: [0, 0.5, 1.0]  } }"
    }],
    ref: "item"
  }, [_vm._t("default")], 2);
};
var render_itemvue_type_template_id_4ba6f808_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=template&id=4ba6f808

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.description.js
var es_symbol_description = __webpack_require__(80);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.iterator.js
var es_symbol_iterator = __webpack_require__(95);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.from.js
var es_array_from = __webpack_require__(88);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.iterator.js
var es_string_iterator = __webpack_require__(76);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/render-item.vue?vue&type=script&lang=js

















function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
function _createForOfIteratorHelper(r, e) { var t = "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && "number" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }

/* harmony default export */ var render_itemvue_type_script_lang_js = ({
  name: 'render-item',
  components: {},
  props: ['index'],
  data: function data() {
    return {
      isShowingOnScreen: false
    };
  },
  created: function created() {},
  methods: {
    _onItemShownScreen: function _onItemShownScreen(entries, observer) {
      var _iterator = _createForOfIteratorHelper(entries),
        _step;
      try {
        for (_iterator.s(); !(_step = _iterator.n()).done;) {
          var entry = _step.value;
          this.isShowingOnScreen = entries[0].intersectionRatio >= 0.5;
          if (this.isShowingOnScreen) {
            this.$emit('enteredScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'red'
          } else {
            this.$emit('leftScreen', this.index);
            // this.$refs.item.style.backgroundColor = 'none'
          }
        }
      } catch (err) {
        _iterator.e(err);
      } finally {
        _iterator.f();
      }
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./components/Common/render-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_render_itemvue_type_script_lang_js = (render_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/render-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_render_itemvue_type_script_lang_js,
  render_itemvue_type_template_id_4ba6f808_render,
  render_itemvue_type_template_id_4ba6f808_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var render_item = (component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-card-items.vue?vue&type=script&lang=js








function app_card_itemsvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function app_card_itemsvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? app_card_itemsvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : app_card_itemsvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var app_card_itemsvue_type_script_lang_js = ({
  name: 'app-card-items',
  components: {
    RenderItem: render_item,
    AppCard: app_card["a" /* default */]
  },
  props: ['items', 'socketChannel'],
  data: function data() {
    return {
      visible: {},
      currentIndex: -1
    };
  },
  methods: {
    _onPrevious: function _onPrevious() {
      if (this.currentIndex > -2) {
        var _this$$refs$;
        this.currentIndex--;
        (_this$$refs$ = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$ === void 0 || _this$$refs$.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$2;
        this.currentIndex = this.items.length - 1;
        (_this$$refs$2 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$2 === void 0 || _this$$refs$2.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onNext: function _onNext() {
      if (this.currentIndex < this.items.length - 2) {
        var _this$$refs$3;
        this.currentIndex++;
        (_this$$refs$3 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$3 === void 0 || _this$$refs$3.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      } else {
        var _this$$refs$4;
        this.currentIndex = 0;
        (_this$$refs$4 = this.$refs["card-".concat(this.currentIndex)][0]) === null || _this$$refs$4 === void 0 || _this$$refs$4.$el.scrollIntoView({
          block: 'end',
          behavior: 'smooth'
        });
      }
    },
    _onEnter: function _onEnter() {
      var item = this.items[this.currentIndex];
      this.$emit('chosen', item);
    },
    _enteredScreen: function _enteredScreen(index) {
      if (this.visible[index]) {
        return;
      }
      this.visible[index] = true;
      this.$emit('enteredView', index);

      // if (this.socketChannel && this.items[index].uuid) {
      //   const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
      //   if ( window.echo ) {
      //     window.echo.listen(channel, 'SocialPostUpdated', ({ post, fromPerson }) => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'update',
      //         post: post,
      //       })
      //     })
      //
      //     window.echo.listen(channel, 'SocialPostRemoved', () => {
      //       this.$emit('socket', {
      //         index: index,
      //         action: 'removed',
      //         post: {},
      //       })
      //     })
      //   }
      //
      // } else {
      //   console.error('socket hook failed - no uuid exists - record too old')
      // }
    },
    _leftScreen: function _leftScreen(index) {
      if (this.visible[index]) {
        // const channel = `${ this.socketChannel }.${ this.items[index].uuid }`
        //
        // // ts-ignore
        // window.echo.leave(channel)
        //
        // delete this.visible[index]
      }
    }
  },
  beforeDestroy: function beforeDestroy() {
    // for (const [ index, value ] of Object.entries(this.visible)) {
    //   const channel = `${ this.socketChannel }.${ this.items[index].id }`
    //
    //   // ts-ignore
    //   window.echo.leave(channel)
    //
    //   delete this.visible[index]
    // }
  },
  watch: {
    currentIndex: function currentIndex(value) {
      this.$emit('index', this.currentIndex);
      if (this.currentIndex === -1) {
        this.$emit('nothingChosen');
      } else {
        this.$emit('stuffChosen');
      }
    }
  },
  computed: app_card_itemsvue_type_script_lang_js_objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    focusing: 'system/focusing'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-card-items.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_card_itemsvue_type_script_lang_js = (app_card_itemsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-card-items.vue?vue&type=style&index=0&id=809e8f90&prod&lang=scss
var app_card_itemsvue_type_style_index_0_id_809e8f90_prod_lang_scss = __webpack_require__(1573);

// CONCATENATED MODULE: ./components/Common/app-card-items.vue






/* normalize component */

var app_card_items_component = Object(componentNormalizer["a" /* default */])(
  Common_app_card_itemsvue_type_script_lang_js,
  app_card_itemsvue_type_template_id_809e8f90_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_card_items = __webpack_exports__["a"] = (app_card_items_component.exports);

/***/ }),

/***/ 1573:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1565);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_card_items_vue_vue_type_style_index_0_id_809e8f90_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1574:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".item-selected{border-left:2px solid wheat!important}.socket-active{background-color:#ff5e5e!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1593:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1646);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("64c527b9", content, true, {"sourceMap":false});

/***/ }),

/***/ 1639:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony default export */ __webpack_exports__["a"] = ({
  mounted: function mounted() {
    var _this = this;
    window.$events.$on('reload', function () {
      _this.refresh();
    });
    window.$events.$on('crudy:form:finish', function () {
      // this.refresh()
    });
  },
  beforeDestroy: function beforeDestroy() {
    window.$events.$off('reload');
    window.$events.$off('crudy:form:finish');
  }
});

/***/ }),

/***/ 1642:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-auto-textarea.vue?vue&type=template&id=5680c6a2
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c("div");
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-auto-textarea.vue?vue&type=template&id=5680c6a2

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.iterator.js
var web_dom_collections_iterator = __webpack_require__(81);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-auto-textarea.vue?vue&type=script&lang=js




/* harmony default export */ var app_auto_textareavue_type_script_lang_js = ({
  name: "app-auto-textarea",
  components: {},
  props: ['predefinedValues', 'disableBadWorld', 'disableAllPerson', 'disableAllLocation'],
  data: function data() {
    return {
      text: '',
      config: {
        collection: [{
          trigger: '@',
          values: []
        }, {
          trigger: '@',
          values: []
          // selectTemplate: item => item.original.value,
        }, {
          trigger: '#',
          values: [],
          selectTemplate: function selectTemplate(item) {
            return item.original.value;
          }
        }]
      }
    };
  },
  mounted: function mounted() {
    if (this.predefinedValues) {
      this.config.collection[0].values = this.predefinedValues;
      this.config.collection[0].selectTemplate = function (item) {
        return item.original.value;
      };
    } else {
      this.config.collection[0].values = this.searchFriends;
    }
    if (!this.disableAllLocation) {
      this.config.collection[2].values = this.searchLocations;
    }
  },
  methods: {
    searchFriends: function searchFriends(text, callback) {
      if (text) {
        return this.$axios.$get("/social/search-mentionable/".concat(text)).then(function (data) {
          callback(data.map(function (i) {
            return {
              key: i.display_name,
              value: i.display_name
            };
          }));
        });
      } else {
        callback([]);
      }
    },
    searchLocations: function searchLocations(text, callback) {
      if (text) {
        return this.$axios.$get("/police/locations/".concat(text)).then(function (data) {
          callback(data.map(function (i) {
            return {
              key: i.name,
              value: i.name
            };
          }));
        });
      } else {
        callback([]);
      }
    },
    replaced: function replaced() {},
    noMatch: function noMatch() {}
  }
});
// CONCATENATED MODULE: ./components/Common/app-auto-textarea.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_auto_textareavue_type_script_lang_js = (app_auto_textareavue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-auto-textarea.vue?vue&type=style&index=0&id=5680c6a2&prod&lang=scss
var app_auto_textareavue_type_style_index_0_id_5680c6a2_prod_lang_scss = __webpack_require__(1645);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-auto-textarea.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_auto_textareavue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_auto_textarea = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1645:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_auto_textarea_vue_vue_type_style_index_0_id_5680c6a2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1593);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_auto_textarea_vue_vue_type_style_index_0_id_5680c6a2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_auto_textarea_vue_vue_type_style_index_0_id_5680c6a2_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1646:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".tribute-container ul{background-color:#131313;border-left:3px solid #4f4f4f;list-style-type:none;margin-top:5px;padding:10px}.tribute-container ul li{cursor:pointer;padding:4px}.tribute-container ul li.highlight{background-color:#9d0000}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1663:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VChip/VChip.js
var VChip = __webpack_require__(467);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js + 4 modules
var VSelect = __webpack_require__(375);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VSpacer.js
var VSpacer = __webpack_require__(1523);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item.vue?vue&type=template&id=0778dfa4











var social_posts_itemvue_type_template_id_0778dfa4_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    on: {
      "mouseenter": function mouseenter($event) {
        _vm.hovering = true;
      },
      "mouseleave": function mouseleave($event) {
        _vm.hovering = false;
      }
    }
  }, [_vm.item ? _c(VCard["a" /* default */], {
    staticClass: "social-posts-item"
  }, [_c(components_VCard["c" /* VCardTitle */], [_vm.item.person ? _c('social-person-tag', {
    attrs: {
      "business": _vm.item.business,
      "group": _vm.item.group,
      "anon": _vm.item.anon,
      "color": "transparent",
      "person": _vm.item.person,
      "timestamp": _vm.item.created_at
    }
  }) : _vm._e(), _vm._v(" "), _c(VSpacer["a" /* default */]), _vm._v(" "), _c('div', {
    staticClass: "text-muted",
    staticStyle: {
      "font-size": "20px"
    }
  }, [!_vm.item.read ? _c(VChip["a" /* default */], {
    staticClass: "ma-2",
    attrs: {
      "color": "primary",
      "text-color": "white",
      "small": ""
    }
  }) : _vm._e(), _vm._v(" "), _c('social-posts-item-actions', {
    attrs: {
      "thread": _vm.thread,
      "post": _vm.item
    },
    on: {
      "startEdit": _vm.startEdit,
      "changed": function changed($event) {
        return _vm.$emit('changed');
      }
    }
  }), _vm._v(" "), _c('span', {
    staticClass: "ml-3"
  }), _vm._v(" "), _c('span', {
    staticClass: "ml-3"
  })], 1)], 1), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_c('div', {
    class: {
      'type-image': _vm.item.type === 'image',
      'social-post-content': _vm.item.type === 'text'
    }
  }, [_vm.item.type === 'text' || _vm.item.type === 'image' ? _c('div', [!_vm.editing ? _c('app-markdown-view', {
    attrs: {
      "source": _vm.item.content
    }
  }) : _vm._e(), _vm._v(" "), _vm.editing ? _c('div', [_c('crudy-editor', {
    model: {
      value: _vm.item.content,
      callback: function callback($$v) {
        _vm.$set(_vm.item, "content", $$v);
      },
      expression: "item.content"
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "success"
    },
    on: {
      "click": _vm.saveEdit
    }
  }, [_vm._v("\n                Save Changes\n              ")])], 1) : _vm._e()], 1) : _vm._e(), _vm._v(" "), _vm.item.type === 'location' ? _c('div', [_c('app-markdown-view', {
    attrs: {
      "source": _vm.item.content
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.item.type === 'location' ? _c('div', {
    staticClass: "mt-2"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "btn btn-outline-dark text-white-50 btn-sm",
    staticStyle: {
      "padding": "3px 15px"
    },
    on: {
      "click": function click($event) {
        _vm.$store.dispatch('nui/setGPS', {
          x: JSON.parse(_vm.item.details.current_cords)[0],
          y: JSON.parse(_vm.item.details.current_cords)[1]
        });
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-location-arrow mr-2"
  }), _vm._v("\n              Set GPS to "), _c('span', {
    staticClass: "text-primary"
  }, [_vm._v("\n              " + _vm._s(_vm.item.details.current_zone) + "\n            ")])])], 1) : _vm._e(), _vm._v(" "), _vm.item.type === 'soundcloud' ? _c('iframe', {
    attrs: {
      "width": "100%",
      "height": "166",
      "scrolling": "no",
      "frameborder": "no",
      "allow": "autoplay",
      "src": "https://w.soundcloud.com/player/?url=https%3A//api.soundcloud.com/tracks/".concat(_vm.item.content, "&color=%23ff5500&auto_play=false&hide_related=false&show_comments=true&show_user=true&show_reposts=false&show_teaser=true")
    }
  }) : _vm._e(), _vm._v(" "), _vm.item.share_location || _vm.item.share_contact ? _c('div', {
    staticClass: "mt-3"
  }, [_vm.item.share_location ? _c('span', {
    staticClass: "mt-3"
  }, [_c(VBtn["a" /* default */], {
    staticStyle: {
      "padding": "3px 15px"
    },
    attrs: {
      "small": ""
    },
    on: {
      "click": function click($event) {
        _vm.$store.dispatch('nui/setGPS', {
          x: JSON.parse(_vm.item.location.current_cords)[0],
          y: JSON.parse(_vm.item.location.current_cords)[1]
        });
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-location-arrow mr-2"
  }), _vm._v("\n                GPS: "), _c('span', {
    staticClass: "ml-2"
  }, [_vm._v("\n                " + _vm._s(_vm.item.location.current_zone) + "\n              ")])])], 1) : _vm._e(), _vm._v(" "), _vm.item.share_contact ? _c('span', {
    staticClass: "mt-3"
  }, [_c(VBtn["a" /* default */], {
    staticStyle: {
      "padding": "3px 15px"
    },
    attrs: {
      "small": ""
    },
    on: {
      "click": function click($event) {
        return _vm.$store.dispatch('nui/call', _vm.item.person.phone);
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-phone mr-2"
  }), _vm._v("\n                Call: "), _c('span', {
    staticClass: "ml-2"
  }, [_vm._v("\n                " + _vm._s(_vm.item.person.phone) + "\n              ")])])], 1) : _vm._e()]) : _vm._e()])]), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c('span', {
    staticClass: "ml-3 hover-icon",
    on: {
      "click": function click($event) {
        _vm.addingComment = !_vm.addingComment;
      }
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-messages mr-2"
  })]), _vm._v(" "), _c('social-posts-item-reactions', {
    staticClass: "ml-3",
    attrs: {
      "thread": _vm.thread,
      "item": _vm.item
    }
  })], 1), _vm._v(" "), _vm.addingComment ? _c('app-card', {
    staticClass: "p-3",
    class: {
      'p-3': _vm.mode === 'tablet'
    },
    attrs: {
      "no-padding": true
    }
  }, [_c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.submitReply.apply(null, arguments);
      }
    }
  }, [_c('crudy-editor', {
    ref: "editor",
    attrs: {
      "mentions-endpoint": "/editor/social-mentions/{term}",
      "simplified": true
    },
    model: {
      value: _vm.addingCommentContent,
      callback: function callback($$v) {
        _vm.addingCommentContent = $$v;
      },
      expression: "addingCommentContent"
    }
  }), _vm._v(" "), _c(VSelect["a" /* default */], {
    staticClass: "mt-4",
    attrs: {
      "items": _vm.possiblePosters,
      "append-icon": "fa-solid fa-person",
      "item-text": "label",
      "return-object": "",
      "label": "Post As",
      "hint": "Post as yourself, business, or group"
    },
    model: {
      value: _vm.poster,
      callback: function callback($$v) {
        _vm.poster = $$v;
      },
      expression: "poster"
    }
  }), _vm._v(" "), _vm.errorText ? _c('div', {
    staticClass: "text-danger"
  }, [_vm._v("\n            " + _vm._s(_vm.errorText) + "\n          ")]) : _vm._e(), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mt-3",
    attrs: {
      "color": "primary",
      "small": ""
    },
    on: {
      "click": _vm.submitReply
    }
  }, [_vm._v("\n            Add Comment\n          ")])], 1), _vm._v(" "), _c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey.once",
      value: ['ctrl', 'enter'],
      expression: "['ctrl', 'enter']",
      modifiers: {
        "once": true
      }
    }],
    on: {
      "shortkey": _vm.submitReply
    }
  })], 1) : _vm._e(), _vm._v(" "), _vm.item.comments.length > 0 ? _c(VCard["a" /* default */], {
    attrs: {
      "color": "transparent",
      "rounded": ""
    }
  }, [_c(components_VCard["b" /* VCardText */], {
    attrs: {
      "color": "transparent"
    }
  }, [_vm.item.comments && _vm.item.comments.length > 0 ? _c('div', [_c('social-posts-item-comments', {
    attrs: {
      "thread": _vm.thread,
      "item": _vm.item
    }
  })], 1) : _vm._e()])], 1) : _vm._e()], 1) : _vm._e()], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item.vue?vue&type=template&id=0778dfa4

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-reactions.vue?vue&type=template&id=4994d678

var social_posts_item_reactionsvue_type_template_id_4994d678_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "post-reactions"
  }, [Object.keys(_vm.renderedReactions).length < 3 ? _c('app-emote-picker', {
    staticClass: "mr-3",
    staticStyle: {
      "display": "inline-block"
    },
    on: {
      "selected": _vm.doReaction
    }
  }) : _vm._e(), _vm._v(" "), _vm._l(_vm.renderedReactions, function (reactions, emoji) {
    return _c('span', {
      key: emoji,
      staticClass: "reaction-item",
      class: {
        'grayed': reactions.length < 1
      },
      staticStyle: {
        "display": "inline"
      },
      on: {
        "click": function click($event) {
          return _vm.doReaction(emoji);
        }
      }
    }, [_c('span', {
      staticClass: "emoji"
    }, [_vm._v("\n        " + _vm._s(emoji) + "\n      ")]), _vm._v(" "), _c('span', {
      staticClass: "number"
    }, [_vm._v("\n         " + _vm._s(reactions.length) + "\n      ")])]);
  })], 2);
};
var social_posts_item_reactionsvue_type_template_id_4994d678_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-reactions.vue?vue&type=template&id=4994d678

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find-index.js
var es_array_find_index = __webpack_require__(113);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.splice.js
var es_array_splice = __webpack_require__(133);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.to-string.js
var es_regexp_to_string = __webpack_require__(62);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.code-point-at.js
var es_string_code_point_at = __webpack_require__(1567);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-reactions-item.vue?vue&type=template&id=550514fa
var social_posts_item_reactions_itemvue_type_template_id_550514fa_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div');
};
var social_posts_item_reactions_itemvue_type_template_id_550514fa_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-reactions-item.vue?vue&type=template&id=550514fa

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-reactions-item.vue?vue&type=script&lang=js

/* harmony default export */ var social_posts_item_reactions_itemvue_type_script_lang_js = ({
  name: 'social-posts-item-reactions-item',
  props: ['postId', 'reaction', 'doReaction'],
  data: function data() {
    return {
      localCount: 0
    };
  },
  mounted: function mounted() {
    this.localCount = this.reaction.count;
  }
});
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-reactions-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_posts_item_reactions_itemvue_type_script_lang_js = (social_posts_item_reactions_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Social/social-posts-item-reactions-item.vue?vue&type=style&index=0&id=550514fa&prod&lang=scss
var social_posts_item_reactions_itemvue_type_style_index_0_id_550514fa_prod_lang_scss = __webpack_require__(1764);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-reactions-item.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Social_social_posts_item_reactions_itemvue_type_script_lang_js,
  social_posts_item_reactions_itemvue_type_template_id_550514fa_render,
  social_posts_item_reactions_itemvue_type_template_id_550514fa_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_posts_item_reactions_item = (component.exports);
// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.search.js
var es_string_search = __webpack_require__(192);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-emote-picker.vue?vue&type=template&id=d682fd94




var app_emote_pickervue_type_template_id_d682fd94_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('emoji-picker', {
    staticStyle: {
      "display": "inline",
      "font-size": "20px"
    },
    attrs: {
      "search": _vm.search
    },
    on: {
      "emoji": _vm.emojiSelected
    },
    scopedSlots: _vm._u([{
      key: "emoji-invoker",
      fn: function fn(_ref) {
        var clickEvent = _ref.events.click;
        return _c('div', {
          staticClass: "emoji-invoker",
          staticStyle: {
            "display": "inline"
          },
          on: {
            "click": function click($event) {
              $event.stopPropagation();
              return clickEvent.apply(null, arguments);
            }
          }
        }, [_c('i', {
          staticClass: "fa-solid fa-face-smile-plus hover-icon text-white-50"
        })]);
      }
    }, {
      key: "emoji-picker",
      fn: function fn(_ref2) {
        var emojis = _ref2.emojis,
          insert = _ref2.insert,
          display = _ref2.display;
        return _c('div', {
          staticClass: "e-picker"
        }, [_c('app-card', {
          staticClass: "emoji-picker",
          style: _vm.styleVars
        }, [_c('div', {
          staticClass: "emoji-picker__search"
        }, [_c('input', {
          directives: [{
            name: "model",
            rawName: "v-model",
            value: _vm.search,
            expression: "search"
          }],
          attrs: {
            "type": "text",
            "autofocus": ""
          },
          domProps: {
            "value": _vm.search
          },
          on: {
            "input": function input($event) {
              if ($event.target.composing) return;
              _vm.search = $event.target.value;
            }
          }
        })]), _vm._v(" "), _c('div', _vm._l(emojis, function (emojiGroup, category) {
          return _c('div', {
            key: category
          }, [_c('h5', [_vm._v(_vm._s(category))]), _vm._v(" "), _c('div', {
            staticClass: "emojis"
          }, _vm._l(emojiGroup, function (emoji, emojiName) {
            return !emojiName.includes('egg') && !emojiName.includes('tounge') && !emojiName.includes('-1') && !emojiName.includes('sweat_drop') && !emojiName.includes('unamused') && !emojiName.includes('ok_hand') && !emojiName.includes('point') && !emojiName.includes('kiss') && !emojiName.includes('rocket') && !emojiName.includes('mens') && !emojiName.includes('womans') && !emojiName.includes('underage') && !emojiName.includes('flashlight') && !emojiName.includes('tea') && !emojiName.includes('nut') && !emojiName.includes('monkey') && !emojiName.includes('pig') && !emojiName.includes('rainbow') && !emojiName.includes('octopus') && !emojiName.includes('sheep') && !emojiName.includes('ok_woman') && !emojiName.includes('hankey') ? _c('span', {
              key: emojiName,
              attrs: {
                "title": emojiName
              },
              on: {
                "click": function click($event) {
                  insert(emoji);
                  display.visible = false;
                }
              }
            }, [_vm._v(_vm._s(emoji))]) : _vm._e();
          }), 0)]);
        }), 0)])], 1);
      }
    }]),
    model: {
      value: _vm.active,
      callback: function callback($$v) {
        _vm.active = $$v;
      },
      expression: "active"
    }
  });
};
var app_emote_pickervue_type_template_id_d682fd94_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-emote-picker.vue?vue&type=template&id=d682fd94

// EXTERNAL MODULE: ./node_modules/vue-emoji-picker/dist-module/main.js
var main = __webpack_require__(731);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-emote-picker.vue?vue&type=script&lang=js


/* harmony default export */ var app_emote_pickervue_type_script_lang_js = ({
  name: "app-emote-picker",
  components: {
    AppCard: app_card["a" /* default */],
    EmojiPicker: main["EmojiPicker"]
  },
  props: ['selected'],
  data: function data() {
    return {
      search: null,
      active: false
    };
  },
  methods: {
    emojiSelected: function emojiSelected(emoji) {
      this.active = false;
      this.$emit('selected', emoji);
    }
  },
  computed: {
    styleVars: function styleVars() {
      return {
        'transition': 'all 0.5s'
        // top: '1px',
        // left: '2px'
      };
    }
  }
});
// CONCATENATED MODULE: ./components/Common/app-emote-picker.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_emote_pickervue_type_script_lang_js = (app_emote_pickervue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-emote-picker.vue?vue&type=style&index=0&id=d682fd94&prod&lang=scss
var app_emote_pickervue_type_style_index_0_id_d682fd94_prod_lang_scss = __webpack_require__(1766);

// CONCATENATED MODULE: ./components/Common/app-emote-picker.vue






/* normalize component */

var app_emote_picker_component = Object(componentNormalizer["a" /* default */])(
  Common_app_emote_pickervue_type_script_lang_js,
  app_emote_pickervue_type_template_id_d682fd94_render,
  app_emote_pickervue_type_template_id_d682fd94_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_emote_picker = (app_emote_picker_component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-reactions.vue?vue&type=script&lang=js










var groupBy = function groupBy(xs, key) {
  return xs.reduce(function (rv, x) {
    (rv[x[key]] = rv[x[key]] || []).push(x);
    return rv;
  }, {});
};
/* harmony default export */ var social_posts_item_reactionsvue_type_script_lang_js = ({
  name: 'social-posts-item-reactions',
  components: {
    AppEmotePicker: app_emote_picker,
    SocialPostsItemReactionsItem: social_posts_item_reactions_item
  },
  props: ['item'],
  data: function data() {
    return {
      localModifier: 0,
      localReactions: []
    };
  },
  // mounted() {
  //   this.localReactions = this.item.reactions
  // },
  methods: {
    doReaction: function doReaction(emoji) {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        var hex, data, indexOfRemoval;
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              hex = emoji.codePointAt(0).toString(16);
              _context.next = 3;
              return _this.$axios.$post("/social/react/".concat(_this.item.id), {
                reaction_name: hex,
                reaction_emoji: emoji
              });
            case 3:
              data = _context.sent;
              if (data.action === 'removed') {
                indexOfRemoval = _this.localReactions.findIndex(function (e) {
                  return e.reaction_name === hex;
                });
                if (indexOfRemoval > -1) {
                  // this.localReactions.splice(indexOfRemoval, 1)
                }
              }
              if (data.action === 'added') {
                // this.localReactions.push({
                //   reaction_emoji: emoji,
                //   reaction_name: hex,
                // })
              }
            case 6:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    localRemove: function localRemove(name) {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var indexOfRemoval;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              indexOfRemoval = _this2.localReactions.findIndex(function (e) {
                return e.reaction_name === name;
              });
              if (indexOfRemoval > -1) {
                _this2.localReactions.splice(indexOfRemoval, 1);
              }
            case 2:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    }
  },
  computed: {
    renderedReactions: function renderedReactions() {
      return groupBy(this.item.reactions, 'reaction_emoji');
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-reactions.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_posts_item_reactionsvue_type_script_lang_js = (social_posts_item_reactionsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Social/social-posts-item-reactions.vue?vue&type=style&index=0&id=4994d678&prod&lang=scss
var social_posts_item_reactionsvue_type_style_index_0_id_4994d678_prod_lang_scss = __webpack_require__(1768);

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-reactions.vue






/* normalize component */

var social_posts_item_reactions_component = Object(componentNormalizer["a" /* default */])(
  Social_social_posts_item_reactionsvue_type_script_lang_js,
  social_posts_item_reactionsvue_type_template_id_4994d678_render,
  social_posts_item_reactionsvue_type_template_id_4994d678_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_posts_item_reactions = (social_posts_item_reactions_component.exports);
// EXTERNAL MODULE: ./components/Pages/Social/social-person-tag.vue + 4 modules
var social_person_tag = __webpack_require__(142);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VList.js
var VList = __webpack_require__(462);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/VListItem.js
var VListItem = __webpack_require__(301);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VList/index.js + 2 modules
var components_VList = __webpack_require__(50);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VMenu/VMenu.js
var VMenu = __webpack_require__(461);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-actions.vue?vue&type=template&id=53422ef2







var social_posts_item_actionsvue_type_template_id_53422ef2_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('span', [_vm.thread && _vm.thread._can_remove_posts || _vm.user.id === _vm.post.person_id && _vm.isLifeInvaderPost ? _c(VMenu["a" /* default */], {
    attrs: {
      "offset-y": ""
    },
    scopedSlots: _vm._u([{
      key: "activator",
      fn: function fn(_ref) {
        var on = _ref.on,
          attrs = _ref.attrs;
        return [_c(VBtn["a" /* default */], _vm._g(_vm._b({
          attrs: {
            "text": "",
            "color": "primary",
            "dark": "",
            "fab": "",
            "icon": "",
            "small": ""
          }
        }, 'v-btn', attrs, false), on), [_c(VIcon["a" /* default */], {
          attrs: {
            "small": ""
          }
        }, [_vm._v("\n          fa-solid fa-ellipsis-vertical\n        ")])], 1)];
      }
    }], null, false, 1381038928)
  }, [_vm._v(" "), _c(VList["a" /* default */], [_vm.user.id === _vm.post.person_id && !_vm.isLifeInvaderPost || _vm.hasAnyGroup(['admin']) ? _c(VListItem["a" /* default */], {
    on: {
      "click": _vm.startEdit
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "primary",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-pencil\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Edit Post\n          ")])])], 1) : _vm._e(), _vm._v(" "), _vm.thread && _vm.thread._can_remove_posts && _vm.canUseActions || _vm.user.id === _vm.post.person_id ? _c(VListItem["a" /* default */], {
    on: {
      "click": _vm.remove
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "red",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-trash-xmark\n            ")])], 1), _vm._v(" "), _c('span', [_vm._v("\n            Remove Post\n          ")])])], 1) : _vm._e(), _vm._v(" "), _vm.thread && _vm.thread._can_pin_posts && _vm.isBusinessPost ? _c(VListItem["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.togglePublic(!_vm.post.is_public);
      }
    }
  }, [_c(components_VList["c" /* VListItemTitle */], [_c('span', {
    staticClass: "mr-2"
  }, [_c(VIcon["a" /* default */], {
    attrs: {
      "color": "orange",
      "small": ""
    }
  }, [_vm._v("\n              fa-solid fa-share-nodes\n            ")])], 1), _vm._v(" "), !_vm.post.is_public ? _c('span', [_vm._v("\n            Make Post Public (For Business's)\n          ")]) : _c('span', [_vm._v("\n            Make Post Private\n          ")])])], 1) : _vm._e()], 1)], 1) : _vm._e(), _vm._v(" "), _c('span', [_c(VBtn["a" /* default */], {
    attrs: {
      "fab": "",
      "icon": "",
      "to": "/social/posts/".concat(_vm.post.uuid),
      "x-small": ""
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-up-right-from-square"
  })])], 1)], 1);
};
var social_posts_item_actionsvue_type_template_id_53422ef2_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-actions.vue?vue&type=template&id=53422ef2

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-actions.vue?vue&type=script&lang=js











function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var social_posts_item_actionsvue_type_script_lang_js = ({
  name: "social-posts-item-actions",
  props: ['post', 'type', 'thread'],
  methods: {
    startEdit: function startEdit() {
      this.$emit('startEdit');
    },
    remove: function remove() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.post("/social/remove/".concat(_this.post.uuid));
            case 2:
              _this.$emit('changed');
              window.$events.$emit('reload');
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    togglePublic: function togglePublic(value) {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              _context2.next = 2;
              return _this2.$axios.post("/social/set-public/".concat(_this2.post.id), {
                value: value
              });
            case 2:
              _this2.$emit('changed');
            case 3:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    }
  },
  computed: _objectSpread({
    canUseActions: function canUseActions() {
      return true;
    },
    isLifeInvaderPost: function isLifeInvaderPost() {
      return this.$route.path.includes('social');
    },
    isFactionPost: function isFactionPost() {
      return this.$route.path.includes('cad/community');
    },
    isBusinessPost: function isBusinessPost() {
      var _this$thread;
      return (_this$thread = this.thread) === null || _this$thread === void 0 ? void 0 : _this$thread.business_id;
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    hasAnyGroup: 'auth/hasAnyGroup'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-actions.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_posts_item_actionsvue_type_script_lang_js = (social_posts_item_actionsvue_type_script_lang_js); 
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-actions.vue





/* normalize component */

var social_posts_item_actions_component = Object(componentNormalizer["a" /* default */])(
  Social_social_posts_item_actionsvue_type_script_lang_js,
  social_posts_item_actionsvue_type_template_id_53422ef2_render,
  social_posts_item_actionsvue_type_template_id_53422ef2_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_posts_item_actions = (social_posts_item_actions_component.exports);
// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-comments.vue?vue&type=template&id=52657c52
var social_posts_item_commentsvue_type_template_id_52657c52_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.item ? _c('div', {
    staticClass: "social-post-comments"
  }, [_vm._l(_vm.visibleComments, function (comment) {
    return _c('div', {
      staticClass: "mb-5",
      class: {
        'my-comment': comment.person.id === _vm.user.id
      },
      attrs: {
        "color": "transparent"
      }
    }, [_c('social-posts-item-comments-item', {
      attrs: {
        "comment": comment,
        "post": _vm.item
      }
    })], 1);
  }), _vm._v(" "), _vm.isOverFlowing ? _c('div', {
    staticClass: "ml-4"
  }, [_c('NuxtLink', {
    attrs: {
      "to": "/social/posts/".concat(_vm.item.uuid)
    }
  }, [_vm._v("\n        ... View " + _vm._s(_vm.overflowDifference) + " more comments\n      ")])], 1) : _vm._e()], 2) : _vm._e();
};
var social_posts_item_commentsvue_type_template_id_52657c52_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-comments.vue?vue&type=template&id=52657c52

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// EXTERNAL MODULE: ./components/Common/app-avatar.vue + 4 modules
var app_avatar = __webpack_require__(189);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-comments-item.vue?vue&type=template&id=65092245



var social_posts_item_comments_itemvue_type_template_id_65092245_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VRow["a" /* default */], {
    staticClass: "ml-2",
    on: {
      "mouseenter": function mouseenter($event) {
        _vm.hovering = true;
      },
      "mouseleave": function mouseleave($event) {
        _vm.hovering = false;
      }
    }
  }, [_c('social-person-tag', {
    staticStyle: {
      "width": "95%",
      "background-color": "transparent !important"
    },
    attrs: {
      "group": _vm.comment.group,
      "person": _vm.comment.person,
      "color": "transparent",
      "small": ""
    },
    scopedSlots: _vm._u([{
      key: "details",
      fn: function fn() {
        return [_c('app-markdown-view', {
          attrs: {
            "source": _vm.comment.content
          }
        })];
      },
      proxy: true
    }, {
      key: "rightName",
      fn: function fn() {
        return [_c('app-timestamp', {
          attrs: {
            "stamp": _vm.comment.created_at
          }
        }), _vm._v(" "), _vm.hovering && _vm.hasAnyGroup(['mod', 'admin']) ? _c(VIcon["a" /* default */], {
          staticClass: "ml-3",
          staticStyle: {
            "cursor": "pointer"
          },
          attrs: {
            "x-small": "",
            "color": "red"
          },
          on: {
            "click": _vm.removeComment
          }
        }, [_vm._v("\n        fa-regular fa-trash-can\n      ")]) : _vm._e()];
      },
      proxy: true
    }])
  })], 1);
};
var social_posts_item_comments_itemvue_type_template_id_65092245_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-comments-item.vue?vue&type=template&id=65092245

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-comments-item.vue?vue&type=script&lang=js








function social_posts_item_comments_itemvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function social_posts_item_comments_itemvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? social_posts_item_comments_itemvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : social_posts_item_comments_itemvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }











/* harmony default export */ var social_posts_item_comments_itemvue_type_script_lang_js = ({
  name: "social-posts-item-comments-item",
  components: {
    AppTimestamp: app_timestamp["a" /* default */],
    AppAvatar: app_avatar["a" /* default */],
    SocialPostsItemActions: social_posts_item_actions,
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppCard: app_card["a" /* default */],
    SocialPersonTag: social_person_tag["a" /* default */],
    SocialPostsItemReactions: social_posts_item_reactions
  },
  props: ['post', 'comment'],
  data: function data() {
    return {
      hovering: false
    };
  },
  methods: {
    removeComment: function removeComment() {
      this.$axios.$post("/social/remove-post/comment/".concat(this.post.uuid, "/").concat(this.comment.id));
    }
  },
  computed: social_posts_item_comments_itemvue_type_script_lang_js_objectSpread({
    personName: function personName() {
      return this.comment.person.display_name;
    },
    showRealName: function showRealName() {
      return this.$route.path.includes('cad/system');
    },
    isOnPostPage: function isOnPostPage() {
      return this.$route.path.includes('posts/');
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    'user': "auth/user",
    'hasAnyGroup': "auth/hasAnyGroup"
  }))
});
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-comments-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_posts_item_comments_itemvue_type_script_lang_js = (social_posts_item_comments_itemvue_type_script_lang_js); 
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-comments-item.vue





/* normalize component */

var social_posts_item_comments_item_component = Object(componentNormalizer["a" /* default */])(
  Social_social_posts_item_comments_itemvue_type_script_lang_js,
  social_posts_item_comments_itemvue_type_template_id_65092245_render,
  social_posts_item_comments_itemvue_type_template_id_65092245_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_posts_item_comments_item = (social_posts_item_comments_item_component.exports);
// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item-comments.vue?vue&type=script&lang=js











function social_posts_item_commentsvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function social_posts_item_commentsvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? social_posts_item_commentsvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : social_posts_item_commentsvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









/* harmony default export */ var social_posts_item_commentsvue_type_script_lang_js = ({
  name: "social-posts-item-comments",
  components: {
    SocialPostsItemCommentsItem: social_posts_item_comments_item,
    AppTimestamp: app_timestamp["a" /* default */],
    AppAvatar: app_avatar["a" /* default */],
    SocialPostsItemActions: social_posts_item_actions,
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppCard: app_card["a" /* default */],
    SocialPersonTag: social_person_tag["a" /* default */],
    SocialPostsItemReactions: social_posts_item_reactions
  },
  props: ['item'],
  methods: {},
  computed: social_posts_item_commentsvue_type_script_lang_js_objectSpread({
    showRealName: function showRealName() {
      return this.$route.path.includes('cad/system');
    },
    visibleComments: function visibleComments() {
      if (!this.isOnPostPage) {
        return this.item.comments.slice(0, 2);
      }
      return this.item.comments;
    },
    isOverFlowing: function isOverFlowing() {
      if (this.isOnPostPage) {
        return false;
      }
      return this.item.comments.length > 2;
    },
    overflowDifference: function overflowDifference() {
      return this.item.comments.length - 2;
    },
    isOnPostPage: function isOnPostPage() {
      return this.$route.path.includes('posts/');
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    'user': "auth/user"
  }))
});
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-comments.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_posts_item_commentsvue_type_script_lang_js = (social_posts_item_commentsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Social/social-posts-item-comments.vue?vue&type=style&index=0&id=52657c52&prod&lang=scss
var social_posts_item_commentsvue_type_style_index_0_id_52657c52_prod_lang_scss = __webpack_require__(1770);

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item-comments.vue






/* normalize component */

var social_posts_item_comments_component = Object(componentNormalizer["a" /* default */])(
  Social_social_posts_item_commentsvue_type_script_lang_js,
  social_posts_item_commentsvue_type_template_id_52657c52_render,
  social_posts_item_commentsvue_type_template_id_52657c52_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_posts_item_comments = (social_posts_item_comments_component.exports);
// EXTERNAL MODULE: ./components/Common/app-card-items.vue + 9 modules
var app_card_items = __webpack_require__(1571);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// EXTERNAL MODULE: ./components/Common/app-socket-channel-presence-listener.vue + 4 modules
var app_socket_channel_presence_listener = __webpack_require__(497);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-posts-item.vue?vue&type=script&lang=js









function social_posts_itemvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function social_posts_itemvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? social_posts_itemvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : social_posts_itemvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }













/* harmony default export */ var social_posts_itemvue_type_script_lang_js = ({
  name: "social-posts-item",
  components: {
    AppSocketChannelPresenceListener: app_socket_channel_presence_listener["a" /* default */],
    CrudyEditor: crudy_editor["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */],
    AppCardItems: app_card_items["a" /* default */],
    SocialPostsItemComments: social_posts_item_comments,
    SocialPostsItemActions: social_posts_item_actions,
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppCard: app_card["a" /* default */],
    SocialPersonTag: social_person_tag["a" /* default */],
    SocialPostsItemReactions: social_posts_item_reactions
  },
  props: ['item', 'thread'],
  data: function data() {
    return {
      editing: false,
      errorText: null,
      hovering: false,
      addingComment: false,
      addingCommentContent: null
    };
  },
  methods: {
    startReplying: function startReplying() {
      this.addingComment = true;
    },
    startEdit: function startEdit() {
      this.editing = true;
      if (this.$refs.editor) {
        this.$refs.editor.focus();
      }
    },
    saveEdit: function saveEdit() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this.$axios.$post("/social/update/".concat(_this.item.uuid), {
                content: _this.item.content
              });
            case 2:
              _this.editing = false;
            case 3:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    submitReply: function submitReply() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (!_this2.addingComment) {
                _context2.next = 8;
                break;
              }
              _this2.errorText = null;
              if (!(_this2.addingCommentContent.length > 1000)) {
                _context2.next = 4;
                break;
              }
              return _context2.abrupt("return", _this2.errorText = 'Content must be shorter');
            case 4:
              _this2.addingComment = false;
              _context2.next = 7;
              return _this2.$axios.$post("/social/add-post-comment/".concat(_this2.item.id), {
                content: _this2.addingCommentContent,
                poster: _this2.poster
              });
            case 7:
              _this2.addingCommentContent = null;
            case 8:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    }
  },
  computed: social_posts_itemvue_type_script_lang_js_objectSpread({
    poster: {
      get: function get() {
        return this.$store.getters["auth/poster"];
      },
      set: function set(value) {
        this.$store.commit('auth/SET_ACTIVE_POSTER', value);
      }
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode',
    possiblePosters: 'auth/possiblePosters'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_posts_itemvue_type_script_lang_js = (social_posts_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Social/social-posts-item.vue?vue&type=style&index=0&id=0778dfa4&prod&lang=scss
var social_posts_itemvue_type_style_index_0_id_0778dfa4_prod_lang_scss = __webpack_require__(1772);

// CONCATENATED MODULE: ./components/Pages/Social/social-posts-item.vue






/* normalize component */

var social_posts_item_component = Object(componentNormalizer["a" /* default */])(
  Social_social_posts_itemvue_type_script_lang_js,
  social_posts_itemvue_type_template_id_0778dfa4_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_posts_item = __webpack_exports__["a"] = (social_posts_item_component.exports);

/***/ }),

/***/ 1664:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(78);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-create-post.vue?vue&type=template&id=7712b02a




var social_create_postvue_type_template_id_7712b02a_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VForm["a" /* default */], {
    attrs: {
      "id": "form"
    },
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.create.apply(null, arguments);
      }
    }
  }, [_c('div', {
    staticClass: "mt-3"
  }, [_c('crudy-editor', {
    ref: "editor",
    attrs: {
      "simplified": _vm.simpleEditor,
      "limit": _vm.limit,
      "mentions-endpoint": "/editor/social-mentions/{term}",
      "placeholder": _vm.placeholder
    },
    on: {
      "invalid": function invalid($event) {
        _vm.isInvalid = false;
      },
      "click": function click($event) {
        _vm.focused = true;
      }
    },
    model: {
      value: _vm.form.content,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "content", $$v);
      },
      expression: "form.content"
    }
  })], 1), _vm._v(" "), _c(transitions["a" /* VExpandTransition */], [_c('div', {
    staticClass: "mb-5"
  }, [_vm._t("left-submit", function () {
    return [_c(VBtn["a" /* default */], {
      staticClass: "mr-5",
      attrs: {
        "fab": "",
        "icon": "",
        "x-small": "",
        "color": "primary"
      },
      on: {
        "click": _vm.takePicture
      }
    }, [_c('i', {
      staticClass: "fa-solid fa-camera"
    })])];
  }), _vm._v(" "), _c('div', {
    staticClass: "mt-2",
    attrs: {
      "align": "right"
    }
  }, [_c(VBtn["a" /* default */], {
    staticClass: "mt-2",
    attrs: {
      "color": "success",
      "disabled": false
    },
    on: {
      "click": _vm.create
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-comment-arrow-up-right mr-2"
  }), _vm._v("\n            Submit Post\n")])], 1)], 2)])], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Social/social-create-post.vue?vue&type=template&id=7712b02a

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./components/Pages/Social/social-posts-item.vue + 34 modules
var social_posts_item = __webpack_require__(1663);

// EXTERNAL MODULE: ./components/Common/app-action-btn.vue + 4 modules
var app_action_btn = __webpack_require__(63);

// EXTERNAL MODULE: ./components/Common/app-auto-textarea.vue + 4 modules
var app_auto_textarea = __webpack_require__(1642);

// EXTERNAL MODULE: ./components/Crud/parts/CrudSelect.vue + 4 modules
var CrudSelect = __webpack_require__(203);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-hotkey-action-text.vue?vue&type=template&id=44d58be5
var app_hotkey_action_textvue_type_template_id_44d58be5_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('kbd', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: _vm.combo,
      expression: "combo"
    }],
    staticClass: "ml-3",
    on: {
      "shortkey": _vm._onPressed
    }
  }, [!_vm.modalShowing ? _c('span', [_vm._v("\n    [ "), _c('i', {
    staticClass: "fa-solid fa-keyboard mr-2"
  }), _vm._v(" " + _vm._s(_vm.label) + " ]\n  ")]) : _c('span', [_vm._v("\n    [ ... ]\n  ")])]);
};
var app_hotkey_action_textvue_type_template_id_44d58be5_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-hotkey-action-text.vue?vue&type=template&id=44d58be5

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-hotkey-action-text.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }

/* harmony default export */ var app_hotkey_action_textvue_type_script_lang_js = ({
  name: 'app-hotkey-action-text',
  props: {
    label: {},
    combo: {}
  },
  data: function data() {
    return {};
  },
  methods: {
    _onPressed: function _onPressed() {
      this.$emit('triggered');
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    focusing: 'system/focusing',
    modalShowing: 'system/modalShowing'
  }))
});
// CONCATENATED MODULE: ./components/Common/app-hotkey-action-text.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_hotkey_action_textvue_type_script_lang_js = (app_hotkey_action_textvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-hotkey-action-text.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_hotkey_action_textvue_type_script_lang_js,
  app_hotkey_action_textvue_type_template_id_44d58be5_render,
  app_hotkey_action_textvue_type_template_id_44d58be5_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_hotkey_action_text = (component.exports);
// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Social/social-create-post.vue?vue&type=script&lang=js









function social_create_postvue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function social_create_postvue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? social_create_postvue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : social_create_postvue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }









/* harmony default export */ var social_create_postvue_type_script_lang_js = ({
  name: "social-create-post",
  components: {
    CrudyEditor: crudy_editor["a" /* default */],
    AppHotkeyActionText: app_hotkey_action_text,
    AppFormGroup: app_form_group["a" /* default */],
    CrudSelect: CrudSelect["a" /* default */],
    AppAutoTextarea: app_auto_textarea["a" /* default */],
    AppActionBtn: app_action_btn["a" /* default */],
    SocialPostsItem: social_posts_item["a" /* default */]
  },
  props: ['preAppend', 'thread', 'privacyType', 'hotkeyActive', 'simpleEditor', 'poster', 'anon', 'limit', 'includeLocation', 'includeContact'],
  data: function data() {
    return {
      isInvalid: false,
      focused: false,
      advanceEditor: false,
      form: {
        type: 'text',
        content: null,
        privacy: 'public'
      }
    };
  },
  mounted: function mounted() {
    if (this.privacyType) {
      this.form.privacy = this.privacyType;
    }
  },
  methods: {
    focusInput: function focusInput() {
      // this.$refs.input.focus()
    },
    _triggered: function _triggered() {
      if (!this.advanceEditor) {
        this.create();
      }
    },
    create: function create() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              if (_this.form.type === 'location') {
                _this.form.details = _this.location;
              }
              _this.form.thread_id = _this.thread.id;
              _this.form.poster = _this.poster;
              _this.form.anon = _this.anon;
              _this.form.location = _this.location;
              _this.form.share_location = _this.includeLocation;
              _this.form.share_contact = _this.includeContact;
              _context.next = 9;
              return _this.$axios.$post('/social/create', _this.form);
            case 9:
              _this.form.content = null;
              _this.focused = false;
              _this.$emit('created');
              return _context.abrupt("return", true);
            case 13:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    takePicture: function takePicture() {
      var _this2 = this;
      this.$axios.$post('https://blrp_tablet/takePictureWithResult').then(function (response) {
        _this2.form.content = response;
      });
    }
  },
  watch: {
    preAppend: function preAppend() {
      if (this.preAppend) {
        this.form.content = this.preAppend;
      }
    }
  },
  computed: social_create_postvue_type_script_lang_js_objectSpread({
    placeholder: function placeholder() {
      if (this.form.type === 'text' && this.hotkeyActive) return "What's on your mind? (Press enter to type)";
      if (this.form.type === 'text') return "What's on your mind?";
      if (this.form.type === 'image') return "Direct Image Link";
      if (this.form.type === 'location') return "What would you like to say about this location?";
      if (this.form.type === 'advert') return "What do you want to sell or buy?";
      if (this.form.type === 'soundcloud') return "Embed code from Sound Cloud Website. Must be exact code.";
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    location: 'location/position',
    markdownConfig: 'system/markdownConfig'
  }))
});
// CONCATENATED MODULE: ./components/Pages/Social/social-create-post.vue?vue&type=script&lang=js
 /* harmony default export */ var Social_social_create_postvue_type_script_lang_js = (social_create_postvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Social/social-create-post.vue?vue&type=style&index=0&id=7712b02a&prod&lang=scss
var social_create_postvue_type_style_index_0_id_7712b02a_prod_lang_scss = __webpack_require__(1774);

// CONCATENATED MODULE: ./components/Pages/Social/social-create-post.vue






/* normalize component */

var social_create_post_component = Object(componentNormalizer["a" /* default */])(
  Social_social_create_postvue_type_script_lang_js,
  social_create_postvue_type_template_id_7712b02a_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var social_create_post = __webpack_exports__["a"] = (social_create_post_component.exports);

/***/ }),

/***/ 1680:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1765);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("1e881c46", content, true, {"sourceMap":false});

/***/ }),

/***/ 1681:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1767);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("ee233d1e", content, true, {"sourceMap":false});

/***/ }),

/***/ 1682:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1769);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("521959f0", content, true, {"sourceMap":false});

/***/ }),

/***/ 1683:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1771);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("05d3fa70", content, true, {"sourceMap":false});

/***/ }),

/***/ 1684:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1773);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("57b12828", content, true, {"sourceMap":false});

/***/ }),

/***/ 1685:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1775);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("51ec2d3e", content, true, {"sourceMap":false});

/***/ }),

/***/ 1686:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1777);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("38bdf342", content, true, {"sourceMap":false});

/***/ }),

/***/ 1764:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_reactions_item_vue_vue_type_style_index_0_id_550514fa_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1680);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_reactions_item_vue_vue_type_style_index_0_id_550514fa_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_reactions_item_vue_vue_type_style_index_0_id_550514fa_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1765:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".grayed{opacity:.3}.reaction-item{background-color:#161616;border-radius:10px;cursor:pointer;font-size:19px;font-weight:700;margin-right:10px;padding:3px 10px 5px}.reaction-item:hover{background-color:#222}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1766:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_emote_picker_vue_vue_type_style_index_0_id_d682fd94_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1681);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_emote_picker_vue_vue_type_style_index_0_id_d682fd94_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_emote_picker_vue_vue_type_style_index_0_id_d682fd94_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1767:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".e-picker{margin-bottom:1px;position:relative}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1768:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_reactions_vue_vue_type_style_index_0_id_4994d678_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1682);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_reactions_vue_vue_type_style_index_0_id_4994d678_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_reactions_vue_vue_type_style_index_0_id_4994d678_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1769:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".reaction-item{background-color:#1f1f1f;border-radius:10px;cursor:pointer;font-size:17px;font-weight:700;margin-right:10px;padding:3px 10px 5px}.reaction-item:hover{background-color:#131313}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1770:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_comments_vue_vue_type_style_index_0_id_52657c52_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1683);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_comments_vue_vue_type_style_index_0_id_52657c52_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_comments_vue_vue_type_style_index_0_id_52657c52_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1771:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, "", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1772:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_vue_vue_type_style_index_0_id_0778dfa4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1684);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_vue_vue_type_style_index_0_id_0778dfa4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_item_vue_vue_type_style_index_0_id_0778dfa4_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1773:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".social-posts-item{margin-bottom:15px}.social-posts-item img{-o-object-fit:cover;object-fit:cover;width:100%}.social-posts-item .social-post-content .type-image{padding:0}.social-posts-item .social-post-content-bottom{display:flex;justify-content:space-between;padding-bottom:5px}.social-posts-item .reply-input{background-color:transparent!important;border-bottom:3px solid #3c3c3c!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1774:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_create_post_vue_vue_type_style_index_0_id_7712b02a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1685);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_create_post_vue_vue_type_style_index_0_id_7712b02a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_create_post_vue_vue_type_style_index_0_id_7712b02a_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1775:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".privacy-option-item{border:1px solid #242424;cursor:pointer;display:inline;margin-right:15px;padding:7px 25px}.privacy-option-item.selected{background-color:#323232;transition:.5s}.privacy-option-sm{cursor:pointer;display:inline;margin-right:15px}.privacy-option-sm.selected{font-weight:700;transition:.5s}.submit-row{display:flex;justify-content:space-between}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1776:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_vue_vue_type_style_index_0_id_e731c186_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1686);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_vue_vue_type_style_index_0_id_e731c186_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_social_posts_vue_vue_type_style_index_0_id_e731c186_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1777:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".social-posts-container{position:relative}.post-scroll-container{overflow-x:hidden}.post-scroll-container::-webkit-scrollbar{width:0}.post-scroll-container::-webkit-scrollbar-thumb,.post-scroll-container::-webkit-scrollbar-track{background:transparent}.post-scroll-container::-webkit-scrollbar-thumb:hover{background:transparent}.filter-area{display:flex;justify-content:space-around;padding:15px}.filter-area .filter-item{cursor:pointer;font-size:25px}.filter-area .filter-item.selected{color:var(--person-color)}.list-enter-active,.list-leave-active,.list-move{transition:.2s cubic-bezier(.59,.12,.34,.95);transition-property:opacity,transform}.list-enter{opacity:0}.list-enter-to{opacity:1}.list-leave-to{opacity:0}.new-post-notification{align-items:center;background:linear-gradient(135deg,#2d2d2d,#1a1a1a);border:1px solid #f44;border-radius:25px;box-shadow:0 4px 12px rgba(0,0,0,.4),0 0 20px rgba(255,68,68,.2),inset 0 1px 0 hsla(0,0%,100%,.1);color:#fff;cursor:pointer;display:flex;font-size:14px;font-weight:500;gap:8px;justify-content:center;line-height:1.2;margin:0 auto 10px;max-width:90%;padding:10px 18px;position:sticky;top:10px;transition:all .3s cubic-bezier(.4,0,.2,1);width:-moz-fit-content;width:fit-content;z-index:1000}.new-post-notification:hover{background:linear-gradient(135deg,#3d3d3d,#2a2a2a);border-color:#f55;box-shadow:0 6px 20px rgba(0,0,0,.5),0 0 30px rgba(255,68,68,.3),inset 0 1px 0 hsla(0,0%,100%,.15);transform:translateY(-2px)}.new-post-notification:active{transform:translateY(-1px);transition:all .1s ease}.new-post-notification .notification-icon{animation:pulse 2s infinite;color:#f44;font-size:16px}.new-post-notification .notification-text{font-weight:600;letter-spacing:.3px;-webkit-user-select:none;-moz-user-select:none;user-select:none;white-space:nowrap}.new-post-notification .notification-close{border-radius:50%;color:#ccc;font-size:14px;opacity:.7;padding:2px;transition:all .2s ease}.new-post-notification .notification-close:hover{background:hsla(0,0%,100%,.1);color:#fff;opacity:1;transform:scale(1.1)}@keyframes pulse{0%,to{opacity:1;transform:scale(1)}50%{opacity:.7;transform:scale(1.05)}}@media(max-width:480px){.new-post-notification{border-radius:20px;font-size:13px;padding:8px 14px}.new-post-notification .notification-icon{font-size:14px}.new-post-notification .notification-close{font-size:12px}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ })

}]);