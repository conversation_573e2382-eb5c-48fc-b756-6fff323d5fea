(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[151],{

/***/ 1580:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1623);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("587dc6ca", content, true, {"sourceMap":false});

/***/ }),

/***/ 1622:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_circle_vue_vue_type_style_index_0_id_7f61f193_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1580);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_circle_vue_vue_type_style_index_0_id_7f61f193_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_app_circle_vue_vue_type_style_index_0_id_7f61f193_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1623:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".pulsating-circle{height:30px;left:50%;position:absolute;top:50%;transform:translateX(-50%) translateY(-50%);width:30px}.pulsating-circle:before{animation:pulse-ring 1.25s cubic-bezier(.215,.61,.355,1) infinite;background-color:var(--pulse-color);border-radius:45px;box-sizing:border-box;content:\"\";display:block;height:300%;margin-left:-100%;margin-top:-100%;position:relative;width:300%}.pulsating-circle:after{animation:pulse-dot 1.25s cubic-bezier(.455,.03,.515,.955) -.4s infinite;background-color:var(--circle-color);border-radius:15px;box-shadow:0 0 8px rgba(0,0,0,.3);content:\"\";display:block;height:100%;left:0;position:absolute;top:0;width:100%}@keyframes pulse-ring{0%{transform:scale(.33)}80%,to{opacity:0}}@keyframes pulse-dot{0%{transform:scale(.8)}50%{transform:scale(1)}to{transform:scale(.8)}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1635:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-circle.vue?vue&type=template&id=7f61f193
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "pulsating-circle",
    style: _vm.styleVars
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/app-circle.vue?vue&type=template&id=7f61f193

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/app-circle.vue?vue&type=script&lang=js
/* harmony default export */ var app_circlevue_type_script_lang_js = ({
  name: 'app-circle',
  props: ['circle', 'pulse'],
  computed: {
    styleVars: function styleVars() {
      return {
        '--circle-color': this.circle,
        '--pulse-color': this.pulse
      };
    }
  }
});
// CONCATENATED MODULE: ./components/Common/app-circle.vue?vue&type=script&lang=js
 /* harmony default export */ var Common_app_circlevue_type_script_lang_js = (app_circlevue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Common/app-circle.vue?vue&type=style&index=0&id=7f61f193&prod&lang=scss
var app_circlevue_type_style_index_0_id_7f61f193_prod_lang_scss = __webpack_require__(1622);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/app-circle.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Common_app_circlevue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var app_circle = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1660:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(105);
/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(leaflet__WEBPACK_IMPORTED_MODULE_0__);

var center_x = 117.3;
var center_y = 172.8;
var scale_x = 0.02072;
var scale_y = 0.0205;
/* harmony default export */ __webpack_exports__["a"] = (leaflet__WEBPACK_IMPORTED_MODULE_0___default.a.extend({}, leaflet__WEBPACK_IMPORTED_MODULE_0___default.a.CRS.Simple, {
  projection: leaflet__WEBPACK_IMPORTED_MODULE_0___default.a.Projection.LonLat,
  scale: function scale(zoom) {
    return Math.pow(2, zoom);
  },
  zoom: function zoom(sc) {
    return Math.log(sc) / 0.6931471805599453;
  },
  distance: function distance(pos1, pos2) {
    var x_difference = pos2.lng - pos1.lng;
    var y_difference = pos2.lat - pos1.lat;
    return Math.sqrt(x_difference * x_difference + y_difference * y_difference);
  },
  transformation: new leaflet__WEBPACK_IMPORTED_MODULE_0___default.a.Transformation(scale_x, center_x, -scale_y, center_y),
  infinite: true
}));

/***/ }),

/***/ 1721:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1849);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("35e278a5", content, true, {"sourceMap":false});

/***/ }),

/***/ 1848:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_eee98716_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1721);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_eee98716_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_index_vue_vue_type_style_index_0_id_eee98716_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1849:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".procedure-item{padding-bottom:15px}.hidden{display:none}.leaflet-control,.leaflet-control-zoom,.leaflet-control-zoom-out{color:#000!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2152:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/gps/index.vue?vue&type=template&id=eee98716
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('l-map', {
    ref: "map",
    staticStyle: {
      "width": "100%",
      "height": "calc(var(--tablet-height) - 105px)"
    },
    attrs: {
      "zoom": _vm.zoom,
      "options": _vm.mapOptions
    },
    on: {
      "ready": _vm.onReady
    }
  }, [_c('l-tile-layer', {
    attrs: {
      "url": _vm.url,
      "options": _vm.tileOptions,
      "attribution": _vm.attribution
    }
  }), _vm._v(" "), _vm.myLocation ? _c('l-marker', {
    attrs: {
      "lat-lng": _vm.myLocation
    }
  }, [_c('l-icon', {
    attrs: {
      "icon-anchor": [0, 0]
    }
  }, [_c('app-circle', {
    attrs: {
      "circle": "red"
    }
  })], 1), _vm._v(" "), _c('l-popup', {
    attrs: {
      "options": {
        minWidth: 50,
        maxWidth: 300,
        className: 'map-popup'
      }
    }
  }, [_vm._v("\n        This is where I am\n      ")])], 1) : _vm._e(), _vm._v(" "), _vm._l(_vm.points, function (point, index) {
    return point ? _c('l-marker', {
      key: index,
      attrs: {
        "index": index,
        "lat-lng": point.coords
      }
    }, [_c('l-icon', {
      attrs: {
        "icon-anchor": [0, 0]
      }
    }, [_c('app-circle', {
      attrs: {
        "circle": "limegreen"
      }
    })], 1), _vm._v(" "), _c('l-popup', {
      attrs: {
        "options": {
          minWidth: 600,
          maxWidth: 600,
          className: 'map-popup'
        }
      }
    }, [_vm._v("\n        Hello World\n      ")])], 1) : _vm._e();
  })], 2)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/gps/index.vue?vue&type=template&id=eee98716

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/leaflet/dist/leaflet-src.js
var leaflet_src = __webpack_require__(105);

// EXTERNAL MODULE: ./components/Common/map/fivemCRS.js
var fivemCRS = __webpack_require__(1660);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LTooltip.js
var LTooltip = __webpack_require__(2014);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LIcon.js
var LIcon = __webpack_require__(2015);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LMap.js
var LMap = __webpack_require__(1541);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LTileLayer.js
var LTileLayer = __webpack_require__(1542);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LMarker.js
var LMarker = __webpack_require__(1543);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LCircleMarker.js
var LCircleMarker = __webpack_require__(2016);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LCircle.js
var LCircle = __webpack_require__(2017);

// EXTERNAL MODULE: ./node_modules/vue2-leaflet/dist/components/LPopup.js
var LPopup = __webpack_require__(2018);

// EXTERNAL MODULE: ./node_modules/leaflet/dist/leaflet.css
var leaflet = __webpack_require__(1653);

// EXTERNAL MODULE: ./components/Common/app-circle.vue + 4 modules
var app_circle = __webpack_require__(1635);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/gps/index.vue?vue&type=script&lang=js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }
















/* harmony default export */ var gpsvue_type_script_lang_js = ({
  components: {
    AppCircle: app_circle["a" /* default */],
    LTooltip: LTooltip["a" /* default */],
    LIcon: LIcon["a" /* default */],
    LMap: LMap["a" /* default */],
    LTileLayer: LTileLayer["a" /* default */],
    LMarker: LMarker["a" /* default */],
    LCircleMarker: LCircleMarker["a" /* default */],
    LCircle: LCircle["a" /* default */],
    LPopup: LPopup["a" /* default */]
  },
  data: function data() {
    return {
      map: null,
      zoom: 4.5,
      center: Object(leaflet_src["latLng"])(47.56, 7.59),
      // center: latLng(this.coords?.x ?? 47.56, this.coords?.y ?? 7.59),
      url: 'http://dev-res.blrp.net/map/mapStyles/styleAtlas/{z}/{x}/{y}.jpg',
      attribution: 'Los Santos',
      mapOptions: {
        crs: fivemCRS["a" /* default */],
        zoomSnap: 0.4
      },
      tileOptions: {
        minZoom: 1.8,
        maxZoom: 11,
        maxNativeZoom: 5,
        tms: true
      }
    };
  },
  mounted: function mounted() {
    this.fetchZones();
  },
  methods: {
    onReady: function onReady(map) {
      var _this = this;
      this.map = map;

      // this.map.setMaxBounds(this.map.getBounds());

      console.log('this.map', this.map);
      setTimeout(function () {
        _this.panToMyLocation();
      }, 400);
      document.getElementsByClassName('leaflet-control-attribution')[0].classList.add('hidden');
    },
    fetchZones: function fetchZones() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        var regions;
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              _context.next = 2;
              return _this2.$axios.$get('http://blrp_tablet/getZoneRegions');
            case 2:
              regions = _context.sent;
              console.log('regions', regions);
            case 4:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    },
    panToMyLocation: function panToMyLocation() {
      this.map.panTo(Object(leaflet_src["latLng"])(this.coords.y, this.coords.x));
    }
  },
  watch: {
    coords: function coords() {
      var _this3 = this;
      setTimeout(function () {
        _this3.panToMyLocation();
      }, 400);
    }
  },
  computed: _objectSpread({
    points: function points() {
      return [];
    },
    myLocation: function myLocation() {
      if (this.coords) {
        return Object(leaflet_src["latLng"])(this.coords.y, this.coords.x);
      }
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    position: 'location/position',
    coords: 'location/coords'
  }))
});
// CONCATENATED MODULE: ./pages/gps/index.vue?vue&type=script&lang=js
 /* harmony default export */ var pages_gpsvue_type_script_lang_js = (gpsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/gps/index.vue?vue&type=style&index=0&id=eee98716&prod&lang=scss
var gpsvue_type_style_index_0_id_eee98716_prod_lang_scss = __webpack_require__(1848);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/gps/index.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  pages_gpsvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var gps = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);