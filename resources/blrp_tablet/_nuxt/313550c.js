(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[118],{

/***/ 1556:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

var render = function render() {
  var _vm$realName, _vm$realView;
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _vm.ready ? _c('crudy-table', {
    attrs: {
      "name": (_vm$realName = _vm.realName) !== null && _vm$realName !== void 0 ? _vm$realName : _vm.name,
      "view": (_vm$realView = _vm.realView) !== null && _vm$realView !== void 0 ? _vm$realView : _vm.view
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-debug.vue + 9 modules
var crudy_form_debug = __webpack_require__(315);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts






/* harmony default export */ var crudy_genvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'crudy-gen',
  components: {
    CrudyFormDebug: crudy_form_debug["a" /* default */],
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['name', 'view', 'filter'],
  data: function data() {
    return {
      meta: null,
      ready: false,
      tableUUID: null,
      realName: null,
      realView: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var name, route;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            name = _this.$route.name;
            _context.next = 3;
            return _this.$router.resolve({
              name: name
            }).route;
          case 3:
            route = _context.sent;
            _this.meta = route === null || route === void 0 ? void 0 : route.meta;
            _this.realName = _this.meta.model;
            _this.realView = _this.meta.view;
            _this.ready = true;
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  }
}));
// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_gen_crudy_genvue_type_script_lang_ts = (crudy_genvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_gen_crudy_genvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_gen = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1591:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-person-item.vue?vue&type=template&id=5dd3cab1&scoped=true




var police_person_itemvue_type_template_id_5dd3cab1_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "case-row",
    attrs: {
      "no-gutters": ""
    },
    on: {
      "click": function click($event) {
        return _vm.$router.push({
          path: "/cad/persons/".concat(_vm.person.id)
        });
      }
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "ml-3 font-weight-bold"
  }, [_c('img', {
    attrs: {
      "src": _vm.person.latest_mugshot,
      "size": "lg",
      "width": "100px"
    }
  })]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-3 font-weight-bold"
  }, [_c('div', [_vm._v(_vm._s(_vm.person.name))]), _vm._v(" "), _c('div', {
    staticClass: "mt-1"
  })]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-3"
  }, [_c('div', {
    staticClass: "mt-1"
  }, [_vm._v("# " + _vm._s(_vm.person.license))])]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-3"
  }, [_vm._v("\n      # " + _vm._s(_vm.person.phone) + "\n      "), _c('span'), _vm._v(" "), _c('i', {
    staticClass: "fa-solid fa-phone-arrow-up-right text-primary",
    on: {
      "click": _vm.callPerson
    }
  })])], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/police-person-item.vue?vue&type=template&id=5dd3cab1&scoped=true

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-person-item.vue?vue&type=script&lang=js

/* harmony default export */ var police_person_itemvue_type_script_lang_js = ({
  props: ['person'],
  name: "PolicePersonItem",
  methods: {
    callPerson: function callPerson() {
      this.$axios.$post('https://blrp_tablet/call', {
        number: this.person.phone
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/police-person-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_police_person_itemvue_type_script_lang_js = (police_person_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Police/police-person-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Police_police_person_itemvue_type_script_lang_js,
  police_person_itemvue_type_template_id_5dd3cab1_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "5dd3cab1",
  null
  
)

/* harmony default export */ var police_person_item = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 2114:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/parole.vue?vue&type=template&id=3b7c237e
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c('div', {
    staticClass: "custom-tabs"
  }, [_c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/parole"
    }
  }, [_vm._v("\n      Active Parole\n    ")]), _vm._v(" "), _c('router-link', {
    staticClass: "custom-link",
    attrs: {
      "to": "/cad/parole/archive"
    }
  }, [_vm._v("\n      Archive\n    ")])], 1), _vm._v(" "), _c('NuxtChild')], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/parole.vue?vue&type=template&id=3b7c237e

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Pages/Police/police-person-item.vue + 4 modules
var police_person_item = __webpack_require__(1591);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue + 4 modules
var crudy_gen = __webpack_require__(1556);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/parole.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }






/* harmony default export */ var parolevue_type_script_lang_js = ({
  components: {
    CrudyTable: crudy_table["a" /* default */],
    CrudyGen: crudy_gen["a" /* default */],
    AppPage: AppPage["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    PolicePersonItem: police_person_item["a" /* default */]
  },
  mixins: [],
  data: function data() {
    var _window$localStorage$;
    return {
      term: (_window$localStorage$ = window.localStorage.getItem('pd-person-search')) !== null && _window$localStorage$ !== void 0 ? _window$localStorage$ : '',
      persons: [],
      loading: true
    };
  },
  mounted: function mounted() {
    if (this.term.length > 2) {
      this.index();
    }
  },
  methods: {
    termChanged: function termChanged(value) {
      if (value.length > 0) {
        this.term = value;
        window.localStorage.setItem('pd-person-search', value);
        this.index();
      }
    },
    index: function index() {
      var _this = this;
      this.loading = true;
      return this.$axios.$get("/police/persons/search/".concat(this.term)).then(function (r) {
        _this.persons = r;
        _this.loading = false;
      });
    },
    scanId: function scanId() {
      var _this2 = this;
      this.isLoading = true;
      return this.$axios.post('https://blrp_tablet/scanID').then(function (r) {
        // const DL = r.data.DL;
        _this2.term = r.data.DL;
        _this2.loading = false;
        return _this2.index();
      });
    }
  },
  watch: {
    pendingNameSearch: function pendingNameSearch(value) {
      if (value) {
        this.term = this.pendingPlateSearch;
        this.$store.commit('system/CLEAR_PENDING_SEARCH_PLATE');
        this.index();
      }
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    pendingPlateSearch: 'system/pendingPlateSearch',
    pendingNameSearch: 'cad/pendingNameSearch'
  }))
});
// CONCATENATED MODULE: ./pages/cad/parole.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_parolevue_type_script_lang_js = (parolevue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/parole.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_parolevue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var parole = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);