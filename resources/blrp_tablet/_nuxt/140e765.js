(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[187],{

/***/ 1735:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1935);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("64f15f7e", content, true, {"sourceMap":false});

/***/ }),

/***/ 1736:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1937);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("f19c80a4", content, true, {"sourceMap":false});

/***/ }),

/***/ 1934:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_message_bubble_vue_vue_type_style_index_0_id_bcbe596c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1735);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_message_bubble_vue_vue_type_style_index_0_id_bcbe596c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_text_message_bubble_vue_vue_type_style_index_0_id_bcbe596c_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1935:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".message-body .message-location .location-btn{background:hsla(0,0%,100%,.1)!important;border:1px solid hsla(0,0%,100%,.2)}.message-body .message-location .location-btn:hover{background:hsla(0,0%,100%,.2)!important}.message-body .message-bank .bank-btn{background:rgba(76,175,80,.8)!important}.message-body .message-bank .bank-btn:hover{background:#4caf50!important}.message-body ::v-deep .markdown-content p{line-height:1.4;margin:0}.message-body ::v-deep .markdown-content>:first-child{margin-top:0}.message-body ::v-deep .markdown-content>:last-child{margin-bottom:0}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1936:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_phone_vue_vue_type_style_index_0_id_a4b99e42_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1736);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_phone_vue_vue_type_style_index_0_id_a4b99e42_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_phone_vue_vue_type_style_index_0_id_a4b99e42_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1937:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".text-messages-container{max-height:385px;overflow-y:auto;padding:8px 0}.text-messages-container .messages-list{display:flex;flex-direction:column;gap:8px;padding:0 12px}.text-messages-container .message-wrapper{align-items:flex-end;display:flex;gap:8px;max-width:85%}.text-messages-container .message-wrapper.message-sent{align-self:flex-end;flex-direction:row-reverse;margin-left:auto}.text-messages-container .message-wrapper.message-sent .message-content{align-items:flex-end}.text-messages-container .message-wrapper.message-received{align-self:flex-start;flex-direction:row;margin-right:auto}.text-messages-container .message-wrapper.message-received .message-content{align-items:flex-start}.text-messages-container .message-avatar{flex-shrink:0;margin-bottom:2px}.text-messages-container .message-content{display:flex;flex-direction:column;max-width:100%}.text-messages-container .message-content .message-body{border-radius:18px;font-size:14px;line-height:1.4;padding:8px 14px;word-wrap:break-word;background-color:#2c2c2e;box-shadow:0 1px 2px rgba(0,0,0,.3);color:#fff;position:relative}.text-messages-container .message-content .message-body.owner{background:linear-gradient(135deg,#007aff,#0056cc);color:#fff}.text-messages-container .message-content .message-body:before{bottom:0;content:\"\";height:0;position:absolute;width:0}.text-messages-container .message-content .message-time{color:#8e8e93;font-size:11px;margin-top:2px;padding:0 4px}.text-messages-container .message-content .message-time.message-time-sent{text-align:right}.text-messages-container .message-content .message-time.message-time-received{text-align:left}.text-messages-container .message-received .message-body:before{border-bottom:8px solid transparent;border-left:8px solid #2c2c2e;left:-6px}.text-messages-container .message-sent .message-body.owner:before{border-bottom:8px solid transparent;border-right:8px solid #007aff;right:-6px}.text-messages-container::-webkit-scrollbar{width:4px}.text-messages-container::-webkit-scrollbar-track{background:transparent}.text-messages-container::-webkit-scrollbar-thumb{background:hsla(0,0%,100%,.2);border-radius:2px}.text-messages-container::-webkit-scrollbar-thumb:hover{background:hsla(0,0%,100%,.3)}.text-messages-container .text-center{color:#8e8e93;padding:40px 20px}.send-area{border-top:1px solid hsla(0,0%,100%,.1);bottom:calc(var(--phone-toolbar-height) + 39px);padding:8px 12px;position:absolute;width:100%}.send-area .text-reply-hint{bottom:8px;color:#8e8e93;font-size:11px;position:absolute;right:8px}.send-area textarea{background-color:#1c1c1e!important;border:1px solid hsla(0,0%,100%,.2)!important;border-radius:20px!important;color:#fff!important;font-size:14px;line-height:1.4;padding:10px 16px!important;resize:none;-webkit-text-decoration:none;text-decoration:none}.send-area textarea:focus{border-color:#007aff!important;box-shadow:none!important}.send-area textarea::-moz-placeholder{color:#8e8e93!important}.send-area textarea::placeholder{color:#8e8e93!important}.send-area ::v-deep .v-text-field .v-input__control .v-input__slot{background-color:#1c1c1e!important;border-radius:20px!important}.send-area ::v-deep .v-text-field .v-input__control .v-input__slot:after,.send-area ::v-deep .v-text-field .v-input__control .v-input__slot:before{display:none!important}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2030:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VForm/VForm.js
var VForm = __webpack_require__(1533);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressCircular/VProgressCircular.js
var VProgressCircular = __webpack_require__(300);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.slice.js
var es_array_slice = __webpack_require__(67);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/thread/_phone.vue?vue&type=template&id=a4b99e42









var _phonevue_type_template_id_a4b99e42_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _vm.thread ? _c('div', [_c('span', {
    directives: [{
      name: "shortkey",
      rawName: "v-shortkey",
      value: ['enter'],
      expression: "['enter']"
    }],
    on: {
      "shortkey": _vm._enter
    }
  }), _vm._v(" "), _c('text-thread', {
    attrs: {
      "thread": _vm.thread,
      "hide-message-preview": true
    }
  }), _vm._v(" "), _c('div', {
    directives: [{
      name: "chat-scroll",
      rawName: "v-chat-scroll",
      value: {
        always: false,
        smooth: true,
        notSmoothOnInit: true
      },
      expression: "{ always: false, smooth: true, notSmoothOnInit: true}"
    }],
    staticClass: "text-messages-container"
  }, [_vm.thread.messages && _vm.thread.messages.length > 0 ? _c('div', {
    staticClass: "messages-list"
  }, _vm._l(_vm.thread.messages.slice(-25), function (message, index) {
    return _c('div', {
      key: message.id || index,
      staticClass: "message-wrapper",
      class: {
        'message-sent': message.owner,
        'message-received': !message.owner
      }
    }, [!message.owner ? _c('div', {
      staticClass: "message-avatar"
    }, [_c('text-avatar', {
      attrs: {
        "phone": _vm.thread.phone
      }
    })], 1) : _vm._e(), _vm._v(" "), _c('div', {
      staticClass: "message-content"
    }, [_c('text-message-bubble', {
      ref: "bubble",
      refInFor: true,
      attrs: {
        "message": message,
        "contact": _vm.currentContact
      }
    }), _vm._v(" "), _c('div', {
      staticClass: "message-time",
      class: {
        'message-time-sent': message.owner,
        'message-time-received': !message.owner
      }
    }, [_c('app-timestamp', {
      attrs: {
        "stamp": message.time,
        "format": "short"
      }
    })], 1)], 1)]);
  }), 0) : _c('div', {
    staticClass: "text-center mt-3"
  }, [_c(VProgressCircular["a" /* default */], {
    attrs: {
      "indeterminate": "",
      "color": "primary",
      "size": "24"
    }
  }), _vm._v(" "), _c('div', {
    staticClass: "text-caption mt-2"
  }, [_vm._v("Loading Messages...")])], 1)]), _vm._v(" "), _c('div', {
    staticClass: "send-area"
  }, [_c('div', {
    staticClass: "text-right mr-3 mb-1"
  }, [_c('span', {
    staticClass: "text-danger"
  }, [_vm._v("\n        " + _vm._s(this.error) + "\n      ")])]), _vm._v(" "), _c(VForm["a" /* default */], {
    on: {
      "submit": function submit($event) {
        $event.preventDefault();
        return _vm.submit.apply(null, arguments);
      }
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "fab": "",
      "icon": "",
      "right": "",
      "absolute": "",
      "x-small": "",
      "color": "primary"
    },
    on: {
      "click": _vm.submit
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-arrow-right"
  })]), _vm._v(" "), _c(VBtn["a" /* default */], {
    staticClass: "mr-5",
    attrs: {
      "fab": "",
      "icon": "",
      "right": "",
      "absolute": "",
      "x-small": "",
      "color": "secondary"
    },
    on: {
      "click": _vm.takePicture
    }
  }, [_c('i', {
    staticClass: "fa-solid fa-camera"
  })]), _vm._v(" "), _c('crudy-editor', {
    ref: "editor",
    attrs: {
      "simplified": true,
      "character-limit": 255
    },
    model: {
      value: _vm.reply,
      callback: function callback($$v) {
        _vm.reply = $$v;
      },
      expression: "reply"
    }
  })], 1)], 1)], 1) : _c('div', [_c(VCard["a" /* default */], [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n      Unable to Locate Messages\n    ")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_vm._v("\n      The SMS thread for " + _vm._s(_vm.$route.params.phone) + " either does not exist, has been automatically removed from phone memory to conserve space, or was deleted.\n\n      "), _c('br'), _vm._v(" "), _c('br'), _vm._v("\n\n      To start a new thread, send a message by tapping the button below. Messages from numbers that were automatically removed will be retrieved from the cloud.\n    ")]), _vm._v(" "), _c(components_VCard["a" /* VCardActions */], [_c(VBtn["a" /* default */], {
    on: {
      "click": function click($event) {
        return _vm.sendNewMessageToPhone(_vm.$route.params.phone);
      }
    }
  }, [_vm._v("\n        Start Messaging "), _c('kbd', [_vm._v("# " + _vm._s(_vm.$route.params.phone))])])], 1)], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/phone/thread/_phone.vue?vue&type=template&id=a4b99e42

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.includes.js
var es_array_includes = __webpack_require__(39);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.includes.js
var es_string_includes = __webpack_require__(47);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// EXTERNAL MODULE: ./components/Pages/Text/text-thread.vue + 4 modules
var text_thread = __webpack_require__(1670);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-editor.vue + 27 modules
var crudy_editor = __webpack_require__(312);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VIcon/VIcon.js
var VIcon = __webpack_require__(259);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-message-bubble.vue?vue&type=template&id=bcbe596c



var text_message_bubblevue_type_template_id_bcbe596c_render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _c('div', {
    staticClass: "message-body",
    class: {
      'owner': _vm.message.owner
    }
  }, [_vm.item.embed === 'text' ? _c('div', {
    staticClass: "message-text"
  }, [_c('app-markdown-view', {
    attrs: {
      "source": _vm.item.packet
    }
  })], 1) : _vm.item.embed === 'position' ? _c('div', {
    staticClass: "message-location"
  }, [_vm.item.packet.current_coords ? _c(VBtn["a" /* default */], {
    staticClass: "location-btn",
    attrs: {
      "small": "",
      "color": "primary"
    },
    on: {
      "click": function click($event) {
        return _vm.setGPS(_vm.item.packet.current_coords);
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-1",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-location-dot")]), _vm._v("\n      " + _vm._s(_vm.item.packet.real_area ? _vm.item.packet.real_area : _vm.item.packet.current_zone) + "\n    ")], 1) : _c('span', {
    staticClass: "text-muted"
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-1",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-location-slash")]), _vm._v("\n      Invalid Location\n    ")], 1)], 1) : _vm.item.embed === 'bank' ? _c('div', {
    staticClass: "message-bank"
  }, [_c(VBtn["a" /* default */], {
    staticClass: "bank-btn",
    attrs: {
      "small": "",
      "color": "success"
    },
    on: {
      "click": function click($event) {
        return _vm.saveBank(_vm.item.packet.bank_account);
      }
    }
  }, [_c(VIcon["a" /* default */], {
    staticClass: "mr-1",
    attrs: {
      "small": ""
    }
  }, [_vm._v("fa-solid fa-building-columns")]), _vm._v("\n      Save Bank Account\n    ")], 1)], 1) : _vm._e()]);
};
var text_message_bubblevue_type_template_id_bcbe596c_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Text/text-message-bubble.vue?vue&type=template&id=bcbe596c

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.starts-with.js
var es_string_starts_with = __webpack_require__(144);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Text/text-message-bubble.vue?vue&type=script&lang=ts









function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/* harmony default export */ var text_message_bubblevue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'text-message-bubble',
  components: {
    AppTimestamp: app_timestamp["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */]
  },
  props: {
    message: {
      type: Object
    },
    contact: {
      type: Object
    }
  },
  data: function data() {
    return {
      hovering: false
    };
  },
  created: function created() {},
  methods: {
    setGPS: function setGPS(coords) {
      this.$store.dispatch('nui/setGPS', coords);
    },
    saveBank: function saveBank(bank, sender_name) {
      this.$store.dispatch('nui/saveBank', {
        bank: bank
      });
    }
  },
  computed: _objectSpread({
    item: function item() {
      if (this.message.message.startsWith('{')) {
        return JSON.parse(this.message.message);
      }
      return {
        embed: 'text',
        packet: this.message.message
      };
    }
  }, Object(vuex_esm["b" /* mapGetters */])({}))
}));
// CONCATENATED MODULE: ./components/Pages/Text/text-message-bubble.vue?vue&type=script&lang=ts
 /* harmony default export */ var Text_text_message_bubblevue_type_script_lang_ts = (text_message_bubblevue_type_script_lang_ts); 
// EXTERNAL MODULE: ./components/Pages/Text/text-message-bubble.vue?vue&type=style&index=0&id=bcbe596c&prod&lang=scss
var text_message_bubblevue_type_style_index_0_id_bcbe596c_prod_lang_scss = __webpack_require__(1934);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Text/text-message-bubble.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Text_text_message_bubblevue_type_script_lang_ts,
  text_message_bubblevue_type_template_id_bcbe596c_render,
  text_message_bubblevue_type_template_id_bcbe596c_staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var text_message_bubble = (component.exports);
// EXTERNAL MODULE: ./components/Pages/Text/ContactFormMixin.js
var ContactFormMixin = __webpack_require__(1566);

// EXTERNAL MODULE: ./components/Pages/Text/text-avatar.vue + 4 modules
var text_avatar = __webpack_require__(1636);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/phone/thread/_phone.vue?vue&type=script&lang=js


function _phonevue_type_script_lang_js_ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _phonevue_type_script_lang_js_objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? _phonevue_type_script_lang_js_ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : _phonevue_type_script_lang_js_ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




















/* harmony default export */ var _phonevue_type_script_lang_js = ({
  name: "index",
  components: {
    TextAvatar: text_avatar["a" /* default */],
    TextMessageBubble: text_message_bubble,
    CrudyEditor: crudy_editor["a" /* default */],
    TextThread: text_thread["a" /* default */],
    AppTimestamp: app_timestamp["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    AppCard: app_card["a" /* default */]
  },
  mixins: [ContactFormMixin["a" /* default */]],
  data: function data() {
    return {
      reply: null,
      submitting: false,
      isCoolingDown: false,
      error: null
    };
  },
  created: function created() {
    // console.log('Loading phone text thread where sender is', this.$route.params.phone)

    // this.$store.commit('text/SET_THREAD_VIEWED', this.$route.params.phone)

    // this.$axios.$post('https://blrp_tablet/markTextThreadRead', {phone: this.$route.params.phone})

    this.$axios.$post('https://blrp_tablet/inquire-text-thread-messages', {
      phone: this.$route.params.phone
    });
    this.$store.commit('text/SET_THREAD_VIEWED', this.$route.params.phone);
  },
  updated: function updated() {
    var _this$lastMessage;
    if (!((_this$lastMessage = this.lastMessage) !== null && _this$lastMessage !== void 0 && _this$lastMessage.isRead)) {
      // console.log('marking read')
      this.$store.commit('text/SET_THREAD_VIEWED', this.$route.params.phone);
    }
  },
  methods: {
    onkeydown: function onkeydown(e) {
      var keyCode = e.which || e.keyCode;
      if (keyCode === 13 && !e.shiftKey) {
        // Enter Pressed
        e.preventDefault();
        this.submit();
      }
    },
    _enter: function _enter() {
      this.submit();
    },
    submit: function submit() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _this.error = null;
              if (!_this.isCoolingDown) {
                _context3.next = 3;
                break;
              }
              return _context3.abrupt("return", _this.error = 'Slow down a little bit!');
            case 3:
              if (_this.reply) {
                _context3.next = 5;
                break;
              }
              return _context3.abrupt("return", _this.error = 'Must type something...');
            case 5:
              if (!(_this.reply.length < 5)) {
                _context3.next = 7;
                break;
              }
              return _context3.abrupt("return", _this.error = 'Messages must be at least 5 characters.');
            case 7:
              if (!(_this.reply.length > 255)) {
                _context3.next = 9;
                break;
              }
              return _context3.abrupt("return", _this.error = 'Messages is too long.');
            case 9:
              _this.submitting = true;
              _this.releaseCooldown();
              _this.$axios.$post('https://blrp_tablet/sendMessage', {
                phone: _this.$route.params.phone,
                message: _this.reply
              }).then(function () {
                _this.submitting = false;
                _this.reply = null;
              });
              if (_this.reply.includes('png') || _this.reply.includes('jpg') || _this.reply.includes('gif') || _this.reply.includes('jpeg')) {
                setTimeout(/*#__PURE__*/Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
                  return regeneratorRuntime.wrap(function _callee2$(_context2) {
                    while (1) switch (_context2.prev = _context2.next) {
                      case 0:
                        _context2.next = 2;
                        return _this.$router.push({
                          path: '/'
                        });
                      case 2:
                        setTimeout(/*#__PURE__*/Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
                          return regeneratorRuntime.wrap(function _callee$(_context) {
                            while (1) switch (_context.prev = _context.next) {
                              case 0:
                                _context.next = 2;
                                return _this.$router.back();
                              case 2:
                              case "end":
                                return _context.stop();
                            }
                          }, _callee);
                        })), 500);
                      case 3:
                      case "end":
                        return _context2.stop();
                    }
                  }, _callee2);
                })), 150);
              }
            case 13:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    },
    takePicture: function takePicture() {
      var _this2 = this;
      this.$axios.$post('https://blrp_tablet/takePictureWithResult').then(function (response) {
        _this2.reply = response;
        _this2.submit();
      });
    },
    releaseCooldown: function releaseCooldown() {
      var _this3 = this;
      this.isCoolingDown = true;
      setTimeout(function () {
        _this3.isCoolingDown = false;
      }, 1000);
    }
  },
  computed: _phonevue_type_script_lang_js_objectSpread({
    thread: function thread() {
      return this.messageThread(this.$route.params.phone);
    },
    currentContact: function currentContact() {
      return this.keyedContacts[this.$route.params.phone];
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    threads: 'text/threads',
    focusing: 'system/focusing',
    mode: 'system/mode',
    messageThread: 'text/messageThread',
    keyedContacts: 'text/keyedContacts'
  })),
  watch: {
    reply: function reply() {
      this.error = null;
    }
  }
});
// CONCATENATED MODULE: ./pages/phone/thread/_phone.vue?vue&type=script&lang=js
 /* harmony default export */ var thread_phonevue_type_script_lang_js = (_phonevue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/phone/thread/_phone.vue?vue&type=style&index=0&id=a4b99e42&prod&lang=scss
var _phonevue_type_style_index_0_id_a4b99e42_prod_lang_scss = __webpack_require__(1936);

// CONCATENATED MODULE: ./pages/phone/thread/_phone.vue






/* normalize component */

var _phone_component = Object(componentNormalizer["a" /* default */])(
  thread_phonevue_type_script_lang_js,
  _phonevue_type_template_id_a4b99e42_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _phone = __webpack_exports__["default"] = (_phone_component.exports);

/***/ })

}]);