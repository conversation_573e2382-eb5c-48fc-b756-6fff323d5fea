(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[125,14],{

/***/ 1556:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

var render = function render() {
  var _vm$realName, _vm$realView;
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _vm.ready ? _c('crudy-table', {
    attrs: {
      "name": (_vm$realName = _vm.realName) !== null && _vm$realName !== void 0 ? _vm$realName : _vm.name,
      "view": (_vm$realView = _vm.realView) !== null && _vm$realView !== void 0 ? _vm$realView : _vm.view
    }
  }) : _vm._e();
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=template&id=95986b30

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// EXTERNAL MODULE: ./components/Crudy/crudy-form/crudy-form-debug.vue + 9 modules
var crudy_form_debug = __webpack_require__(315);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts






/* harmony default export */ var crudy_genvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  name: 'crudy-gen',
  components: {
    CrudyFormDebug: crudy_form_debug["a" /* default */],
    CrudyTable: crudy_table["a" /* default */]
  },
  props: ['name', 'view', 'filter'],
  data: function data() {
    return {
      meta: null,
      ready: false,
      tableUUID: null,
      realName: null,
      realView: null
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      var name, route;
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            name = _this.$route.name;
            _context.next = 3;
            return _this.$router.resolve({
              name: name
            }).route;
          case 3:
            route = _context.sent;
            _this.meta = route === null || route === void 0 ? void 0 : route.meta;
            _this.realName = _this.meta.model;
            _this.realView = _this.meta.view;
            _this.ready = true;
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  }
}));
// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue?vue&type=script&lang=ts
 /* harmony default export */ var crudy_gen_crudy_genvue_type_script_lang_ts = (crudy_genvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  crudy_gen_crudy_genvue_type_script_lang_ts,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crudy_gen = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1591:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-person-item.vue?vue&type=template&id=5dd3cab1&scoped=true




var police_person_itemvue_type_template_id_5dd3cab1_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VRow["a" /* default */], {
    staticClass: "case-row",
    attrs: {
      "no-gutters": ""
    },
    on: {
      "click": function click($event) {
        return _vm.$router.push({
          path: "/cad/persons/".concat(_vm.person.id)
        });
      }
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "ml-3 font-weight-bold"
  }, [_c('img', {
    attrs: {
      "src": _vm.person.latest_mugshot,
      "size": "lg",
      "width": "100px"
    }
  })]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-3 font-weight-bold"
  }, [_c('div', [_vm._v(_vm._s(_vm.person.name))]), _vm._v(" "), _c('div', {
    staticClass: "mt-1"
  })]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-3"
  }, [_c('div', {
    staticClass: "mt-1"
  }, [_vm._v("# " + _vm._s(_vm.person.license))])]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "ml-3"
  }, [_vm._v("\n      # " + _vm._s(_vm.person.phone) + "\n      "), _c('span'), _vm._v(" "), _c('i', {
    staticClass: "fa-solid fa-phone-arrow-up-right text-primary",
    on: {
      "click": _vm.callPerson
    }
  })])], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/police-person-item.vue?vue&type=template&id=5dd3cab1&scoped=true

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-person-item.vue?vue&type=script&lang=js

/* harmony default export */ var police_person_itemvue_type_script_lang_js = ({
  props: ['person'],
  name: "PolicePersonItem",
  methods: {
    callPerson: function callPerson() {
      this.$axios.$post('https://blrp_tablet/call', {
        number: this.person.phone
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/police-person-item.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_police_person_itemvue_type_script_lang_js = (police_person_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Police/police-person-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Police_police_person_itemvue_type_script_lang_js,
  police_person_itemvue_type_template_id_5dd3cab1_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "5dd3cab1",
  null
  
)

/* harmony default export */ var police_person_item = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1592:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Housing/HouseListingItem.vue?vue&type=template&id=4545cfe8
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div');
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Housing/HouseListingItem.vue?vue&type=template&id=4545cfe8

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Housing/HouseListingItem.vue?vue&type=script&lang=js



/* harmony default export */ var HouseListingItemvue_type_script_lang_js = ({
  name: 'HouseListingItem',
  components: {
    AppFormGroup: app_form_group["a" /* default */]
  },
  props: ['house', 'manage'],
  data: function data() {
    return {
      selected: null
    };
  },
  methods: {
    select: function select(house) {
      window.$events.$emit('housing::record::selected', house);
      if (this.selected && this.selected.id === house.id) {
        this.selected = null;
      } else {
        this.selected = house;
      }
    },
    modifyProperty: function modifyProperty(house) {
      window.$events.$emit('housing::record::edit', house);
    },
    deleteProperty: function deleteProperty(house) {
      return this.$axios.$post('/housinfRg/mark-deleted', {
        id: house.id
      }).then(function (data) {
        window.$events.$emit('housing:record:update');
        fetch("https://blrp_tablet/updateProperty", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          },
          body: JSON.stringify(data)
        }).then(function (resp) {
          return resp.json();
        }).then(function (resp) {});
      });
    },
    markAsListed: function markAsListed(house) {
      return this.$axios.$post('/housing/mark-listed', {
        id: house.id
      }).then(function (data) {
        window.$events.$emit('housing:record:update');
        // console.log('is listed', response.is_listed)
        fetch("https://blrp_tablet/updateProperty", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          },
          body: JSON.stringify(data)
        }).then(function (resp) {
          return resp.json();
        }).then(function (resp) {});
      });
    },
    removeListed: function removeListed(house) {
      return this.$axios.$post('/housing/unmark-listed', {
        id: house.id
      }).then(function (data) {
        window.$events.$emit('housing:record:update');
        fetch("https://blrp_tablet/updateProperty", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          },
          body: JSON.stringify(data)
        }).then(function (resp) {
          return resp.json();
        }).then(function (resp) {});
      });
    },
    wayPointHouse: function wayPointHouse(house) {
      fetch("https://blrp_tablet/waypointHouse", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          coords: house.door_location
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {});
    },
    sellClosest: function sellClosest(house) {
      window.$events.$emit('housing:record:update');
      fetch("https://blrp_tablet/sellClosest", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: house.id
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {});
    },
    revokeProperty: function revokeProperty(house) {
      window.$events.$emit('housing:record:update');
      return this.$axios.$post('/housing/revoke-property', {
        id: house.id
      }).then(function (data) {
        fetch("https://blrp_tablet/updateProperty", {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json; charset=UTF-8'
          },
          body: JSON.stringify(data)
        }).then(function (resp) {
          return resp.json();
        }).then(function (resp) {});
      });
    },
    makeClosestCoowner: function makeClosestCoowner(house) {
      window.$events.$emit('housing:record:update');
      fetch("https://blrp_tablet/makeClosestCoowner", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: house.id
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {});
    },
    setGarageHeading: function setGarageHeading(house) {
      window.$events.$emit('housing:record:update');
      fetch("https://blrp_tablet/setGarageHeading", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: house.id
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {});
    },
    changeInterior: function changeInterior(house) {
      window.$events.$emit('housing:record:update');
      fetch("https://blrp_tablet/changeInterior", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: house.id
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {});
    }
  },
  computed: Object(vuex_esm["b" /* mapGetters */])({
    canManageProperties: 'auth/canManageProperties',
    isAdmin: 'auth/isAdmin'
  })
});
// CONCATENATED MODULE: ./components/Housing/HouseListingItem.vue?vue&type=script&lang=js
 /* harmony default export */ var Housing_HouseListingItemvue_type_script_lang_js = (HouseListingItemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Housing/HouseListingItem.vue?vue&type=style&index=0&id=4545cfe8&prod&lang=scss
var HouseListingItemvue_type_style_index_0_id_4545cfe8_prod_lang_scss = __webpack_require__(1658);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Housing/HouseListingItem.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Housing_HouseListingItemvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var HouseListingItem = __webpack_exports__["a"] = (component.exports);

/***/ }),

/***/ 1631:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1659);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("59cdb970", content, true, {"sourceMap":false});

/***/ }),

/***/ 1658:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HouseListingItem_vue_vue_type_style_index_0_id_4545cfe8_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1631);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HouseListingItem_vue_vue_type_style_index_0_id_4545cfe8_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_7_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_7_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_7_oneOf_1_2_node_modules_sass_loader_dist_cjs_js_ref_7_oneOf_1_3_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_HouseListingItem_vue_vue_type_style_index_0_id_4545cfe8_prod_lang_scss__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1659:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".house-mini-image{border-radius:10%;height:64px}.house-normal-image{height:250px}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 1665:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/ColumnNormalText.vue?vue&type=template&id=cae8ae6a
var render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('span', [_vm._v("\n  " + _vm._s(_vm.value) + "\n")]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./components/Common/parts/ColumnNormalText.vue?vue&type=template&id=cae8ae6a

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Common/parts/ColumnNormalText.vue?vue&type=script&lang=js
/* harmony default export */ var ColumnNormalTextvue_type_script_lang_js = ({
  name: "ColumnNormalText",
  props: ['value']
});
// CONCATENATED MODULE: ./components/Common/parts/ColumnNormalText.vue?vue&type=script&lang=js
 /* harmony default export */ var parts_ColumnNormalTextvue_type_script_lang_js = (ColumnNormalTextvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Common/parts/ColumnNormalText.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  parts_ColumnNormalTextvue_type_script_lang_js,
  render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var ColumnNormalText = __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 1714:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1835);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("88fc4c88", content, true, {"sourceMap":false});

/***/ }),

/***/ 1834:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_charges_frequency_vue_vue_type_style_index_0_id_50af5ac5_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1714);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_charges_frequency_vue_vue_type_style_index_0_id_50af5ac5_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_police_charges_frequency_vue_vue_type_style_index_0_id_50af5ac5_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1835:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".v-data-table[data-v-50af5ac5] .v-data-table__wrapper{border-radius:8px}.clickable-rows[data-v-50af5ac5] tbody tr{cursor:pointer;transition:background-color .2s ease}.clickable-rows[data-v-50af5ac5] tbody tr:hover{background-color:hsla(0,0%,100%,.05)!important}.clickable-rows[data-v-50af5ac5] .v-data-table__expand-icon{font-size:14px!important;height:20px!important;width:20px!important}.clickable-rows[data-v-50af5ac5] .v-data-table__expand-icon .v-icon{font-size:14px!important}.type-badge[data-v-50af5ac5]{border-radius:2px;color:#fff;display:inline-block;font-size:10px;font-weight:600;letter-spacing:.5px;line-height:1.2;min-width:60px;padding:4px 8px;text-align:center;text-transform:uppercase}.type-infraction[data-v-50af5ac5]{background:#2e7d32;border:1px solid #1b5e20}.type-misdemeanor[data-v-50af5ac5]{background:#f57c00;border:1px solid #e65100}.type-felony[data-v-50af5ac5]{background:#c62828;border:1px solid #b71c1c}.type-evasion[data-v-50af5ac5]{background:#ff6f00;border:1px solid #e65100}.type-aggravated[data-v-50af5ac5]{background:#ad1457;border:1px solid #880e4f}.type-court[data-v-50af5ac5]{background:#6a1b9a;border:1px solid #4a148c}.type-default[data-v-50af5ac5]{background:#424242;border:1px solid #212121}.dark-expanded[data-v-50af5ac5]{background:#1e1e1e!important;border-top:1px solid #424242}.expanded-content[data-v-50af5ac5]{background:#2d2d2d;border:1px solid #424242;border-radius:8px;padding:16px}.chart-container[data-v-50af5ac5]{background:#2d2d2d;border-radius:4px;height:80px;margin:8px 0;padding:8px;position:relative}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2021:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTab.js
var VTab = __webpack_require__(1534);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabItem.js + 1 modules
var VTabItem = __webpack_require__(1554);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabs.js + 3 modules
var VTabs = __webpack_require__(1550);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTabs/VTabsItems.js + 1 modules
var VTabsItems = __webpack_require__(892);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/persons/_id/index.vue?vue&type=template&id=0e323b4b





var _idvue_type_template_id_0e323b4b_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('app-page', {
    attrs: {
      "loading": _vm.loading
    }
  }, [_vm.profile ? _c('div', [_c('div', {}, [_vm.isWanted ? _c('span', {
    staticClass: "text-danger",
    staticStyle: {
      "font-size": "100px",
      "position": "absolute",
      "opacity": "15%"
    }
  }, [_vm._v("\n        WANTED. THIS PERSON IS WANTED.\n        WANTED. THIS PERSON IS WANTED.\n        WANTED. THIS PERSON IS WANTED.\n        WANTED. THIS PERSON IS WANTED.\n      ")]) : _vm._e(), _vm._v(" "), _c('crudy-table-items-component-citizen', {
    attrs: {
      "item": _vm.profile
    }
  }), _vm._v(" "), _c('br'), _vm._v(" "), _c(VTabs["a" /* default */], {
    model: {
      value: _vm.tab,
      callback: function callback($$v) {
        _vm.tab = $$v;
      },
      expression: "tab"
    }
  }, [_c(VTab["a" /* default */], [_vm._v("\n          Involved Incidents\n        ")]), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "lazy": ""
    }
  }, [_vm._v("\n          Details\n        ")]), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "lazy": ""
    }
  }, [_vm._v("\n          Known Affiliates\n        ")]), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "lazy": ""
    }
  }, [_vm._v("\n          Vehicles\n        ")]), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "lazy": ""
    }
  }, [_vm._v("\n          Parole History\n        ")]), _vm._v(" "), _c(VTab["a" /* default */], {
    attrs: {
      "lazy": ""
    }
  }, [_vm._v("\n          All Charges\n        ")])], 1), _vm._v(" "), _c(VTabsItems["a" /* default */], {
    staticStyle: {
      "background-color": "transparent"
    },
    model: {
      value: _vm.tab,
      callback: function callback($$v) {
        _vm.tab = $$v;
      },
      expression: "tab"
    }
  }, [_c(VTabItem["a" /* default */], [_c('crudy-table', {
    attrs: {
      "name": "PoliceIncident",
      "view": "index",
      "filter": {
        persons: _vm.profile.id
      }
    }
  })], 1), _vm._v(" "), _c(VTabItem["a" /* default */], [_c('crudy-table', {
    ref: "citizenDetails",
    attrs: {
      "name": "CitizenDetail",
      "view": "index",
      "autofill": {
        person_id: _vm.profile.id
      },
      "filter": {
        person_id: _vm.profile.id
      }
    }
  })], 1), _vm._v(" "), _c(VTabItem["a" /* default */], [_c('affiliations')], 1), _vm._v(" "), _c(VTabItem["a" /* default */], [_c('crudy-table', {
    attrs: {
      "name": "UserCar",
      "view": "index",
      "filter": {
        characterNumber: _vm.profile.character_number
      }
    }
  })], 1), _vm._v(" "), _c(VTabItem["a" /* default */], [_c('crudy-table', {
    attrs: {
      "name": "CitizenParole",
      "view": "index",
      "filter": {
        character_number: _vm.profile.id
      }
    }
  })], 1), _vm._v(" "), _c(VTabItem["a" /* default */], [_c('police-charges-frequency', {
    attrs: {
      "character-id": _vm.profile.id
    }
  })], 1)], 1)], 1)]) : _vm._e()]);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/persons/_id/index.vue?vue&type=template&id=0e323b4b

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.regexp.exec.js
var es_regexp_exec = __webpack_require__(36);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.string.replace.js
var es_string_replace = __webpack_require__(72);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Housing/HouseListingItem.vue + 4 modules
var HouseListingItem = __webpack_require__(1592);

// EXTERNAL MODULE: ./components/Common/Loader.vue + 4 modules
var Loader = __webpack_require__(190);

// EXTERNAL MODULE: ./components/Common/ItemDetail.vue + 4 modules
var ItemDetail = __webpack_require__(151);

// EXTERNAL MODULE: ./components/Crud/parts/CrudSelect.vue + 4 modules
var CrudSelect = __webpack_require__(203);

// EXTERNAL MODULE: ./components/Common/PoliceCarSelector.vue + 4 modules
var PoliceCarSelector = __webpack_require__(355);

// EXTERNAL MODULE: ./components/Common/TableWrapper.vue + 4 modules
var TableWrapper = __webpack_require__(311);

// EXTERNAL MODULE: ./components/Pages/Police/police-person-item.vue + 4 modules
var police_person_item = __webpack_require__(1591);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/person-car.vue?vue&type=template&id=36c94f24&scoped=true



var person_carvue_type_template_id_36c94f24_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VRow["a" /* default */], {
    staticClass: "case-row-no-hover",
    staticStyle: {
      "cursor": "default"
    },
    attrs: {
      "no-gutters": ""
    }
  }, [_c(VCol["a" /* default */], {
    staticClass: "ml-4",
    attrs: {
      "md": "2"
    }
  }, [_c('b', [_vm._v(_vm._s(_vm.ownerName))])]), _vm._v(" "), _c(VCol["a" /* default */], {
    staticClass: "font-weight-bold",
    attrs: {
      "md": "2"
    }
  }, [_c('b', [_vm._v("#")]), _vm._v(" " + _vm._s(_vm.car.registration) + "\n    ")]), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n        " + _vm._s(_vm.car.vehicle.toUpperCase()) + "\n    ")]), _vm._v(" "), _c(VCol["a" /* default */], [_vm._v("\n        " + _vm._s(_vm.car.crimes_used_in) + "\n        "), _c('span', {
    staticClass: "text-muted"
  }, [_vm._v("\n            Incidents\n        ")])]), _vm._v(" "), _c(VCol["a" /* default */], [_vm.car.in_impound ? _c('span', {
    staticClass: "text-warning"
  }, [_vm._v("IMPOUNDED")]) : _vm._e(), _vm._v(" "), _vm.car.seized ? _c('span', {
    staticClass: "text-danger"
  }, [_vm._v("SEIZED")]) : _vm._e(), _vm._v(" "), !_vm.car.seized || !_vm.car.in_impound ? _c('span', {
    staticClass: "text-muted"
  }, [_vm._v("--")]) : _vm._e()])], 1);
};
var person_carvue_type_template_id_36c94f24_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/person-car.vue?vue&type=template&id=36c94f24&scoped=true

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/person-car.vue?vue&type=script&lang=js
/* harmony default export */ var person_carvue_type_script_lang_js = ({
  props: ['car', 'ownerName'],
  name: "PersonCar"
});
// CONCATENATED MODULE: ./components/Pages/Police/person-car.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_person_carvue_type_script_lang_js = (person_carvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./components/Pages/Police/person-car.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  Police_person_carvue_type_script_lang_js,
  person_carvue_type_template_id_36c94f24_scoped_true_render,
  person_carvue_type_template_id_36c94f24_scoped_true_staticRenderFns,
  false,
  null,
  "36c94f24",
  null
  
)

/* harmony default export */ var person_car = (component.exports);
// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/affiliations.vue?vue&type=template&id=414758c7&scoped=true
var affiliationsvue_type_template_id_414758c7_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', _vm._l(_vm.persons, function (person) {
    return _c('div', [_c('crudy-table-items-component-citizen', {
      attrs: {
        "item": person
      }
    })], 1);
  }), 0);
};
var affiliationsvue_type_template_id_414758c7_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/affiliations.vue?vue&type=template&id=414758c7&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/affiliations.vue?vue&type=script&lang=js



/* harmony default export */ var affiliationsvue_type_script_lang_js = ({
  name: "affiliations",
  components: {
    PolicePersonItem: police_person_item["a" /* default */]
  },
  data: function data() {
    return {
      persons: []
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _this.$axios.$get("/police/persons/affiliations/".concat(_this.$route.params.id));
          case 2:
            _this.persons = _context.sent;
          case 3:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/affiliations.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_affiliationsvue_type_script_lang_js = (affiliationsvue_type_script_lang_js); 
// CONCATENATED MODULE: ./components/Pages/Police/affiliations.vue





/* normalize component */

var affiliations_component = Object(componentNormalizer["a" /* default */])(
  Police_affiliationsvue_type_script_lang_js,
  affiliationsvue_type_template_id_414758c7_scoped_true_render,
  affiliationsvue_type_template_id_414758c7_scoped_true_staticRenderFns,
  false,
  null,
  "414758c7",
  null
  
)

/* harmony default export */ var affiliations = (affiliations_component.exports);

/* nuxt-component-imports */
installComponents(affiliations_component, {CrudyTableItemsComponentCitizen: __webpack_require__(499).default})

// EXTERNAL MODULE: ./components/app-roll-out.vue + 4 modules
var app_roll_out = __webpack_require__(82);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-profile-charges.vue?vue&type=template&id=82a02d4a&scoped=true
var police_profile_chargesvue_type_template_id_82a02d4a_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_vm.items.length > 0 ? _c('div', [_c('table-wrapper', {
    attrs: {
      "items": _vm.items,
      "config": _vm.config
    }
  })], 1) : _vm._e()]);
};
var police_profile_chargesvue_type_template_id_82a02d4a_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/police-profile-charges.vue?vue&type=template&id=82a02d4a&scoped=true

// EXTERNAL MODULE: ./components/Common/parts/ColumnNormalText.vue + 4 modules
var ColumnNormalText = __webpack_require__(1665);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-profile-charges.vue?vue&type=script&lang=js


/* harmony default export */ var police_profile_chargesvue_type_script_lang_js = ({
  name: "police-profile-charges",
  components: {
    TableWrapper: TableWrapper["a" /* default */]
  },
  data: function data() {
    return {
      items: [],
      config: {
        hideHeader: true,
        fields: [{
          key: 'type',
          label: ''
        }, {
          key: 'name',
          label: 'Weapon Name'
        }, {
          key: 'charged_with_count',
          label: '',
          component: ColumnNormalText["default"]
        }]
      }
    };
  },
  mounted: function mounted() {
    this.index();
  },
  methods: {
    index: function index() {
      var _this = this;
      return this.$axios.$get("/police/persons/charges/".concat(this.$route.params.id)).then(function (data) {
        _this.items = data;
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/police-profile-charges.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_police_profile_chargesvue_type_script_lang_js = (police_profile_chargesvue_type_script_lang_js); 
// CONCATENATED MODULE: ./components/Pages/Police/police-profile-charges.vue





/* normalize component */

var police_profile_charges_component = Object(componentNormalizer["a" /* default */])(
  Police_police_profile_chargesvue_type_script_lang_js,
  police_profile_chargesvue_type_template_id_82a02d4a_scoped_true_render,
  police_profile_chargesvue_type_template_id_82a02d4a_scoped_true_staticRenderFns,
  false,
  null,
  "82a02d4a",
  null
  
)

/* harmony default export */ var police_profile_charges = (police_profile_charges_component.exports);
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VDataTable/VDataTable.js + 14 modules
var VDataTable = __webpack_require__(1545);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressCircular/VProgressCircular.js
var VProgressCircular = __webpack_require__(300);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-charges-frequency.vue?vue&type=template&id=50af5ac5&scoped=true




var police_charges_frequencyvue_type_template_id_50af5ac5_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_vm.loading ? _c('div', {
    staticClass: "text-center py-4"
  }, [_c(VProgressCircular["a" /* default */], {
    attrs: {
      "indeterminate": "",
      "color": "primary"
    }
  }), _vm._v(" "), _c('div', {
    staticClass: "mt-2"
  }, [_vm._v("Loading charge statistics...")])], 1) : _vm.charges.length === 0 ? _c('div', {
    staticClass: "text-center py-4 text-muted"
  }, [_c('i', {
    staticClass: "fa-solid fa-gavel fa-2x mb-3"
  }), _vm._v(" "), _c('div', [_vm._v("No charges found for this person.")])]) : _c('div', [_c(VDataTable["a" /* default */], {
    staticClass: "elevation-1 clickable-rows",
    attrs: {
      "headers": _vm.headers,
      "items": _vm.charges,
      "items-per-page": 1000,
      "sort-by": "charge_frequency",
      "sort-desc": "",
      "hide-default-footer": "",
      "show-expand": "",
      "expanded": _vm.expanded
    },
    on: {
      "update:expanded": function updateExpanded($event) {
        _vm.expanded = $event;
      },
      "click:row": _vm.toggleExpand
    },
    scopedSlots: _vm._u([{
      key: "item.type",
      fn: function fn(_ref) {
        var item = _ref.item;
        return [_c('span', {
          staticClass: "type-badge",
          class: _vm.getChargeTypeClass(item.type)
        }, [_vm._v("\n              " + _vm._s(_vm.getChargeTypeLabel(item.type)) + "\n            ")])];
      }
    }, {
      key: "item.charge_frequency",
      fn: function fn(_ref2) {
        var item = _ref2.item;
        return [_c('span', {
          staticClass: "font-weight-bold"
        }, [_vm._v("\n              " + _vm._s(item.charge_frequency) + "\n            ")])];
      }
    }, {
      key: "item.total_counts",
      fn: function fn(_ref3) {
        var item = _ref3.item;
        return [_c('span', {
          staticClass: "font-weight-bold text-success"
        }, [_vm._v("\n              " + _vm._s(item.total_counts) + "\n            ")])];
      }
    }, {
      key: "expanded-item",
      fn: function fn(_ref4) {
        var headers = _ref4.headers,
          item = _ref4.item;
        return [_c('td', {
          staticClass: "pa-4 dark-expanded",
          attrs: {
            "colspan": headers.length
          }
        }, [_c('div', {
          staticClass: "expanded-content"
        }, [_c('h4', {
          staticClass: "mb-3 white--text"
        }, [_vm._v(_vm._s(item.name) + " - Last 360 Days")]), _vm._v(" "), _vm.loadingCharts[item.id] ? _c('div', {
          staticClass: "text-center py-4"
        }, [_c(VProgressCircular["a" /* default */], {
          attrs: {
            "indeterminate": "",
            "color": "primary",
            "size": "24"
          }
        }), _vm._v(" "), _c('span', {
          staticClass: "ml-2"
        }, [_vm._v("Loading chart data...")])], 1) : _vm.chartData[item.id] ? _c('div', {
          staticClass: "chart-container"
        }, [_c('canvas', {
          class: "chart-".concat(item.id),
          attrs: {
            "width": "400",
            "height": "200"
          }
        })]) : _c('div', {
          staticClass: "text-center py-4 text-muted"
        }, [_c('span', [_vm._v("No chart data available")])])])])];
      }
    }])
  })], 1)]);
};
var police_charges_frequencyvue_type_template_id_50af5ac5_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/police-charges-frequency.vue?vue&type=template&id=50af5ac5&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules
var toConsumableArray = __webpack_require__(51);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.concat.js
var es_array_concat = __webpack_require__(34);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find.js
var es_array_find = __webpack_require__(89);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.find-index.js
var es_array_find_index = __webpack_require__(113);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.map.js
var es_array_map = __webpack_require__(70);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.splice.js
var es_array_splice = __webpack_require__(133);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.number.constructor.js
var es_number_constructor = __webpack_require__(42);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.values.js
var es_object_values = __webpack_require__(316);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/chart.js/dist/chart.mjs + 1 modules
var chart = __webpack_require__(306);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-charges-frequency.vue?vue&type=script&lang=js














chart["a" /* Chart */].register.apply(chart["a" /* Chart */], Object(toConsumableArray["a" /* default */])(chart["b" /* registerables */]));
/* harmony default export */ var police_charges_frequencyvue_type_script_lang_js = ({
  name: 'police-charges-frequency',
  props: {
    characterId: {
      type: [String, Number],
      required: true
    }
  },
  data: function data() {
    return {
      charges: [],
      loading: true,
      expanded: [],
      chartData: {},
      loadingCharts: {},
      charts: {},
      headers: [{
        text: 'Charge Name',
        value: 'name',
        sortable: false
      }, {
        text: 'Type',
        value: 'type',
        sortable: false
      }, {
        text: 'Times Charged',
        value: 'charge_frequency',
        sortable: false
      }]
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _this.loadCharges();
          case 2:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  watch: {
    characterId: {
      handler: function handler() {
        this.loadCharges();
      },
      immediate: false
    },
    expanded: {
      handler: function handler(newExpanded, oldExpanded) {
        var _this2 = this;
        // Destroy charts for collapsed rows
        if (oldExpanded) {
          oldExpanded.forEach(function (item) {
            var stillExpanded = newExpanded.find(function (newItem) {
              return newItem.id === item.id;
            });
            if (!stillExpanded && _this2.charts[item.id]) {
              _this2.charts[item.id].destroy();
              delete _this2.charts[item.id];
            }
          });
        }

        // Load chart data when a row is expanded
        newExpanded.forEach(function (item) {
          if (!_this2.chartData[item.id] && !_this2.loadingCharts[item.id]) {
            _this2.loadChartData(item);
          } else if (_this2.chartData[item.id] && !_this2.charts[item.id]) {
            // Re-create chart if data exists but chart was destroyed
            _this2.createChart(item);
          }
        });
      },
      deep: true
    }
  },
  methods: {
    loadCharges: function loadCharges() {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        var response;
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (_this3.characterId) {
                _context2.next = 2;
                break;
              }
              return _context2.abrupt("return");
            case 2:
              _context2.prev = 2;
              _this3.loading = true;
              _context2.next = 6;
              return _this3.$axios.$get("/police/charges/by-frequency/".concat(_this3.characterId));
            case 6:
              response = _context2.sent;
              if (response.error) {
                console.error('API Error:', response.error);
                _this3.charges = [];
              } else if (response.data) {
                console.log('Debug info:', response.debug);
                _this3.charges = response.data;
              } else {
                _this3.charges = response;
              }
              _context2.next = 14;
              break;
            case 10:
              _context2.prev = 10;
              _context2.t0 = _context2["catch"](2);
              console.error('Failed to load charge statistics:', _context2.t0);
              _this3.$toast.error('Failed to load charge statistics');
            case 14:
              _context2.prev = 14;
              _this3.loading = false;
              return _context2.finish(14);
            case 17:
            case "end":
              return _context2.stop();
          }
        }, _callee2, null, [[2, 10, 14, 17]]);
      }))();
    },
    loadChartData: function loadChartData(item) {
      var _this4 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        var chartData;
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              if (_this4.characterId) {
                _context3.next = 2;
                break;
              }
              return _context3.abrupt("return");
            case 2:
              _context3.prev = 2;
              _this4.$set(_this4.loadingCharts, item.id, true);
              _context3.next = 6;
              return _this4.$axios.$get("/police/charges/history-chart/".concat(_this4.characterId, "/").concat(item.id));
            case 6:
              chartData = _context3.sent;
              _this4.$set(_this4.chartData, item.id, chartData);

              // Wait for DOM to update and then create chart
              _context3.next = 10;
              return _this4.$nextTick();
            case 10:
              setTimeout(function () {
                _this4.createChart(item);
              }, 100);
              _context3.next = 16;
              break;
            case 13:
              _context3.prev = 13;
              _context3.t0 = _context3["catch"](2);
              console.error('Failed to load chart data:', _context3.t0);
            case 16:
              _context3.prev = 16;
              _this4.$set(_this4.loadingCharts, item.id, false);
              return _context3.finish(16);
            case 19:
            case "end":
              return _context3.stop();
          }
        }, _callee3, null, [[2, 13, 16, 19]]);
      }))();
    },
    createChart: function createChart(item) {
      var _this5 = this;
      setTimeout(function () {
        // Destroy existing chart if it exists
        if (_this5.charts[item.id]) {
          _this5.charts[item.id].destroy();
          delete _this5.charts[item.id];
        }
        var data = _this5.chartData[item.id];
        if (!data) return;

        // Check if canvas element exists
        var canvasElements = document.getElementsByClassName("chart-".concat(item.id));
        if (!canvasElements || canvasElements.length === 0) {
          console.warn('Canvas element not found for chart:', item.id);
          return;
        }

        // Prepare chart data
        var labels = data.map(function (d) {
          var date = new Date(d.date);
          return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
          });
        });
        var counts = data.map(function (d) {
          return d.count;
        });

        // Create sparkline chart
        var createdChart = new chart["a" /* Chart */](document.getElementsByClassName("chart-".concat(item.id)), {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              data: counts,
              borderColor: '#1976D2',
              backgroundColor: 'rgba(25, 118, 210, 0.2)',
              borderWidth: 1.5,
              fill: true,
              tension: 0.4,
              pointRadius: 0,
              pointHoverRadius: 3,
              pointHoverBackgroundColor: '#1976D2',
              pointHoverBorderColor: '#FFFFFF',
              pointHoverBorderWidth: 2
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
              intersect: false,
              mode: 'index'
            },
            scales: {
              y: {
                display: false,
                beginAtZero: true
              },
              x: {
                display: false
              }
            },
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#FFFFFF',
                bodyColor: '#FFFFFF',
                borderColor: '#1976D2',
                borderWidth: 1,
                displayColors: false,
                callbacks: {
                  title: function title(context) {
                    return labels[context[0].dataIndex];
                  },
                  label: function label(context) {
                    return "".concat(context.parsed.y, " charge").concat(context.parsed.y !== 1 ? 's' : '');
                  }
                }
              }
            },
            elements: {
              point: {
                hoverRadius: 4
              }
            }
          }
        });
        _this5.charts[item.id] = createdChart;
      }, 100);
    },
    toggleExpand: function toggleExpand(item) {
      var index = this.expanded.findIndex(function (expandedItem) {
        return expandedItem.id === item.id;
      });
      if (index > -1) {
        // Item is expanded, remove it
        this.expanded.splice(index, 1);
      } else {
        // Item is not expanded, add it
        this.expanded.push(item);
      }
    },
    getChargeTypeClass: function getChargeTypeClass(type) {
      switch (type) {
        case 'Infractions':
        case 'Infraction':
          return 'type-infraction';
        case 'Misdemeanors':
        case 'Misdemeanor':
        case 'Fish and Wildlife Misdemeanor':
          return 'type-misdemeanor';
        case 'Felonies':
        case 'Felony':
          return 'type-felony';
        case 'Evasion':
          return 'type-evasion';
        case 'Aggravated':
          return 'type-aggravated';
        case 'Court':
          return 'type-court';
        default:
          return 'type-default';
      }
    },
    getChargeTypeLabel: function getChargeTypeLabel(type) {
      switch (type) {
        case 'Infractions':
        case 'Infraction':
          return 'INFRACTION';
        case 'Misdemeanors':
        case 'Misdemeanor':
          return 'MISDEMEANOR';
        case 'Fish and Wildlife Misdemeanor':
          return 'F&W MISDEMEANOR';
        case 'Felonies':
        case 'Felony':
          return 'FELONY';
        case 'Evasion':
          return 'EVASION';
        case 'Aggravated':
          return 'AGGRAVATED';
        case 'Court':
          return 'COURT';
        default:
          return type.toUpperCase();
      }
    }
  },
  beforeDestroy: function beforeDestroy() {
    // Clean up all charts when component is destroyed
    Object.values(this.charts).forEach(function (chart) {
      if (chart && typeof chart.destroy === 'function') {
        chart.destroy();
      }
    });
    this.charts = {};
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/police-charges-frequency.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_police_charges_frequencyvue_type_script_lang_js = (police_charges_frequencyvue_type_script_lang_js); 
// EXTERNAL MODULE: ./components/Pages/Police/police-charges-frequency.vue?vue&type=style&index=0&id=50af5ac5&prod&scoped=true&lang=css
var police_charges_frequencyvue_type_style_index_0_id_50af5ac5_prod_scoped_true_lang_css = __webpack_require__(1834);

// CONCATENATED MODULE: ./components/Pages/Police/police-charges-frequency.vue






/* normalize component */

var police_charges_frequency_component = Object(componentNormalizer["a" /* default */])(
  Police_police_charges_frequencyvue_type_script_lang_js,
  police_charges_frequencyvue_type_template_id_50af5ac5_scoped_true_render,
  police_charges_frequencyvue_type_template_id_50af5ac5_scoped_true_staticRenderFns,
  false,
  null,
  "50af5ac5",
  null
  
)

/* harmony default export */ var police_charges_frequency = (police_charges_frequency_component.exports);
// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-profile-vehicles.vue?vue&type=template&id=2cf93f24&scoped=true

var police_profile_vehiclesvue_type_template_id_2cf93f24_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_vm.items.length > 0 ? _c('div', _vm._l(_vm.items, function (car, index) {
    return _c('person-car', {
      key: index,
      attrs: {
        "car": car,
        "ownerName": _vm.profile.name
      }
    });
  }), 1) : _vm._e()]);
};
var police_profile_vehiclesvue_type_template_id_2cf93f24_scoped_true_staticRenderFns = [];

// CONCATENATED MODULE: ./components/Pages/Police/police-profile-vehicles.vue?vue&type=template&id=2cf93f24&scoped=true

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./components/Pages/Police/police-profile-vehicles.vue?vue&type=script&lang=js



/* harmony default export */ var police_profile_vehiclesvue_type_script_lang_js = ({
  name: "police-profile-vehicles",
  components: {
    PersonCar: person_car,
    TableWrapper: TableWrapper["a" /* default */]
  },
  props: ['profile'],
  data: function data() {
    return {
      items: [],
      config: {
        hideHeader: true,
        fields: [{
          key: 'type',
          label: ''
        }, {
          key: 'name',
          label: 'Weapon Name'
        }, {
          key: 'charged_with_count',
          label: '',
          component: ColumnNormalText["default"]
        }]
      }
    };
  },
  mounted: function mounted() {
    this.index();
  },
  methods: {
    index: function index() {
      var _this = this;
      return this.$axios.$get("/police/persons/cars/".concat(this.$route.params.id)).then(function (data) {
        _this.items = data;
      });
    }
  }
});
// CONCATENATED MODULE: ./components/Pages/Police/police-profile-vehicles.vue?vue&type=script&lang=js
 /* harmony default export */ var Police_police_profile_vehiclesvue_type_script_lang_js = (police_profile_vehiclesvue_type_script_lang_js); 
// CONCATENATED MODULE: ./components/Pages/Police/police-profile-vehicles.vue





/* normalize component */

var police_profile_vehicles_component = Object(componentNormalizer["a" /* default */])(
  Police_police_profile_vehiclesvue_type_script_lang_js,
  police_profile_vehiclesvue_type_template_id_2cf93f24_scoped_true_render,
  police_profile_vehiclesvue_type_template_id_2cf93f24_scoped_true_staticRenderFns,
  false,
  null,
  "2cf93f24",
  null
  
)

/* harmony default export */ var police_profile_vehicles = (police_profile_vehicles_component.exports);
// EXTERNAL MODULE: ./components/app-form-group.vue + 4 modules
var app_form_group = __webpack_require__(186);

// EXTERNAL MODULE: ./components/Common/AppPage.vue + 4 modules
var AppPage = __webpack_require__(314);

// EXTERNAL MODULE: ./components/Crudy/crudy-gen/crudy-gen.vue + 4 modules
var crudy_gen = __webpack_require__(1556);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/persons/_id/index.vue?vue&type=script&lang=js

function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




























/* harmony default export */ var _idvue_type_script_lang_js = ({
  name: 'PolicePersonView',
  components: {
    CrudyTable: crudy_table["a" /* default */],
    CrudyGen: crudy_gen["a" /* default */],
    AppPage: AppPage["a" /* default */],
    AppFormGroup: app_form_group["a" /* default */],
    PoliceProfileVehicles: police_profile_vehicles,
    PoliceProfileCharges: police_profile_charges,
    PoliceChargesFrequency: police_charges_frequency,
    AppRollOut: app_roll_out["a" /* default */],
    Affiliations: affiliations,
    PersonCar: person_car,
    PolicePersonItem: police_person_item["a" /* default */],
    TableWrapper: TableWrapper["a" /* default */],
    PoliceCarSelector: PoliceCarSelector["a" /* default */],
    CrudSelect: CrudSelect["a" /* default */],
    ItemDetail: ItemDetail["a" /* default */],
    Loader: Loader["a" /* default */],
    HouseListingItem: HouseListingItem["a" /* default */]
  },
  data: function data() {
    return {
      tab: null,
      detailsTableConfig: {
        fields: [{
          key: 'author',
          label: 'Executor'
        }, {
          key: 'created_at',
          label: 'Created'
        }, {
          key: 'type',
          label: ''
        }, {
          key: 'value',
          label: ''
        }]
      },
      loading: false,
      modal: false,
      incidentModal: false,
      profile: null,
      evidenceItems: [],
      detailForm: {},
      incidentForm: {
        incident_type_id: null,
        detail_type_id: null,
        crimeType: null
      }
    };
  },
  mounted: function mounted() {
    var _this = this;
    this.loading = true;
    this.index();
    window.$events.$on('items:listItems', function (data) {
      _this.evidenceItems = data.items;
    });

    // this.$refs.incidentType.doSearch('for', this.currentFaction.for)
  },
  beforeRouteUpdate: function beforeRouteUpdate(to, from, next) {
    this.index();
    next();
  },
  destroyed: function destroyed() {
    window.$events.$off('items:listItems');
  },
  methods: {
    index: function index() {
      var _this2 = this;
      var byCharacterNumber = this.$route.query.character_number;
      return this.$axios.$get("/police/persons/view/".concat(this.$route.params.id), {
        params: {
          character_number: byCharacterNumber
        }
      }).then(function (r) {
        _this2.profile = r;
        _this2.loading = false;
        _this2.$store.dispatch('system/addHistoryItem', {
          name: _this2.profile.name,
          path: _this2.$route.fullPath,
          icon: 'fa-solid fa-square-user'
        });
      });
    },
    addPersonDetails: function addPersonDetails() {
      var _this3 = this;
      return this.$axios.$post("/police/persons/add-person-detail/".concat(this.profile.id), this.detailForm).then(function (r) {
        _this3.$refs.citizenDetails.refresh();
        _this3.index();
        _this3.modal = false;
      });
    },
    createBase: function createBase() {
      var _this4 = this;
      var faction = null;
      if (this.hasGroup('LSPD_Internal')) {
        faction = 'lspd';
      } else if (this.hasGroup('Sheriff_Internal')) {
        faction = 'bcso';
      } else if (this.hasGroup('DOC')) {
        faction = 'doc';
      }
      return this.$axios.$post("/police/incidents/create", {
        location_name: this.locationName,
        title: this.incidentForm.title,
        incident_variation_id: this.incidentForm.incident_variation_id,
        incident_crime_id: this.incidentForm.incident_crime_id,
        incident_type_id: this.incidentForm.incident_type_id,
        content: this.incidentForm.content,
        car_id: this.incidentForm.car_id,
        faction: faction,
        officer_id: this.user.id,
        suspect_id: this.profile.id
      }).then(function (r) {
        _this4.$router.push({
          path: "/cad/incidents/view/".concat(r.id)
        });
      });
    },
    openEvidence: function openEvidence() {
      this.detailForm.type = 'evidence_accessed';
      this.detailForm.value = "accessed by ".concat(this.user.name);
      this.addPersonDetails();
      fetch("https://blrp_tablet/openEvidence", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=UTF-8'
        },
        body: JSON.stringify({
          id: "person_".concat(this.profile.id)
        })
      }).then(function (resp) {
        return resp.json();
      }).then(function (resp) {
        return resp;
      });
    },
    callPerson: function callPerson() {
      this.$axios.$post('https://blrp_tablet/call', {
        number: this.profile.phone
      });
    },
    camelToEnglish: function camelToEnglish(text) {
      return text.replace(/([A-Z])/g, ' $1').replace(/^./, function (str) {
        return str.toUpperCase();
      });
    }
  },
  watch: {},
  computed: _objectSpread({
    isWanted: function isWanted() {
      var _this$profile;
      return ((_this$profile = this.profile) === null || _this$profile === void 0 ? void 0 : _this$profile.incidents_wanted.length) > 0;
    },
    dangerLevel: function dangerLevel() {
      return {};
      if (this.profile.aggregated_charges_count > 20) {
        return {
          n: 'Watch your Back',
          c: '#fc0000'
        };
      }
      if (this.profile.aggregated_charges_count > 12) {
        return {
          n: 'Extremely Dangerous',
          c: '#fc0000'
        };
      }
      if (this.profile.aggregated_charges_count > 7) {
        return {
          n: 'Extremely Dangerous',
          c: '#fc0000'
        };
      }
      if (this.profile.felony_charges_count > 5) {
        return {
          n: 'Highly Dangerous',
          c: '#ff7474'
        };
      }
      if (this.profile.felony_charges_count > 3) {
        return {
          n: 'Possibly Dangerous',
          c: '#ff7474'
        };
      }
      if (this.profile.misdemeanor_charges_count > 15) {
        return {
          n: 'Somewhat Dangerous',
          c: '#ffa7a7'
        };
      }
      if (this.profile.misdemeanor_charges_count > 7) {
        return {
          n: 'Possible Threat',
          c: '#fffd81'
        };
      }
      if (this.profile.infraction_charges_count > 3) {
        return {
          n: 'Little Threat',
          c: '#41ff44'
        };
      }

      // 48479322

      return {
        n: 'No Threat',
        c: 'limegreen'
      };
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    hasGroup: 'auth/hasGroup',
    locationName: 'location/name',
    currentFaction: 'auth/currentFaction'
  }))
});
// CONCATENATED MODULE: ./pages/cad/persons/_id/index.vue?vue&type=script&lang=js
 /* harmony default export */ var persons_idvue_type_script_lang_js = (_idvue_type_script_lang_js); 
// CONCATENATED MODULE: ./pages/cad/persons/_id/index.vue





/* normalize component */

var _id_component = Object(componentNormalizer["a" /* default */])(
  persons_idvue_type_script_lang_js,
  _idvue_type_template_id_0e323b4b_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (_id_component.exports);

/* nuxt-component-imports */
installComponents(_id_component, {CrudyTableItemsComponentCitizen: __webpack_require__(499).default})


/***/ })

}]);