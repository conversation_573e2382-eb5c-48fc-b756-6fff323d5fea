(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[174],{

/***/ 1749:
/***/ (function(module, exports, __webpack_require__) {

// style-loader: Adds some css to the DOM by adding a <style> tag

// load the styles
var content = __webpack_require__(1998);
if(content.__esModule) content = content.default;
if(typeof content === 'string') content = [[module.i, content, '']];
if(content.locals) module.exports = content.locals;
// add the styles to the DOM
var add = __webpack_require__(26).default
var update = add("35bac21c", content, true, {"sourceMap":false});

/***/ }),

/***/ 1997:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_starting_vue_vue_type_style_index_0_id_1f2842c2_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1749);
/* harmony import */ var _node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_starting_vue_vue_type_style_index_0_id_1f2842c2_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_vue_style_loader_index_js_ref_3_oneOf_1_0_node_modules_css_loader_dist_cjs_js_ref_3_oneOf_1_1_node_modules_vue_loader_lib_loaders_stylePostLoader_js_node_modules_postcss_loader_dist_cjs_js_ref_3_oneOf_1_2_node_modules_nuxt_components_dist_loader_js_ref_0_0_node_modules_vue_loader_lib_index_js_vue_loader_options_starting_vue_vue_type_style_index_0_id_1f2842c2_prod_scoped_true_lang_css__WEBPACK_IMPORTED_MODULE_0__);
/* unused harmony reexport * */


/***/ }),

/***/ 1998:
/***/ (function(module, exports, __webpack_require__) {

// Imports
var ___CSS_LOADER_API_IMPORT___ = __webpack_require__(25);
var ___CSS_LOADER_EXPORT___ = ___CSS_LOADER_API_IMPORT___(function(i){return i[1]});
// Module
___CSS_LOADER_EXPORT___.push([module.i, ".lds-ripple[data-v-1f2842c2]{display:inline-block;height:80px;position:absolute;top:40%;width:80px}.lds-ripple div[data-v-1f2842c2]{animation:lds-ripple-1f2842c2 1s cubic-bezier(0,.2,.8,1) infinite;border:4px solid #fff;border-radius:50%;opacity:1;position:absolute}.lds-ripple div[data-v-1f2842c2]:nth-child(2){animation-delay:-.5s}@keyframes lds-ripple-1f2842c2{0%{height:0;left:36px;opacity:0;top:36px;width:0}4.9%{height:0;left:36px;opacity:0;top:36px;width:0}5%{height:0;left:36px;opacity:1;top:36px;width:0}to{height:72px;left:0;opacity:0;top:0;width:72px}}", ""]);
// Exports
___CSS_LOADER_EXPORT___.locals = {};
module.exports = ___CSS_LOADER_EXPORT___;


/***/ }),

/***/ 2034:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/src/components/VGrid/_grid.sass
var _grid = __webpack_require__(518);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/grid.js
var grid = __webpack_require__(588);

// CONCATENATED MODULE: ./node_modules/vuetify/lib/components/VGrid/VLayout.js


/* harmony default export */ var VLayout = (Object(grid["a" /* default */])('layout'));
// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VProgressLinear/VProgressLinear.js
var VProgressLinear = __webpack_require__(460);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.function.name.js
var es_function_name = __webpack_require__(20);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/starting.vue?vue&type=template&id=1f2842c2&scoped=true





var startingvue_type_template_id_1f2842c2_scoped_true_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c(VLayout, {
    staticClass: "p-5",
    staticStyle: {
      "font-family": "monospace, monospace"
    },
    attrs: {
      "justify-center": ""
    }
  }, [_c(VCard["b" /* VCardText */], [_vm.user && _vm.user.name && _vm.user.name.split(' ') && _vm.user.name.split(' ')[0] ? _c('span', [_vm._v("\n      " + _vm._s(_vm.user.name.split(' ')[0].toLowerCase()) + "@bados > start.sh\n    ")]) : _c('span', [_vm._v("\n      device@bados > login -user 1 -p\n    ")]), _vm._v(" "), _c(VProgressLinear["a" /* default */], {
    staticClass: "mt-4",
    staticStyle: {
      "width": "100%"
    },
    style: "color: ".concat(_vm.color),
    attrs: {
      "color": "deep-purple accent-4",
      "indeterminate": "",
      "rounded": "",
      "height": "12"
    }
  })], 1)], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/misc/starting.vue?vue&type=template&id=1f2842c2&scoped=true

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.timers.js
var web_timers = __webpack_require__(71);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Common/app-card.vue + 4 modules
var app_card = __webpack_require__(68);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/misc/starting.vue?vue&type=script&lang=js


function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }











/* harmony default export */ var startingvue_type_script_lang_js = ({
  components: {
    AppCard: app_card["a" /* default */]
  },
  data: function data() {
    return {
      lineHeight: '25px',
      message: null,
      speed: 1,
      runner: null,
      timesRun: 0,
      progress: 0,
      color: null,
      components: [],
      componentsMapped: []
    };
  },
  mounted: function mounted() {
    var _this = this;
    return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
      return regeneratorRuntime.wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _this.runner = setInterval(_this._run, _this.speed);
            setTimeout(function () {
              _this.finished();
            }, 500);
          case 2:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }))();
  },
  methods: {
    _run: function _run() {
      this.timesRun++;
      this.addMessage('Hello World ' + this.timesRun);
    },
    addMessage: function addMessage(message) {
      this.color = this.getRandomColor();
      this.message = message;
    },
    getRandomColor: function getRandomColor() {
      var letters = '0123456789ABCDEF';
      var color = '#';
      for (var i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
      }
      return color;
    },
    userLoaded: function userLoaded() {
      var _this2 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee2() {
        return regeneratorRuntime.wrap(function _callee2$(_context2) {
          while (1) switch (_context2.prev = _context2.next) {
            case 0:
              if (!_this2.booted) {
                _context2.next = 6;
                break;
              }
              _context2.next = 3;
              return _this2.$store.dispatch('nav/tryReturnLastRoute');
            case 3:
              if (_context2.sent) {
                _context2.next = 6;
                break;
              }
              _context2.next = 6;
              return _this2.$router.push({
                path: '/'
              });
            case 6:
            case "end":
              return _context2.stop();
          }
        }, _callee2);
      }))();
    },
    finished: function finished() {
      var _this3 = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee3() {
        return regeneratorRuntime.wrap(function _callee3$(_context3) {
          while (1) switch (_context3.prev = _context3.next) {
            case 0:
              _this3.$store.commit('system/SET_BOOTED', true);
              _this3.$store.commit('system/SET_BOOTING', false);
              _context3.next = 4;
              return _this3.$store.dispatch('nav/tryReturnLastRoute');
            case 4:
              if (_context3.sent) {
                _context3.next = 7;
                break;
              }
              _context3.next = 7;
              return _this3.$router.push({
                path: '/'
              });
            case 7:
            case "end":
              return _context3.stop();
          }
        }, _callee3);
      }))();
    }
  },
  watch: {
    speed: function speed(newSpeed) {
      // console.log('changing speed')
      // clearInterval(this.runner)
      // this.runner = setInterval(this._run, newSpeed)
    },
    user: function user() {
      if (this.user) {
        this.userLoaded();
      }
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    user: 'auth/user',
    booted: 'system/booted'
  })),
  destroyed: function destroyed() {
    clearInterval(this.runner);
  }
});
// CONCATENATED MODULE: ./pages/misc/starting.vue?vue&type=script&lang=js
 /* harmony default export */ var misc_startingvue_type_script_lang_js = (startingvue_type_script_lang_js); 
// EXTERNAL MODULE: ./pages/misc/starting.vue?vue&type=style&index=0&id=1f2842c2&prod&scoped=true&lang=css
var startingvue_type_style_index_0_id_1f2842c2_prod_scoped_true_lang_css = __webpack_require__(1997);

// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/misc/starting.vue






/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  misc_startingvue_type_script_lang_js,
  startingvue_type_template_id_1f2842c2_scoped_true_render,
  staticRenderFns,
  false,
  null,
  "1f2842c2",
  null
  
)

/* harmony default export */ var starting = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);