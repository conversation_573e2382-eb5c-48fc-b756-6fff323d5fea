(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[147],{

/***/ 1757:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/transitions/index.js + 2 modules
var transitions = __webpack_require__(78);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=template&id=22ac7036


var crew_chat_message_itemvue_type_template_id_22ac7036_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', {
    staticClass: "mt-2",
    on: {
      "mouseenter": function mouseenter($event) {
        _vm.hovering = true;
      },
      "mouseleave": function mouseleave($event) {
        _vm.hovering = false;
      }
    }
  }, [_c('social-person-tag', {
    attrs: {
      "person": _vm.message.person,
      "nickname": _vm.message.nickname,
      "color": "transparent",
      "small": true
    },
    scopedSlots: _vm._u([{
      key: "details",
      fn: function fn() {
        return [_c('app-markdown-view', {
          attrs: {
            "source": _vm.message.message
          }
        }), _vm._v(" "), _c(transitions["g" /* VScrollYTransition */], [_vm.hovering ? _c('div', [_c('app-timestamp', {
          attrs: {
            "stamp": _vm.message.created_at
          }
        }), _vm._v(" ago\n        ")], 1) : _vm._e()])];
      },
      proxy: true
    }])
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=template&id=22ac7036

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Pages/Social/social-person-tag.vue + 4 modules
var social_person_tag = __webpack_require__(142);

// EXTERNAL MODULE: ./components/Common/app-markdown-view.vue + 4 modules
var app_markdown_view = __webpack_require__(108);

// EXTERNAL MODULE: ./components/Common/app-timestamp.vue + 4 modules
var app_timestamp = __webpack_require__(77);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }




/* harmony default export */ var crew_chat_message_itemvue_type_script_lang_js = ({
  name: 'crew-chat-message-item',
  components: {
    AppTimestamp: app_timestamp["a" /* default */],
    AppMarkdownView: app_markdown_view["a" /* default */],
    SocialPersonTag: social_person_tag["a" /* default */]
  },
  props: ['message'],
  data: function data() {
    return {
      hovering: false
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({}))
});
// CONCATENATED MODULE: ./pages/crew-chat/_id/crew-chat-message-item.vue?vue&type=script&lang=js
 /* harmony default export */ var _id_crew_chat_message_itemvue_type_script_lang_js = (crew_chat_message_itemvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/crew-chat/_id/crew-chat-message-item.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  _id_crew_chat_message_itemvue_type_script_lang_js,
  crew_chat_message_itemvue_type_template_id_22ac7036_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var crew_chat_message_item = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);