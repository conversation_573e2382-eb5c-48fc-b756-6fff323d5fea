(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[216],{

/***/ 2206:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBadge/VBadge.js
var VBadge = __webpack_require__(1525);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/people/_id.vue?vue&type=template&id=db835ee2


var _idvue_type_template_id_db835ee2_render = function render() {
  var _vm = this,
    _c = _vm._self._c,
    _setup = _vm._self._setupProxy;
  return _c('crudy-page', {
    attrs: {
      "name": "CitizenSocial",
      "config": {
        hideHeader: true,
        hideRelationships: true,
        hideLoader: true
      }
    },
    on: {
      "loaded": function loaded($event) {
        _vm.person = $event;
      }
    },
    scopedSlots: _vm._u([{
      key: "body",
      fn: function fn(_ref) {
        var item = _ref.item,
          model = _ref.model,
          uuid = _ref.uuid,
          actions = _ref.actions;
        return [_c('app-page-header', {
          attrs: {
            "tabs": _vm.tabs
          },
          scopedSlots: _vm._u([{
            key: "title",
            fn: function fn() {
              return [_c(VBadge["a" /* default */], {
                attrs: {
                  "color": _vm.statusColor,
                  "offset-x": "1",
                  "offset-y": "8"
                }
              }, [_vm._v("\n          " + _vm._s(item.display_name) + "\n        ")]), _vm._v(" "), _c('crudy-context-menu', {
                attrs: {
                  "uuid": uuid,
                  "name": model,
                  "item": item,
                  "actions": actions
                }
              })];
            },
            proxy: true
          }], null, true)
        }), _vm._v(" "), _c('NuxtChild', {
          attrs: {
            "person": item
          }
        })];
      }
    }])
  });
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/social/people/_id.vue?vue&type=template&id=db835ee2

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./node_modules/vue/dist/vue.runtime.esm.js
var vue_runtime_esm = __webpack_require__(2);

// EXTERNAL MODULE: ./components/Crudy/crudy-page/crudy-page.vue + 9 modules
var crudy_page = __webpack_require__(1555);

// EXTERNAL MODULE: ./components/Crudy/crudy-parts/crudy-context-menu.vue + 4 modules
var crudy_context_menu = __webpack_require__(74);

// EXTERNAL MODULE: ./components/Common/app-page-header.vue + 4 modules
var app_page_header = __webpack_require__(1666);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--13-0!./node_modules/ts-loader??ref--13-1!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/people/_id.vue?vue&type=script&lang=ts








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }





/* harmony default export */ var _idvue_type_script_lang_ts = (vue_runtime_esm["default"].extend({
  components: {
    AppPageHeader: app_page_header["a" /* default */],
    CrudyContextMenu: crudy_context_menu["a" /* default */],
    CrudyPage: crudy_page["a" /* default */]
  },
  props: [],
  data: function data() {
    return {
      person: null,
      tabs: [{
        name: 'Profile',
        icon: 'fa-solid fa-square-user',
        iconColor: 'orange',
        route: 'profile'
      }, {
        name: 'Content',
        icon: 'fa-solid fa-message-dots',
        iconColor: 'primary',
        route: 'content'
      }, {
        name: 'Friendship',
        icon: 'fa-solid fa-heart-pulse',
        iconColor: 'red',
        route: 'friendship'
      }]
    };
  },
  created: function created() {},
  methods: {},
  computed: _objectSpread({
    statusColor: function statusColor() {
      if (this.person.status === 'online') return 'success';
      if (this.person.status === 'offline') return 'danger';
      if (this.person.status === 'away') return 'warning';
    }
  }, Object(vuex_esm["b" /* mapGetters */])({
    mode: 'system/mode'
  }))
}));
// CONCATENATED MODULE: ./pages/social/people/_id.vue?vue&type=script&lang=ts
 /* harmony default export */ var people_idvue_type_script_lang_ts = (_idvue_type_script_lang_ts); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/social/people/_id.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  people_idvue_type_script_lang_ts,
  _idvue_type_template_id_db835ee2_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var _id = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);