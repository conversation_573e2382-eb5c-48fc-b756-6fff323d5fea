(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[69],{

/***/ 2079:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VAlert/VAlert.js
var VAlert = __webpack_require__(1537);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/VCard.js
var VCard = __webpack_require__(468);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VCard/index.js
var components_VCard = __webpack_require__(19);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VCol.js
var VCol = __webpack_require__(1521);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VGrid/VRow.js
var VRow = __webpack_require__(1520);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VSelect/VSelect.js + 4 modules
var VSelect = __webpack_require__(375);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VTextarea/VTextarea.js
var VTextarea = __webpack_require__(1532);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/alerts.vue?vue&type=template&id=2f36d7d3










var alertsvue_type_template_id_2f36d7d3_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_c(VCard["a" /* default */], {
    staticClass: "m-1"
  }, [_c(components_VCard["c" /* VCardTitle */], [_vm._v("\n      Dispatch New Alert\n    ")]), _vm._v(" "), _c(components_VCard["b" /* VCardText */], [_vm.hasAnyGroup(['admin']) ? _c('hr') : _vm._e(), _vm._v(" "), _vm.hasAnyGroup(['admin']) ? _c('div', [_c('div', {
    staticClass: "text-muted ml-2"
  }, [_vm._v("\n          Only shown to staff\n        ")]), _vm._v(" "), _c(VRow["a" /* default */], {
    attrs: {
      "no-gutters": ""
    }
  }, _vm._l(_vm.factions, function (faction, index) {
    return _c(VCol["a" /* default */], {
      key: index,
      staticClass: "record-type",
      class: {
        selected: _vm.form.faction === faction.identifier
      },
      style: "color: ".concat(faction.color),
      on: {
        "click": function click($event) {
          _vm.form.faction = faction.identifier;
        }
      }
    }, [_vm._v("\n            " + _vm._s(faction.short) + "\n          ")]);
  }), 1)], 1) : _vm._e(), _vm._v(" "), _vm.hasAnyGroup(['admin']) ? _c('hr') : _vm._e(), _vm._v(" "), _c(VSelect["a" /* default */], {
    attrs: {
      "label": "Urgency",
      "items": [{
        value: 'notice',
        text: 'Notice (Non Urgent)'
      }, {
        value: 'emergency',
        text: 'Emergency (Urgent)'
      }]
    },
    model: {
      value: _vm.form.urgency,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "urgency", $$v);
      },
      expression: "form.urgency"
    }
  }), _vm._v(" "), _c(VSelect["a" /* default */], {
    attrs: {
      "label": "Range",
      "items": [{
        text: 'Around Me',
        value: 'local'
      }, {
        text: 'Anywhere',
        value: 'anywhere'
      }]
    },
    model: {
      value: _vm.form.distance,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "distance", $$v);
      },
      expression: "form.distance"
    }
  }), _vm._v(" "), _c(VAlert["a" /* default */], {
    staticClass: "text-red",
    attrs: {
      "color": "red"
    }
  }, [_vm._v("\n        When writing an alert it is important to have proper punctuation, grammar, and professionalism.\n        Mis-use of this system can result in removal from your department.\n\n        "), _c('br'), _vm._v(" "), _c('br'), _vm._v(" "), _c('b', [_vm._v("Example:")]), _vm._v(" "), _c('br'), _vm._v("\n        Police are requesting that all individuals immediately cease fire and put your hands in the air.\n        Failure to comply will result in lethal force.\n      ")]), _vm._v(" "), _c(VTextarea["a" /* default */], {
    attrs: {
      "label": "Alert Content"
    },
    model: {
      value: _vm.form.content,
      callback: function callback($$v) {
        _vm.$set(_vm.form, "content", $$v);
      },
      expression: "form.content"
    }
  }), _vm._v(" "), _c(VBtn["a" /* default */], {
    attrs: {
      "color": "primary"
    },
    on: {
      "click": _vm.submit
    }
  }, [_vm._v("\n        Trigger and Dispatch Alert\n      ")]), _vm._v(" "), _vm.hasAnyGroup(['LEO']) ? _c(VBtn["a" /* default */], {
    staticClass: "ml-4",
    attrs: {
      "color": "red"
    },
    on: {
      "click": _vm.clearAreaQuickAction
    }
  }, [_vm._v("\n        (Quick Action) LEO SURRENDER WARNING\n      ")]) : _vm._e()], 1)], 1), _vm._v(" "), _c('crudy-table', {
    staticClass: "mt-4",
    attrs: {
      "name": "CadAlert",
      "view": "index"
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/cad/alerts.vue?vue&type=template&id=2f36d7d3

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js
var asyncToGenerator = __webpack_require__(4);

// EXTERNAL MODULE: ./node_modules/regenerator-runtime/runtime.js
var runtime = __webpack_require__(58);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/cad/alerts.vue?vue&type=script&lang=js









function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }



/* harmony default export */ var alertsvue_type_script_lang_js = ({
  name: 'alerts',
  components: {
    CrudyTable: crudy_table["a" /* default */]
  },
  props: [],
  data: function data() {
    return {
      form: {
        urgency: 'notice',
        distance: 'local',
        faction: null,
        content: null
      }
    };
  },
  created: function created() {
    this.form.faction = this.currentFaction.identifier;
    this.$store.dispatch('cad/fetchFactions').then(function (r) {});
  },
  methods: {
    clearAreaQuickAction: function clearAreaQuickAction() {
      this.form.urgency = 'emergency';
      this.form.distance = 'local';
      this.form.content = 'Police are requesting that all individuals immediately cease fire and put your hands in the air. Failure to comply will result in lethal force.';
      this.submit();
    },
    submit: function submit() {
      var _this = this;
      return Object(asyncToGenerator["a" /* default */])(/*#__PURE__*/regeneratorRuntime.mark(function _callee() {
        var sendToPlayers;
        return regeneratorRuntime.wrap(function _callee$(_context) {
          while (1) switch (_context.prev = _context.next) {
            case 0:
              sendToPlayers = [];
              if (!(_this.form.distance === 'local')) {
                _context.next = 5;
                break;
              }
              _context.next = 4;
              return _this.$axios.$post('http://blrp_tablet/playersWithinDistance', {
                distance: 100
              }).then(function (res) {
                return res;
              });
            case 4:
              sendToPlayers = _context.sent;
            case 5:
              _this.form.players = sendToPlayers;
              _this.$axios.$post('/system/submit-alert', _this.form).then(function () {
                _this.form.content = null;
              });
            case 7:
            case "end":
              return _context.stop();
          }
        }, _callee);
      }))();
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    hasAnyGroup: 'auth/hasAnyGroup',
    currentFaction: 'auth/currentFaction',
    hasGroup: 'auth/hasGroup',
    position: 'location/position',
    factions: 'cad/factions'
  }))
});
// CONCATENATED MODULE: ./pages/cad/alerts.vue?vue&type=script&lang=js
 /* harmony default export */ var cad_alertsvue_type_script_lang_js = (alertsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/cad/alerts.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  cad_alertsvue_type_script_lang_js,
  alertsvue_type_template_id_2f36d7d3_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var alerts = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);