(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[226],{

/***/ 2204:
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
// ESM COMPAT FLAG
__webpack_require__.r(__webpack_exports__);

// EXTERNAL MODULE: ./node_modules/vuetify/lib/components/VBtn/VBtn.js
var VBtn = __webpack_require__(299);

// CONCATENATED MODULE: ./node_modules/vuetify-loader/lib/loader.js??ref--4!./node_modules/babel-loader/lib??ref--2-0!./node_modules/vue-loader/lib/loaders/templateLoader.js??ref--7!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/topics/index.vue?vue&type=template&id=c67bc730


var topicsvue_type_template_id_c67bc730_render = function render() {
  var _vm = this,
    _c = _vm._self._c;
  return _c('div', [_vm.hasAnyGroup(['admin']) ? _c('div', {
    staticClass: "p-4",
    attrs: {
      "align": "right"
    }
  }, [_c(VBtn["a" /* default */], {
    attrs: {
      "color": "red"
    },
    on: {
      "click": _vm.startCreate
    }
  }, [_vm._v("\n      Add new Life Invader Topic (Admin)\n    ")])], 1) : _vm._e(), _vm._v(" "), _c('crudy-table', {
    ref: "list",
    attrs: {
      "name": "BusinessThread",
      "view": "social",
      "allow-create": false,
      "autofill": {
        life_name: 'social'
      },
      "filter": {
        life_name: 'social'
      }
    }
  })], 1);
};
var staticRenderFns = [];

// CONCATENATED MODULE: ./pages/social/topics/index.vue?vue&type=template&id=c67bc730

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.symbol.js
var es_symbol = __webpack_require__(7);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.array.filter.js
var es_array_filter = __webpack_require__(9);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptor.js
var es_object_get_own_property_descriptor = __webpack_require__(14);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.get-own-property-descriptors.js
var es_object_get_own_property_descriptors = __webpack_require__(15);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.keys.js
var es_object_keys = __webpack_require__(8);

// EXTERNAL MODULE: ./node_modules/core-js/modules/es.object.to-string.js
var es_object_to_string = __webpack_require__(6);

// EXTERNAL MODULE: ./node_modules/core-js/modules/web.dom-collections.for-each.js
var web_dom_collections_for_each = __webpack_require__(11);

// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js
var defineProperty = __webpack_require__(0);

// EXTERNAL MODULE: ./node_modules/vuex/dist/vuex.esm.js
var vuex_esm = __webpack_require__(13);

// EXTERNAL MODULE: ./components/Crudy/crudy-table/crudy-table.vue + 9 modules
var crudy_table = __webpack_require__(490);

// CONCATENATED MODULE: ./node_modules/babel-loader/lib??ref--2-0!./node_modules/@nuxt/components/dist/loader.js??ref--0-0!./node_modules/vue-loader/lib??vue-loader-options!./pages/social/topics/index.vue?vue&type=script&lang=js








function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }
function _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { Object(defineProperty["a" /* default */])(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }


/* harmony default export */ var topicsvue_type_script_lang_js = ({
  components: {
    CrudyTable: crudy_table["a" /* default */]
  },
  props: [],
  data: function data() {
    return {};
  },
  created: function created() {},
  methods: {
    startCreate: function startCreate() {
      this.$refs.list.startCreate();
    }
  },
  computed: _objectSpread({}, Object(vuex_esm["b" /* mapGetters */])({
    hasAnyGroup: 'auth/hasAnyGroup'
  }))
});
// CONCATENATED MODULE: ./pages/social/topics/index.vue?vue&type=script&lang=js
 /* harmony default export */ var social_topicsvue_type_script_lang_js = (topicsvue_type_script_lang_js); 
// EXTERNAL MODULE: ./node_modules/vue-loader/lib/runtime/componentNormalizer.js
var componentNormalizer = __webpack_require__(5);

// CONCATENATED MODULE: ./pages/social/topics/index.vue





/* normalize component */

var component = Object(componentNormalizer["a" /* default */])(
  social_topicsvue_type_script_lang_js,
  topicsvue_type_template_id_c67bc730_render,
  staticRenderFns,
  false,
  null,
  null,
  null
  
)

/* harmony default export */ var topics = __webpack_exports__["default"] = (component.exports);

/***/ })

}]);