local categories_built = {}

SetTimeout(1000, function()
  for category_id, category_alias in pairs(tattoo_shops_config.categories) do
    local data = LoadResourceFile(GetCurrentResourceName(), './json/' .. category_id .. '.json')

    if data then
      data = json.decode(data)

      categories_built[category_id] = {}

      for tattoo_id, tattoo_data in ipairs(data) do
        local name = tattoo_data.LocalizedName
        local hash = tattoo_data.HashNameMale
        local zone = tattoo_data.Zone
        local collection = tattoo_data.Collection

        if not hash or hash == '' then
          hash = tattoo_data.HashNameFemale
        end

        local price = math.floor(tattoo_data.Price / 10)

        table.insert(categories_built[category_id], {
          name,
          hash,
          price,
          zone,
          collection,
        })
      end

      table.sort(categories_built[category_id], function(a, b)
        return string.upper(a[1]) < string.upper(b[1])
      end)
    else
      print('^3[blrp_tattoos] Error loading resource file: ' .. category_id .. '.json^7')
    end
  end
end)

RegisterNetEvent('core:server:registerSelectedPlayer', function(source)
	local character = exports.blrp_core:character(source)

  SetTimeout(100, function()
    for shop_id, shop_data in pairs(tattoo_shops_config.shops) do
      if not shop_data.programmatic then
        character.removeMarker({
          coords = shop_data.coords - vector3(0.0, 0.0, 0.97)
        })

        character.drawMarker({
          coords = shop_data.coords - vector3(0.0, 0.0, 0.97),
          call_enter = 'core:server:tattoo-shops:resolveConfig',
          args_enter = shop_id,
          call_leave = 'badMenu:server:requestClose',

          -- Drawing config
          type = 23,
          scale = {
            x = 1.5,
            y = 1.5,
            z = 0.5
          },
          r = 0,
          g = 255,
          b = 125,
          a = 125,
          trigger_distance = 1.7,
        })
      end
    end
  end)

  SetTimeout(15000, function()
    repaintCharacterTattoos(character.source)
  end)
end)

RegisterNetEvent('core:server:tattoo-shops:resolveConfig', function(shop_id)
  local character = exports.blrp_core:character(source)

	local shop_data = tattoo_shops_config.shops[shop_id]

	if not shop_data then
		return
	end

	local menu = BMenu:new(true, {
		resource_title = '',
		section_title = 'Main Menu',
		title_bg_image = 'https://i.gyazo.com/b942e3bedfd3a1e56b505e4beef35fe2.png',
	})

  menu:addSelection(false, 'Remove All Tattoos <span class="float-right">$1000</span>', function(source, value, callback)
    callback()

    if not character.request('Do you want to remove ALL tattoos? (Yes, ALL of them)') or not character.tryPayment(1000) then
      return
    end

    getCharacterTattoos(character, function(character_tattoos)
      local new_tattoos = {}

      for tattoo_key, tattoo_category in pairs(character_tattoos) do
        if tattoo_shops_config.categories_noremove[tattoo_category] then
          new_tattoos[tattoo_key] = tattoo_category
        end
      end

      character.log('ACTION', 'Removed all tattoos')
      character.notify('All tattoos removed')

      MySQL.Async.execute('INSERT INTO vrp_user_data (`user_id`, `dkey`, `dvalue`) VALUES (@user_id, @dkey, @dvalue) ON DUPLICATE KEY UPDATE `dvalue` = @dvalue', {
        user_id = character.get('identifier'),
        dkey = 'vRP:tattoos' .. character.get('id'),
        dvalue = json.encode(new_tattoos),
      }, function(rows)
        repaintCharacterTattoos(character.source)
      end)
    end)
  end)

  getCharacterTattoos(character, function(character_tattoos)
    local owned_tattoo_count = 0
    local submenu_name_remover = 'Tattoo Removal'
    menu:addSubMenu(false, submenu_name_remover)

    for _, category_id in ipairs(shop_data.categories) do
      local category_data = categories_built[category_id]

      if category_data then
        local submenu_name = tattoo_shops_config.categories[category_id]

        menu:addSubMenu(false, submenu_name)

        for _, tattoo_data in ipairs(category_data) do
          local tattoo_name = tattoo_data[1]
          local tattoo_zone = tattoo_data[4]:sub(6)
          local tattoo_collection = category_id

          if tattoo_data[5] then
            tattoo_collection = tattoo_data[5] .. '_overlays'
            tattoo_name = tattoo_name .. ' (' .. tattoo_data[5] .. ')'
          end

          if character_tattoos and character_tattoos[tattoo_data[2]] then
            owned_tattoo_count = owned_tattoo_count + 1

            menu:addSelection(submenu_name_remover, tattoo_name .. ' <span class="float-right">'..tattoo_zone..' | $150</span>', function(source, value, callback)
              callback('close')

              if not character_tattoos[tattoo_data[2]] then
                return
              end

              character.client('blrp_tattoos:client:cleanPlayer', false)
              character.client('blrp_tattoos:client:drawTattoo', tattoo_collection, tattoo_data[2])

              if not character.request('Do you want to remove this tattoo for $150?') then
                repaintCharacterTattoos(character.source)
                return
              end

              if not character.tryPayment(150) then
                repaintCharacterTattoos(character.source)
                return
              end

              character_tattoos[tattoo_data[2]] = nil

              character.client('blrp_tattoos:client:cleanPlayer', false)

              for _overlay, _collection in pairs(character_tattoos) do
                character.client('blrp_tattoos:client:drawTattoo', _collection, _overlay, false)
              end

              character.log('ACTION', 'Removed tattoo / name = ' .. tattoo_data[1] .. ' / price = 150')
              character.notify('Tattoo Removed')

              MySQL.Async.execute('INSERT INTO vrp_user_data (`user_id`, `dkey`, `dvalue`) VALUES (@user_id, @dkey, @dvalue) ON DUPLICATE KEY UPDATE `dvalue` = @dvalue', {
                user_id = character.get('identifier'),
                dkey = 'vRP:tattoos' .. character.get('id'),
                dvalue = json.encode(character_tattoos),
              })
            end)

            tattoo_name = tattoo_name .. ' (Owned)'
          end

          menu:addSelection(submenu_name, tattoo_name .. ' <span class="float-right">'..tattoo_zone..' | $' .. tattoo_data[3] .. '</span>', function(source, value, callback)
            callback()

            if character_tattoos[tattoo_data[2]] then
              return
            end

            character.client('blrp_tattoos:client:drawTattoo', tattoo_collection, tattoo_data[2])

            if not character.request('Do you want to purchase this tattoo for $' .. tattoo_data[3] .. '?') then
              repaintCharacterTattoos(character.source)
              return
            end

            if not character.tryPayment(tattoo_data[3]) then
              return
            end

            character_tattoos[tattoo_data[2]] = tattoo_collection

            character.log('ACTION', 'Purchased tattoo / name = ' .. tattoo_data[1] .. ' / price = ' .. tattoo_data[3])

            MySQL.Async.execute('INSERT INTO vrp_user_data (`user_id`, `dkey`, `dvalue`) VALUES (@user_id, @dkey, @dvalue) ON DUPLICATE KEY UPDATE `dvalue` = @dvalue', {
              user_id = character.get('identifier'),
              dkey = 'vRP:tattoos' .. character.get('id'),
              dvalue = json.encode(character_tattoos),
            })
          end)
        end
      end
  	end

    if owned_tattoo_count <= 0 then
      menu:addSelection(submenu_name_remover, 'You have no tattoos to be removed!', function(source, value, callback)
        callback('')
      end)
    end

  	menu:show(character.source)
  end)
end)

function getCharacterTattoos(character, callback)
  if not character or not character.get('id') then
    return
  end

  MySQL.Async.fetchAll('SELECT dvalue FROM vrp_user_data WHERE dkey = @dkey', {
    dkey = 'vRP:tattoos' .. character.get('id')
  }, function(rows)
    local character_tattoos = {}

    if #rows > 0 then
      character_tattoos = json.decode(rows[1].dvalue)
    end

    callback(character_tattoos)
  end)
end

function repaintCharacterTattoos(source)
  local character = exports.blrp_core:character(source)

  character.client('blrp_tattoos:client:cleanPlayer', false)

  getCharacterTattoos(character, function(character_tattoos)
    for _overlay, _collection in pairs(character_tattoos) do
      character.client('blrp_tattoos:client:drawTattoo', _collection, _overlay, false)
    end
  end)
end

RegisterNetEvent('blrp_tattoos:server:targetTattooPlayer', function(_, event_data)
  local character = exports.blrp_core:character(source)

  if not event_data or not event_data.target_source then
    return
  end

  openPlayerTattooMenu(character.source, 10, 15, event_data.target_source)
end)

function openPlayerTattooMenu(player, shop_id, apply_duration, target_source)
  local character = exports.blrp_core:character(player)

  if not character.hasGroup('staff') and not character.hasGroup('Obsidian Tattoos') and not character.hasGroup('lifer_rank2') then
    return
  end

  if not target_source then
    target_source = character.targetClosePlayer(5)
  end

  if not target_source or target_source <= 0 then
    return
  end

  local character_target = exports.blrp_core:character(target_source)

  local menu = BMenu:new(true, {
    resource_title = '',
    section_title = 'Main Menu',
    title_bg_image = 'https://i.gyazo.com/b942e3bedfd3a1e56b505e4beef35fe2.png',
  })

  local shop_data = tattoo_shops_config.shops[shop_id]

  getCharacterTattoos(character_target, function(character_tattoos)
    local owned_tattoo_count = 0
    local submenu_name_remover = 'Tattoo Removal'

    if #(character.getCoordinates() - vector3(1596.155, 2551.104, 45.667)) <= 10.0 or #(character.getCoordinates() - vector3(324.220,181.317,103.588)) <= 25.0 then
      menu:addSubMenu(false, submenu_name_remover)

      for category_id, _ in pairs(tattoo_shops_config.categories) do
        if not tattoo_shops_config.categories_noremove[category_id] or (tattoo_shops_config.categories_noremove[category_id] and shop_id == 8) or (tattoo_shops_config.categories_noremove[category_id] and shop_id == 10) then
          local category_data = categories_built[category_id]

          if category_data then
            for _, tattoo_data in pairs(category_data) do
              local tattoo_collection = category_id

              if tattoo_data[5] then
                tattoo_collection = tattoo_data[5] .. '_overlays'
              end

              if character_tattoos and character_tattoos[tattoo_data[2]] then
                owned_tattoo_count = owned_tattoo_count + 1

                menu:addSelection(submenu_name_remover, tattoo_data[1] .. ' <span class="float-right">' .. tattoo_data[4] .. '</span>', function(source, value, callback)
                  callback('close')

                  if not character_tattoos[tattoo_data[2]] then
                    return
                  end

                  character_target.client('blrp_tattoos:client:cleanPlayer', false)
                  character_target.client('blrp_tattoos:client:drawTattoo', tattoo_collection, tattoo_data[2])
                  character.client('badMenu:client:hideAll')

                  if not character.request('Do you want to remove this tattoo?') then
                    repaintCharacterTattoos(character_target.source)
                    return
                  end

                  if shop_id == 8 then
                    character.playSoundAround(6, 'sizzle', 0.1)

                    if not character.progressPromise('Removing Tattoo', 6) then
                      return
                    end

                    character_target.varyHealth(-200)
                  end

                  character_tattoos[tattoo_data[2]] = nil

                  character_target.client('blrp_tattoos:client:cleanPlayer', false)

                  for _overlay, _collection in pairs(character_tattoos) do
                    character_target.client('blrp_tattoos:client:drawTattoo', _collection, _overlay, false)
                  end

                  character.log('ACTION', 'Removed tattoo from other player / name = ' .. tattoo_data[1])
                  character.notify('Tattoo Removed')

                  MySQL.Async.execute('INSERT INTO vrp_user_data (`user_id`, `dkey`, `dvalue`) VALUES (@user_id, @dkey, @dvalue) ON DUPLICATE KEY UPDATE `dvalue` = @dvalue', {
                    user_id = character_target.get('identifier'),
                    dkey = 'vRP:tattoos' .. character_target.get('id'),
                    dvalue = json.encode(character_tattoos),
                  })
                end)
              end
            end
          end
        end
      end

      if owned_tattoo_count <= 0 then
        menu:addSelection(submenu_name_remover, 'This person has no tattoos to be removed!', function(source, value, callback)
          callback('')
        end)
      end
    end

    for _, category_id in ipairs(shop_data.categories) do
      local category_data = categories_built[category_id]

      if category_data then
        local submenu_name = tattoo_shops_config.categories[category_id]

        menu:addSubMenu(false, submenu_name)

        for _, tattoo_data in ipairs(category_data) do
          local tattoo_name = tattoo_data[1]
          local tattoo_zone = tattoo_data[4]:sub(6)
          local tattoo_collection = category_id

          if tattoo_data[5] then
            tattoo_collection = tattoo_data[5] .. '_overlays'
            tattoo_name = tattoo_name .. ' (' .. tattoo_data[5] .. ')'
          end

          if character_tattoos and character_tattoos[tattoo_data[2]] then
            tattoo_name = tattoo_name .. ' (Owned)'
          end

          menu:addSelection(submenu_name, tattoo_name .. ' <span class="float-right">' .. tattoo_zone .. '</span>', function(source, value, callback)
            callback()

            if character_tattoos[tattoo_data[2]] then
              return
            end

            character_target.client('blrp_tattoos:client:drawTattoo', tattoo_collection, tattoo_data[2])

            if not character.request('Apply the "' .. tattoo_name .. '" tattoo?') then
              repaintCharacterTattoos(character_target.source)
              return
            end

            if not character_target.request('Apply the "' .. tattoo_name .. '" tattoo?') then
              character.notify('Client rejected tattoo request')
              repaintCharacterTattoos(character_target.source)
              return
            end

            if not character.hasItemQuantity('tattoo_ink', 1) then
              character.notify('Missing tattoo ink')
              repaintCharacterTattoos(character_target.source)
              return
            end

            local tattoo_gun_item_meta, tattoo_gun_item_id = character.hasGetItemMeta('tattoo_gun', true)
            if not tattoo_gun_item_meta then
              character.notify('Missing tattoo gun')
              repaintCharacterTattoos(character_target.source)
              return
            end

            if tattoo_gun_item_meta.dur_cur == 0 then
              character.notify('Tattoo gun is broken')
              repaintCharacterTattoos(character_target.source)
              return
            end

            -- Add meta to non meta item
            if not string.match(tattoo_gun_item_id, 'meta') then
              character.take(tattoo_gun_item_id, 1, false)
              character.give(tattoo_gun_item_id, 1, false, false)

              tattoo_gun_item_meta, tattoo_gun_item_id = character.hasGetItemMeta('tattoo_gun', true)
            end

            exports.blrp_core:ModifyItemMeta(character.source, tattoo_gun_item_id, 'dur_cur', (tattoo_gun_item_meta.dur_cur - 1))
            if not character.take('tattoo_ink', 1) then
              repaintCharacterTattoos(character_target.source)
              return
            end

            character.client('badMenu:client:hideAll')

            if not character.progressPromise('Applying Tattoo', apply_duration, {
              animation = {
                animDict = 'misstattoo_parlour@shop_ig_4',
                anim = 'shop_ig_4_tattooist',
                flags = 49,
              },
            }) then
              repaintCharacterTattoos(character_target.source)
              return
            end

            character_tattoos[tattoo_data[2]] = tattoo_collection

            character_target.log('ACTION', 'Applied tattoo to other player / name = ' .. tattoo_name .. ' / collection = ' .. tattoo_collection)

            MySQL.Async.execute('INSERT INTO vrp_user_data (`user_id`, `dkey`, `dvalue`) VALUES (@user_id, @dkey, @dvalue) ON DUPLICATE KEY UPDATE `dvalue` = @dvalue', {
              user_id = character_target.get('identifier'),
              dkey = 'vRP:tattoos' .. character_target.get('id'),
              dvalue = json.encode(character_tattoos),
            })
          end)
        end
      end
    end

    menu:show(character.source)
  end)
end

AddEventHandler('chatCommand', function(player, command, args)
  if command ~= 'prisontattoos' then
    return
  end

  CancelEvent()

	local character = exports.blrp_core:character(player)

  if not character.hasGroup('staff') and not character.hasGroup('lifer_rank2') then
    return
  end

  openPlayerTattooMenu(character.source, 8, 60)
end)
