RegisterNetEvent('blrp_tattoos:client:cleanPlayer', function(playsound)
  local is_male = (GetEntityModel(PlayerPedId()) == `mp_m_freemode_01`)
  
  local retains = {}

  for _, decoration in pairs(GetPedDecorations(PlayerPedId())) do
    if exports.blrp_clothingstore:IsHairOverlay(decoration[1], decoration[2], is_male) then
      table.insert(retains, decoration)
    end
  end

  ClearPedDecorations(PlayerPedId())

  for _, decoration in pairs(retains) do
    AddPedDecorationFromHashes(PlayerPedId(), decoration[1], decoration[2])
  end

  if playsound == false then
    return
  end

  PlaySoundFrontend(-1, 'Tattooing_Oneshot_Remove', 'TATTOOIST_SOUNDS', 1)
end)

RegisterNetEvent('blrp_tattoos:client:drawTattoo', function(collection, overlay, playsound)
  AddPedDecorationFromHashes(PlayerPedId(), GetHash<PERSON><PERSON>(collection), GetHash<PERSON>ey(overlay))

  if playsound == false then
    return
  end

  PlaySoundFrontend(-1, 'Tattooing_Oneshot', 'TATTOOIST_SOUNDS', 1)
end)
