tattoo_shops_config = {
  categories_noremove = {
    ['gangs_overlays'] = true,
    ['prison_overlays'] = true,
    ['excluded_overlays'] = true,
  },

  categories = {
    ['mpairraces_overlays'] = 'Air Races Tattoos',
    ['mpsmuggler_overlays'] = 'Smugglers Run Tattoos',
    ['mpbeach_overlays'] = 'Beach Bum Tattoos',
    ['mpbiker_overlays'] = 'Biker Tattoos',
    ['mpchristmas2_overlays'] = 'Rebel Tattoos',
    ['mphipster_overlays'] = 'Hipster Tattoos',
    ['mplowrider_overlays'] = 'Low Rider Tattoos (Group 1)',
    ['mplowrider2_overlays'] = 'Low Rider Tattoos (Group 2)',
    ['mpluxe_overlays'] = 'Ill Gotten Gains Tattoos (Group 1)',
    ['mpluxe2_overlays'] = 'Ill Gotten Gains Tattoos (Group 2)',
    ['multiplayer_overlays'] = 'MP Tattoos',
    ['mpbusiness_overlays'] = 'Business',
    ['mpgunrunning_overlays'] = 'Gunrunning Tattoos',
    ['mpimportexport_overlays'] = 'Fuck It Tattoos',
    ['mpstunt_overlays'] = 'Stunt Tattoos',
    ['mpchristmas2017_overlays'] = 'Mythical Tattoos',
    ['mpheist3_overlays'] = 'Conspiracy Tattoos',
    ['mpvinewood_overlays'] = 'Casino Tattoos',
    ['mpheist4_overlays'] = 'Cayo Perico Tattoos',
    ['gangs_overlays'] = 'Gangs Tattoos',
    ['prison_overlays'] = 'Prison Tattoos',
    ['excluded_overlays'] = 'Face Tattoos',
    ['mpsecurity_overlays'] = 'The Contract Tattoos',
    ['mpsum2_overlays'] = 'Criminal Enterprises Tattoos',
    ['mpchristmas3_overlays'] = 'Los Santos Drug Wars Tattoos',
    ['mp2023_02_overlays'] = 'The Chop Shop Tattoos',
    ['obsidian_overlays'] = 'Obsidian Tattoos',
  },

  shops = {
    -- El Burro Heights
    [1] = {
      coords = vector3(1321.743,-1655.448,52.277),
      categories = {
        'mpluxe_overlays',
        'mpluxe2_overlays',
        'mpsecurity_overlays',
        'mpsum2_overlays',
      }
    },

    --[[
    -- Downtown Vinewood
    [2] = {
      coords = vector3(323.776, 179.708, 103.587),
      categories = {
				'mpgunrunning_overlays',
        'mpvinewood_overlays',
        'mpchristmas2017_overlays',
        'mpbiker_overlays',
      }
    },
    ]]--

    -- Chumash
    [3] = {
      coords = vector3(-3170.410,1078.917,20.831),
      categories = {
        'mpchristmas2_overlays',
        'mpbusiness_overlays',
        'mpheist3_overlays',
      }
    },

--[[
    -- Paleto Bay
    [4] = {
      coords = vector3(-296.072,6200.564,31.488),
			categories = {
        'mpgunrunning_overlays',
        'mpvinewood_overlays',
        'mpchristmas2017_overlays',
        'mpbiker_overlays',
      }
    },
]]--
    -- La Puerta
    [5] = {
      coords = vector3(-1155.429,-1428.841,4.956),
      categories = {
        'mplowrider2_overlays',
        'multiplayer_overlays',
        'mpimportexport_overlays',
        'mpstunt_overlays',
      }
    },

    -- Sandy Shores
    [6] = {
      coords = vector3(1866.033,3746.700,33.029),
      categories = {
        'mplowrider_overlays',
        'mphipster_overlays',
        'mpbeach_overlays',
        'mpheist4_overlays',
      }
    },

    -- Entry point for prison tattoos
    [8] = {
      programmatic = true,
      categories = {
        'gangs_overlays',
        'prison_overlays',
        'excluded_overlays',
      }
    },

    -- Admin Shop
    [9] = {
      coords = vector3(-1410.713, -1012.539, 22.559),
      categories = {
        'mpairraces_overlays',
        'mpsmuggler_overlays',
        'mpluxe_overlays',
        'mpluxe2_overlays',
        'mpsecurity_overlays',
        'mpsum2_overlays',

        'mpgunrunning_overlays',
        'mpvinewood_overlays',
        'mpchristmas2017_overlays',

        'mpchristmas2_overlays',
        'mpbusiness_overlays',
        'mpheist3_overlays',

        'mpbiker_overlays',
        'mplowrider_overlays',
        'mplowrider2_overlays',

        'multiplayer_overlays',
        'mpimportexport_overlays',
        'mpstunt_overlays',

        'mphipster_overlays',
        'mpbeach_overlays',
        'mpheist4_overlays',

        'gangs_overlays',
        'prison_overlays',
        'excluded_overlays',

        'mpchristmas3_overlays',
        'mp2023_02_overlays',

        'obsidian_overlays',
      }
    },

    -- Entry Point for Obsidian Tattoos
    [10] = {
      programmatic = true,
      categories = {
        'mpairraces_overlays',
        'mpsmuggler_overlays',
        'mpluxe_overlays',
        'mpluxe2_overlays',
        'mpsecurity_overlays',
        'mpsum2_overlays',

        'mpgunrunning_overlays',
        'mpvinewood_overlays',
        'mpchristmas2017_overlays',

        'mpchristmas2_overlays',
        'mpbusiness_overlays',
        'mpheist3_overlays',

        'mpbiker_overlays',
        'mplowrider_overlays',
        'mplowrider2_overlays',

        'multiplayer_overlays',
        'mpimportexport_overlays',
        'mpstunt_overlays',

        'mphipster_overlays',
        'mpbeach_overlays',
        'mpheist4_overlays',

        'mpchristmas3_overlays',
        'mp2023_02_overlays',

        'obsidian_overlays',
      }
    },
  }
}
