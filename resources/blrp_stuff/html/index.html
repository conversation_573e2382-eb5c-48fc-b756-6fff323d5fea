<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crosshair</title>
    <style>
        #crosshair {
            border-radius: 100%;
            border: 2px solid black;
            background-color: white;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
        }
    </style>
</head>
<body>
    <div id="crosshair"></div>

    <script>
        const crosshair = document.getElementById('crosshair');

        window.addEventListener('message', (evt) => {
            if (evt.data.type === 'xhairShow') {
                crosshair.style.display = 'block';
            } else if (evt.data.type === 'xhairHide') {
                crosshair.style.display = 'none';
            } else if (evt.data.type === 'updateConfig') {
                crosshair.style.backgroundColor = evt.data.color;
                const size = `${evt.data.size}px`;
                crosshair.style.width = size;
                crosshair.style.height = size;
            }
        });
    </script>
</body>
</html>