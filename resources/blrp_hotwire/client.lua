local tHotWire = {}
T.bindInstance("HotWire", tHotWire)

local inGame

RegisterNUICallback("Result", function(data, cb)
  SetNuiFocus(false, false)
  if inGame then
    inGame:resolve({
      success       = data.success,
      sequencesDone = data.sequencesDone,
      timeLeft      = data.timeLeft,
      reason        = data.reason
    })
    inGame = nil
  end
  cb("ok")
end)

tHotWire.hack = function(time, sequences, shapesCount, scramble)
  
  TriggerEvent('blrp_inventory:hide')
  inGame = promise.new()

  SetNuiFocus(true, true)
  SendNUIMessage({
    action    = "startHotwire",
    timeout   = time,
    sequences = sequences,
    shapes    = shapesCount,
    scramble  = scramble
  })

  return Citizen.Await(inGame)
end

exports("HotWire", tHotWire.hack)