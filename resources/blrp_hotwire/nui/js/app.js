const SHAPES = [
  `<i class="far fa-circle"></i>`,
  `<i class="far fa-square"></i>`,
  `<i class="far fa-star"></i>`,
  `<i class="far fa-heart"></i>`,
  `<i class="far fa-burst"></i>`,
  `<i class="far fa-certificate"></i>`,
  `<i class="far fa-burst"></i>`,
  `<i class="far fa-hexagon"></i>`,
  `<i class="far fa-play"></i>`,
  `<i class="far fa-badge"></i>`
];

let timeout, sequencesRemaining, shapesToWire, scrambleEach;
let currentTime, timerInterval;
let connections = [];
let dragging = false, dragStartShape = null;
let previewPath = null;

const hackingContainer = document.getElementById("hackingContainer");
const stageHack        = document.getElementById("stageHack");
const topRow           = document.getElementById("topRow");
const bottomRow        = document.getElementById("bottomRow");
const hackFill         = document.getElementById("hackFill");
const wireSvg          = document.getElementById("wireSvg");
const timerSound = document.getElementById("timer");
const zapSounds  = ["zap2","zap3","zap4"];

console.log('timerSound is', timerSound);

function playSound(id) {
  const a = document.getElementById(id);
  if (!a) return;
  a.currentTime = 0;
  a.play().catch(() => {});
}

function shuffle(arr) {
  for (let i = arr.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr;
}

function startTimer(sec) {
  clearInterval(timerInterval);
  currentTime = sec;
  hackFill.style.width = "100%";

  if (timerSound) {
    timerSound.loop = true;
    timerSound.currentTime = 0;
    timerSound.play().catch(() => {});
  }

  timerInterval = setInterval(() => {
    currentTime -= 0.1;
    if (currentTime <= 0) {
      clearInterval(timerInterval);
      finish(false, "Ran out of time");
      return;
    }
    hackFill.style.width = (currentTime / timeout * 100) + "%";
  }, 100);
}

function openHotwireUI(data) {
  timeout            = data.timeout;
  sequencesRemaining = data.sequences;
  shapesToWire       = data.shapes;
  scrambleEach       = data.scramble;
  connections        = [];

  hackingContainer.classList.add("active");
  stageHack.style.display = "flex";
  wireSvg.innerHTML       = "";
  startSequence();
}

function startSequence() {
  connections = [];

  const picks = shuffle([...SHAPES]).slice(0, shapesToWire);
  topRow.innerHTML = "";
  shuffle(picks).forEach((html, i) => {
    const el = document.createElement("div");
    el.classList.add("shape");
    el.dataset.idx = i;
    el.dataset.row = "top";
    el.innerHTML   = html;
    topRow.appendChild(el);
  });
  bottomRow.innerHTML = "";
  shuffle(picks).forEach((html, i) => {
    const el = document.createElement("div");
    el.classList.add("shape");
    el.dataset.idx = i;
    el.dataset.row = "bottom";
    el.innerHTML   = html;
    bottomRow.appendChild(el);
  });

  document.querySelectorAll(".shape").forEach(el =>
    el.addEventListener("mousedown", onShapeMouseDown)
  );
  document.addEventListener("mousemove", onDrag);
  document.addEventListener("mouseup",   onShapeMouseUp);

  startTimer(timeout);
}

function onShapeMouseDown(e) {
  const el = e.currentTarget;
  if (dragging || el.classList.contains("connected")) return;
  dragging = true;
  dragStartShape = el;

  previewPath = document.createElementNS("http://www.w3.org/2000/svg","path");
  previewPath.setAttribute("stroke", "#fff");
  previewPath.setAttribute("stroke-width", "3");
  previewPath.setAttribute("fill", "none");
  wireSvg.appendChild(previewPath);
}

function onDrag(e) {
  if (!dragging || !previewPath) return;
  const R = wireSvg.getBoundingClientRect();
  const rawX = e.clientX, rawY = e.clientY;
  const clX = Math.max(R.left, Math.min(rawX, R.right));
  const clY = Math.max(R.top,  Math.min(rawY, R.bottom));
  const x2 = clX - R.left, y2 = clY - R.top;

  const A = dragStartShape.getBoundingClientRect();
  const x1 = A.left + A.width/2 - R.left;
  const y1 = A.top  + A.height/2 - R.top;
  const centerY = R.height / 2;
  const dx = x2 - x1;
  const midX1 = x1 + dx * 0.25;
  const midX2 = x1 + dx * 0.75;

  const d =
    `M${x1},${y1}` +
    ` C ${x1},${centerY} ${midX1},${centerY} ${midX1},${centerY}` +
    ` L ${midX2},${centerY}` +
    ` C ${midX2},${centerY} ${x2},${centerY} ${x2},${y2}`;
  previewPath.setAttribute("d", d);
}

function onShapeMouseUp(e) {
  if (!dragging) return;
  dragging = false;

  if (previewPath) {
    wireSvg.removeChild(previewPath);
    previewPath = null;
  }

  const tgt = e.target.closest(".shape");
  if (
    !tgt ||
    tgt === dragStartShape ||
    tgt.classList.contains("connected") ||
    tgt.dataset.row === dragStartShape.dataset.row
  ) {
    dragStartShape = null;
    return;
  }

  const R = wireSvg.getBoundingClientRect();
  const A = dragStartShape.getBoundingClientRect();
  const B = tgt.getBoundingClientRect();
  const x1 = A.left + A.width/2 - R.left, y1 = A.top + A.height/2 - R.top;
  const x2 = B.left + B.width/2 - R.left, y2 = B.top + B.height/2 - R.top;
  const centerY = R.height / 2;
  const dx = x2 - x1;
  const midX1 = x1 + dx * 0.25;
  const midX2 = x1 + dx * 0.75;

  function drawWire(color) {
    let d;
    if (parseInt(dragStartShape.dataset.idx, 10) === parseInt(tgt.dataset.idx, 10)) {
      d = `M${x1},${y1} L${x2},${y2}`;
    } else {
      d =
        `M${x1},${y1}` +
        ` C ${x1},${centerY} ${midX1},${centerY} ${midX1},${centerY}` +
        ` L ${midX2},${centerY}` +
        ` C ${midX2},${centerY} ${x2},${centerY} ${x2},${y2}`;
    }
    const p = document.createElementNS("http://www.w3.org/2000/svg","path");
    p.setAttribute("d", d);
    p.setAttribute("stroke", color);
    p.setAttribute("stroke-width", "3");
    p.setAttribute("fill", "none");
    wireSvg.appendChild(p);
  }

  const correct = dragStartShape.innerHTML === tgt.innerHTML;
  drawWire(correct ? "#6a9053" : "#e63946");
  dragStartShape.classList.add("connected");
  tgt.classList.add("connected");

  if (!correct) {
    playSound("fail");
    return setTimeout(() => finish(false, "Incorrect match"), 300);
  }

  const idx = Math.floor(Math.random() * zapSounds.length);
  const chosen = zapSounds[idx];
  playSound(chosen);

  connections.push({ top: dragStartShape.dataset.idx, bottom: tgt.dataset.idx });

  if (scrambleEach) {
    const topRem = Array.from(topRow.children).filter(c => !c.classList.contains("connected"));
    const botRem = Array.from(bottomRow.children).filter(c => !c.classList.contains("connected"));
    const tHtml  = shuffle(topRem.map(c => c.innerHTML));
    const bHtml  = shuffle(botRem.map(c => c.innerHTML));
    topRem.forEach((c, i) => c.innerHTML = tHtml[i]);
    botRem.forEach((c, i) => c.innerHTML = bHtml[i]);
  }

  if (connections.length === shapesToWire) {
    sequencesRemaining--;
    if (sequencesRemaining > 0) {
      wireSvg.innerHTML = "";  
      return startSequence();
    } else {
      return finish(true, "");
    }
  }

  dragStartShape = null;
}

function finish(success, reason) {
  clearInterval(timerInterval);
  if (timerSound) {
    timerSound.pause();
    timerSound.currentTime = 0;
  }
  playSound(success ? "finish" : "fail");

  setTimeout(() => {
    fetch(`https://blrp_hotwire/Result`, {
      method: "POST",
      headers: { "Content-Type": "application/json; charset=UTF-8" },
      body: JSON.stringify({
        success,
        sequencesDone: 0,
        timeLeft: parseFloat(currentTime.toFixed(1)),
        reason: reason || ""
      })
    }).catch(() => {});

    hackingContainer.classList.remove("active");
    stageHack.style.display = "none";
    topRow.innerHTML    = "";
    bottomRow.innerHTML = "";
    wireSvg.innerHTML   = "";
  }, 400);
}

window.addEventListener("message", e => {
  if (e.data.action === "startHotwire") openHotwireUI(e.data);
});

window.addEventListener("keydown", e => {
  if (e.key === "Escape") {
    playSound("fail");
    finish(false, "Escape pressed");
  }
});
