@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;0,700;1,300&display=swap');

:root {
  --accent: #222;
  --card-back: #222;
  --card-front: #1c1c1b;
  --bg-stage: #1c1c1b;
  --bg-screen: #2a2a28;
  --bar-bg: #444;
  --bar-fill: #fff;
  --text-white: #fff;
  --text-green: #6a9053;
  --text-blue: #0e789e;
  --text-red:   #e63946;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body, html {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: transparent;
  font-family: 'Roboto', sans-serif;
  user-select: none;
}

.hacking-container {
  position: fixed;
  top: 25%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 900px;
  height: auto;
  background-color: var(--bg-stage);
  border: 10px solid #333;
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.hacking-container.active {
  display: flex;
}

.stage-screen {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background: var(--bg-stage);
  color: var(--text-white);
  position: relative;
  padding: 18px;
}

#hotwire-container {
  width: 100%;
  height: calc(2 * 120px + 12px /* gap */ + 24px /* padding top/bottom */);
  background: var(--bg-screen);
  padding: 12px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 12px;
  border: 2px solid var(--bg-stage);
  border-radius: 4px;
}

.row {
  display: flex;
  justify-content: space-evenly;
  gap: 12px;
}

.shape {
  width: 120px;
  height: 120px;
  background: var(--card-front);
  border: 2px solid var(--bar-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 4em;
  cursor: pointer;
  user-select: none;
  position: relative;
}
.shape.connected {
  opacity: 0.5;
  pointer-events: none;
}
.shape i {
  color: var(--text-white) !important;
}

.progress-bar {
  width: 100%;
  height: 30px;
  background: var(--bar-bg);
  margin-top: 12px;
  overflow: hidden;
  position: relative;
}
.progress-bar .fill {
  position: absolute;
  top: 0; bottom: 0; left: 0;
  width: 100%;
  background: var(--bar-fill);
  transition: width 0.2s linear;
}

#wireSvg {
  position: absolute;
  top: calc(18px + 0);  
  left: 12px;
  width: calc(100% - 24px);
  height: calc(2 * 120px + 12px /* gap */);
  pointer-events: none;
  z-index: 1;
}
