

-- Main_Entrance_Left_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = -1911661372,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-282.5876, -1924.784, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main_Entrance_Left_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00003051758,
	objHash = 160224187,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-284.4262, -1923.241, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main_Entrance_Right_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = -1911661372,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-286.5671, -1921.444, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main_Entrance_Right_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00003051758,
	objHash = 160224187,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-288.4058, -1919.902, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Merch_Store
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-299.5952, -1928.978, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Food_Store
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-289.5816, -1937.38, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Mainlobby_to_left_stairs_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00004577637,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-283.4648, -1937.731, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Mainlobby_to_left_stairs_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.00004196167,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-285.0077, -1939.57, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Mainlobby_to_right_stairs_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-302.5464, -1924.853, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Mainlobby_to_right_stairs_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-301.0034, -1923.014, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- right_tickets
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-302.0008, -1920.23, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- left_tickets
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-280.5498, -1938.23, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main_Lobby_to_Arena_left_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00001525879,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-288.1545, -1941.448, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main_Lobby_to_Arena_left_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-289.9933, -1939.905, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main_Lobby_to_Arena_right_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-302.0108, -1929.821, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main_Lobby_to_Arena_right_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-303.8496, -1928.278, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_staris_to_basement_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-301.0023, -1923.013, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_staris_to_basement_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-302.5452, -1924.852, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_staris_to_basement_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-285.0066, -1939.568, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_staris_to_basement_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-283.4636, -1937.73, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_stairs_to_Lounge_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-282.6718, -1946.049, 34.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_stairs_to_Lounge_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-284.5105, -1944.506, 34.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_stairs_to_Lounge_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-307.4936, -1925.221, 34.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_stairs_to_Lounge_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-309.3324, -1923.678, 34.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_stairs_to_commentary_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-307.4936, -1925.221, 38.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_stairs_to_commentary_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-309.3324, -1923.678, 38.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_stairs_to_commentary_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-282.6718, -1946.049, 38.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_stairs_to_commentary_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-284.5105, -1944.506, 38.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_commentary_to_controlroom
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-292.9879, -1940.345, 38.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_commentary_to_controlroom
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-301.9236, -1932.847, 38.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_staris_to_vip_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-307.4936, -1925.221, 42.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right_staris_to_vip_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-309.3324, -1923.678, 42.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_staris_to_vip_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00006103516,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-282.6718, -1946.049, 42.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left_staris_to_vip_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-284.5105, -1944.506, 42.15302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Tunnel_to_garage_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00012207031,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-351.6596, -1904.279, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Tunnel_to_garage_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-353.4984, -1902.736, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Garagedoor_01
table.insert(Config.DoorList, {
	garage = true,
	objHeading = 210.47044372559,
	objHash = -1098702270,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-386.8642, -1884.247, 21.68489),
	slides = true,
	lockpick = false,
	audioRemote = false,
	maxDistance = 6.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Garagedoor_02
table.insert(Config.DoorList, {
	garage = true,
	objHeading = 201.49885559082,
	objHash = -1098702270,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-375.949, -1878.938, 21.67491),
	slides = true,
	lockpick = false,
	audioRemote = false,
	maxDistance = 6.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00003051758,
	objHash = -1911661372,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-255.7744, -2027.674, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000003814697,
	objHash = 160224187,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-254.2315, -2025.835, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_reception_left
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-270.0722, -2019.442, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_reception_right
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-264.6426, -2012.971, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_left_backhall_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000102996826,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-273.6999, -2020.967, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_left_backhall_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-272.157, -2019.128, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_right_backhall_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000102996826,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-265.3132, -2010.972, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_right_backhall_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-263.7703, -2009.133, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_right_stairs_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-262.2973, -2008.933, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_right_stairs_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00012207031,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-260.4585, -2010.476, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_left_stairs_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00012207031,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-271.8023, -2023.995, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side_entrance_left_stairs_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-273.6411, -2022.452, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Pressroom
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00007629395,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-275.8455, -2020.603, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Office
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00007629395,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-264.5017, -2007.083, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Sideentrance_right_stairs_to_basement_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00012207031,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-260.4585, -2010.476, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Sideentrance_right_stairs_to_basement_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-262.2973, -2008.933, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Sideentrance_left_stairs_to_basement_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-273.6411, -2022.452, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Sideentrance_left_stairs_to_basement_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 140.00012207031,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-271.8023, -2023.995, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Lockers_guest_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-258.6906, -2014.585, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Lockers_guest_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-260.2334, -2016.424, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Lockers_home_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-265.9058, -2023.184, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Lockers_home_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-267.4486, -2025.022, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- makeup_left_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-279.6095, -2009.587, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- makeup_right_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 320.00003051758,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-276.0103, -2005.298, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- sideentrance_basement_to_tunnels_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000053405762,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-279.2685, -2007.781, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- sideentrance_basement_to_tunnels_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-277.7258, -2005.942, 21.71078),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- sideentrance_backhall_to_arena_01
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 50.000102996826,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-279.497, -2007.586, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- sideentrance_backhall_to_arena_02
table.insert(Config.DoorList, {
	garage = false,
	objHeading = 230.00010681152,
	objHash = 1722387194,
	locked = true,
	authorizedJobs = { ['mba']=0 },
	objCoords = vector3(-277.9541, -2005.748, 30.25302),
	slides = false,
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})