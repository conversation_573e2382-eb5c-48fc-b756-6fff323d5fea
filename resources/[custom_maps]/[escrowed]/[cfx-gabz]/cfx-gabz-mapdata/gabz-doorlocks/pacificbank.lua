

-- Main entrance left - doorname "gabz_sm_pb_door_06"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(231.5031, 216.5134, 106.4304),
	objHeading = 115.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main entrance right - doorname "gabz_sm_pb_door_07"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(232.6007, 214.1564, 106.4304),
	objHeading = 295.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side entrance right - doorname "gabz_sm_pb_door_07"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(267.3223, 200.7542, 106.4499),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side entrance left - doorname "gabz_sm_pb_door_06"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(264.8785, 201.6437, 106.4499),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Roof entrance left - doorname "gabz_sm_pb_door_06"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(273.897, 234.5862, 123.9748),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Roof entrance right - doorname "gabz_sm_pb_door_07"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(271.4533, 235.4757, 123.9748),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- main entrance to main hall left - doorname "gabz_sm_pb_door_05"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(244.7755, 227.2479, 106.7452),
	objHeading = 70.************,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- main entrance to main hall right - doorname "gabz_sm_pb_door_05"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(244.039, 225.2242, 106.7452),
	objHeading = 250.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- side entrance employee only - doorname "gabz_sm_pb_door_02"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(272.7905, 206.4805, 106.3822),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- side entrance 2nd floor - doorname "gabz_sm_pb_door_02"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(272.7905, 206.4805, 110.2805),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- roof entrance 2nd floor - doorname "gabz_sm_pb_door_02"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(277.5953, 223.5416, 110.2792),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- roof entrance 1st floor - doorname "gabz_sm_pb_door_02"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(277.5953, 223.5416, 106.3804),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- counter left - doorname "gabz_sm_pb_door_04"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = -**********,
	maxDistance = 2.0,
	objCoords = vector3(256.6068, 229.6896, 106.3702),
	objHeading = 70.************,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- counter right - doorname "gabz_sm_pb_door_04"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = -**********,
	maxDistance = 2.0,
	objCoords = vector3(251.5199, 215.7132, 106.3702),
	objHeading = 250.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- counter left 2 - doorname "gabz_sm_pb_door_04"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = -**********,
	maxDistance = 2.0,
	objCoords = vector3(270.2307, 221.2673, 106.3702),
	objHeading = 70.************,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- counter right 2 - doorname "gabz_sm_pb_door_04"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = -**********,
	maxDistance = 2.0,
	objCoords = vector3(267.37, 213.408, 106.3702),
	objHeading = 250.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 1 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(252.7898, 213.76, 106.3822),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 2 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(262.1839, 210.3409, 106.3822),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 3 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(252.7898, 213.76, 110.2805),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 4 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(262.1839, 210.3409, 110.2805),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 5 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(258.8165, 230.3764, 106.3822),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 6 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(268.2106, 226.9573, 106.3822),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 7 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(258.8165, 230.3764, 110.2805),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office 8 - doorname "gabz_sm_pb_door_01"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(268.2106, 226.9573, 110.2805),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main office left - doorname "gabz_sm_pb_door_03"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(273.1839, 216.8628, 110.2805),
	objHeading = 70.************,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Main office right - doorname "gabz_sm_pb_door_03"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = **********,
	maxDistance = 2.0,
	objCoords = vector3(272.4721, 214.9073, 110.2805),
	objHeading = 250.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Basement right - doorname "gabz_sm_pb_door_08"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(270.1032, 212.9229, 97.31798),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Basement left - doorname "gabz_sm_pb_door_08"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(272.6422, 219.8987, 97.31798),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Basement reception - doorname "gabz_sm_pb_door_08"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(265.7791, 225.8668, 97.31798),
	objHeading = 70.************,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Basement middle right - doorname "gabz_sm_pb_door_08"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(256.4124, 229.2759, 97.31798),
	objHeading = 70.************,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Basement middle left - doorname "gabz_sm_pb_door_08"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(251.6498, 216.1906, 97.31798),
	objHeading = 250.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Basement left side vault - doorname "gabz_sm_pb_door_08"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(244.558, 216.8973, 97.31798),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Basement right side vault - doorname "gabz_sm_pb_door_08"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(250.5642, 233.3994, 97.31798),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- vault door - doorname "v_ilev_bk_vaultdoor"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(234.9857, 228.0696, 97.72185),
	objHeading = 70.************,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- vault gold left - doorname "gabz_sm_pb_door_09"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(229.8905, 227.342, 97.32397),
	objHeading = 340.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- vault gold right - doorname "gabz_sm_pb_door_09"
table.insert(Config.DoorList, {
	locked = true,
	slides = false,
	objHash = *********,
	maxDistance = 2.0,
	objCoords = vector3(225.6463, 228.8868, 97.32397),
	objHeading = 160.***********,
	fixText = false,
	lockpick = false,
	audioRemote = false,
	garage = false,
	authorizedJobs = { ['bank']=0 },		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})