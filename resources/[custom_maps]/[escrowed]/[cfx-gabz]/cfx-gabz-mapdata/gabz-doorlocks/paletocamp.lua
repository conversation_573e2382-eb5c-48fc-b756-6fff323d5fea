

-- entrance door
table.insert(Config.<PERSON>List, {
	objCoords = vector3(1432.42, 6338.22, 24.02298),
	objHash = 1471868433,
	audioRemote = false,
	lockpick = false,
	objHeading = 0.0,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['paleto']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet
table.insert(Config.DoorList, {
	objCoords = vector3(1424.427, 6331.298, 23.99483),
	objHash = 1729989846,
	audioRemote = false,
	lockpick = false,
	objHeading = 270.00003051758,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['paleto']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- meetingroom
table.insert(Config.DoorList, {
	objCoords = vector3(1424.427, 6327.992, 23.99483),
	objHash = 1729989846,
	audioRemote = false,
	lockpick = false,
	objHeading = 270.00003051758,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['paleto']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- bossoffice
table.insert(Config.DoorList, {
	objCoords = vector3(1429.087, 6329.188, 23.99483),
	objHash = 1729989846,
	audioRemote = false,
	lockpick = false,
	objHeading = 89.999977111816,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['paleto']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})