

-- entrance
table.insert(Config.<PERSON>List, {
	objCoords = vector3(2196.781, 5570.083, 54.05622),
	objHash = 819505495,
	audioRemote = false,
	lockpick = false,
	objHeading = 270.0,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['weed']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet
table.insert(Config.DoorList, {
	objCoords = vector3(2189.865, 5578.076, 54.02808),
	objHash = 363295477,
	audioRemote = false,
	lockpick = false,
	objHeading = 180.00001525879,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['weed']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- meetingroom
table.insert(Config.DoorList, {
	objCoords = vector3(2186.558, 5578.076, 54.02808),
	objHash = 363295477,
	audioRemote = false,
	lockpick = false,
	objHeading = 180.00001525879,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['weed']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- bossoffice
table.insert(Config.DoorList, {
	objCoords = vector3(2187.754, 5573.417, 54.02808),
	objHash = 363295477,
	audioRemote = false,
	lockpick = false,
	objHeading = 359.99996948242,
	fixText = false,
	slides = false,
	garage = false,
	locked = true,
	authorizedJobs = { ['weed']=0 },
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})