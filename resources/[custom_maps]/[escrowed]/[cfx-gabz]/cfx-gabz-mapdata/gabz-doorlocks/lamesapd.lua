
-- Front left entrance - doorname: gabz_lamesapd_doors01_entrancea
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(827.9521, -1288.786, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = 277920071,
	maxDistance = 2.0,
	objHeading = 89.999977111816,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Front right entrance - doorname: gabz_lamesapd_doors01_entranceb
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(827.9521, -1291.387, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -34368499,
	maxDistance = 2.0,
	objHeading = 269.99987792969,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Observation - doorname: gabz_lamesapd_doors03_observation
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(840.0884, -1280.999, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -1011300766,
	maxDistance = 2.0,
	objHeading = 269.99996948242,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Interrogation - doorname: gabz_lamesapd_doors03_interogation
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(840.0861, -1281.824, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -1189294593,
	maxDistance = 2.0,
	objHeading = 89.999977111816,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left entrance meetingroom (left door) - doorname: gabz_lamesapd_doors01_meetinga
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(849.9325, -1287.346, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -1983352576,
	maxDistance = 2.0,
	objHeading = 179.99984741211,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Left entrance meetingroom (right door) - doorname: gabz_lamesapd_doors01_meetingb
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(852.5331, -1287.346, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = 2076628221,
	maxDistance = 2.0,
	objHeading = 0.0,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right entrance meetingroom (left door) - doorname: gabz_lamesapd_doors01_meetinga
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(856.5074, -1287.346, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -1983352576,
	maxDistance = 2.0,
	objHeading = 179.99984741211,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Right entrance meetingroom (right door) - doorname: gabz_lamesapd_doors01_meetingb
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(859.1082, -1287.346, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = 2076628221,
	maxDistance = 2.0,
	objHeading = 0.0,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Archives - doorname: gabz_lamesapd_doors01_archives
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(858.865, -1291.385, 28.37111),
	authorizedJobs = { ['police']=0 },
	objHash = 539497004,
	maxDistance = 2.0,
	objHeading = 0.0,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Cpt.Office - doorname: gabz_lamesapd_doors01_office
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(851.9497, -1298.389, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = 1861900850,
	maxDistance = 2.0,
	objHeading = 89.999862670898,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Cell - doorname: gabz_lamesapd_doors01_cell
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(834.2814, -1295.986, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = 1162089799,
	maxDistance = 2.0,
	objHeading = 89.999977111816,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Lobby fence door - doorname: gabz_lamesapd_fancegate
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(835.9445, -1292.193, 27.78268),
	authorizedJobs = { ['police']=0 },
	objHash = -147896569,
	maxDistance = 2.0,
	objHeading = 270.00003051758,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Breakroom - doorname: gabz_lamesapd_doors01_breakroom
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(837.2611, -1309.514, 28.37111),
	authorizedJobs = { ['police']=0 },
	objHash = 1491736897,
	maxDistance = 2.0,
	objHeading = 269.99996948242,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Evidence - doorname: gabz_lamesapd_doors01_evidences
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(846.3696, -1310.04, 28.37111),
	authorizedJobs = { ['police']=0 },
	objHash = 272264766,
	maxDistance = 2.0,
	objHeading = 179.99995422363,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Lockers1 - doorname: gabz_lamesapd_doors02_lockers
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(854.7811, -1310.04, 28.37111),
	authorizedJobs = { ['police']=0 },
	objHash = -1213101062,
	maxDistance = 2.0,
	objHeading = 0.0,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Lockers2 - doorname: gabz_lamesapd_doors02_lockers
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(855.7422, -1314.608, 28.37111),
	authorizedJobs = { ['police']=0 },
	objHash = -1213101062,
	maxDistance = 2.0,
	objHeading = 269.99993896484,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Hallway to openspace (Left door) - doorname: gabz_lamesapd_doors02
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(856.5074, -1310.038, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -375301406,
	maxDistance = 2.0,
	objHeading = 179.99984741211,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Hallway to openspace (Right door) - doorname: gabz_lamesapd_doors02
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(859.1082, -1310.038, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -375301406,
	maxDistance = 2.0,
	objHeading = 0.0,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Backentrance to hallway - doorname: gabz_lamesapd_doors02_entranceb
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(859.0076, -1320.125, 28.37111),
	authorizedJobs = { ['police']=0 },
	objHash = -1339729155,
	maxDistance = 2.0,
	objHeading = 0.0,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Backentrance to breakroom - doorname: gabz_lamesapd_doors02_entrancea
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(829.6385, -1310.128, 28.37117),
	authorizedJobs = { ['police']=0 },
	objHash = -1246730733,
	maxDistance = 2.0,
	objHeading = 179.99989318848,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Parkinglot gate - doorname: gabz_lamesapd_cargate
table.insert(Config.DoorList, {
	garage = false,
	objCoords = vector3(816.9862, -1325.258, 25.09328),
	authorizedJobs = { ['police']=0 },
	objHash = -1372582968,
	maxDistance = 2.0,
	objHeading = 269.0,
	audioRemote = false,
	slides = false,
	locked = true,
	lockpick = false,
	fixText = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})