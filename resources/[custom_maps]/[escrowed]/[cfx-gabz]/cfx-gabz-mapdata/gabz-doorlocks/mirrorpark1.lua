-- ## GABZ - MIRROR PARK 1 

-- ## HOUSE 1 - COORDINATES: 983.289,-709.422,57.585
-- Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror1_1' },		
	objCoords = vector3(978.4756, -716.1016, 58.43926),
	objHeading = 132.00003051758,
	objHash = -232187956, -- gabz_mp_house_10_1_entrancedoor_b
	maxDistance = 2.0,
	slides = false,
	audioRemote = false,
	locked = true,
	garage = false,
	lockpick = false,
	fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror1_1' },		
	objCoords = vector3(982.3516, -726.1836, 58.148),
	objHeading = 222.00003051758,
	objHash = -2080370239, -- gabz_mp_house_10_1_kitchendoor
	maxDistance = 2.0,
	slides = false,
	audioRemote = false,
	locked = true,
	garage = false,
	lockpick = false,
	fixText = true,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Back Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror1_1' },
	objCoords = vector3(971.6006, -726.4808, 58.3438),
	objHeading = 222.00003051758,
	objHash = 12662004, -- gabz_mp_house_10_1_entrancedoor_f
	maxDistance = 2.0,
	slides = false,
	audioRemote = false,
	locked = true,
	garage = false,
	lockpick = false,
	fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})


-- ## HOUSE 2 - COORDINATES: 1099.321, -430.321, 67.392
-- Entry
table.insert(Config.DoorList, {
	fixText = false,
	garage = false,
	slides = false,
	objHash = -232187956, -- gabz_mp_house_10_1_entrancedoor_b
	objCoords = vector3(1098.834, -438.9998, 68.0089),
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objHeading = 172.66996765136,
	items = { 'keys_mirror1_2' },
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Back door
table.insert(Config.DoorList, {
	fixText = false,
	garage = false,
	slides = false,
	objHash = -2080370239, -- gabz_mp_house_10_1_kitchendoor
	objCoords = vector3(1108.344, -444.1208, 67.71766),
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objHeading = 262.66998291016,
	items = { 'keys_mirror1_2' },
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side door
table.insert(Config.DoorList, {
	fixText = false,
	garage = false,
	slides = false,
	objHash = 12662004, -- gabz_mp_house_10_1_entrancedoor_f
	objCoords = vector3(1100.384, -451.3526, 67.91344),
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objHeading = 262.66998291016,
	items = { 'keys_mirror1_2' },
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})


-- ## HOUSE 3 - COORDINATES: 1255.108, -494.784 ,69.396
-- Entry
table.insert(Config.DoorList, {
	garage = false,
	items = { 'keys_mirror1_3' },
	fixText = false,
	objCoords = vector3(1251.234, -493.4762, 70.1251),
	audioRemote = false,
	objHash = -232187956, -- gabz_mp_house_10_1_entrancedoor_b
	objHeading = 76.999519348144,
	maxDistance = 2.0,
	locked = true,
	slides = false,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Back door
table.insert(Config.DoorList, {
	garage = false,
	items = { 'keys_mirror1_3' },
	fixText = true,
	objCoords = vector3(1245.2, -502.4338, 69.83384),
	audioRemote = false,
	objHash = -2080370239, -- gabz_mp_house_10_1_kitchendoor
	objHeading = 166.99955749512,
	maxDistance = 2.0,
	locked = true,
	slides = false,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side door
table.insert(Config.DoorList, {
	garage = false,
	items = { 'keys_mirror1_3' },
	fixText = false,
	objCoords = vector3(1238.79, -493.7974, 70.02964),
	audioRemote = false,
	objHash = 12662004, -- gabz_mp_house_10_1_entrancedoor_f
	objHeading = 166.99955749512,
	maxDistance = 2.0,
	locked = true,
	slides = false,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})