

-- location 1 - entrance door (doorname: "v_ilev_clothmiddoor")
table.insert(Config.DoorList, {
	garage = false,
	authorizedJobs = { ['suburban']=0 },
	slides = false,
	objCoords = vector3(127.835, -211.7779, 55.22748),
	lockpick = false,
	fixText = false,
	locked = true,
	objHeading = 160.00003051758,
	audioRemote = false,
	objHash = 1780022985,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 2 - entrance door (doorname: "v_ilev_clothmiddoor")
table.insert(Config.DoorList, {
	garage = false,
	authorizedJobs = { ['suburban']=0 },
	slides = false,
	objCoords = vector3(617.2521, 2750.972, 42.75804),
	lockpick = false,
	fixText = false,
	locked = true,
	objHeading = 4.0609426498413,
	audioRemote = false,
	objHash = 1780022985,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 3 - entrance door (doorname: "v_ilev_clothmiddoor")
table.insert(Config.DoorList, {
	garage = false,
	authorizedJobs = { ['suburban']=0 },
	slides = false,
	objCoords = vector3(-1201.475, -776.8891, 17.99211),
	lockpick = false,
	fixText = false,
	locked = true,
	objHeading = 306.62994384766,
	audioRemote = false,
	objHash = 1780022985,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 4 - entrance door (doorname: "v_ilev_clothmiddoor")
table.insert(Config.DoorList, {
	garage = false,
	authorizedJobs = { ['suburban']=0 },
	slides = false,
	objCoords = vector3(-3167.732, 1055.583, 21.53314),
	lockpick = false,
	fixText = false,
	locked = true,
	objHeading = 156.25939941406,
	audioRemote = false,
	objHash = 1780022985,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})