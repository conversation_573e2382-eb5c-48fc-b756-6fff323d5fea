

-- entrance front
table.insert(Config.<PERSON>List, {
	objHash = 825709191,
	objHeading = 340.0,
	objCoords = vector3(-1111.63, 4938.297, 218.5258),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance side
table.insert(Config.DoorList, {
	objHash = 825709191,
	objHeading = 70.000007629395,
	objCoords = vector3(-1102.261, 4940.414, 218.5258),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- boss office
table.insert(Config.DoorList, {
	objHash = 193467871,
	objHeading = 250.00003051758,
	objCoords = vector3(-1108.429, 4939.941, 218.5198),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet
table.insert(Config.DoorList, {
	objHash = 193467871,
	objHeading = 70.000007629395,
	objCoords = vector3(-1111.949, 4939.74, 218.5198),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- meeting room
table.insert(Config.DoorList, {
	objHash = 193467871,
	objHeading = 340.0,
	objCoords = vector3(-1109.154, 4945.101, 218.5198),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- hallway to bar
table.insert(Config.DoorList, {
	objHash = 193467871,
	objHeading = 160.00003051758,
	objCoords = vector3(-1100.694, 4946.943, 218.5198),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet right
table.insert(Config.DoorList, {
	objHash = -173806003,
	objHeading = 70.000007629395,
	objCoords = vector3(-1107.025, 4953.253, 218.5541),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet left
table.insert(Config.DoorList, {
	objHash = 409744349,
	objHeading = 70.000007629395,
	objCoords = vector3(-1108.214, 4949.987, 218.5541),
	slides = false,
	garage = false,
	audioRemote = false,
	authorizedJobs = { ['altruist']=0 },
	lockpick = false,
	fixText = false,
	maxDistance = 2.0,
	locked = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})