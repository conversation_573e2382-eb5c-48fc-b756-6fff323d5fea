

-- entrance_parking_left
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1183.373, -885.5644, 13.90346),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 124.04663848877,
	fixText = false,
	slides = false,
	objHash = 1724308471,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance_parking_right
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1184.716, -883.5756, 13.90346),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 304.04663085938,
	fixText = false,
	slides = false,
	objHash = -571782594,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance_parking_employee
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1176.609, -895.5757, 13.90446),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 124.04663848877,
	fixText = false,
	slides = false,
	objHash = 1009568243,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance_street_right
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1198.777, -885.0333, 13.90346),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 34.046661376953,
	fixText = false,
	slides = false,
	objHash = -571782594,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance_street_left
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1196.788, -883.6895, 13.90346),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 214.04669189453,
	fixText = false,
	slides = false,
	objHash = 1724308471,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance_drive_thru
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1199.886, -903.0258, 13.90446),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 304.04666137695,
	fixText = false,
	slides = false,
	objHash = 1009568243,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1200.195, -901.2343, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 34.046661376953,
	fixText = false,
	slides = false,
	objHash = 846116471,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- freezer
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1193.738, -900.0775, 13.94934),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 304.04663085938,
	fixText = false,
	slides = false,
	objHash = 1309514423,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- drive_thru
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1191.715, -902.7607, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 124.04666137695,
	fixText = false,
	slides = false,
	objHash = 547885802,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- drive_thru_window
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1194.544, -905.5848, 14.3733),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 169.04664611816,
	fixText = false,
	slides = false,
	objHash = -1905927556,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- kitchen_to_lockers
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1182.503, -899.5582, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 124.04666137695,
	fixText = false,
	slides = false,
	objHash = 547885802,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- employee_toilet
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1183.268, -897.0563, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 34.046661376953,
	fixText = false,
	slides = false,
	objHash = 846116471,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- kitchen_to_recep
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1185.813, -895.4786, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 214.04667663574,
	fixText = false,
	slides = false,
	objHash = 1618088565,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- recep_to_store
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1185.497, -894.5898, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 124.04666137695,
	fixText = false,
	slides = false,
	objHash = 1618088565,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet_handicap
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1202.231, -892.0251, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 34.046661376953,
	fixText = false,
	slides = false,
	objHash = 846116471,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- toilet_men_female
table.insert(Config.DoorList, {
	locked = true,
	garage = false,
	audioRemote = false,
	lockpick = false,
	maxDistance = 2.0,
	objCoords = vector3(-1201.331, -894.7256, 13.90246),
	authorizedJobs = { ['burgershot']=0 },
	objHeading = 124.04666137695,
	fixText = false,
	slides = false,
	objHash = 846116471,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})