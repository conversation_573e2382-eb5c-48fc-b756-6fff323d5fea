

-- location 1 - door left (doorname: "v_ilev_ch_glassdoor")
table.insert(Config.DoorList, {
	objCoords = vector3(-157.1486, -306.4171, 40.00813),
	garage = false,
	fixText = false,
	authorizedJobs = { ['ponsonbys']=0 },
	locked = true,
	maxDistance = 2.0,
	objHash = -1922281023,
	audioRemote = false,
	slides = false,
	objHeading = 250.99998474121,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 1 - door right (doorname: "v_ilev_ch_glassdoor")
table.insert(Config.DoorList, {
	objCoords = vector3(-156.4593, -304.4153, 40.00813),
	garage = false,
	fixText = false,
	authorizedJobs = { ['ponsonbys']=0 },
	locked = true,
	maxDistance = 2.0,
	objHash = -1922281023,
	audioRemote = false,
	slides = false,
	objHeading = 70.999992370605,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 2 - door left (doorname: "v_ilev_ch_glassdoor")
table.insert(Config.DoorList, {
	objCoords = vector3(-716.65, -155.4166, 37.68999),
	garage = false,
	fixText = false,
	authorizedJobs = { ['ponsonbys']=0 },
	locked = true,
	maxDistance = 2.0,
	objHash = -1922281023,
	audioRemote = false,
	slides = false,
	objHeading = 119.99993896484,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 2 - door right (doorname: "v_ilev_ch_glassdoor")
table.insert(Config.DoorList, {
	objCoords = vector3(-715.5914, -157.2501, 37.68999),
	garage = false,
	fixText = false,
	authorizedJobs = { ['ponsonbys']=0 },
	locked = true,
	maxDistance = 2.0,
	objHash = -1922281023,
	audioRemote = false,
	slides = false,
	objHeading = 299.99996948242,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 3 - door left (doorname: "v_ilev_ch_glassdoor")
table.insert(Config.DoorList, {
	objCoords = vector3(-1454.771, -231.8158, 50.07159),
	garage = false,
	fixText = false,
	authorizedJobs = { ['ponsonbys']=0 },
	locked = true,
	maxDistance = 2.0,
	objHash = -1922281023,
	audioRemote = false,
	slides = false,
	objHeading = 47.995693206787,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- location 3 - door right (doorname: "v_ilev_ch_glassdoor")
table.insert(Config.DoorList, {
	objCoords = vector3(-1456.188, -233.389, 50.07158),
	garage = false,
	fixText = false,
	authorizedJobs = { ['ponsonbys']=0 },
	locked = true,
	maxDistance = 2.0,
	objHash = -1922281023,
	audioRemote = false,
	slides = false,
	objHeading = 227.99571228027,
	lockpick = false,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})