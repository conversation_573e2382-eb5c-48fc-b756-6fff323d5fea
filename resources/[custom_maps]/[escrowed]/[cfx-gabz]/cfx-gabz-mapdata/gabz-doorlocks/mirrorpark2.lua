-- ## GABZ - <PERSON><PERSON><PERSON>R PARK 2

-- ## HOUSE 1 - COORDINATES: 950.990, -654.549, 57.951
-- Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror2_1' },		
	textCoords = vector3(943.03, -652.95, 58.84),
	objCoords = vector3(943.43, -652.6096, 58.72104),
	objHeading = 130.18592834472,
	objHash = -1422530141, -- gabz_mp_house_08_1_entrancedoor_f
	maxDistance = 2.0,
	slides = false,
	audioRemote = false,
	locked = true,
	garage = false,
	lockpick = false,
	setText = true,
	-- fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror2_1' },
	textCoords = vector3(934.976, -651.5201, 58.52227),
	objCoords = vector3(934.5808, -651.854, 58.52228),
	objHeading = 130.18592834472,
	objHash = -672840959, -- gabz_mp_house_08_1_entrancedoor_b
	maxDistance = 2.0,
	slides = false,
	audioRemote = false,
	locked = true,
	garage = false,
	lockpick = false,
	setText = true,
	-- fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ## HOUSE 2 - COORDINATES: 947.997,-515.353,60.229
-- Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror2_2' },
	textCoords = vector3(945.3816, -519.2326, 60.91783),
	objCoords = vector3(945.1006, -518.7496, 60.91784),	
	objHeading = 210.05422973632,
	objHash = -1422530141, -- gabz_mp_house_08_1_entrancedoor_f
	maxDistance = 2.0,
	locked = true,
	lockpick = false,
	audioRemote = false,
	slides = false,
	garage = false,
	setText = true,
	-- fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Back Door
table.insert(Config.DoorList, {
	items = { 'keys_mirror2_2' },
	textCoords = vector3(942.53, -526.9, 60.81),
	objCoords = vector3(942.8, -527.3278, 60.71908),		
	objHeading = 210.05422973632,
	objHash = -672840959, -- gabz_mp_house_08_1_entrancedoor_b
	maxDistance = 2.0,
	locked = true,
	lockpick = false,
	audioRemote = false,
	slides = false,
	garage = false,
	setText = true,
	-- fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- ## HOUSE 3 - COORDINATES: 1216.839, -664.932, 62.853
-- Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror2_3' },
	textCoords = vector3(1221.65, -669.8226, 63.7855),
	objCoords = vector3(1221.058, -669.8226, 63.7855),
	objHeading = 282.67892456054,
	objHash = -1422530141, -- gabz_mp_house_08_1_entrancedoor_f
	maxDistance = 2.0,
	locked = true,
	lockpick = false,
	audioRemote = false,
	slides = false,
	garage = false,
	setText = true,
	-- fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Side Entry
table.insert(Config.DoorList, {
	items = { 'keys_mirror2_3' },
	textCoords = vector3(1227.9900, -674.5798, 63.58674),
	objCoords = vector3(1228.558, -674.5798, 63.58674),		
	objHeading = 282.67892456054,
	objHash = -672840959, -- gabz_mp_house_08_1_entrancedoor_b
	maxDistance = 2.0,
	locked = true,
	lockpick = false,
	audioRemote = false,
	slides = false,
	garage = false,
	setText = true,
	-- fixText = false,
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})