

-- main entrance - doorname: "gabz_paletobank_officedoors06"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = **********,
	objHeading = 135.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-110.6423, 6462.013, 31.79334),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- side entrance - doorname: "v_ilev_ct_doorl"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = **********,
	objHeading = 225.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-116.5127, 6478.96, 31.78798),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- back entrance - doorname: "v_ilev_ct_doorl"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = **********,
	objHeading = 135.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-96.70866, 6474.057, 31.78798),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- security room - doorname: "xm_prop_iaa_base_door_01"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = -*********,
	objHeading = 225.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-92.23223, 6468.96, 31.78798),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- back hallway - doorname: "xm_prop_iaa_base_door_01"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = -*********,
	objHeading = 315.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-100.1123, 6474.392, 31.78798),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- side hallway - doorname: "gabz_paletobank_officedoors03"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = -********,
	objHeading = 225.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-111.0427, 6475.328, 31.78798),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office1 - doorname: "gabz_paletobank_officedoors01b"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = **********,
	objHeading = 225.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-104.8371, 6463.774, 31.79335),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- office2 - doorname: "gabz_paletobank_officedoors01b"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = **********,
	objHeading = 45.************,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-100.6217, 6467.989, 31.79335),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- main office - doorname: "gabz_paletobank_officedoors02"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = -*********,
	objHeading = 45.************,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-104.7057, 6473.918, 31.78798),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- vault - doorname: "ch_prop_ch_vault_d_door_01a"
table.insert(Config.DoorList, {
	garage = false,
	locked = true,
	objHash = -**********,
	objHeading = 225.***********,
	fixText = false,
	authorizedJobs = { ['bank']=0 },
	slides = false,
	objCoords = vector3(-100.2419, 6464.549, 31.8846),
	lockpick = false,
	audioRemote = false,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})