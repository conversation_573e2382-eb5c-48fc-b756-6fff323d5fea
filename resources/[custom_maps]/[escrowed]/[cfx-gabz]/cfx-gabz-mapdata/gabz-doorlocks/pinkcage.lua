-- ## GABZ - PINK CAGE MOTEL
-- ## COORDINATES: 322.326, -218.825, 54.087

-- Room1
table.insert(Config.DoorList, {
	items = { 'pinkcage_room1' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(306.849, -213.6744, 54.37154),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 68.909614562988,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room2
table.insert(Config.DoorList, {
	items = { 'pinkcage_room2' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(310.6428, -203.7912, 54.37158),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 68.909614562988,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room3
table.insert(Config.DoorList, {
	items = { 'pinkcage_room3' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(315.3926, -194.1746, 54.3714),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 338.94680786132,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room4 (right side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room4' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(343.6002, -209.215, 54.37164),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 248.90963745118,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room5 (right side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room5' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(339.8062, -219.0978, 54.37208),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 248.90963745118,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room6 (right side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room6' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(347.3948, -199.3318, 54.37208),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 248.90963745118,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room7 (top left side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room7' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(315.251, -220.2672, 58.1704),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 158.94679260254,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room8 (top left side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room8' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(306.849, -213.6744, 58.16916),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 68.909614562988,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room9 (top left side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room9' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(310.6432, -203.7904, 58.1695),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 68.909614562988,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room10 (top left side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room10' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(315.3926, -194.1746, 58.16974),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 338.94680786132,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room11 (top right side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room11' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(335.34, -227.9818, 58.16958),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 158.94679260254,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room12 (top right side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room12' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(339.8062, -219.0978, 58.16852),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 248.90963745118,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room13 (top right side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room13' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(343.6002, -209.215, 58.16852),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 248.90963745118,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- Room14 (top right side)
table.insert(Config.DoorList, {
	items = { 'pinkcage_room14' },
	lockpick = false,
	audioRemote = false,
	locked = true,
	objCoords = vector3(347.3948, -199.3318, 58.16852),
	objHash = -1156992775, -- gabz_pinkcage_doors_front
	maxDistance = 2.0,
	garage = false,
	slides = false,
	objHeading = 248.90963745118,
	fixText = true,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})