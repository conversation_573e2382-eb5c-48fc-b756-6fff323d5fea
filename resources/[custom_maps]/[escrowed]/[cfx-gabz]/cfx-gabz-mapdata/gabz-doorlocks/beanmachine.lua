

-- entrance door single (doorname: "gabz_bmlegion_door_small")
table.insert(Config.DoorList, {
	garage = false,
	authorizedJobs = { ['beanmachine']=0 },
	slides = false,
	objCoords = vector3(128.2134, -1029.455, 29.2618),
	lockpick = false,
	fixText = false,
	locked = true,
	objHeading = 340.00003051758,
	audioRemote = false,
	objHash = 494354570,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance door double left (doorname: "gabz_bmlegion_door_left")
table.insert(Config.DoorList, {
	garage = false,
	authorizedJobs = { ['beanmachine']=0 },
	slides = false,
	objCoords = vector3(115.3757, -1037.655, 29.34832),
	lockpick = false,
	fixText = false,
	locked = true,
	objHeading = 69.999992370605,
	audioRemote = false,
	objHash = -747011272,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})

-- entrance door double right (doorname: "gabz_bmlegion_door_right")
table.insert(Config.DoorList, {
	garage = false,
	authorizedJobs = { ['beanmachine']=0 },
	slides = false,
	objCoords = vector3(114.5629, -1039.888, 29.34832),
	lockpick = false,
	fixText = false,
	locked = true,
	objHeading = 250.00004577637,
	audioRemote = false,
	objHash = -1182160879,
	maxDistance = 2.0,		
	-- oldMethod = true,
	-- audioLock = {['file'] = 'metal-locker.ogg', ['volume'] = 0.6},
	-- audioUnlock = {['file'] = 'metallic-creak.ogg', ['volume'] = 0.7},
	-- autoLock = 1000
})