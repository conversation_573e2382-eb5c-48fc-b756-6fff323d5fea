RegisterCommand('deleteallsounds', function(source, args, rawCommand)
    DestroyAllSounds()
    PushNotification('All sounds have been deleted')
end, false)

if not Config.Debug then
    return
end


local urlList = {
    'https://www.youtube.com/watch?v=VmoLYmAXrAk&list=RDVmoLYmAXrAk&start_radio=1',
    'https://www.youtube.com/watch?v=iqoNoU-rm14&list=RDVmoLYmAXrAk&index=2',
    'https://www.youtube.com/watch?v=ymq1WdGUcw8&list=RDVmoLYmAXrAk&index=3',
    'https://www.youtube.com/watch?v=4JHBrLXZBhw&list=RDVmoLYmAXrAk&index=4',
    'https://www.youtube.com/watch?v=YgS-Ap9K_aM&list=RDVmoLYmAXrAk&index=5',
    'https://www.youtube.com/watch?v=9qTmFnUqoLs&list=RDVmoLYmAXrAk&index=6',
    'https://www.youtube.com/watch?v=TbDyQuMJ5QI&list=RDVmoLYmAXrAk&index=7',
    'https://www.youtube.com/watch?v=OJhqsUnKUWw&list=RDVmoLYmAXrAk&index=8',
    'https://www.youtube.com/watch?v=MqyOPyBPlns&list=RDVmoLYmAXrAk&index=9',
    'https://www.youtube.com/watch?v=8Yue9YYdNLM&list=RDVmoLYmAXrAk&index=10',
    'https://www.youtube.com/watch?v=ZLohS_HScwc&list=RDVmoLYmAXrAk&index=11',
    'https://www.youtube.com/watch?v=d__uFhNmbBI&list=RDVmoLYmAXrAk&index=12',
    'https://www.youtube.com/watch?v=klTBagBYXpk&list=RDVmoLYmAXrAk&index=13',
    'https://www.youtube.com/watch?v=KrSzyJyYC5Y&list=RDVmoLYmAXrAk&index=14',
    'https://www.youtube.com/watch?v=t2Ti8d992RM&list=RDVmoLYmAXrAk&index=15',
    'https://www.youtube.com/watch?v=Q7SYKDhzu88&list=RDVmoLYmAXrAk&index=16',
    'https://www.youtube.com/watch?v=EZUPEoj3Qjs&list=RDVmoLYmAXrAk&index=17',
}

local soundCoords = {
    vec3(233.75135803222656, -1067.8619384765625, 29.21747589111328),
    vec3(280.00286865234375, -1033.23828125, 29.28166007995605),
    vec3(322.5904541015625, -1068.127197265625, 29.31263542175293),
    vec3(358.3526611328125, -1032.498291015625, 29.34980583190918),
    vec3(406.33056640625, -1068.867919921875, 29.38520622253418),
    vec3(414.8170166015625, -990.7307739257812, 29.40600395202636),
    vec3(415.30609130859375, -890.2222290039062, 29.42370414733886),
    vec3(416.7891845703125, -833.3514404296875, 29.43476676940918),
    vec3(411.73822021484375, -707.4351196289062, 29.45202445983886),
    vec3(438.9271240234375, -592.4657592773438, 29.49538993835449),
    vec3(462.9969482421875, -518.3805541992188, 29.52902030944824),
    vec3(148.8815155029297, -580.4983520507812, 43.81758117675781),
    vec3(43.68973541259765, -697.158935546875, 45.72592163085937),
    vec3(-52.32830429077148, -722.502197265625, 44.26382446289062),
    vec3(-225.50965881347656, -653.652587890625, 34.06594848632812),
    vec3(-333.009521484375, -673.5787353515625, 33.02227020263672),
    vec3(-598.0825805664062, -644.6072387695312, 35.54351806640625),
    vec3(-726.0289916992188, -671.3406982421875, 31.04931640625),
    vec3(-657.6828002929688, -821.67333984375, 27.27066993713379),
    vec3(-481.8772583007813, -1081.4578857421875, 26.50606155395507),
    vec3(-297.74822998046875, -1155.1435546875, 25.17542457580566),
    vec3(-74.04403686523438, -1111.3751220703125, 27.33841705322265),
    vec3(-32.33475875854492, -993.5839233398438, 30.21953392028808),
    vec3(315.8694152832031, -233.6157379150391, 54.01786041259765),
    vec3(190.3390350341797, -192.5137481689453, 54.63930130004883),
    vec3(43.94049835205078, -133.5149078369141, 56.95316696166992),
    vec3(-90.47498321533205, -85.56704711914062, 57.54666900634765),
    vec3(-225.94717407226565, -38.87411117553711, 50.63346862792969),
    vec3(-375.85357666015625, 11.72151660919189, 47.68080902099609),
    vec3(-494.9742126464844, 10.58783054351806, 45.85217666625976)
}

Debug('Initialized test commands')

local fakeSongCount = 50

local soundCoords = {
    vec3(233.75135803222656, -1067.8619384765625, 29.21747589111328),
    vec3(280.00286865234375, -1033.23828125, 29.28166007995605),
    vec3(322.5904541015625, -1068.127197265625, 29.31263542175293),
    vec3(358.3526611328125, -1032.498291015625, 29.34980583190918),
    vec3(406.33056640625, -1068.867919921875, 29.38520622253418),
    vec3(414.8170166015625, -990.7307739257812, 29.40600395202636),
    vec3(415.30609130859375, -890.2222290039062, 29.42370414733886),
    vec3(416.7891845703125, -833.3514404296875, 29.43476676940918),
    vec3(411.73822021484375, -707.4351196289062, 29.45202445983886),
    vec3(438.9271240234375, -592.4657592773438, 29.49538993835449),
    vec3(462.9969482421875, -518.3805541992188, 29.52902030944824),
    vec3(148.8815155029297, -580.4983520507812, 43.81758117675781),
    vec3(43.68973541259765, -697.158935546875, 45.72592163085937),
    vec3(-52.32830429077148, -722.502197265625, 44.26382446289062),
    vec3(-225.50965881347656, -653.652587890625, 34.06594848632812),
    vec3(-333.009521484375, -673.5787353515625, 33.02227020263672),
    vec3(-598.0825805664062, -644.6072387695312, 35.54351806640625),
    vec3(-726.0289916992188, -671.3406982421875, 31.04931640625),
    vec3(-657.6828002929688, -821.67333984375, 27.27066993713379),
    vec3(-481.8772583007813, -1081.4578857421875, 26.50606155395507),
    vec3(-297.74822998046875, -1155.1435546875, 25.17542457580566),
    vec3(-74.04403686523438, -1111.3751220703125, 27.33841705322265),
    vec3(-32.33475875854492, -993.5839233398438, 30.21953392028808),
    vec3(315.8694152832031, -233.6157379150391, 54.01786041259765),
    vec3(190.3390350341797, -192.5137481689453, 54.63930130004883),
    vec3(43.94049835205078, -133.5149078369141, 56.95316696166992),
    vec3(-90.47498321533205, -85.56704711914062, 57.54666900634765),
    vec3(-225.94717407226565, -38.87411117553711, 50.63346862792969),
    vec3(-375.85357666015625, 11.72151660919189, 47.68080902099609),
    vec3(-494.9742126464844, 10.58783054351806, 45.85217666625976)
}

local function showAreas()
    CreateThread(function()
        while true do
            for k, v in pairs(soundCoords) do
                DrawMarker(1, v.x, v.y, v.z, 0, 0, 0, 0, 0, 0, 1.0, 1.0, 1.0, 255, 0, 0, 200, false, false, 2, nil, nil, false)
            end
            Wait(0)
        end
    end)
end

RegisterCommand('playAreas', function(source, args)
    local coords = GetEntityCoords(PlayerPedId())
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    local panner = {
        panningModel = 'HRTF',
        refDistance = 15.0,
        rolloffFactor = 0.01,
        distanceModel = 'exponential',
        coneInnerAngle = 360.0,
        coneOuterAngle = 0.0,
    }

    for k, v in pairs(soundCoords) do
        CreateThread(function()
            local soundId = exports['mx-surround']:createUniqueId()
            exports['mx-surround']:Play(soundId, args[1], v, true, nil, panner)
        end)
    end
    showAreas()
end, false)

RegisterCommand('playcl', function(source, args)
    local coords = GetEntityCoords(PlayerPedId())
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    local panner = {
        panningModel = 'HRTF',
        refDistance = 1.2,
        rolloffFactor = 1.0,
        distanceModel = 'exponential',
        coneInnerAngle = 360.0,
        coneOuterAngle = 0.0,
    }
    TriggerServerEvent('InteractSound_SV:PlayOnSource', 'beltalarm', 1.0)
    local soundId = exports['mx-surround']:createUniqueId()
    exports['mx-surround']:Play(soundId, args[1], coords, true, nil)
    local playerId = GetPlayerServerId(PlayerId())
    exports['mx-surround']:attachPlayer(soundId, playerId)
    if vehicle ~= 0 then
        local networkId = VehToNet(vehicle)
        -- local ped = PlayerPedId()
        -- local networkId = PedToNet(ped)
        AttachEntity(soundId, networkId)
    end
end, false)

RegisterCommand('playsvev', function(source, args)
    local coords = GetEntityCoords(PlayerPedId())
    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    local panner = {
        panningModel = 'HRTF',
        refDistance = 1.2,
        rolloffFactor = 1.0,
        distanceModel = 'exponential',
        coneInnerAngle = 360.0,
        coneOuterAngle = 0.0,
    }
    local soundId = exports['mx-surround']:createUniqueId()
    TriggerServerEvent('mx-surround:playsv', -1, soundId, args[1], coords)
    exports['mx-surround']:Play(soundId, args[1], coords, true, nil)
    local playerId = GetPlayerServerId(PlayerId())
    exports['mx-surround']:attachPlayer(soundId, playerId)
    if vehicle ~= 0 then
        local networkId = VehToNet(vehicle)
        local ped = PlayerPedId()
        local networkId = PedToNet(ped)
        AttachEntity(soundId, networkId)
    end
end, false)

RegisterCommand('fltr', function(source, args)
    for k, v in pairs(Sounds) do
        local type = args[1]
        local freq = tonumber(args[2])
        local q = tonumber(args[3])
        local gain = tonumber(args[4])
        exports['mx-surround']:addFilter(k, 'fltrtest', {
            type = type,
            frequency = freq,
            Q = q,
            gain = gain
        })
    end
end, false)
TriggerEvent('chat:addSuggestion', '/fltr', 'Test the filter', {
    { name = 'type',      help = 'lowpass, highpass, bandpass, lowshelf, highshelf, peaking, notch, allpass' },
    { name = 'frequency', help = 'Frequency (Hz)' },
    { name = 'Q',         help = 'Quality factor (float >= 0 and < 1000)' },
    { name = 'gain',      help = 'Gain (float >= -40 and <= 40) (dB)' }
})

RegisterCommand('clearfltr', function(source, args)
    for k, v in pairs(Sounds) do
        if v.filter then
            exports['mx-surround']:removeFilter(k)
        end
    end
end, false)

-- Example Usage: /testpan HRTF 1.0 4.0 exponential
RegisterCommand('testpan', function(source, args)
    local panningModel = args[1]
    local refDistance = tonumber(args[2])
    local rolloffFactor = tonumber(args[3])
    local distanceModel = args[4]
    local panner = { panningModel = panningModel, refDistance = refDistance, rolloffFactor = rolloffFactor, distanceModel = distanceModel }
    for soundId, soundData in pairs(Sounds) do
        SetPanner(soundId, panner)
    end
end, false)
TriggerEvent('chat:addSuggestion', '/testpan', 'Test the panner', {
    { name = 'panningModel',  help = 'HRTF, equalpower,' },
    { name = 'refDistance',   help = 'Distance of the volume dropoff start' },
    { name = 'rolloffFactor', help = 'How fast the volume drops off' },
    { name = 'distanceModel', help = 'linear, inverse, or exponential' }
})

RegisterCommand('toggleAttach', function(source, args)
    for soundId, soundData in pairs(Sounds) do
        if soundData.netId then
            DetachEntity(soundId)
        else
            local vehicle = GetVehiclePedIsIn(PlayerPedId(), true)
            if vehicle ~= 0 then
                local networkId = VehToNet(vehicle)
                AttachEntity(soundId, networkId)
            end
        end
    end
end, false)

RegisterCommand('create_ped_inside_vehicle', function(source, args)
    local ped = PlayerPedId()
    local model = joaat('adder')
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    local vehicle = CreateVehicle(model, 116.87315368652344, -1318.4442138671875, 28.99452781677246, 292.1488952636719, true, false)
    local coords = GetEntityCoords(vehicle)
    local model = joaat('a_m_y_business_01')
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
    local ped = CreatePedInsideVehicle(vehicle, 26, model, -1, true, false)
    Debug('Ped', ped)
    SetEntityAsMissionEntity(ped, true, true)
    SetPedIntoVehicle(ped, vehicle, -1)
    SetModelAsNoLongerNeeded(model)
    local url = math.random(1, #urlList)
    local soundId = exports['mx-surround']:createUniqueId()
    local coords = GetEntityCoords(ped)
    exports['mx-surround']:Play(soundId, urlList[url], coords, true)
    local networkId = PedToNet(ped)
    AttachEntity(soundId, networkId)
end, false)

Debug('Initialized limit command')
local imitor = false
RegisterCommand('limit', function(source, args)
    local speedlimit = tonumber(args[1])
    local entity = InVehicle
    if not entity then return end
    imitor = not imitor
    while imitor do
        SetVehicleForwardSpeed(entity, speedlimit)
        Wait(0)
    end
end, false)

if GetResourceState('ox_target'):find('start') then
    Debug('Registering ox_target commands')
    exports.ox_target:addGlobalVehicle({
        {
            name = 'surround:play',
            icon = 'fa-solid fa-play',
            label = 'Play random sound on entity',
            distance = 10,
            onSelect = function(data)
                -- check is networked
                local entity = data.entity
                if not entity then return end
                if not NetworkGetEntityIsNetworked(entity) then return end
                local networkId = VehToNet(entity)
                TriggerServerEvent('mx-surround:debug:playRandomSoundOnEntity', networkId)
            end
        }
    })

    exports.ox_target:addGlobalPed({
        {
            name = 'surround:play',
            icon = 'fa-solid fa-play',
            label = 'Play random sound on ped',
            distance = 10,
            onSelect = function(data)
                -- check is networked
                local entity = data.entity
                if not entity then return end
                if not NetworkGetEntityIsNetworked(entity) then return end
                local networkId = PedToNet(entity)
                TriggerServerEvent('mx-surround:debug:playRandomSoundOnEntity', networkId)
            end
        }
    })
end

local isTracing = false
local function tracing()
    CreateThread(function()
        while isTracing do
            Wait(0)
            local pedCoords = GetEntityCoords(PlayerPedId())
            local playingSounds = table.concat(table.map(table.deep_clone(Sounds), function(v, k)
                if v.playing then
                    return k
                end
            end), ', ')
            DrawGenericText('Playing sounds: ' .. playingSounds, 0.40, 0.00)
            for k, v in pairs(Sounds) do
                if v.coords then
                    local dst = #(pedCoords - v.coords)
                    local color = v.playing and { 0, 255, 0 } or { 255, 0, 0 }
                    DrawMarker(28, v.coords.x, v.coords.y, v.coords.z, 0, 0, 0, 0, 0, 0, 1.0, 1.0, 1.0, color[1], color[2], color[3], 200, false, false, 2, nil, nil, false)
                    DrawLine(pedCoords.x, pedCoords.y, pedCoords.z, v.coords.x, v.coords.y, v.coords.z, color[1], color[2], color[3], 255)
                    if dst <= 15 then
                        local text0 = ([[
                            ID %s |
                            ]]):format(k)
                        DrawText3D(v.coords.x, v.coords.y, v.coords.z + 0.10, text0)
                        local text = ([[
                            Distance %s | Max Distance %s | Volume %s | Playing %s |
                            ]]):format(dst, v.maxDistance, v.volume, v.playing)
                        DrawText3D(v.coords.x, v.coords.y, v.coords.z + 0.8, text)
                        local text2 = ([[
                                Filter %s | Stereo %s | Dynamic %s | Loaded %s |
                            ]]):format(v.filter, v.stereo, v.dynamic, v.loaded)
                        DrawText3D(v.coords.x, v.coords.y, v.coords.z + 0.6, text2)
                        local text3 = ([[
                                Controlled %s | Sync %s | NetId %s | EntityType %s |
                            ]]):format(v.controlled, v.sync, v.netId, v.entityType)
                        DrawText3D(v.coords.x, v.coords.y, v.coords.z + 0.4, text3)
                        local text4 = ([[
                                IsVehicle %s | Player %s | InteriorStereo %s | Filter %s |
                            ]]):format(v.isVehicle, v.player and v.player.serverId or 'nil', v.interiorStereo, v.filter)
                        DrawText3D(v.coords.x, v.coords.y, v.coords.z + 0.2, text4)
                        local text5 = ([[
                                Destroying %s | DestroyOnFinish %s | ForcedToPlay %s | Loop %s | Panner %s
                            ]]):format(v.destroying, v.destroyOnFinish, v.forcedToPlay, v.loop, json.encode(v.panner))
                        DrawText3D(v.coords.x, v.coords.y, v.coords.z, text5)
                    end
                end
            end
        end
    end)
end
tracing()

function DrawGenericText(text, x, y, z)
    SetTextColour(186, 186, 186, 255)
    SetTextFont(4)
    SetTextScale(0.5, 0.5)
    SetTextWrap(0.0, 1.0)
    SetTextCentre(false)
    SetTextDropshadow(0, 0, 0, 0, 255)
    SetTextEdge(1, 0, 0, 0, 205)
    SetTextEntry('STRING')
    AddTextComponentString(text)
    DrawText(0.40, 0.00)
end

Debug('Initialized surround:tracing command')
RegisterCommand('surround:tracing', function(source, args, raw)
    isTracing = not isTracing
    tracing()
end)
