(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))i(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const h of o.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&i(h)}).observe(document,{childList:!0,subtree:!0});function e(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(n){if(n.ep)return;n.ep=!0;const o=e(n);fetch(n.href,o)}})();const et=()=>!window.invokeNative;let R=!1;const it=s=>{R=s},d=(...s)=>{R&&console.log("^5[Surround UI]",...s,"^0")},st=s=>new Promise(t=>setTimeout(t,s)),nt=window.GetParentResourceName?window.GetParentResourceName():"mx-surround";async function f(s,t){if(!et())try{return await(await fetch(`https://${nt}/${s}`,{method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(t)})).json()}catch(e){throw Error(`Failed to fetch NUI callback ${s}! (${e})`)}}const ot=/^https?:\/\//,k=/^[a-zA-Z0-9-_]{11}$/,M=new Set(["youtube.com","www.youtube.com","m.youtube.com","music.youtube.com","gaming.youtube.com"]),at=/^https?:\/\/(youtu\.be\/|(www\.)?youtube\.com\/(embed|v|shorts|live)\/)/;function rt(s){return k.test(s.trim())}function dt(s){const t=new URL(s.trim());let e=t.searchParams.get("v");if(at.test(s.trim())&&!e){const i=t.pathname.split("/");e=t.host==="youtu.be"?i[1]:i[2]}else if(t.hostname&&!M.has(t.hostname))throw new Error("Not a YouTube domain");if(!e)throw new Error(`No video id found: "${s}"`);if(e=e.substring(0,11),!rt(e))throw new TypeError(`Video id (${e}) does not match expected format (${k.toString()})`);return e}function ut(s){if(!ot.test(s.trim()))return!1;const t=new URL(s.trim());return M.has(t.hostname)}class lt{posX;posY;posZ;volume;panner;volumeGain;filters;filterNode=null;constructor(t,e,i,n,o,h){this.posX=t,this.posY=e,this.posZ=i,this.volume=n,this.panner=o,this.volumeGain=h,this.filters=[],this.panner.positionX.value=this.posX,this.panner.positionY.value=this.posY,this.panner.positionZ.value=this.posZ}update(t,e,i,n){this.posX=t,this.posY=e,this.posZ=i,this.volume=n}addFilter(t,e){this.filterNode=t;const i=this.filters.length;if(i===0)e.disconnect(this.panner),e.connect(this.filterNode),this.filterNode.connect(this.panner);else{const n=this.filters[i-1];n.disconnect(),n.connect(this.filterNode),this.filterNode.connect(this.panner)}this.filters.push(this.filterNode)}removeFilter(t){if(!this.filterNode)return console.error("No filter node to remove");const e=t,i=this.panner;e.disconnect(this.filterNode),this.filterNode.disconnect(),e.connect(i),this.filters=[],this.filterNode=null}getFirstChainNode(){return this.filters[0]??this.panner}getLastChainNode(){return this.filters.length>0?this.filters[this.filters.length-1]:this.panner}cleanup(){this.panner?.disconnect(),this.volumeGain?.disconnect(),this.filters?.forEach(t=>t.disconnect()),this.filters.length=0}}const ht=1e3*60*1;class ct{sound=null;player=null;youtubeId=null;track=null;filterFadeDuration=.3;debug=!1;soundCreated=null;chain=null;filter=null;audioCtx=null;soundId;maxDuration=null;url;data;started=0;playing=!0;stopped=!1;paused=!1;loop=!1;stereo=!1;isYoutubeUrl=!1;playerCreated=!1;onPlayerDestroy=()=>{};initialized=!1;constructor(t,e,i,n,o){i&&(this.audioCtx=i),this.soundId=e,this.debug=o.debug,this.data=n,this.url=t,this.isYoutubeUrl=ut(t),this.isYoutubeUrl&&(this.youtubeId=dt(t)||""),this.playing=this.data.play,n.coords&&this.setCoords(n.coords),this.playing?this.load():this.triggerLoaded()}async getPlayerCoords(){if(!this.audioCtx)return{x:0,y:0,z:0};const t={x:this.audioCtx.listener.positionX.value,y:this.audioCtx.listener.positionY.value,z:this.audioCtx.listener.positionZ.value};return t.x===0&&t.y===0&&t.z===0?await f("getPlayerCoords"):t}async loadYTPlayer(){const t=document.getElementById("app"),e=document.createElement("div");if(e.classList.add("sound"),e.id=this.soundId,t?.appendChild(e),!this.youtubeId){f("soundError",{soundId:this.soundId,msg:"Youtube ID is not valid"}),console.error("Youtube ID is not valid. Sound failed to play.");return}this.playerCreated=!0,this.player=new YT.Player(this.soundId,{width:"0",height:"0",videoId:this.youtubeId,playerVars:{autoplay:0,controls:0},events:{onReady:i=>{i.target.seekTo(0,!0),i.target.mute(),this.maxDuration=i.target.getDuration()},onStateChange:i=>{i.data==YT.PlayerState.PLAYING&&!this.sound&&(this.Debug("Youtube player is playing. Sound will be connected..."),this.sound=document.getElementById(this.soundId)?.contentWindow.document.querySelector("video"),this.track=new MediaElementAudioSourceNode(this.audioCtx,{mediaElement:this.sound}),this.player?.seekTo(this.data.seek??0,!0),this.connect(),this.player?.unMute(),this.onLoadMetadata()),i.data==YT.PlayerState.ENDED&&this.onEnded()},onError:()=>{f("soundError",{soundId:this.soundId,msg:"Youtube player failed to load."}),console.error("Youtube player failed to load.")}}}),this.Debug("Youtube player is created.")}async load(t){if(t&&this.data.sync){const e=await f("getTimestamp",{soundId:this.soundId});this.data.seek=e,this.Debug("Synced with server. Seek:",e)}this.isYoutubeUrl?this.loadYTPlayer():(this.playerCreated=!0,this.sound=new Audio(this.url),this.track=new MediaElementAudioSourceNode(this.audioCtx,{mediaElement:this.sound}),this.sound.onloadedmetadata=()=>{this.maxDuration=this.sound?.duration||0,this.seek(this.data.seek||0),this.connect(),this.onLoadMetadata()},this.sound.onended=()=>this.onEnded(),this.sound.onerror=()=>{f("soundError",{soundId:this.soundId,msg:"Sound failed to load."})}),this.loop=this.data.loop}async destroyPlayer(){this.Debug("Player is destroyed."),this.data.seek=this.getCurrentTime(),this.soundCreated=null,this.isYoutubeUrl?this.player?.destroy():this.sound&&(this.sound.pause(),this.sound.src=""),this.player=null,this.playerCreated=!1,this.onPlayerDestroy(),this.sound=null,this.track=null,this.playing=!1,this.disconnect(),document.getElementById(this.soundId)?.remove()}async play(){const t=await this.getPlayerCoords();this.data.dynamic||this.setCoords(t);const e=Date.now();for(;!this.chain||!this.chain.panner||this.chain.panner.positionX.value===0&&this.chain.panner.positionY.value===0&&this.chain.panner.positionZ.value===0&&Date.now()-e<5e3;)await st(100);if(this.chain.panner.positionX.value===0&&this.chain.panner.positionY.value===0&&this.chain.panner.positionZ.value===0)return this.Debug("Panner position is not set. Play is passing.");if(this.data.stereo&&this.audioCtx){const i=this.audioCtx.listener.positionX.value===0?t:{x:this.audioCtx.listener.positionX.value,y:this.audioCtx.listener.positionY.value,z:this.audioCtx.listener.positionZ.value};this.setCoords(i)}if(this.playing)return this.Debug("Sound is already playing. Play is passing.");this.playing=!0,this.stopped=!1,this.paused=!1}async resume(){this.Debug("Resuming sound. Current Time:",this.getCurrentTime()),this.playing=!0,this.stopped=!1,this.paused=!1}seek(t){this.maxDuration&&t>this.maxDuration&&(t=this.maxDuration),this.Debug("Seeking to:",t),this.isYoutubeUrl?this.player?.seekTo(t,!0):this.sound&&(this.sound.currentTime=t)}destroy(){f("soundDestroyed",{soundId:this.soundId}),this.destroyPlayer(),this.Debug("Sound is destroyed.")}connect(){if(!this.track)return this.Debug("connectSpatial is passing. Because song is not loaded yet.");const t={x:this.data.coords?.x||0,y:this.data.coords?.y||0,z:this.data.coords?.z||0};this.chain=this.createAudioOutput(t.x,t.y,t.z,this.data.volume)}clearFilter(){this.filter=null}createFilter(t){this.updateFilterParams(t.frequency,t.quality,t.gain,this.filterFadeDuration)}createBiquadFilter(t){const e=this.audioCtx.createBiquadFilter();return e.type=t.type,t.frequency&&(e.frequency.value=t.frequency),t.quality&&(e.Q.value=t.quality),t.gain&&(e.gain.value=t.gain),t.detune&&(e.detune.value=t.detune),e}createAudioOutput(t,e,i,n){this.Debug("createAudioOutput Initialized");const o=this.audioCtx.createGain();o.gain.value=n;const h=this.audioCtx.createPanner();this.setPanner(this.data.panner,h);const u=new lt(t,e,i,n,h,o),g=u.getLastChainNode(),v=u.getFirstChainNode();return this.track?.connect(v),g!==u.panner&&g.connect(u.panner),u.panner.connect(u.volumeGain),u.volumeGain.connect(this.audioCtx.destination),u.panner.positionX.value=t,u.panner.positionY.value=e,u.panner.positionZ.value=i,u}updateFilterParams(t,e,i,n){if(this.filter={type:"lowpass",frequency:t,quality:e,gain:i},!this.chain)return this.Debug("updateFilterParams :: Chain is not created yet.");if(!this.chain.filterNode){const h=this.createBiquadFilter(this.filter);this.chain?.addFilter(h,this.track);return}const o=this.audioCtx.currentTime;this.chain.filterNode.frequency.cancelAndHoldAtTime(o),this.chain.filterNode.frequency.setValueAtTime(t,o),this.chain.filterNode.frequency.linearRampToValueAtTime(t,o+n),this.chain.filterNode.Q.cancelAndHoldAtTime(o),this.chain.filterNode.Q.setValueAtTime(e,o),this.chain.filterNode.Q.linearRampToValueAtTime(e,o+n),this.chain.filterNode.gain.cancelAndHoldAtTime(o),this.chain.filterNode.gain.setValueAtTime(i,o),this.chain.filterNode.gain.linearRampToValueAtTime(i,o+n)}onLoadMetadata(){this.started=Date.now(),this.soundCreated=Date.now(),this.triggerLoaded()}triggerLoaded(){f("soundLoaded",{soundId:this.soundId,startedTime:Date.now(),maxDuration:this.maxDuration}),this.initialized=!0}onEnded(){if(this.loop){this.seek(0);return}this.Debug("Sound ended. Sound ID:",this.soundId),f("soundEnded",{soundId:this.soundId})}playPlayer(){this.isYoutubeUrl?this.player?.playVideo():this.sound&&this.sound.play()}stopPlayer(){this.isYoutubeUrl?this.player?.pauseVideo():this.sound&&this.sound.pause()}async pause(){this.paused=Date.now(),this.playing=!1,this.Debug("Sound is paused.")}async stop(){this.stopped=!0,this.playing=!1,this.Debug("Sound is stopped.")}async tick(t){if(this.initialized){if(t&&(this.audioCtx=t),this.chain&&this.soundCreated)if(this.chain.volumeGain.gain.value!==this.data.volume&&this.chain.volumeGain.gain.linearRampToValueAtTime(this.data.volume,this.audioCtx.currentTime),this.filter&&!this.chain.filterNode){const e=this.createBiquadFilter(this.filter);this.chain.addFilter(e,this.track)}else!this.filter&&this.chain.filterNode&&this.chain.removeFilter(this.track);await this.handlePlayer()}}isPlayerPlaying(){return this.isYoutubeUrl?this.player?.getPlayerState?.()===YT.PlayerState.PLAYING:!this.sound?.paused}async handlePlayer(){if(!this.playerCreated&&this.playing){if(this.Debug("handlePlayer ::: player created is false but playing is true. Loading the sound."),this.audioCtx?.state==="closed")return this.Debug("handlePlayer ::: AudioContext is not created yet.");this.playerCreated=!0,await this.load(!0)}if(!this.soundCreated)return;const t=this.isPlayerPlaying();this.playing&&!t?(this.Debug("handlePlay::: Playing variable is true but there is no sound. Playing the sound"),this.playPlayer()):this.stopped?(this.Debug("handlePlay::: Stop variable is true. But there is a sound. Destroying the player."),this.destroyPlayer()):this.paused&&t?(this.Debug("handlePlay::: Pause variable is true. But there is a sound. Stopping the player."),this.stopPlayer()):this.paused&&this.paused+ht<Date.now()&&(this.Debug("handlePlay::: Pause variable is true for too long. We are destroying the player for releasing the memory."),this.destroyPlayer())}setCoords(t){if(t&&!(t?.x===this.data.coords?.x&&t?.y===this.data?.coords?.y&&t?.z===this.data?.coords?.z)){if(this.data.coords=t,!this.chain||!this.chain.panner)return this.Debug("setCoords :: Chain is not created yet.");if(!this.audioCtx)return this.Debug("setCoords :: AudioContext is not created yet.");this.chain.panner.positionX.linearRampToValueAtTime(t.x,this.audioCtx.currentTime),this.chain.panner.positionY.linearRampToValueAtTime(t.y,this.audioCtx.currentTime),this.chain.panner.positionZ.linearRampToValueAtTime(t.z,this.audioCtx.currentTime)}}getCoords(){return!this.chain||!this.chain.panner?{x:0,y:0,z:0}:{x:this.chain.panner.positionX.value,y:this.chain.panner.positionY.value,z:this.chain.panner.positionZ.value}}setLoop(t){this.loop=t,this.data.loop=t}setPanner(t,e=null){if(e=e??this.chain?.panner,!e)return this.Debug("setPanner ::: Chain is not created yet.");const i=this.data.stereo?"equalpower":t.panningModel;return i!==t.panningModel&&this.Debug("setPanner ::: Forced panningModel to equalpower because it is stereo."),e.panningModel=i,e.refDistance=t.refDistance,e.rolloffFactor=t.rolloffFactor,e.distanceModel=t.distanceModel,this.data.panner=t,this.data.panner}disconnect(){this.chain&&(this.chain?.cleanup(),this.chain=null)}async setStereo(t){if(t&&!this.soundCreated&&this.playing&&this.Debug("Sound was set to stereo. We force to load the sound."),this.data.stereo=t,!this.chain||!this.chain.panner)return this.Debug("setStereo ::: Chain is not created yet.");this.chain.panner.panningModel=t?"equalpower":this.data.panner.panningModel,this.Debug("setStereo ::: Stereo is set to:",t)}setMaxDistance(t){if(this.data.maxDistance=t,!this.chain||!this.chain.panner)return this.Debug("setMaxDistance ::: Chain is not created yet.");this.chain.panner.maxDistance=t}setVolume(t){this.Debug("setVolume :: Volume:",t),this.chain&&this.audioCtx?this.chain.volumeGain.gain.linearRampToValueAtTime(t,this.audioCtx.currentTime):this.Debug("setVolume :: Chain is not created yet."),this.data.volume=t}getCurrentTime(){return this.isYoutubeUrl?this.player?this.player?.getCurrentTime?.()||0:this.data.seek:this.sound?this.sound.currentTime:0}Debug(...t){this.debug&&console.log("^5[Surround UI] Sound ID: "+this.soundId,...t,"^0")}}function ft(s,t){if(!(s instanceof t))throw new TypeError("Cannot call a class as a function")}function pt(s,t){for(var e=0;e<t.length;e++){var i=t[e];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(s,i.key,i)}}function yt(s,t,e){return t&&pt(s.prototype,t),s}var mt=Object.defineProperty,p=function(s,t){return mt(s,"name",{value:t,configurable:!0})},It=`<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">\r
  <path d="m8.94 8 4.2-4.193a.67.67 0 0 0-.947-.947L8 7.06l-4.193-4.2a.67.67 0 1 0-.947.947L7.06 8l-4.2 4.193a.667.667 0 0 0 .217 1.093.666.666 0 0 0 .73-.146L8 8.94l4.193 4.2a.666.666 0 0 0 1.094-.217.665.665 0 0 0-.147-.73L8.94 8Z" fill="currentColor"/>\r
</svg>\r
`,gt=`<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r
  <path d="M16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24A10.667 10.667 0 0 1 5.333 16a10.56 10.56 0 0 1 2.254-6.533l14.946 14.946A10.56 10.56 0 0 1 16 26.667Zm8.413-4.134L9.467 7.587A10.56 10.56 0 0 1 16 5.333 10.667 10.667 0 0 1 26.667 16a10.56 10.56 0 0 1-2.254 6.533Z" fill="currentColor"/>\r
</svg>\r
`,vt=`<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r
  <path d="M16 14.667A1.333 1.333 0 0 0 14.667 16v5.333a1.333 1.333 0 0 0 2.666 0V16A1.333 1.333 0 0 0 16 14.667Zm.507-5.227a1.333 1.333 0 0 0-1.014 0 1.334 1.334 0 0 0-.44.28 1.56 1.56 0 0 0-.28.44c-.075.158-.11.332-.106.507a1.332 1.332 0 0 0 .386.946c.13.118.279.213.44.28a1.334 1.334 0 0 0 1.84-1.226 1.4 1.4 0 0 0-.386-.947 1.334 1.334 0 0 0-.44-.28ZM16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24a10.666 10.666 0 1 1 0-21.333 10.666 10.666 0 0 1 0 21.333Z" fill="currentColor"/>\r
</svg>\r
`,Tt=`<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r
  <path d="m19.627 11.72-5.72 5.733-2.2-2.2a1.334 1.334 0 1 0-1.88 1.881l3.133 3.146a1.333 1.333 0 0 0 1.88 0l6.667-6.667a1.333 1.333 0 1 0-1.88-1.893ZM16 2.667a13.333 13.333 0 1 0 0 26.666 13.333 13.333 0 0 0 0-26.666Zm0 24a10.666 10.666 0 1 1 0-21.333 10.666 10.666 0 0 1 0 21.333Z" fill="currentColor"/>\r
</svg>\r
`,Ct=`<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">\r
  <path d="M16.334 17.667a1.334 1.334 0 0 0 1.334-1.333v-5.333a1.333 1.333 0 0 0-2.665 0v5.333a1.333 1.333 0 0 0 1.33 1.333Zm-.508 5.227c.325.134.69.134 1.014 0 .165-.064.314-.159.44-.28a1.56 1.56 0 0 0 .28-.44c.076-.158.112-.332.107-.507a1.332 1.332 0 0 0-.387-.946 1.532 1.532 0 0 0-.44-.28 1.334 1.334 0 0 0-1.838 1.226 1.4 1.4 0 0 0 .385.947c.127.121.277.216.44.28Zm.508 6.773a13.333 13.333 0 1 0 0-26.667 13.333 13.333 0 0 0 0 26.667Zm0-24A10.667 10.667 0 1 1 16.54 27a10.667 10.667 0 0 1-.206-21.333Z" fill="currentColor"/>\r
</svg>\r
`,Nt=p(function(s){return new DOMParser().parseFromString(s,"text/html").body.childNodes[0]},"stringToHTML"),m=p(function(s){var t=new DOMParser().parseFromString(s,"application/xml");return document.importNode(t.documentElement,!0).outerHTML},"getSvgNode"),a={CONTAINER:"sn-notifications-container",NOTIFY:"sn-notify",NOTIFY_CONTENT:"sn-notify-content",NOTIFY_ICON:"sn-notify-icon",NOTIFY_CLOSE:"sn-notify-close",NOTIFY_TITLE:"sn-notify-title",NOTIFY_TEXT:"sn-notify-text",IS_X_CENTER:"sn-is-x-center",IS_Y_CENTER:"sn-is-y-center",IS_CENTER:"sn-is-center",IS_LEFT:"sn-is-left",IS_RIGHT:"sn-is-right",IS_TOP:"sn-is-top",IS_BOTTOM:"sn-is-bottom",NOTIFY_OUTLINE:"sn-notify-outline",NOTIFY_FILLED:"sn-notify-filled",NOTIFY_ERROR:"sn-notify-error",NOTIFY_WARNING:"sn-notify-warning",NOTIFY_SUCCESS:"sn-notify-success",NOTIFY_INFO:"sn-notify-info",NOTIFY_FADE:"sn-notify-fade",NOTIFY_FADE_IN:"sn-notify-fade-in",NOTIFY_SLIDE:"sn-notify-slide",NOTIFY_SLIDE_IN:"sn-notify-slide-in",NOTIFY_AUTOCLOSE:"sn-notify-autoclose"},y={ERROR:"error",WARNING:"warning",SUCCESS:"success",INFO:"info"},P={OUTLINE:"outline",FILLED:"filled"},T={FADE:"fade",SLIDE:"slide"},I={CLOSE:m(It),SUCCESS:m(Tt),ERROR:m(gt),WARNING:m(Ct),INFO:m(vt)},Y=p(function(s){s.wrapper.classList.add(a.NOTIFY_FADE),setTimeout(function(){s.wrapper.classList.add(a.NOTIFY_FADE_IN)},100)},"fadeIn"),A=p(function(s){s.wrapper.classList.remove(a.NOTIFY_FADE_IN),setTimeout(function(){s.wrapper.remove()},s.speed)},"fadeOut"),wt=p(function(s){s.wrapper.classList.add(a.NOTIFY_SLIDE),setTimeout(function(){s.wrapper.classList.add(a.NOTIFY_SLIDE_IN)},100)},"slideIn"),Et=p(function(s){s.wrapper.classList.remove(a.NOTIFY_SLIDE_IN),setTimeout(function(){s.wrapper.remove()},s.speed)},"slideOut"),U=function(){function s(t){var e=this;ft(this,s),this.notifyOut=p(function(tt){tt(e)},"notifyOut");var i=t.notificationsGap,n=i===void 0?20:i,o=t.notificationsPadding,h=o===void 0?20:o,u=t.status,g=u===void 0?"success":u,v=t.effect,Z=v===void 0?T.FADE:v,N=t.type,G=N===void 0?"outline":N,B=t.title,q=t.text,w=t.showIcon,X=w===void 0?!0:w,E=t.customIcon,z=E===void 0?"":E,b=t.customClass,W=b===void 0?"":b,O=t.speed,H=O===void 0?500:O,D=t.showCloseButton,$=D===void 0?!0:D,L=t.autoclose,Q=L===void 0?!0:L,S=t.autotimeout,j=S===void 0?3e3:S,x=t.position,J=x===void 0?"right top":x,F=t.customWrapper,K=F===void 0?"":F;if(this.customWrapper=K,this.status=g,this.title=B,this.text=q,this.showIcon=X,this.customIcon=z,this.customClass=W,this.speed=H,this.effect=Z,this.showCloseButton=$,this.autoclose=Q,this.autotimeout=j,this.notificationsGap=n,this.notificationsPadding=h,this.type=G,this.position=J,!this.checkRequirements()){console.error("You must specify 'title' or 'text' at least.");return}this.setContainer(),this.setWrapper(),this.setPosition(),this.showIcon&&this.setIcon(),this.showCloseButton&&this.setCloseButton(),this.setContent(),this.container.prepend(this.wrapper),this.setEffect(),this.notifyIn(this.selectedNotifyInEffect),this.autoclose&&this.autoClose(),this.setObserver()}return yt(s,[{key:"checkRequirements",value:function(){return!!(this.title||this.text)}},{key:"setContainer",value:function(){var e=document.querySelector(".".concat(a.CONTAINER));e?this.container=e:(this.container=document.createElement("div"),this.container.classList.add(a.CONTAINER),document.body.appendChild(this.container)),this.notificationsPadding&&this.container.style.setProperty("--sn-notifications-padding","".concat(this.notificationsPadding,"px")),this.notificationsGap&&this.container.style.setProperty("--sn-notifications-gap","".concat(this.notificationsGap,"px"))}},{key:"setPosition",value:function(){this.container.classList[this.position==="center"?"add":"remove"](a.IS_CENTER),this.container.classList[this.position.includes("left")?"add":"remove"](a.IS_LEFT),this.container.classList[this.position.includes("right")?"add":"remove"](a.IS_RIGHT),this.container.classList[this.position.includes("top")?"add":"remove"](a.IS_TOP),this.container.classList[this.position.includes("bottom")?"add":"remove"](a.IS_BOTTOM),this.container.classList[this.position.includes("x-center")?"add":"remove"](a.IS_X_CENTER),this.container.classList[this.position.includes("y-center")?"add":"remove"](a.IS_Y_CENTER)}},{key:"setCloseButton",value:function(){var e=this,i=document.createElement("div");i.classList.add(a.NOTIFY_CLOSE),i.innerHTML=I.CLOSE,this.wrapper.appendChild(i),i.addEventListener("click",function(){e.close()})}},{key:"setWrapper",value:function(){var e=this;switch(this.customWrapper?this.wrapper=Nt(this.customWrapper):this.wrapper=document.createElement("div"),this.wrapper.style.setProperty("--sn-notify-transition-duration","".concat(this.speed,"ms")),this.wrapper.classList.add(a.NOTIFY),this.type){case P.OUTLINE:this.wrapper.classList.add(a.NOTIFY_OUTLINE);break;case P.FILLED:this.wrapper.classList.add(a.NOTIFY_FILLED);break;default:this.wrapper.classList.add(a.NOTIFY_OUTLINE)}switch(this.status){case y.SUCCESS:this.wrapper.classList.add(a.NOTIFY_SUCCESS);break;case y.ERROR:this.wrapper.classList.add(a.NOTIFY_ERROR);break;case y.WARNING:this.wrapper.classList.add(a.NOTIFY_WARNING);break;case y.INFO:this.wrapper.classList.add(a.NOTIFY_INFO);break}this.autoclose&&(this.wrapper.classList.add(a.NOTIFY_AUTOCLOSE),this.wrapper.style.setProperty("--sn-notify-autoclose-timeout","".concat(this.autotimeout+this.speed,"ms"))),this.customClass&&this.customClass.split(" ").forEach(function(i){e.wrapper.classList.add(i)})}},{key:"setContent",value:function(){var e=document.createElement("div");e.classList.add(a.NOTIFY_CONTENT);var i,n;this.title&&(i=document.createElement("div"),i.classList.add(a.NOTIFY_TITLE),i.textContent=this.title.trim(),this.showCloseButton||(i.style.paddingRight="0")),this.text&&(n=document.createElement("div"),n.classList.add(a.NOTIFY_TEXT),n.innerHTML=this.text.trim(),this.title||(n.style.marginTop="0")),this.wrapper.appendChild(e),this.title&&e.appendChild(i),this.text&&e.appendChild(n)}},{key:"setIcon",value:function(){var e=p(function(n){switch(n){case y.SUCCESS:return I.SUCCESS;case y.ERROR:return I.ERROR;case y.WARNING:return I.WARNING;case y.INFO:return I.INFO}},"computedIcon"),i=document.createElement("div");i.classList.add(a.NOTIFY_ICON),i.innerHTML=this.customIcon||e(this.status),(this.status||this.customIcon)&&this.wrapper.appendChild(i)}},{key:"setObserver",value:function(){var e=this,i=new IntersectionObserver(function(n){if(n[0].intersectionRatio<=0)e.close();else return},{threshold:0});setTimeout(function(){i.observe(e.wrapper)},this.speed)}},{key:"notifyIn",value:function(t){t(this)}},{key:"autoClose",value:function(){var e=this;setTimeout(function(){e.close()},this.autotimeout+this.speed)}},{key:"close",value:function(){this.notifyOut(this.selectedNotifyOutEffect)}},{key:"setEffect",value:function(){switch(this.effect){case T.FADE:this.selectedNotifyInEffect=Y,this.selectedNotifyOutEffect=A;break;case T.SLIDE:this.selectedNotifyInEffect=wt,this.selectedNotifyOutEffect=Et;break;default:this.selectedNotifyInEffect=Y,this.selectedNotifyOutEffect=A}}}]),s}();p(U,"Notify");var V=U;globalThis.Notify=V;let l=[],C={},r=null;const bt=s=>{if(!r)return d("updateCoords: AudioContext not found.");r.listener.positionX.setValueAtTime(s.coords.x,r.currentTime),r.listener.positionY.setValueAtTime(s.coords.y,r.currentTime),r.listener.positionZ.setValueAtTime(s.coords.z,r.currentTime),r.listener.forwardX.setValueAtTime(s.rotation.x,r.currentTime),r.listener.forwardY.setValueAtTime(s.rotation.y,r.currentTime),r.listener.forwardZ.setValueAtTime(s.rotation.z,r.currentTime),r.listener.upX.value=0,r.listener.upY.value=0,r.listener.upZ.value=1,[...l].filter(e=>!e.sound.data.dynamic).forEach(e=>{e.sound.setCoords(s.coords)}),s.dynamicPlayList.length>0&&s.dynamicPlayList.forEach(e=>{const i=l.find(n=>n.id===e.soundId)?.sound;if(!i)return f("soundDestroyed",{soundId:e.soundId}),d("tick: sound not found. We are forcing it to destroy:",e.soundId);i.setCoords(e.coords)})},Ot=()=>{d("destroyAudioContext"),r&&r.close().then(()=>{d("successfully audio ctx destroyed"),r=null})};setInterval(()=>{const s=[];l.forEach(t=>{const e=t.sound.getCurrentTime();s.push({soundId:t.id,time:e||0})}),f("updateTime",{sounds:s})},1e3);setInterval(async()=>{if(l.length==0)return;for(let t=0;t<l.length;t++)await l[t].sound.tick(r);l.some(t=>t.sound.playerCreated||t.sound.playing)&&!r&&(r=new AudioContext,d("tick interval ::: AudioContext created"))},100);const _=s=>{const t=l.find(e=>e.id===s);t&&(t.sound.destroy(),l=l.filter(e=>e.id!==s))},c=s=>{const t=l.find(e=>e.id===s);return t?t.sound:!1},Dt=()=>{!l.some(t=>t.sound.playerCreated)&&r&&(d("handlePlayerDestroy: AudioContext destroyed"),Ot())};window.addEventListener("message",function(s){const t=s.data.action;let e=s.data.data;if(t==="updateCoords")bt(e);else if(t=="play"){e=e;const i=e.soundId;_(i),!r&&e.play&&(r=new AudioContext,d("play ::: AudioContext created"));const n=new ct(e.url,i,r,e,C);n.onPlayerDestroy=Dt,l.push({sound:n,id:i})}else if(t=="setLoop"){const i=c(e.soundId);if(!i)return d("setLoop: sound not found. soundId:",e.soundId);i.setLoop(e.loop)}else if(t=="setCoords"){const i=c(e.soundId);if(!i)return d("setCoords: sound not found. soundId:",e.soundId);i.setCoords(e.coords)}else if(t=="setMaxDistance"){const i=c(e.soundId);if(!i)return d("setMaxDistance: sound not found. soundId:",e.soundId);i.setMaxDistance(e.maxDistance)}else if(t=="resume"){const i=c(e.soundId);if(!i)return d("resume: sound not found. soundId:",e.soundId);i.resume()}else if(t=="seek"){const i=c(e.soundId);if(!i)return d("seek: sound not found. soundId:",e.soundId);i.seek(e.seek)}else if(t=="stop"){const i=c(e.soundId);if(!i)return d("stop: sound not found. soundId:",e.soundId);i.stop()}else if(t=="pause"){const i=c(e.soundId);if(!i)return d("pause: sound not found. soundId:",e.soundId);i.pause()}else if(t=="destroy")_(e.soundId);else if(t=="addFilter"){const i=c(e.soundId);if(!i)return d("addFilter: sound not found. soundId:",e.soundId);if(!e.filter)return d("addFilter: filter not found. soundId:",e.soundId);const n=e.filter||Lt;i.createFilter(n)}else if(t=="removeFilter"){const i=c(e.soundId);if(!i)return d("removeFilter: sound not found. soundId:",e.soundId);i.clearFilter()}else if(t=="setVolumeMax"){e.volume>1&&(e.volume=1),e.volume<0&&(e.volume=0);const i=c(e.soundId);if(!i)return d("setVolumeMax: sound not found. soundId:",e.sound);i.setVolume(e.volume)}else if(t=="setPanner"){const i=c(e.soundId);if(!i)return d("setPanner: sound not found. soundId:",e.soundId);i.setPanner(e.panner)}else if(t=="setStereo"){const i=c(e.soundId);if(!i)return d("setStereo: sound not found. soundId:",e.soundId);i.setStereo(e.stereo)}else t=="notification"&&new V({text:String(e),status:"info",effect:"slide",type:"filled"})});window.addEventListener("load",()=>{f("getConfig").then(s=>{C=s,it(C.debug)})});const Lt={type:"lowpass",frequency:500,quality:1,gain:0};
