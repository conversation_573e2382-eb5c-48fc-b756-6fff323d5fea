const { createGetData, getImages } = require('./server/api/spinfo.js');
const YTMusic = require('ytmusic-api').default

const ytm = new YTMusic();

async function getSpotifyTracks(url) {
    const data = await createGetData(url)
    if (!data) return null;
    const _data = []
    let thumbnails = data?.visualIdentity?.image ?? data?.coverArt?.sources ?? []
    if (thumbnails.length === 0) {
        thumbnails.push({
            url: 'https://i.ytimg.com/vi/0W4pMAv0YjY/maxresdefault.jpg',
            width: 1280,
            height: 720
        })
    }
    for (const track of data.trackList) {
        _data.push({
            thumbnails,
            artist: {
                name: track.subtitle ?? 'Unknown',
            },
            name: track.title ?? 'Unknown',
            album: data.name ?? 'Unknown',
            description: 'Unknown',
        })
    }
    return _data
}

const getYoutubeInfo = async (url) => {
    await ytm.initialize()
    const soundId = getVideoID(url)
    if (!soundId) return {
        message: 'Invalid URL',
        statusCode: 400
    }
    const info = await ytm.getVideo(soundId)
    const thumbnail = info.thumbnails?.[info.thumbnails.length - 1]?.url ?? 'https://i.ytimg.com/vi/0W4pMAv0YjY/maxresdefault.jpg'
    const artist = info.artist?.name ?? 'Unknown'
    return {
        thumbnail,
        artist,
        title: info?.name ?? 'Unknown',
        album: 'none',
        description: 'none',
        url,
        videoId: soundId
    }
}

const spotifyGetInfo = async (url) => {
    const info = await createGetData(url)
    const images = getImages(info)
    const youtube = await Search(`${info.artists[0].name} - ${info.title}`)
    if (youtube.error) return {
        message: 'No results found',
        statusCode: 404
    }
    const yt = youtube[0]
    return {
        thumbnail: images.reduce((acc, img) => img.width < acc.width ? img : acc).url,
        artist: info.artists ? info.artists?.map?.((artist) => artist.name)?.join(', ') : 'Unknown',
        title: info.title || 'Unknown',
        album: info.name || 'Unknown',
        description: info.description || info.subtitle,
        videoId: yt.videoId,
        url: yt.url
    }
}

exports('getInfo', async (url) => {
    try {
        if (isYoutube(url)) {
            return await getYoutubeInfo(url)
        } else {
            return await spotifyGetInfo(url)
        }
    } catch (e) {
        return {
            message: 'No results found',
            statusCode: 404
        }
    }
});

const Search = async (query) => {
    try {
        await ytm.initialize()
        const results = await ytm.searchSongs(query)
        if (!results) return {
            error: 'No results found',
            errorCode: 404
        }
        return results.map((result) => ({
            ...result,
            url: `${BASE_URL}/watch?v=${result.videoId}`
        }))
    } catch (e) {
        console.log(e)
        return {
            error: 'No results found',
            errorCode: 404
        }
    }
}

exports('search', Search)

exports('getSpotifyPlaylist', async (url, list) => {
    let data
    if (isSpotifyPlaylist(url) || isSpotifyAlbum(url)) {
        data = await getSpotifyTracks(url)
    }
    if (!data) return {
        error: 'No results found',
        errorCode: 404
    }
    return {
        trackLength: data.length,
        data
    }
});


const BASE_URL = 'https://www.youtube.com',
    URL_REGEX = /^https?:\/\//,
    ID_REGEX = /^[a-zA-Z0-9-_]{11}$/,
    VALID_QUERY_DOMAINS = new Set(['youtube.com', 'www.youtube.com', 'm.youtube.com', 'music.youtube.com', 'gaming.youtube.com']),
    VALID_PATH_DOMAINS = /^https?:\/\/(youtu\.be\/|(www\.)?youtube\.com\/(embed|v|shorts|live)\/)/;

function validateID(id) {
    return ID_REGEX.test(id.trim());
}

function getURLVideoID(link) {
    const PARSED = new URL(link.trim());
    let id = PARSED.searchParams.get('v');

    if (VALID_PATH_DOMAINS.test(link.trim()) && !id) {
        const PATHS = PARSED.pathname.split('/');
        id = PARSED.host === 'youtu.be' ? PATHS[1] : PATHS[2];
    } else if (PARSED.hostname && !VALID_QUERY_DOMAINS.has(PARSED.hostname)) {
        throw new Error('Not a YouTube domain');
    }

    if (!id) {
        throw new Error(`No video id found: "${link}"`);
    }

    id = id.substring(0, 11);

    if (!validateID(id)) {
        throw new TypeError(`Video id (${id}) does not match expected format (${ID_REGEX.toString()})`);
    }

    return id;
}


function getVideoID(str) {
    if (validateID(str)) {
        return str;
    } else if (URL_REGEX.test(str.trim())) {
        return getURLVideoID(str);
    } else {
        return null;
    }
}

const isSpotifyPlaylist = (url) => {
    const regex = /^(https:\/\/open\.spotify\.com\/playlist\/)([a-zA-Z0-9]+)(.*)$/;
    return regex.test(url)
}

const isSpotifyAlbum = (url) => {
    const regex = /^(https:\/\/open\.spotify\.com\/album\/)([a-zA-Z0-9]+)(.*)$/;
    return regex.test(url)
}

const isSpotifyUrl = (url) => {
    return isSpotifyPlaylist(url) || isSpotifyAlbum(url)
}

exports('isSpotifyUrl', isSpotifyUrl)

const validQueryDomains = new Set([
    'youtube.com',
    'www.youtube.com',
    'm.youtube.com',
    'music.youtube.com',
    'gaming.youtube.com',
]);

const getYoutubePlaylistID = (url) => {
    try {
        const parsed = new URL(url);
        if (!validQueryDomains.has(parsed.hostname)) return null;
        let id = parsed.searchParams.get('list');
        if (!id) return null;
        return id
    } catch (e) {
        return null;
    }
}

const isYoutube = (url) => {
    const regex = /^(https?:\/\/)?(www\.)?(m\.)?(youtube\.com|youtu\.?be)\/.+$/gi;
    return regex.test(url)
}

exports('getYoutubePlaylistID', getYoutubePlaylistID)