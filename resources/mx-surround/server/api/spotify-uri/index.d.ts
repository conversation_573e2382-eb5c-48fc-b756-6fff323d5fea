declare enum SpotifyTypes {
    Album = "album",
    Artist = "artist",
    Episode = "episode",
    Local = "local",
    Playlist = "playlist",
    Search = "search",
    Show = "show",
    Track = "track",
    User = "user",
    Embed = "embed"
}

declare abstract class SpotifyUri {
    type: SpotifyTypes;
    id: string;
    uri: string;
    constructor(uri: string, id: string, type: SpotifyTypes);
    static is(v: any): v is SpotifyUri;
    toURI(): string;
    toURL(): string;
    toEmbedURL(): string;
    toOpenURL(): string;
    toPlayURL(): string;
}

/**
 * Parses a "Spotify URI".
 *
 * @param {String} input
 * @return {Object} parsed Spotify uri object
 * @api public
 */
declare function parse(input: string | SpotifyUri): ParsedSpotifyUri;

declare class Search extends SpotifyUri {
    get query(): string;
    static is(v: any): v is Search;
}

declare class Local extends SpotifyUri {
    artist: string;
    album: string;
    track: string;
    seconds: number;
    constructor(uri: string, artist: string, album: string, track: string, seconds: number);
    static is(v: any): v is Local;
    toURI(): string;
    toURL(): string;
}

declare class Playlist extends SpotifyUri {
    user?: string;
    constructor(uri: string, id: string, user?: string);
    static is(v: any): v is Playlist;
    toURI(): string;
    toURL(): string;
}

declare class Artist extends SpotifyUri {
    static is(v: any): v is Artist;
}

declare class Album extends SpotifyUri {
    static is(v: any): v is Album;
}

declare class Track extends SpotifyUri {
    static is(v: any): v is Track;
}

declare class User extends SpotifyUri {
    get user(): string;
    static is(v: any): v is User;
}

declare class Episode extends SpotifyUri {
    static is(v: any): v is Episode;
}

declare class Show extends SpotifyUri {
    static is(v: any): v is Show;
}

type ParsedSpotifyUri = Search | Episode | Local | Playlist | Track | Artist | Album | User | Show;

declare function formatURI(input: string | SpotifyUri): string;
declare function formatEmbedURL(input: string | SpotifyUri): string;
declare function formatOpenURL(input: string | SpotifyUri): string;
declare function formatPlayURL(input: string | SpotifyUri): string;

export { Album, Artist, Episode, Local, type ParsedSpotifyUri, Playlist, Search, Show, Track, User, formatEmbedURL, formatOpenURL, formatPlayURL, formatURI, parse };
