-- https://github.com/overextended/ox_lib

local events = {}
local cbEvent = ('__ox_cb_%s')
_G['lib'] = {}

RegisterNetEvent(cbEvent:format(GetCurrentResourceName()), function(key, ...)
	local cb = events[key]
	return cb and cb(...)
end)

---@param _ any
---@param event string
---@param playerId number
---@param cb function|false
---@param ... any
---@return unknown?
local function triggerClientCallback(_, event, playerId, cb, ...)
	local key

	repeat
		key = ('%s:%s:%s'):format(event, math.random(0, 100000), playerId)
	until not events[key]

	TriggerClientEvent(cbEvent:format(event), playerId, GetCurrentResourceName(), key, ...)

	---@type promise | false
	local promise = not cb and promise.new()

	events[key] = function(response)
		events[key] = nil

		if promise then
			return promise:resolve(response and { msgpack.unpack(response) } or {})
		end

		if cb and response then
			cb(msgpack.unpack(response))
		end
	end

	if promise then
		return table.unpack(Citizen.Await(promise))
	end
end

---@overload fun(event: string, playerId: number, cb: function, ...)
lib.callback = setmetatable({}, {
	__call = triggerClientCallback
})

---@param event string
---@param playerId number
--- Sends an event to a client and halts the current thread until a response is returned.
function lib.callback.await(event, playerId, ...)
	return triggerClientCallback(nil, event, playerId, false, ...)
end

local function callbackResponse(success, result, ...)
	if not success then
		if result then
			return print(('^1SCRIPT ERROR: %s^0\n%s'):format(result, Citizen.InvokeNative(`FORMAT_STACK_TRACE` & 0xFFFFFFFF, nil, 0, Citizen.ResultAsString()) or ''))
		end

		return false
	end

	return msgpack.pack(result, ...)
end

local pcall = pcall

---@param name string
---@param cb function
--- Registers an event handler and callback function to respond to client requests.
function lib.callback.register(name, cb)
	RegisterNetEvent(cbEvent:format(name), function(resource, key, ...)
		local src = source
		TriggerClientEvent(cbEvent:format(resource), src, key, callbackResponse(pcall(cb, source, ...)))
	end)
end

exports('callback', function()
	return lib.callback
end)

return lib.callback
