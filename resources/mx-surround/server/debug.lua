if not Config.Debug then
    return
end

Debug('Initialized test commands')

local urlList = {
    'https://www.youtube.com/watch?v=VmoLYmAXrAk&list=RDVmoLYmAXrAk&start_radio=1',
    'https://www.youtube.com/watch?v=iqoNoU-rm14&list=RDVmoLYmAXrAk&index=2',
    'https://www.youtube.com/watch?v=ymq1WdGUcw8&list=RDVmoLYmAXrAk&index=3',
    'https://www.youtube.com/watch?v=4JHBrLXZBhw&list=RDVmoLYmAXrAk&index=4',
    'https://www.youtube.com/watch?v=YgS-Ap9K_aM&list=RDVmoLYmAXrAk&index=5',
    'https://www.youtube.com/watch?v=9qTmFnUqoLs&list=RDVmoLYmAXrAk&index=6',
    'https://www.youtube.com/watch?v=TbDyQuMJ5QI&list=RDVmoLYmAXrAk&index=7',
    'https://www.youtube.com/watch?v=OJhqsUnKUWw&list=RDVmoLYmAXrAk&index=8',
    'https://www.youtube.com/watch?v=MqyOPyBPlns&list=RDVmoLYmAXrAk&index=9',
    'https://www.youtube.com/watch?v=8Yue9YYdNLM&list=RDVmoLYmAXrAk&index=10',
    'https://www.youtube.com/watch?v=ZLohS_HScwc&list=RDVmoLYmAXrAk&index=11',
    'https://www.youtube.com/watch?v=d__uFhNmbBI&list=RDVmoLYmAXrAk&index=12',
    'https://www.youtube.com/watch?v=klTBagBYXpk&list=RDVmoLYmAXrAk&index=13',
    'https://www.youtube.com/watch?v=KrSzyJyYC5Y&list=RDVmoLYmAXrAk&index=14',
    'https://www.youtube.com/watch?v=t2Ti8d992RM&list=RDVmoLYmAXrAk&index=15',
    'https://www.youtube.com/watch?v=Q7SYKDhzu88&list=RDVmoLYmAXrAk&index=16',
    'https://www.youtube.com/watch?v=EZUPEoj3Qjs&list=RDVmoLYmAXrAk&index=17',
}

local soundCoords = {
    vec3(233.75135803222656, -1067.8619384765625, 29.21747589111328),
    vec3(280.00286865234375, -1033.23828125, 29.28166007995605),
    vec3(322.5904541015625, -1068.127197265625, 29.31263542175293),
    vec3(358.3526611328125, -1032.498291015625, 29.34980583190918),
    vec3(406.33056640625, -1068.867919921875, 29.38520622253418),
    vec3(414.8170166015625, -990.7307739257812, 29.40600395202636),
    vec3(415.30609130859375, -890.2222290039062, 29.42370414733886),
    vec3(416.7891845703125, -833.3514404296875, 29.43476676940918),
    vec3(411.73822021484375, -707.4351196289062, 29.45202445983886),
    vec3(438.9271240234375, -592.4657592773438, 29.49538993835449),
    vec3(462.9969482421875, -518.3805541992188, 29.52902030944824),
    vec3(148.8815155029297, -580.4983520507812, 43.81758117675781),
    vec3(43.68973541259765, -697.158935546875, 45.72592163085937),
    vec3(-52.32830429077148, -722.502197265625, 44.26382446289062),
    vec3(-225.50965881347656, -653.652587890625, 34.06594848632812),
    vec3(-333.009521484375, -673.5787353515625, 33.02227020263672),
    vec3(-598.0825805664062, -644.6072387695312, 35.54351806640625),
    vec3(-726.0289916992188, -671.3406982421875, 31.04931640625),
    vec3(-657.6828002929688, -821.67333984375, 27.27066993713379),
    vec3(-481.8772583007813, -1081.4578857421875, 26.50606155395507),
    vec3(-297.74822998046875, -1155.1435546875, 25.17542457580566),
    vec3(-74.04403686523438, -1111.3751220703125, 27.33841705322265),
    vec3(-32.33475875854492, -993.5839233398438, 30.21953392028808),
    vec3(315.8694152832031, -233.6157379150391, 54.01786041259765),
    vec3(190.3390350341797, -192.5137481689453, 54.63930130004883),
    vec3(43.94049835205078, -133.5149078369141, 56.95316696166992),
    vec3(-90.47498321533205, -85.56704711914062, 57.54666900634765),
    vec3(-225.94717407226565, -38.87411117553711, 50.63346862792969),
    vec3(-375.85357666015625, 11.72151660919189, 47.68080902099609),
    vec3(-494.9742126464844, 10.58783054351806, 45.85217666625976)
}

Debug('Initialized sv_play_npc_vehicles command')
RegisterCommand('sv_play_npc_vehicles', function(source, args)
    local panner = {
        panningModel = 'HRTF',
        refDistance = 15.0,
        rolloffFactor = 0.01,
        distanceModel = 'exponential',
        coneInnerAngle = 360.0,
        coneOuterAngle = 0.0,
    }

    local pedCoords = GetEntityCoords(GetPlayerPed(source))
    local vehicleList = GetAllVehicles()
    vehicleList = table.filter(vehicleList, function(v)
        return not IsPedAPlayer(GetPedInVehicleSeat(v, -1)) and #(GetEntityCoords(v) - pedCoords) < 100.0
    end)
    Debug('Vehicle count:', #vehicleList)
    for k, v in pairs(vehicleList) do
        CreateThread(function()
            local soundId = exports['mx-surround']:createUniqueId()
            local url = urlList[math.random(1, #urlList)]
            local netId = NetworkGetNetworkIdFromEntity(v)
            exports['mx-surround']:Play(-1, soundId, url, GetEntityCoords(v), true, nil)
            exports['mx-surround']:attachEntity(-1, soundId, netId)
        end)
    end
end, false)


Debug('Initialized sv_play_area command')
RegisterCommand('sv_play_area', function(source, args)
    local panner = {
        -- panningModel = 'HRTF',
        -- refDistance = 15.0,
        -- rolloffFactor = 0.01,
        -- distanceModel = 'exponential',
        -- coneInnerAngle = 360.0,
        -- coneOuterAngle = 0.0,
    }

    for k, v in pairs(soundCoords) do
        CreateThread(function()
            local soundId = exports['mx-surround']:createUniqueId()
            local url = urlList[math.random(1, #urlList)]
            exports['mx-surround']:Play(-1, soundId, url, v, true, nil)
        end)
    end
end, false)

RegisterCommand('playsv', function(source, args)
    local ped = GetPlayerPed(source)
    local coords = GetEntityCoords(ped)
    local vehicle = GetVehiclePedIsIn(ped, false)
    local panner = {
        panningModel = 'HRTF',
        refDistance = 1.2,
        rolloffFactor = 1.0,
        distanceModel = 'exponential',
        coneInnerAngle = 360.0,
        coneOuterAngle = 0.0,
    }
    -- You can't play the sound if it's not prepared.
    for i = 1, 1 do
        CreateThread(function()
            local soundId = CreateUniqueId() .. i
            exports['mx-surround']:Play(-1, soundId, args[1], coords, false)
            if vehicle ~= 0 then
                local networkId = NetworkGetNetworkIdFromEntity(vehicle)
                exports['mx-surround']:attachEntity(-1, soundId, networkId)
            end
        end)
    end
    -- Wait(15000)
    -- destroy sound
    -- destroyAllSound()
    -- play(args[1], coords, vehicle, true, panner)
end, false)

RegisterCommand('playsvint', function(source, args)
    local ped = GetPlayerPed(source)
    local coords = GetEntityCoords(ped)
    local vehicle = GetVehiclePedIsIn(ped, false)
    local panner = {
        panningModel = 'HRTF',
        refDistance = 20.0,  -- Distance of the volume dropoff start
        rolloffFactor = 1.8, -- How fast the volume drops off (don't 0.1)
        distanceModel = 'exponential',
        coneInnerAngle = 360.0,
        coneOuterAngle = 0.0,
    }
    local soundId = CreateUniqueId()
    -- You can't play the sound if it's not prepared.
    exports['mx-surround']:Play(-1, soundId, args[1], vec3(108.65, -1288.81, 28.86), true, nil, panner)

    Wait(5000)
    SetTimeStamp(-1, soundId, 5)
    Wait(1000)
    local timeStamp = GetTimeStamp(soundId)
end, false)

RegisterCommand('asset', function(source, args)
    local sound = 'beltalarm'
    local player = GetPlayerPed(source)
    local vehicle = GetVehiclePedIsIn(player, true)
    local vehNetId = NetworkGetNetworkIdFromEntity(vehicle)
    local coords = GetEntityCoords(vehicle)
    exports['mx-surround']:PlayAsync(-1, nil, '/ui/sounds/' .. sound .. '.ogg', coords, false, 1.0, nil, {
        attachEntity = vehNetId
    })
end, false)

RegisterNetEvent('mx-surround:debug:playRandomSoundOnEntity', function(netId)
    local url = urlList[math.random(1, #urlList)]
    local soundId = CreateUniqueId()
    local entity = NetworkGetEntityFromNetworkId(netId)
    exports['mx-surround']:Play(-1, soundId, url, GetEntityCoords(entity), true)
    exports['mx-surround']:attachEntity(-1, soundId, netId)
end)
