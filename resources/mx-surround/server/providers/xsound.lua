if not Config.ProvideXsound then return end

-- https://github.com/overextended/ox_target/blob/main/client/compat/qb-target.lua
local function exportHandler(exportName, func)
    AddEventHandler(('__cfx_export_xsound_%s'):format(exportName), function(setCB)
        Warning('Please update your exports to mx-surround. Do not use xsound exports anymore.')
        setCB(func)
    end)
end

exportHandler('Position', function(soundId, coords)
    return SetCoords(soundId, coords, true)
end)

exportHandler('PlayUrl', function(source, name_, url_, volume_, loop_)
    TriggerClientEvent('mx-surround:xsound:PlayUrl', source, name_, url_, volume_, loop_)
end)

exportHandler('PlayUrlPos', function(source, name_, url_, volume_, pos, loop_)
    return SPlaySound(source, name_, url_, pos, loop_, volume_)
    -- TriggerClientEvent('mx-surround:xsound:PlayUrlPos', source, name_, url_, volume_, pos, loop_)
end)

exportHandler('Distance', SetMaxDistance)
exportHandler('Destroy', DestroySound)
exportHandler('Resume', ResumeSound)
exportHandler('Pause', PauseSound)
exportHandler('setVolume', SetVolumeMax)
exportHandler('setTimeStamp', SetTimeStamp)
exportHandler('destroyOnFinish', SetDestroyOnFinish)
